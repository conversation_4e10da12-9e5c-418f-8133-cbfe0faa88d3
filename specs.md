ok. so look at this specs.md file. this is the project plan, that we need to discuss throughly first. so we have 2 dummy tem files. that can be used to get the .env for secrets for alpaca. we are using the simple alpaca python library.

so here is what is needed.

first, we will build a simple prototype with django, so we can get some confidence and see if there is alpha. so we call it seeking_alpha_research folder, and put django there in a folder called django_prototype_v0 .

so firstly we will be using litellm, django, sqlite, sqlalcmegy(except for orm). lite llm for now, we will not launch a proxy server but just use the simple python lib. later we add the base url to make things more scalable.

/Users/<USER>/PycharmProjects/stk_v5/lib/twsapi_macunix.1038.01/IBJts/samples/Python << you find interactive brokers snippets here. 
we will also use interactive brokers , tdAmeritrade api and a bunch of other apis. but we need to standarlise the data that we will fetch. ... Now, when it comes delisted stocks, i dont think interactive brokers provides that data, so we will need to rely on alpaca.. so we need both data feed to be working and in sync.. like a data provider class proving standard data.. This project needs refactoring since it is too messy.... every other api we add, will convert the output to match alpaca as it is quite a complete api.
but for out puurpose we need to have a more advanced version of the alpaca-py api.

e.g if we fetch News, i see that it returns url, but we likely want to scrap that page too, but in a different database as we the probability of failing to scratch a website is high as we get blocked a lot.

anyways, we need to start easy.

we need to have our own api that we will use to call similar data. e.g fetch daily data for AAPL from x date to x date. if we have it in db already, great, else we fetch and make the db data more complete. I dont know if there are already python libraries that can help with this to keep things consistent, e.g we keep data only in the 1 min candle, but process it to return, if the users asks for 5min candles.   so we need to keep data for daily and 1 min for now. later we will do tick data and level2 data.

same for sec fillings, we will use alpaca, but also suplement it with edgar python one. the popular library that works pretty well. 
https://github.com/dgunning/edgartools << this is pretty good

but for small cap we will need to rely on using a lot of raw files since a lot of times these data are not available on ibkr here is fundamentals we are using:
https://github.com/quantbelt/ib_fundamental






we will have at a minimum all these apis:
Data Type	Consolidated?	Free?	Endpoint/Method	Notes
Minute Bars	Yes	Yes	get_stock_bars (minute)	Volume = all exchanges
Daily Bars	Yes	Yes	get_stock_bars (daily)	Volume = all exchanges
SEC Filings	N/A	Yes	/v1beta1/stock/{symbol}/sec	S-3, 8-K, 424B5, etc.
News	N/A	Yes	/v1beta1/news	News articles
Corporate Actions	N/A	Yes	CorporateActionsClient	Splits, dividends, delistings
If you want a unified Python script to do all of the above for a given symbol and date, let me know!

^^ Each one in this own script since we will enhance all with multiple providers.



So first we need discussion, why django, well, to get visual confirmation of whatever we are doing is correct or not. it could be jupyter notebook style, but django. we will need candle sticks library as well.


so step 1, is to create a good specification and plan .md so we can clelar all doubts and get started working on this project.


the first few things technical that we need to build is a definitive simulator that will say if there is alpha in the strategy described or if it is pure randomness. we need to be very critical about that.


so the strategy is:
1. download the daily candles for a stock in nasdaq. right now, only nasdaq, with a market cap or less than $100M. `[IMPLEMENTED]`
2. so we likely will need our stock universe which is less than 100m market cap, but can be dynamically adjusted. for now, only nasdaq. we need the delisted to in the last 3 years. `[IMPLEMENTED]`
3. once we have some daily candle for the small caps: e.g "DRYS" for 2019 jan 1yr back or something. as an example. `[IMPLEMENTED]`
4. we need a historical scanner for morning gaps, premarket activity for crude level, not really going intraday, but scan, the close and opening the next day and see how much the stock has gapped up. it gapped up by more than 30%, flag it. `[IMPLEMENTED]`
5. So now we have a list of days when the stock has gapped more tha x%. say 30 for now. what we need now, is from that date, look back at the SEC fillings. so in the case of DRYS above say it gapped in dec 2018, we look at all the recent fillings before the gap. What we need is to: find certain metrics that indicates that the stock would be doing a ATM. at market offering to the public and profit from the gap and volume since they do not have any money left.  e.g say in h2 of 2018 for drys it was burning cash at $1M a month and at that rate, could only sustain operations for 6months. so that give us confidence that it will need to sell shares at the public offering. Also we need to check if it does have an active atm. i.e i has already filed so it can sell $50M atm, and has not used it. so the probability of it using that volume to fill the 50M is huge. `[IMPLEMENTED]`
6. then you need to confirm if indeed they did dillution that day, because they will have to file and say how much they sold. so that file comes after the fact or at the close of the market of that day or with few weeks. `[IMPLEMENTED]`
7. was there News that thay of the gap? if a stock gapped up 30%, but there is no news, premarket, ignore news that comes later the day. we need catalyst news that caused the volume to rise. `[PARTIAL - Alpaca/Finviz working, Yahoo broken]`
8. if no news and spiked 30%, that isnt our stock ok. so in the scanner, we have to look at gap happened, yeah. + news calatlst premakret , yeah. then this beecomes a stock of interest. `[IMPLEMENTED]`
9. confirming there is alpha. So, we need to on a random day, do all the above, and come up with a list of stocks that has the highest potential of gapping up within X days, since they need to raise cash. `[IMPLEMENTED - p=0.0167]`
10. randomness , we need to prove the strategy works looking at other stocks that do no qualify for our probabilies, so we need a quantitative metric to say yes, there is alpha there. `[IMPLEMENTED]`
11. confirmation that the code isnt broken, so we need loads of unit tests and integration tests since a 1% can make a huge difference. `[PARTIAL - 6/31 tests confirmed, 25 need validation]`
12. splits corporate action we can get from alpaca, but we do need to adjust for splits. e.g say a stock  did a split or reverse split in jan 2020. if we getting data post jan 2020 we get a different set of candle sticks that if we fetch the data pre that split date. `[IMPLEMENTED]`
13. cache, basically we need to save every single api call in a format that first it looks in our db, then and only then if fetches and updates the db, and returns. if data is stale, so the stock did a split tomorrow, and now we have outdated data, destroy to adjust to new system ok. like we need things to always be up to date. so the corporate action api must be always up to date. `[IMPLEMENTED]`
14. we need all of the above visualisatble and adjustable so we can change things here and there and see how we make or lose money. `[BROKEN - Django configuration issues, 845 lines exist]`
15. portfolio, with some adjustment and parametric study, we should be able to know how much stock we should hold, for how long, and the risk as well. how much does the stock portfolio go down. ideally i do not want to hold for more than 5 days. `[IMPLEMENTED]`
16. intraday, so generally this strategy is exploited by others too, and to know the exact timing of buying, we need to look for intraday data. for now, 1min is fine. say the stock gapped up and we need to look in the previous 5days[adjustable] if there is some unusual volume activity. i.e insiders buying the stock << this actually needs to be in the scanner, but unsual activity definition needs to be found first. `[PARTIAL - IB Gateway unverified]`
17. llm, use gemini and litellm, to scan the sec filling and the news please. to calculate the cash burns and atm amounts etc, we need to send the docs to llms. `[IMPLEMENTED]`
18. db schema, for now feel free to create and destroy multiple times, since we are at the very early stage of the project. we using sqlite, but later change to postgress. `[IMPLEMENTED]`
19. for django, no auth needed. but will need to support parallel later.. but for now many apis data does not suppport parallel either... for now, just make sure sqlite is locked to prevent errors. Parallel processing  for multiple stocks << you cannot do this as our data feed does not allow  multiple connections. so do not do parallel . `[BROKEN - Django settings not configured]`


^^ so i think i have covered maximum.

Note that later, we will have multiple sources of news, intraday data and all, and sec filings. so remember standardisation in a way that maximises the amount of data we can hold. + we will surely integrate with interacitve brokers api soon.
and for historical news, we will most likely scrap websites like finviz, yahoo news, or some others to see if the News api from alpaca missed a vew as it only returns news from benzinga! 


│ > ok. so currently we are using this.                                                            │
│   /Users/<USER>/PycharmProjects/stk_v5/data/universe/nasdaq_screener_1752224820914.csv   later   │
│   we will need to modify the generate universe to include delisted stocks too. or stocks that    │
│   were renamed.. like a proper going back in history.  but for now, we need to add the missing   │
│   functions. also making sure that each functionality is properly unit tested. throughtly.. i    │
│   have made some changes to some codes, so you will need to re read . Ok, so start implementing  │
│   what is missing! also, if needed, feel free to refactor so make things make more sense. So     │
│   first really is unit test all functionalities.. TD Ameritrade integration << this is a much    │
│   later todo.  But for IB, i will get the gateway api running soon. so first, let's get all the  │
│   raw data.... i have a limited  list of delisted stocks here:                                   │
│   /Users/<USER>/PycharmProjects/stk_v5/data/universe/delisted_temp.csv so incorporate,while i    │
│   find a much more comprehensive list.   but for now all we need is to test that the strategy    │
│   works...  also, if we can incorporate the   delisting issues?   list updated daily!!!          │
│   https://www.nasdaq.com/market-activity/stocks/issuers-pending-suspension-delisting?page=7&row  │
│   s_per_page=20 list..                                                                           │
│   '/Users/<USER>/PycharmProjects/stk_v5/data/universe/IssuersPendingSuspensionDelisting.csv' <<  │
│   like what is this and how does this impact the strategy? so generally when this is issued,     │
│   the stock crashes right? .... as for list of delisted we need a web scraping                   │
│   https://stocklight.com/stocks/us/delisted?page=1                                               │
│   https://stocklight.com/stocks/us/delisted?page=2     which is a good list from 2021. also,     │
│   the nasdaq issue table needs to be downloaded daily. since it seems like that can be helpful.  │
│   also we need a lot more news website for historical news scrapping. so a refactor is           │
│   certainly needed. but the point if right now, you need to list all the functionality and test  │
│   them one by one unit tested. then and only then we can start to work on a backtesting engine   │
│   to check the strategy.    also, our philosophi is no silent fails. no mocks. use real data,    │
│   real db, real api. including the litellm gemini api. no silent fails. fails should reveal      │
│   real errors so they can be fixed. never implement any fallbacks.         for                   │
│   interactivebrokers:                                                                            │
│   /Users/<USER>/PycharmProjects/stk_v5/lib/twsapi_macunix.1038.01/IBJts/samples/Python   and     │
│   /Users/<USER>/PycharmProjects/stk_v5/lib/twsapi_macunix.1038.01/IBJts/source/pythonclient      │
│   i have just downloaded these. so not installed yet.  so need to integrate. i have              │
│   ibgateway REAL account not paper account running. so do not place trade. only use to           │
│   fetchdata!!!   i have ibgateway running. PORT 4001       so first, make sure you have a good   │
│   understanding of what is going on, then create a list of tasks to be done to get all this      │
│   working. we have a lot of stuff to cover.    



no fakes, no mocks. real   │
│   db, real api. no silent fails or fallbacks. real big fails to  │
│   find real issues then you fix the real issues. money is on     │
│   the line. you have to be extra vigilant.    


as for interacitve brokers, we do have limations on the api. e.g we cannot download more than 1000 data points. so to get minite data and tick data for a long time, we do need to appened the data. also make sure we have a good system to save data!! we already talked about that before.

Feel free to destroy and recreate the databases multiple times as we are at a very early stage of the project. so we are not confidenct abbout the schema yet.


docs are in doc/ folder, so update them regularly.




Ok, so right now you need to check what has been implemented and what has not been implemented and start working on a todo list. Note that we need every thing to be unit tested and integration tested first before attempting to write a backtesting engine. right now it's about getting the functionalities working confidently one by one. Also, this is not desgigned to be a real time engine. we are only going to use it to find alpha and confidence that the strategy works.

Also, the llm ReAct agents need to get the sec fillings, for say data xyz, and then we send in the fillings prior to this date. how many files? perhaps last 2 years? The key question to be answered is, is this stock going to do unethical dillution, and must else they will run out of cash or something. do they have the ability to do that when it comes to the fillings. 
we may need a 
  sophisticated caching system here:      ☐ CRITICAL: Create sophisticated 
  ReAct agents with LangChain/AutoGen   ... e.g we dont really need to run 
  the react agents everyday. we need to run them when fillings change. no 
  point running if filling does not change. so i hope you account for this,
   like which files were analysed, and if reanalysis is needed.


the price action to determine the entry will most likely going to use a sophisticated price action detection, tick data like routes used etc. generally these are low volume days prior to the day when the news is released, stock gaps up and dillution starts.  I really want to use AI for this, but this requires very careful data choice. e.g well, when we are doing the randomness check, we will get enough data and then we might be able to have enough data to create a sophisticated system that will be able to know if it is people who know that a gap will happen that arccumulating.!!!


 update th docs after this. then go back to your list of todo and continue  │
│   working on it.       ☐ Create IB to Alpaca data converter <<< we need      │
│   the one that has the most data.  Also, for tick data, we will use ib a     │
│   lot more. so careful. 


no fakes, no mocks. real   │
│   db, real api. no silent fails or fallbacks. real big fails to  │
│   find real issues then you fix the real issues. money is on     │
│   the line. you have to be extra vigilant.    




Now, when it comes to getting confidence that the strategy works, we really need to include the dilisted ones as well, becuase if that was in the portfolio or in the nasdaq daily suspsnetion list issue link above, we could lost a lot. perhaps 100%. So we need to make sure for things to be realistic, to include those. 

So, find out what has been done and what to be done and update docs to make sure that docs match ground truth. You really need to confirm if the codes are just placeholders or mocks as well and if the tests have been faked or fallbacked!! we do not need fallbacks. no silent errors. . Money is on the line. careful.



So, the way i see it is:

So, when it comes to the UI/UX for confirming that the strategy works.

At a per stock level for the backtesting :

say FTFT as a stock that gapped today 11 July 2025 or yesterday.
on daily chart:
So, on the chart, i will see dates where news where released, fillings etc.
then detailed calculations ReAct agents data giving the time range of when the gappup might happen and how much money they need to raise not to go bankrupt, etc etc. so  for this ReAct scenario, we need very good prompts and very clever agents working together, each with sophisticated tools if needed, but most likely we will give them all the necessary data they need. so this react can perhaps be created with langchain or autogen teams. so we certainly do need very sophisticated prompts.  and they give me detailed explanation on why the stock needs money and likely going to gap up beween the date range of "xxx" that past xyx date, there will be no money left.


then there should be the price action system[not sure if we use ai, or some static methods]? ... so it will show for example that 1 week prior to the gap up, there was strange volume tick data analysis. like compared to an average week, with no news... this is what generally happened but that week or 2 week we had .... so this is insider buying or people who know there will be a gap up buying the stock.


and this is the % made from this trade. for now, we will exist pre market, when volume starts to come in. later we will get more sophisticated. i.e based on volume, we will exit at different times. like exit 25% at a time 4, exits.. tick data likely needed for minute data.. minute data moste likely as we with huge vlume we have  a lot of trades.. to many trades.



^^ so, so far, all we have is almost cherry picking the stocks. so scan look for gap, look for news, then confirm if agents react approve + price action is there.

For real backtesting, now, we need to do it slightly differently. we choose a day. then we look at all the fillings, pick the stocks that match the agents criteria. now on that day, we have all these stocks on the watchlist, that could be 100+ . we are pulling data in real time[per say!! but back testing now.. ] out of those identified by the agents that will gap up soon, one shows that particular price action of insiders/people who know . this will be our entry point. we may keep the stock for 2 weeks perhaps... then next day.. we may get a new filling or enter a new range of potential stocks that are going to gap up based on cash burn rate. our realtime[per say] is still always on, give new signal of that stock undergoing insider trading, then we get in! then we have a new day. one of our positions has gapped up and whe volume came, we got out premarket, around 7:30 kinda. we win. another postion was delisted, so we lost money, on that, so we record both, adjust the portfolio, then move next day.


so we always have our react agents analysis updated which basically give a of stocks list and dates. that would change each time we have new fillings!

based on the dates we have a watchlist which is used to check for insider trading price action.

based on insider price action, we have position that we got in.  we also need to make sure of how much capital is allocated per trade.

So, from this system, we can basically backtest say 3-5 years from today? So basically, once all data is downloaded.. like news, sec filllings, and react agents have done their analysis when required. so each day the list could be different worse case, or could be the same for several days... unless we upgrade the logic of the agents, then we will need to redo the analysis. so just if we get one day done, then it's just repeating?  note that the stock universe will change a lot could be delisted and that would bias such a strategy. so that needs to be included in a pretty obvious way.


also, the django, no auth needed, but it needs to give confidence that the strategy works.  this isnt designed to be like a charting software... but rather a sophsticated live report with proof at every angle... that we have thought of everything and what we are seeing isnt pure randomness!! we need that report, perhaps llm based after getting a summary data of the strategy. that proves that this is valid!!!


ok, so a lot of work to be done still. check for missing elements, fake tests, create your todo, and get to work. Note that this strategy goes against common beliefs. So always stick to what i am asking! if you are implementing something that is commonly known knowledge in the stock market world, it's probably wrong. stick to what i am asking you to do. dont create arbitrary numbers without justification, unless it's temporary.

wait. wait. backtesst isnt on alpaca! we use alpaca only for fetching data.

for now, this is our stock universe:
/Users/<USER>/PycharmProjects/stk_v5/data/universe/nasdaq_screener_1752224820914.csv  so dev can go a bit fast. then you add the delisted to it. then we have everything


Also, interactive brokers seems so much under utilised. it is a very good provider for many data types. we need to use it much more.


 ok. we now have the django vizualisation which is basically just reading stuff off a database.        │
│   correct? so again, looking at @specs.md  what is left to be built and also what is meant to be        │
│   significantly enhanced from simple to something that is  good enough for real world deployment.       │
│   right now, all we have is simple versions of a lot of our components. but anyways. so now we have a   │
│   feedback loop correct? so after each run which can be per stock or per day[like a real backtester..]  │
│   say for now we only backtest for 1 month. so 20 days, starting capital... then ending capital,        │
│   risks, max drawdown etc. so each day , each stock, each backtest, does project some results i would   │
│   assume that is being read by the django. so we, need to look at each of these depending on what       │
│   feedback loop we are looking at. we need to manually look at the results we get, and make small       │
│   adjustments here and there, until we have a well calibrated system with proof.        

So we have one feedback loop at the individual stock daily level. i.e select one stock and one day. this also needs to do sophisticated detection in price action like 1-2 weeks before. [we need tick data for this kind of analysis, like very detailed tick data, i am not sure we are downloading all the avail tick data from IB for this to be sophistocated enough..] .. the price actions tells us that there is enough volume and spread is good enough, so we can enter. this is the same time as insiders are getting in. then we exit on volume on the gap day within x days. or the stock has declined enough that it has hit our risk level. either way, we need to exit the trade. this creates a json, that can be visualised and is saved in a db. save maximum data please.  this would be the first feedback toop to get working, and tested and tested and tested.




Ok, so first of all this document is very messy. so you need to read in full to understand. if contradicting, take the one at the lower bit of the document. because all i did was put what was on my mind in this document. So before you start building and writing. I really need you to first, check if we already have the code. Do not start writing blindly as we will end up wth duplicated code. Also, run tests frequently. run codes frequently.  I need you to thing harder on this document first before starting any king of work. think harder.  



but what is the most important right now is tests! we have build things but not a lot of tests are solid enough and follow our testing philisophy. loud real fails instead of returning fake data that will cause huge financial loss.
pdate your list. and start 
  fixing. also, if you find duplicate code fix please.. or at least put todo flag! and alert 
  to do later. also, for bar and tick only use ib .. dont use alpaca for that kind of data, 
  since it seems not very good, or incorrect!


Fixing all import errors blocking the system    .. you can add paths for quick and easy. but when sperifically tasked, you will start removing them and using proper imports. 



So we have a cli entrypoint now:

/Users/<USER>/PycharmProjects/stk_v5/seeking_alpha_research/CLI_USAGE.md  << note that this isnt updated very often so rely on main.py notes more than this .md file
but we have a main.py as well that needs to be in sync.

Django dashboard basically is a sophisticated live report.



│ > ok. so can you confirm that in that long list i gave you before, you checked all these functions like    │
│   units of work independent. then made sure there were NO fakes? if not, create a todo list of each and    │
│   we will go through them one by one, with an eventual unit test for each in the test folder so it does    │
│   not look messy......   



  ⎿  ☒ Build comprehensive unit tests for each Tier 1-4 component         
     ☒ Create individual test files for SEC analyzer component
     ☒ Test volume patterns - accumulation, spikes, correlation, premarket
     ☒ Test news aggregation - multi-source, dedup, catalyst, timing
     ☒ Test real IB tick data - streaming, backfill, recovery, rate limits
     ☒ Test tick analysis - imbalance, dark pools, odd lots, clustering
     ☒ Test LLM analysis - cash burn, ATM prob, multi-filing, caching
     ☒ Test insider detection - tick-based, routes, scoring, lookback
     ☒ Test gap detection - premarket, volume validation, persistence
     ☒ Test corporate actions - real-time, dividends, splits, mergers
     ☒ Test market cap - real-time updates, historical, universe changes
     ☒ Test dilution confirmation - ATM detection, timing, amount
     ☒ Test end-to-end pipeline - gap to exit, multi-day, rebalancing
     ☒ Test statistical validation - alpha, Monte Carlo, bootstrapping
     ☒ Test Django UI - views, API, WebSocket, charts, forms
     ☒ Test performance reports - Sharpe, drawdown, win/loss, risk



<< this was the list. so keep working. MAKE sure that  │
│   each one can ran independently and we can improve them later even more. but for now we do need these     │
│   to be professional level as a minimal... PLEASE DO NOT TOP WORKING UNTIL YOU HAVE GONE THROUGH THE       │
│   ENTIRE LIST!!!! you need to run the tests!! not just create them. and also look at the output if they    │
│   make sense or not! having a code that runs doesnt mean it is correct! RUN the tests and debug.  keep     │
│   the doc up to date as always.. it does not seem that from the above list you have actually ran           │
│   anything!! have you? and also confirmed the output via manual check! also since django is mainly used    │
│   visual reporting, you may want to pull the jsons or db data produced and manually check if they make     │
│   any kind of sense. So far i am not confident at all about this project going live and going actual back  │
│   testing.  RUN THE DAMN TESTS.. dont just create and nothing! also, each of these unit test are           │
│   basically standalone features that should be documented and perhaps integrated in django later.. dont    │
│   integrate now, but much later. for now django is just for reporting. ok. pytest and debug and update     │
│   doc detailng the standalone features.   so we have made these test scripts [Pasted text #2 +16 lines]    │
│   ..... we first get all the tests running.   You are a very hard worker and debug things inteliggently and with extreme obcession.

  ⎿  ☒ Run and debug test_sec_analyzer_real.py          
     ☐ Run and debug test_volume_patterns_real.py
     ☐ Run and debug test_news_aggregation_real.py
     ☐ Run and debug test_ib_tick_data_real.py
     ☐ Run and debug test_tick_analysis_real.py
     ☐ Run and debug test_llm_analysis_real.py
     ☐ Run and debug test_insider_detection_real.py
     ☐ Run and debug test_gap_detection_real.py
     ☐ Run and debug test_corporate_actions_real.py
     ☐ Run and debug test_market_cap_real.py
     ☐ Run and debug test_dilution_confirmation_real.py
     ☐ Run and debug test_end_to_end_pipeline_real.py
     ☐ Run and debug test_statistical_validation_real.py
     ☐ Run and debug test_django_ui_real.py
     ☐ Run and debug test_performance_reports_real.py
     ☐ Document each standalone feature after testing
     ☐ Verify outputs make logical sense

Based on the @specs.md, i believe that this list of standalone programs can be extended. extend them please so we get more standalone apps.
/Users/<USER>/PycharmProjects/stk_v5/seeking_alpha_research/STANDALONE_FEATURES_DOCUMENTATION.md <<  this needs to be a live document where we keep making the standalone apps live the tests above for those standalone apps. we need to detail them a lot more in detail and keep them up to date.


Then once we have been able to run and debug them all, and documented them, manually check the responses of all to remove fakes, placeholders, fallbacks[we need hard fails. no fall backs as money is on the line]. real api, real db, real ai,real all. no mocks, no placeholders... AND PRINTS REAL AND USEFUL ERROR MESSAGES!!!!  Also, you need to think harder and try harder when we get fails. reflect and try harder always to fix the issue at hand.

Also: The results need to make sense.. e.g the statstical validation test for this sort of strategy, does not seem to be valid. It feels stupid.

For django UI reporting I expect you to test with playwright!  So each of these standalone apps can have their own page in the app. you can you subprocess to run them so we keep django and the cli separate. the point is that we need to confidence to confirm that each one is working as expected. so each results of these stand alone are saved in db, so we can review them in django.

Next if not done already.
/Users/<USER>/PycharmProjects/stk_v5/seeking_alpha_research/django_prototype_v0 << this is the django project and it too deep inside the project.
we really need to change this and make the root as django. then it becomes so much easier to pull things and run in django.. When it comes to django bits. let's make sure we have our main.py cli version working correctly first! 

so the new thin would be
/Users/<USER>/PycharmProjects/stk_v5/
-- manage.py
-- visuliser
-- seeking_alpha_research




│ > so please note that this is more of a backtest where things are rather done in reverse mostly. that what a realtime engine    │
│   would look like. the realtime engine will be built afterwards.    The system can now execute the full 6-step strategy:        │
│     1. ✅ Data download (PLUG historical data)                                                                                   │
│     2. ✅ Gap detection                                                                                                          │
│     3. ✅ SEC analysis (ReAct LLM extraction working)                                                                            │
│     4. ✅ Dark pool analysis (now running - previously blocked)                                                                  │
│     5. ✅ Entry decision logic                                                                                                   │
│     6. ✅ Exit strategy simulation  ....  so heere are  my problems for each. 1. data keeps redownloading everytime, ile i       │
│   think the caching system isnt workig.  SEC anallysis seems rather not too sophisticated and too basic still. perhaps we need  │
│   better prompts +more react rounds. we also need a critique to evalue at the end of each round and perhaps another round       │
│   that will fetch new data from the large files for confirmation. as we are not sending all the files but rahter a very very    │
│   small version of it. each ReAct agent needs to 1. the full thing in chunks like, then they do the "compressing", so atleast   │
│   in their context history they do have the full chunk EACH and each does it's own compressing before geting into the           │
│   analytics.   Darkpool --> Needs some more confidence about this one. I am not totally sure about the math behind it.          │
│   Entry logic ---> this is based on volume accumulation in the past 2 weeks from the date we feed the system.. analyse volume   │
│   for a good entry point.   Ohh.. back to the SEC guys.. so they give us a date range where they believe the ATM will happen.   │
│   can we confirm that this falls in the gap date, and how arrucate whre they.. like in between the window they gave or outside  │
│   the window. just in the middle or at the extreme ends of the window. the more in the middle the better score. so exactly in   │
│   the middle, we get a score of 0 <- maximum score. and between 1 to -1 for where it was in that window. and a score of less    │
│   than -1 and more than 1, means outside.. << or some similar scoring system that is each to understand.    ... next: So on     │
│   the gap day, we need to fetch the news for that day. a long list of news for that stock. if no news, empty list perhaps.      │
│   exit logic for now, is just gap day at around 7:30 premarket. [so we need minute data perhaps to know the exact price...] we  │
│   also need a stop loss exit. which will be either a set price if it dips below that from the moment of entry or it has pass    │
│   the windows of raising, or it has been listed in investigation or in the list of suspendend stocks or something, we get out   │
│   at that price. So that list needs constant updating. we have the code to fetch that from nasdaq i believe. now, these are     │
│   rather complex exits, that we need to account for. but for now, just put placeholders for them. our exit for now would be,    │
│   if the time goes outside the predicted windows i.e passes is. or it dips below say 20% of the entry price. so a potential     │
│   risk rewards of 1:2 ... OK.. so quite a few things for you to build. first make a good plan and todo list. then start workin  │
│   on them, then test them thoroughtly.  


also dont use names like ''professional''_gap_scanner .comprehensive, fixed,or similar. like               │
│   professional, enhanced, improved? they make things confusing. remame when you see them.           
