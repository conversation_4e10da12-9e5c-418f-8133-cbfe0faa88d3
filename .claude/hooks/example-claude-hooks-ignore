# .claude-hooks-ignore - Patterns for files to skip during linting/testing
#
# IMPORTANT: Use this feature sparingly! The goal is 100% clean code.
# Only exclude files you don't control or can't modify.
#
# Patterns follow gitignore syntax:
# - Comments start with #
# - Blank lines are ignored
# - / suffix matches directories only
# - /** matches everything inside a directory
# - * matches anything except /
# - ? matches any single character except /
# - [abc] matches any character inside the brackets

# Vendor directories
vendor/**
node_modules/**
.git/**

# Generated files
*.pb.go
*_gen.go
*_generated.go
*.generated.ts
dist/**
build/**
out/**

# Third-party code
third_party/**
external/**

# Test fixtures that might contain unusual patterns
testdata/**
fixtures/**
**/testdata/**

# Documentation that might have code examples
docs/**/*.go
examples/**

# Specific files
.golangci.yml  # Has example configs that might trigger checks
TODO.md        # Might have many TODO items

# IDE and editor files
.idea/**
.vscode/**
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Temporary files
tmp/**
temp/**
*.tmp
*.temp

# Archives
*.zip
*.tar.gz
*.tgz

# Binaries
*.exe
*.dll
*.so
*.dylib

# Images and media
*.jpg
*.jpeg
*.png
*.gif
*.svg
*.mp4
*.mp3

# ============================================================================
# LEGITIMATE USE CASES
# ============================================================================

# GENERATED CODE (you don't write or control this)
*.pb.go                    # Protocol buffers
*.pb.gw.go                 # gRPC gateway
*_generated.go             # Various code generators
*_string.go                # Stringer tool
bindata.go                 # Embedded assets
**/zz_generated.*          # Kubernetes generators

# DATABASE MIGRATIONS (often have different formatting standards)
migrations/*.sql
db/migrate/**

# LEGACY CODE (during gradual refactoring)
# legacy/**                # Uncomment and adjust path as needed
# old_api/**              # Example: old API being phased out

# ============================================================================
# INLINE DISABLING
# ============================================================================
#
# You can also disable checks for specific files by adding this comment
# in the first 5 lines of the file:
#
# Go:         // claude-hooks-disable
# Python:     # claude-hooks-disable
# JavaScript: // claude-hooks-disable
# Rust:       // claude-hooks-disable
# Tilt:       # claude-hooks-disable
#
# ALWAYS document WHY the file is excluded:
#   // claude-hooks-disable - Legacy code, will be removed in v2.0
#   // claude-hooks-disable - Test fixture with intentional errors
#   # claude-hooks-disable - Generated by tools/codegen.py
#
# ============================================================================
# WARNING: DO NOT EXCLUDE YOUR OWN CODE!
# ============================================================================
#
# These patterns should NOT be used to bypass standards:
# ❌ src/**/*.go           # Don't exclude your entire source
# ❌ *.go                  # Don't exclude all Go files
# ❌ main.go               # Fix the issues instead
# ❌ *_test.go             # Tests should meet standards too
#
# Remember: The goal is 100% clean code. Exclusions are for code you
# don't control, not code you don't want to fix.