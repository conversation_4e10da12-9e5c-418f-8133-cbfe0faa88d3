# .claude-hooks-ignore - Exclude files from Claude Code hooks
#
# This file uses gitignore syntax to specify files and directories that should
# be skipped by smart-lint.sh and smart-test.sh hooks.
#
# IMPORTANT: Use this sparingly! The goal is 100% clean code. Only exclude:
# - Generated files that can't be modified
# - Third-party vendored code
# - Files with special formatting requirements
# - Legacy code pending migration
#
# Format:
# - One pattern per line
# - Lines starting with # are comments
# - Patterns support standard glob matching (* and ?)
# - End patterns with / to match directories
# - Use /** to match all files in a directory recursively

# ============================================================================
# GENERATED CODE - Cannot be modified
# ============================================================================

# Protocol Buffers
*.pb.go
*.pb.gw.go

# Go generated files
*_generated.go
*_string.go
mock_*.go
bindata.go

# JavaScript/TypeScript generated
*.min.js
*.min.css
dist/**
build/**
coverage/**

# Python generated
__pycache__/**
*.pyc
.eggs/**
*.egg-info/**

# ============================================================================
# VENDORED/THIRD-PARTY CODE
# ============================================================================

vendor/**
node_modules/**
.venv/**
venv/**

# ============================================================================
# SPECIAL FORMATTING REQUIREMENTS
# ============================================================================

# Migrations often have specific formatting
migrations/*.sql
db/migrations/**

# Golden test files must match exact output
testdata/**
*.golden

# ============================================================================
# TEMPORARY EXCLUSIONS (with explanation)
# ============================================================================

# TODO: Remove after migrating to new API (ticket #123)
# legacy/old_api.go

# TODO: Fix formatting after upstream PR is merged
# third_party/wonky_formatter.js

# ============================================================================
# INLINE EXCLUSIONS
# ============================================================================
# 
# For one-off exclusions, you can also add a comment to the top of any file:
#
#   // claude-hooks-disable
#
# Or for specific languages:
#   # claude-hooks-disable
#   /* claude-hooks-disable */
#
# This is useful for:
# - Files with intentionally non-standard formatting
# - Test files that must preserve specific whitespace
# - Configuration files with strict formatting requirements