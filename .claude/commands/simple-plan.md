---
allowed-tools: all
description: Create simple task execution files and run them with cc_executor
---

# Simple Plan Command

Based on: $ARGUMENTS

## Step 1: Create Task Files

Generate in ./tasks/:
- `run_simple.py` - Python script for cc_executor
- `task_simple.md` - Documentation

## Step 2: Execute with cc_executor

After creating files, run:
```bash
python ./tasks/run_simple.py
```

Keep it simple and focused on the user's request.