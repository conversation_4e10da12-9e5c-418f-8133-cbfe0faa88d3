{"permissions": {"allow": ["Bash(.claude/hooks/token-tracker.sh:*)", "Bash(sqlite3:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(/Users/<USER>/PycharmProjects/stk_v5/.claude/hooks/token-tracker.sh track:*)", "Bash(grep:*)"], "deny": []}, "hooks": {"PreToolUse": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/enhanced-debug-logger.sh"}]}], "PostToolUse": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/enhanced-debug-logger.sh"}, {"type": "command", "command": ".claude/hooks/smart-lint.sh"}]}], "Stop": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/enhanced-debug-logger.sh"}, {"type": "command", "command": ".claude/hooks/current-conversation-tracker.sh"}]}], "SubagentStop": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/enhanced-debug-logger.sh"}]}], "Notification": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/enhanced-debug-logger.sh"}]}]}}