#!/usr/bin/env bash

# Project-specific configuration for Claude Code hooks
# This file is sourced by common-helpers.sh to set project-specific variables

# ============================================================================
# TOKEN TRACKING CONFIGURATION
# ============================================================================

# Enable token tracking for this project
export CLAUDE_HOOKS_TRACK_TOKENS=1

# Token tracking database location (relative to project root)
export CLAUDE_TOKEN_DB_PATH=".claude/token_usage.db"

# Enable debug logging for troubleshooting
export CLAUDE_HOOKS_DEBUG=0

# ============================================================================
# HOOK CONFIGURATION
# ============================================================================

# Set the hook type for this project (used in token tracking)
export CLAUDE_HOOK_TYPE="python-trading-research"

# ============================================================================
# PROJECT-SPECIFIC SETTINGS
# ============================================================================

# This is a Python trading research project
export PROJECT_TYPE="python"
export PROJECT_NAME="stk_v5_gap_trading"

# Path to main project directory
export PROJECT_MAIN_DIR="seeking_alpha_research"

# ============================================================================
# COST TRACKING SETTINGS
# ============================================================================

# Claude Sonnet 4 pricing (adjust as needed)
export CLAUDE_INPUT_COST_PER_1K=0.015   # $0.015 per 1K input tokens
export CLAUDE_OUTPUT_COST_PER_1K=0.075  # $0.075 per 1K output tokens

# ============================================================================
# REPORTING SETTINGS
# ============================================================================

# Default reporting period in days
export CLAUDE_DEFAULT_REPORT_DAYS=7

# Enable cost alerts when daily usage exceeds threshold (in USD)
export CLAUDE_COST_ALERT_THRESHOLD=5.00