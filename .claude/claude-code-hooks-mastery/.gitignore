# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytype/
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
*.egg-info/
dist/
build/
*.egg

# TypeScript
node_modules/
*.tsbuildinfo
.npm
.eslintcache
.node_repl_history
*.tgz
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
dist/
build/
*.js.map
*.d.ts.map

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
.directory
.Apple*
.LSOverride

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Testing
htmlcov/
.coverage
.nyc_output
coverage/

# Temporary files
*.tmp
*.temp
*.bak
.cache/
tmp/
temp/