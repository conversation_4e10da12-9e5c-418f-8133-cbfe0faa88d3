{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(uv:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(npm:*)", "Bash(ls:*)", "Bash(cp:*)", "Write", "Edit", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(touch:*)"], "deny": []}, "hooks": {"PreToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/pre_tool_use.py"}]}], "PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/post_tool_use.py"}]}], "Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/notification.py --notify"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/stop.py --chat"}]}], "SubagentStop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/subagent_stop.py"}]}]}}