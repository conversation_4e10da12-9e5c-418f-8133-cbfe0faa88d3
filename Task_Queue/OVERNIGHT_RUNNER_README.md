# 🌙 Enhanced Overnight Task Runner

A simple yet powerful web UI for running your trading strategy development tasks overnight using cc_executor.

## ✨ New Features Added

### 🖥️ System Monitoring
- **Working Directory** - Shows current working directory
- **Python Environment** - Shows if you're in venv or system Python
- **Redis Status** - Checks if <PERSON><PERSON> is running (required for cc_executor)
- **CLAUDE.md Viewer** - Quick access to project documentation

### ⚡ Individual Task Control
- **Run Individual Tasks** - Execute any task independently 
- **Delete Tasks** - Remove tasks you don't need
- **Real-time Status** - Visual indicators for running/completed/error states
- **Task Sources** - Shows if task is from workflow or created via chat

### 💬 Chat Interface for Task Creation
- **Chat with <PERSON> Code** - Create custom tasks through conversation
- **Smart Task Generation** - <PERSON> analyzes requests and creates actionable tasks
- **Task Approval Workflow** - Review and approve tasks before adding to queue
- **Chat History** - Persistent conversation history

### 📋 Task Management
- **Pending Tasks Tab** - Review tasks created via chat
- **Approve/Reject** - Control which tasks get added to the queue
- **Task Details** - See estimated time, description, and prompts
- **Mixed Sources** - Handle both programmatic and chat-created tasks

## 🚀 Quick Start

```bash
# 1. Start the enhanced UI
./start_overnight_ui.sh

# 2. Open in browser
open http://localhost:8080

# 3. Check system status (working dir, venv, Redis)
# 4. Chat with Claude to create custom tasks
# 5. Approve tasks you want to run
# 6. Run individual tasks or start full workflow
```

## 📱 UI Overview

### 📊 Dashboard Tab
- **System Status** - Working dir, venv, Redis status
- **Workflow Controls** - Start/pause/stop overnight runs
- **Task Queue** - All tasks with individual run/delete buttons
- **Live Logs** - Real-time cc_executor output
- **Progress Tracking** - Visual progress bar and task counts

### 💬 Chat & Create Tasks Tab
- **Natural Language** - "Create a task to analyze PLUG's SEC filings"
- **Smart Suggestions** - Claude provides examples and guidance
- **Multi-task Creation** - Claude can create multiple related tasks
- **Interactive Planning** - Back-and-forth refinement of task ideas

### ⏳ Pending Tasks Tab
- **Review Area** - See all tasks waiting for approval
- **Task Details** - Name, type, estimated time, description, full prompt
- **Approval Controls** - Approve to add to queue or reject to discard
- **Safety Check** - Prevents bad tasks from running overnight

## 🎯 Example Chat Interactions

### Creating Custom Tasks
```
You: "Create a task to backtest gap-up strategies on small-cap stocks"

Claude: "I'll create a comprehensive backtesting task for you:

Task 1: Gap-Up Strategy Backtest
- Scan NASDAQ small-cap stocks for 30%+ morning gaps
- Analyze entry/exit performance over 3-year period  
- Calculate win rate, average return, max drawdown
- Generate performance report with charts
Estimated time: 15-20 minutes"

[Task appears in Pending Tasks tab for approval]
```

### Getting Guidance
```
You: "I want to improve my insider trading detection"

Claude: "Here are some overnight tasks I can create:

1. Enhance the existing insider detector with new signals
2. Backtest current detector performance 
3. Build Django UI for manual review of insider alerts
4. Create automated alerts for high-confidence signals

Which would you like me to create first?"
```

## 🔧 Technical Features

### Token Limit Handling
- **Auto-detection** - Catches when cc_execute returns None
- **Intelligent Retry** - Extracts wait time from Claude CLI
- **Countdown Display** - Shows remaining time until retry
- **Automatic Resume** - Continues tasks when limit resets

### Mobile-Friendly
- **Responsive Design** - Works on phones and tablets
- **Touch Navigation** - Easy tab switching
- **Mobile Logs** - Scrollable output for monitoring
- **Status Checking** - Check progress from anywhere

### Real-time Updates
- **WebSocket Streaming** - Live task output and progress
- **Status Broadcasting** - All connected clients see updates
- **Task State Sync** - UI reflects actual task status
- **Error Notifications** - Immediate feedback on failures

## 📝 Task Types Supported

### Programmatic Tasks (from run_workflow.py)
- **Phase 1** - Test fixing with Five Whys methodology
- **Phase 2** - Django UI building with Playwright testing
- **Automatic Generation** - Based on test files in your project

### Chat-Created Tasks
- **Custom Analysis** - SEC filings, market data, technical analysis  
- **Code Generation** - New features, utilities, improvements
- **Backtesting** - Strategy validation with historical data
- **UI Development** - Django views, templates, components

### Any cc_execute Compatible Task
- **Flexible Prompts** - Any task that works with Claude Code
- **Long-running** - Perfect for overnight execution
- **Streaming Output** - See progress in real-time
- **Error Recovery** - Built-in retry logic

## 🔍 System Requirements

- **Python Environment** - Virtual env recommended
- **Redis Server** - Required for cc_executor (install: `brew install redis`)
- **Claude Code CLI** - Authenticated and working
- **cc_executor** - Installed in your project

## 💡 Tips for Best Results

### Chat Interface
- **Be Specific** - "Analyze PLUG's latest 10-K filing" vs "analyze something"
- **Context Aware** - Claude knows your project is Gap-Up ATM Trading Strategy
- **Iterative** - Refine tasks through conversation
- **Review Carefully** - Check generated prompts in Pending Tasks

### Task Management  
- **Start Small** - Test individual tasks before overnight runs
- **Monitor Redis** - Ensure it's running for cc_executor
- **Check Working Dir** - Make sure you're in the right project
- **Review CLAUDE.md** - Click to see current project context

### Overnight Strategy
1. **Chat & Create** - Use chat to plan your overnight work
2. **Review & Approve** - Check all pending tasks carefully
3. **Test Individual** - Run one task to verify setup
4. **Start Full Workflow** - Begin overnight execution
5. **Monitor Mobile** - Check status on phone before bed

## 🐛 Troubleshooting

### Redis Not Running
```bash
# Start Redis
brew services start redis

# Or run manually
redis-server
```

### cc_executor Issues
- Check working directory matches your project
- Verify Claude Code CLI is authenticated
- Ensure cc_executor is installed in current environment

### Token Limits
- UI automatically handles limits with countdown
- Will extract retry time from Claude CLI
- Tasks resume automatically when limit resets

## 🎉 Perfect for Trading Development

This enhanced overnight runner is specifically designed for your Gap-Up ATM Trading Strategy development:

- **Fix all 31 test files** with automated Five Whys debugging
- **Build Django UIs** for each trading feature  
- **Create custom analysis tasks** via natural language chat
- **Run comprehensive backtests** on historical data
- **Monitor progress remotely** on mobile while sleeping

**Set it up before bed, wake up to completed work! 🌅**