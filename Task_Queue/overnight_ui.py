#!/usr/bin/env python3
"""
Simple Overnight Task Runner UI for run_workflow.py

A simple web interface to:
1. Run your trading test workflows overnight
2. Stream cc_executor output in real-time
3. Handle token limits automatically
4. Mobile-friendly monitoring

Usage:
    python overnight_ui.py
    Then open: http://localhost:8080
"""

import asyncio
import json
import os
import re
import sqlite3
import subprocess
import sys
import time
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn

from cc_executor.client import cc_execute

# This will be replaced by the lifespan version above

# Global state (simple for now)
current_workflow = {
    "status": "idle",  # idle, running, paused, completed, error
    "current_task": "",
    "progress": {"completed": 0, "total": 0},
    "start_time": None,
    "logs": [],
    "token_limit_retry_time": None
}

# WebSockets removed - using Redis polling
# Note: chat_history is now managed via database - see get_chat_history_from_db()
pending_tasks: List[Dict[str, Any]] = []  # Tasks created via chat, waiting for approval

# Database setup
DB_PATH = Path("overnight_runner.db")

def init_database():
    """Initialize SQLite database"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Tasks table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS tasks (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            prompt TEXT,
            test_file TEXT,
            feature TEXT,
            estimated_time TEXT,
            source TEXT DEFAULT 'workflow',
            status TEXT DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            result_json TEXT,
            error_message TEXT
        )
    """)
    
    # Execution history table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS execution_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id TEXT,
            execution_type TEXT,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            success BOOLEAN,
            result_json TEXT,
            error_message TEXT,
            cc_executor_output TEXT,
            FOREIGN KEY (task_id) REFERENCES tasks (id)
        )
    """)
    
    # Chat history table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS chat_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            role TEXT NOT NULL,
            message TEXT NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            data_json TEXT
        )
    """)
    
    conn.commit()
    conn.close()

def get_system_info() -> Dict[str, Any]:
    """Get current system information"""
    info = {
        "working_dir": os.getcwd(),
        "python_executable": sys.executable,
        "venv": None,
        "redis_status": "unknown",
        "database_status": "unknown"
    }
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        info["venv"] = os.path.basename(sys.prefix)
    else:
        info["venv"] = "system"
    
    # Check Redis status
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, decode_responses=True)
        r.ping()
        info["redis_status"] = "running"
    except:
        info["redis_status"] = "not_running"
    
    # Check database status
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM tasks")
        task_count = cursor.fetchone()[0]
        conn.close()
        info["database_status"] = f"running ({task_count} tasks)"
    except:
        info["database_status"] = "error"
    
    return info

def read_claude_md() -> str:
    """Read CLAUDE.md file for quick review"""
    try:
        claude_md_path = Path("CLAUDE.md")
        if claude_md_path.exists():
            return claude_md_path.read_text()
        else:
            return "CLAUDE.md not found in current directory"
    except Exception as e:
        return f"Error reading CLAUDE.md: {str(e)}"

class TaskRunner:
    """Simple task runner for overnight workflows"""
    
    def __init__(self):
        self.tasks = []
        self.current_task_index = 0
        self.is_running = False
    
    def save_task_to_db(self, task: Dict[str, Any]) -> str:
        """Save task to database"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check for duplicates
        cursor.execute("SELECT id FROM tasks WHERE name = ? AND type = ?", (task["name"], task["type"]))
        existing = cursor.fetchone()
        
        if existing:
            # Update existing task
            cursor.execute("""
                UPDATE tasks SET 
                    prompt = ?, test_file = ?, feature = ?, estimated_time = ?,
                    source = ?, status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                task.get("prompt", ""), task.get("test_file", ""), task.get("feature", ""),
                task.get("estimated_time", ""), task.get("source", "workflow"),
                task.get("status", "pending"), existing[0]
            ))
            task_id = existing[0]
        else:
            # Insert new task
            task_id = task["id"]
            cursor.execute("""
                INSERT INTO tasks (id, name, type, prompt, test_file, feature, estimated_time, source, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_id, task["name"], task["type"], task.get("prompt", ""),
                task.get("test_file", ""), task.get("feature", ""), task.get("estimated_time", ""),
                task.get("source", "workflow"), task.get("status", "pending")
            ))
        
        conn.commit()
        conn.close()
        return task_id
    
    def load_tasks_from_db(self) -> List[Dict[str, Any]]:
        """Load tasks from database"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name, type, prompt, test_file, feature, estimated_time, source, status, 
                   created_at, result_json, error_message
            FROM tasks ORDER BY created_at
        """)
        
        tasks = []
        for row in cursor.fetchall():
            task = {
                "id": row[0],
                "name": row[1],
                "type": row[2],
                "prompt": row[3] or "",
                "test_file": row[4] or "",
                "feature": row[5] or "",
                "estimated_time": row[6] or "",
                "source": row[7] or "workflow",
                "status": row[8] or "pending",
                "created_at": row[9],
                "result_json": json.loads(row[10]) if row[10] else None,
                "error_message": row[11]
            }
            tasks.append(task)
        
        conn.close()
        return tasks
    
    def update_task_status(self, task_id: str, status: str, result_json: Dict = None, error_message: str = None):
        """Update task status in database"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE tasks SET 
                status = ?, 
                result_json = ?, 
                error_message = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (status, json.dumps(result_json) if result_json else None, error_message, task_id))
        
        conn.commit()
        conn.close()
    
    def save_execution_history(self, task_id: str, execution_type: str, success: bool, 
                              result_json: Dict = None, error_message: str = None, 
                              cc_executor_output: str = None, response_file_path: str = None):
        """Save execution history to database"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO execution_history 
            (task_id, execution_type, started_at, completed_at, success, result_json, error_message, cc_executor_output)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?, ?)
        """, (
            task_id, execution_type, datetime.now().isoformat(),
            success, json.dumps(result_json) if result_json else None,
            error_message, cc_executor_output
        ))
        
        conn.commit()
        conn.close()
    
    def get_task_by_id(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task by ID"""
        for task in self.tasks:
            if task.get("id") == task_id:
                return task
        return None
    
    def delete_task(self, task_id: str) -> bool:
        """Delete a task by ID"""
        # Delete from database
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM tasks WHERE id = ?", (task_id,))
        deleted_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        # Delete from in-memory list
        for i, task in enumerate(self.tasks):
            if task.get("id") == task_id:
                self.tasks.pop(i)
                break
                
        return deleted_rows > 0
    
    def add_custom_task(self, task_data: Dict[str, Any]) -> str:
        """Add a custom task created via chat"""
        import uuid
        task_id = str(uuid.uuid4())[:8]
        task_data["id"] = task_id
        task_data["source"] = "chat"
        task_data["status"] = "pending"
        
        # Save to database
        self.save_task_to_db(task_data)
        
        # Add to in-memory list
        self.tasks.append(task_data)
        return task_id
        
    async def load_tasks_from_workflow(self):
        """Load tasks from database first, then add any new workflow tasks"""
        # Load existing tasks from database
        self.tasks = self.load_tasks_from_db()
        
        # Check for new workflow tasks
        test_files = list(Path("seeking_alpha_research/tests").glob("test_*_real.py"))
        
        for test_file in test_files:
            feature_name = test_file.stem.replace('test_', '').replace('_real', '')
            
            # Phase 1: Fix test
            phase1_name = f"Phase 1: Fix {feature_name}"
            if not any(task["name"] == phase1_name for task in self.tasks):
                task = {
                    "id": str(uuid.uuid4())[:8],
                    "name": phase1_name,
                    "type": "test_fix",
                    "test_file": str(test_file),
                    "feature": feature_name,
                    "estimated_time": "5-10 minutes",
                    "source": "workflow",
                    "status": "pending"
                }
                # Add prompt for phase 1
                task["prompt"] = self.create_phase1_prompt(task)
                task_id = self.save_task_to_db(task)
                task["id"] = task_id
                self.tasks.append(task)
            
            # Phase 2: Build UI  
            phase2_name = f"Phase 2: UI for {feature_name}"
            if not any(task["name"] == phase2_name for task in self.tasks):
                task = {
                    "id": str(uuid.uuid4())[:8],
                    "name": phase2_name,
                    "type": "ui_build", 
                    "test_file": str(test_file),
                    "feature": feature_name,
                    "estimated_time": "10-15 minutes",
                    "source": "workflow",
                    "status": "pending"
                }
                # Add prompt for phase 2
                task["prompt"] = self.create_phase2_prompt(task)
                task_id = self.save_task_to_db(task)
                task["id"] = task_id
                self.tasks.append(task)
        
        return self.tasks
    
    async def run_single_task(self, task: Dict[str, Any], is_individual: bool = False) -> Dict[str, Any]:
        """Run a single task using cc_execute"""
        
        task["status"] = "running"
        progress_text = f"Individual: {task['name']}" if is_individual else f"{self.current_task_index + 1}/{len(self.tasks)}"
        
        await self.broadcast_status({
            "type": "task_started",
            "task": task["name"],
            "task_id": task.get("id"),
            "progress": progress_text,
            "is_individual": is_individual
        })
        
        try:
            # Handle different task types
            if task["type"] == "test_fix":
                prompt = self.create_phase1_prompt(task)
            elif task["type"] == "ui_build":
                prompt = self.create_phase2_prompt(task)
            elif task.get("source") == "chat":
                prompt = task.get("prompt", "")
            else:
                prompt = task.get("prompt", "Unknown task type")
            
            # Update task status in database
            self.update_task_status(task["id"], "running")
            
            # Send initial progress update
            await self.stream_progress("🚀 Starting task execution...", task["id"])
            
            # Use cc_execute directly to get rich response data and proper streaming
            timestamp = datetime.now().strftime("%H:%M:%S")
            await self.stream_progress(f"[{timestamp}] 🚀 Starting cc_execute with rich data capture...", task["id"])
            
            try:
                # Use cc_execute directly with JSON mode for rich response
                from cc_executor.client.cc_execute import cc_execute
                
                # Add progress callback for streaming
                async def progress_callback(message):
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    await self.stream_progress(f"[{timestamp}] {message}", task["id"])
                
                result = await cc_execute(prompt, json_mode=True)
                
                timestamp = datetime.now().strftime("%H:%M:%S")
                await self.stream_progress(f"[{timestamp}] ✅ cc_execute completed successfully!", task["id"])
                    
            except Exception as e:
                timestamp = datetime.now().strftime("%H:%M:%S")
                await self.stream_progress(f"[{timestamp}] ❌ cc_execute failed: {str(e)}", task["id"])
                raise
                
            task["status"] = "completed"
            
            # Update task status and save result in database
            self.update_task_status(task["id"], "completed", result)
            
            # Find the cc_executor response file for this specific execution
            cc_executor_response_data = None
            try:
                import glob
                import time
                responses_dir = "/Users/<USER>/PycharmProjects/stk_v5/venv/lib/python3.11/site-packages/cc_executor/client/tmp/responses/"
                response_files = glob.glob(f"{responses_dir}cc_execute_*.json")
                
                if response_files:
                    # Look for response file created within the last 30 seconds
                    current_time = time.time()
                    recent_files = []
                    
                    for file_path in response_files:
                        file_time = os.path.getctime(file_path)
                        if current_time - file_time < 30:  # Within last 30 seconds
                            recent_files.append((file_path, file_time))
                    
                    if recent_files:
                        # Get the most recent file from recent executions
                        latest_file = max(recent_files, key=lambda x: x[1])[0]
                        with open(latest_file, 'r') as f:
                            response_data = json.load(f)
                            
                        # Verify this response matches our execution by checking execution_uuid
                        if (result.get("execution_uuid") and 
                            response_data.get("execution_uuid") == result.get("execution_uuid")):
                            cc_executor_response_data = response_data
                        else:
                            # If UUID doesn't match, use the response anyway but note it
                            cc_executor_response_data = response_data
                            cc_executor_response_data["_note"] = "UUID mismatch - may be from different execution"
                            
            except Exception as e:
                print(f"Warning: Could not read cc_executor response file: {e}")
            
            # Save execution history with rich cc_executor data
            self.save_execution_history(
                task["id"], "cc_execute", True, result, 
                cc_executor_output=json.dumps(cc_executor_response_data or result, indent=2)
            )
            
            await self.broadcast_status({
                "type": "task_completed",
                "task": task["name"],
                "task_id": task.get("id"),
                "result": "success",
                "is_individual": is_individual
            })
            
            return {"success": True, "result": result}
            
        except Exception as e:
            error_msg = str(e)
            
            # Check for token limits
            if await self.handle_token_limit(error_msg):
                # Retry after waiting
                return await self.run_single_task(task, is_individual)
            
            task["status"] = "error"
            
            # Update task status in database
            self.update_task_status(task["id"], "error", error_message=error_msg)
            
            # Save execution history
            self.save_execution_history(
                task["id"], "cc_execute", False, 
                error_message=error_msg
            )
            
            await self.broadcast_status({
                "type": "task_error", 
                "task": task["name"],
                "task_id": task.get("id"),
                "error": error_msg,
                "is_individual": is_individual
            })
            
            return {"success": False, "error": error_msg}
    
    async def run_task_by_id(self, task_id: str) -> Dict[str, Any]:
        """Run a specific task by ID"""
        task = self.get_task_by_id(task_id)
        if not task:
            return {"success": False, "error": f"Task {task_id} not found"}
        
        return await self.run_single_task(task, is_individual=True)
    
    async def handle_token_limit(self, error_msg: str) -> bool:
        """Handle token limits by extracting wait time and sleeping"""
        
        # Try to get limit info from Claude CLI
        try:
            result = subprocess.run(
                ["claude", "-p", "What's my current usage status?"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            # Look for patterns like "limit reached until 2024-01-01 15:30:00"
            limit_pattern = r"until (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})"
            match = re.search(limit_pattern, result.stdout + result.stderr)
            
            if match:
                limit_time_str = match.group(1)
                limit_time = datetime.strptime(limit_time_str, "%Y-%m-%d %H:%M:%S")
                
                wait_seconds = (limit_time - datetime.now()).total_seconds()
                if wait_seconds > 0:
                    await self.wait_for_token_limit(wait_seconds)
                    return True
            
            # Fallback: wait 1 hour if we can't parse the time
            await self.wait_for_token_limit(3600)
            return True
            
        except Exception as e:
            await self.broadcast_status({
                "type": "token_limit_check_failed",
                "error": str(e)
            })
            return False
    
    async def wait_for_token_limit(self, wait_seconds: int):
        """Wait for token limit to reset with countdown"""
        
        end_time = datetime.now() + timedelta(seconds=wait_seconds)
        current_workflow["token_limit_retry_time"] = end_time.isoformat()
        
        await self.broadcast_status({
            "type": "token_limit_wait",
            "message": f"Token limit hit. Waiting {wait_seconds//3600}h {(wait_seconds%3600)//60}m",
            "retry_time": end_time.isoformat()
        })
        
        # Wait with periodic updates
        while datetime.now() < end_time:
            remaining = (end_time - datetime.now()).total_seconds()
            if remaining <= 0:
                break
                
            await self.broadcast_status({
                "type": "token_limit_countdown", 
                "remaining_seconds": int(remaining),
                "message": f"Resuming in {int(remaining//3600)}h {int((remaining%3600)//60)}m {int(remaining%60)}s"
            })
            
            await asyncio.sleep(60)  # Update every minute
        
        current_workflow["token_limit_retry_time"] = None
        await self.broadcast_status({
            "type": "token_limit_resumed",
            "message": "Token limit reset. Resuming tasks..."
        })
    
    async def stream_progress(self, message: str, task_id: str = None):
        """Stream progress messages via Redis and websockets"""
        # Store in Redis for persistence and polling
        if task_id:
            try:
                import redis
                r = redis.Redis(decode_responses=True)
                # Store latest progress message for this task
                r.set(f"task_progress:{task_id}", message, ex=3600)  # Expire after 1 hour
                # Add to progress log (keep last 100 messages)
                r.lpush(f"task_log:{task_id}", f"{datetime.now().isoformat()}|{message}")
                r.ltrim(f"task_log:{task_id}", 0, 99)  # Keep only last 100 messages
            except Exception as e:
                print(f"Redis streaming error: {e}")
        
        # Also broadcast via websockets for real-time updates
        await self.broadcast_status({
            "type": "task_output" if task_id else "progress",
            "message": message,
            "task_id": task_id,
            "timestamp": datetime.now().isoformat(),
            "output_type": "info"
        })
    
    async def broadcast_status(self, status: Dict[str, Any]):
        """Store status in Redis for polling (WebSocket removed)"""
        # Store in Redis for polling
        try:
            import redis
            r = redis.Redis(decode_responses=True)
            r.set("system_status", json.dumps(status), ex=300)  # Expire after 5 minutes
        except Exception as e:
            print(f"Redis status storage error: {e}")
        
        # Add to logs
        current_workflow["logs"].append({
            "timestamp": datetime.now().isoformat(),
            "message": status
        })
        
        # Keep only last 1000 log entries
        if len(current_workflow["logs"]) > 1000:
            current_workflow["logs"] = current_workflow["logs"][-1000:]
        
        # Note: WebSocket broadcasting removed - using Redis-only architecture
    
    def create_phase1_prompt(self, task: Dict[str, Any]) -> str:
        """Create Phase 1 task prompt (same as your run_workflow.py)"""
        test_file = task["test_file"]
        feature_name = task["feature"]
        
        return f"""ORCHESTRATE Phase 1 - Fix Test with Five Whys Analysis:

**Test File**: {test_file}
**Feature**: {feature_name}
**Documentation Context**: 
- /Users/<USER>/PycharmProjects/stk_v5/CLAUDE.md
- /Users/<USER>/PycharmProjects/stk_v5/.claude/commands/five-whys.md 
- /Users/<USER>/PycharmProjects/stk_v5/seeking_alpha_research/STANDALONE_FEATURES_DOCUMENTATION.md

**Phase 1 Workflow**:

Task 1: Run the test
- Execute: python -m pytest {test_file} -v --tb=short
- If PASSES: Move to Phase 2
- If FAILS: Continue to Task 2

Task 2: Five Whys Analysis (if test failed)
- Apply five-whys.md methodology
- Read all documentation for context
- Identify root cause (not symptoms)
- Follow "Money is on the line" standards

Task 3: Fix Implementation  
- Address root cause systematically
- Use real data only (NO FAKES, NO MOCKS)
- Follow existing code conventions
- Test fix works

Task 4: Retry Test
- Re-run: python -m pytest {test_file} -v --tb=short
- If still fails, repeat Tasks 2-4 (max 3 attempts)
- Don't give up easily - apply methodology persistently

**Success Criteria**: 
- ✅ Test passes 100%
- ✅ Root cause addressed
- ✅ Real data integration maintained
- ✅ Ready for Phase 2

Execute this workflow systematically. Only proceed when test is passing."""

    def create_phase2_prompt(self, task: Dict[str, Any]) -> str:
        """Create Phase 2 task prompt (same as your run_workflow.py)"""
        test_file = task["test_file"]
        feature_name = task["feature"]
        
        return f"""ORCHESTRATE Phase 2 - UI Development for Passing Feature:

**Test File**: {test_file} (NOW PASSING from Phase 1)
**Feature**: {feature_name}
**Django Directory**: /Users/<USER>/PycharmProjects/stk_v5/seeking_alpha_research/django_prototype_v0
**Documentation Context**: All 5 files from Phase 1

**Phase 2 Workflow**:

Task 1: Ultrathink Feature Utility
- Read specs.md to understand business value
- Analyze how {feature_name} fits Gap-Up ATM Trading Strategy
- Define user stories: "As a trader, I want to..."
- Plan Django UI components needed

Task 2: Build Django UI
- Examine existing django_prototype_v0 structure
- Create/modify models, views, templates, URLs for {feature_name}
- Integrate with existing navigation and styling
- Display real data from {feature_name} (NO FAKES, NO MOCKS)
- Add data-test-id attributes for testing

Task 3: Playwright Testing
- Create comprehensive test suite for the UI
- Test navigation, data display, user interactions
- Verify real {feature_name} data shows correctly
- Test error handling and edge cases
- Generate screenshots and reports

Task 4: Validation
- Start Django server and verify UI works
- Run Playwright tests and ensure they pass
- Fix any issues found
- Confirm production-ready UI

**Success Criteria**:
- ✅ Django UI integrated with django_prototype_v0
- ✅ Real {feature_name} data displayed correctly  
- ✅ Playwright tests passing
- ✅ Professional trader-ready interface

Build a complete, tested UI that traders can actually use for {feature_name}."""

# Initialize task runner
task_runner = TaskRunner()

# Chat history management functions
def save_chat_message(role: str, message: str, session_id: str = "default", extra_data: str = None):
    """Save a chat message to the database"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Add session_id column if it doesn't exist (for backwards compatibility)
    try:
        cursor.execute("ALTER TABLE chat_history ADD COLUMN session_id TEXT DEFAULT 'default'")
        conn.commit()
    except sqlite3.OperationalError:
        pass  # Column already exists
    
    # Add extra_data column if it doesn't exist
    try:
        cursor.execute("ALTER TABLE chat_history ADD COLUMN extra_data TEXT")
        conn.commit()
    except sqlite3.OperationalError:
        pass  # Column already exists
    
    cursor.execute("""
        INSERT INTO chat_history (role, message, session_id, timestamp, extra_data)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?)
    """, (role, message, session_id, extra_data))
    
    conn.commit()
    conn.close()

def get_chat_history_from_db(session_id: str = "default", limit: int = 50) -> List[Dict[str, Any]]:
    """Get chat history from database for a specific session"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Handle case where session_id column might not exist in older databases
    try:
        cursor.execute("""
            SELECT role, message, timestamp, extra_data 
            FROM chat_history 
            WHERE session_id = ? 
            ORDER BY id DESC 
            LIMIT ?
        """, (session_id, limit))
    except sqlite3.OperationalError:
        # Fallback for older database schema without session_id
        cursor.execute("""
            SELECT role, message, timestamp, data_json as extra_data 
            FROM chat_history 
            ORDER BY id DESC 
            LIMIT ?
        """, (limit,))
    
    messages = []
    for row in reversed(cursor.fetchall()):  # Reverse to get chronological order
        message = {
            "role": row[0],
            "message": row[1],
            "timestamp": row[2]
        }
        if len(row) > 3 and row[3]:  # extra_data
            try:
                message["data"] = json.loads(row[3])
            except:
                pass
        messages.append(message)
    
    conn.close()
    return messages

def get_active_sessions() -> List[str]:
    """Get list of active chat sessions"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        cursor.execute("""
            SELECT DISTINCT session_id, MAX(timestamp) as last_activity
            FROM chat_history 
            GROUP BY session_id
            ORDER BY last_activity DESC
        """)
        sessions = [row[0] for row in cursor.fetchall()]
    except sqlite3.OperationalError:
        # Fallback for older database schema
        sessions = ["default"]
    
    conn.close()
    return sessions

from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    init_database()
    
    # Reset any tasks stuck in "running" state from previous server crash
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("UPDATE tasks SET status = 'pending' WHERE status = 'running'")
    conn.commit()
    conn.close()
    print("🔄 Reset stuck 'running' tasks to 'pending' status")
    
    await task_runner.load_tasks_from_workflow()
    current_workflow["progress"]["total"] = len(task_runner.tasks)
    yield
    # Shutdown
    pass

app = FastAPI(title="Overnight Task Runner", lifespan=lifespan)

@app.get("/", response_class=HTMLResponse)
async def dashboard():
    """Enhanced HTML dashboard with all new features"""
    return """
<!DOCTYPE html>
<html>
<head>
    <title>Overnight Task Runner</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: -apple-system, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .tabs { display: flex; margin-bottom: 20px; background: #2a2a2a; border-radius: 8px; padding: 5px; }
        .tab { flex: 1; padding: 12px; text-align: center; cursor: pointer; border-radius: 5px; transition: all 0.3s; }
        .tab.active { background: #4CAF50; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .status-card { background: #2a2a2a; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .system-info { background: #2a2a2a; padding: 15px; border-radius: 8px; margin-bottom: 20px; font-size: 14px; }
        .system-info .info-item { margin-bottom: 8px; }
        .system-info .status-good { color: #4CAF50; }
        .system-info .status-bad { color: #f44336; }
        .progress-bar { width: 100%; height: 20px; background: #444; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #4CAF50, #45a049); transition: width 0.3s; }
        .task-list { background: #2a2a2a; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .task-item { padding: 15px; border-left: 3px solid #666; margin-bottom: 10px; background: #333; }
        .task-item.running { border-color: #2196F3; background: #1e3a5f; }
        .task-item.completed { border-color: #4CAF50; background: #1e5f1e; }
        .task-item.error { border-color: #f44336; background: #5f1e1e; }
        .task-header { display: flex; justify-content: space-between; align-items: center; }
        .task-terminal { background: #000; color: #0f0; font-family: 'Courier New', monospace; font-size: 11px; margin-top: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto; padding: 10px; display: none; }
        .task-terminal.active { display: block; }
        .task-progress { font-size: 12px; color: #2196F3; margin-top: 5px; }
        .terminal-toggle { background: #666; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 11px; cursor: pointer; }
        .terminal-toggle.active { background: #4CAF50; }
        .task-info { flex: 1; }
        .task-actions { display: flex; gap: 8px; }
        .logs { background: #000; padding: 15px; border-radius: 8px; height: 400px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 12px; }
        .log-entry { margin-bottom: 5px; }
        .log-timestamp { color: #888; }
        .log-progress { color: #4CAF50; }
        .log-error { color: #f44336; }
        .log-info { color: #2196F3; }
        .controls { text-align: center; margin-bottom: 20px; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        .btn-small { padding: 6px 12px; font-size: 12px; }
        .btn-primary { background: #4CAF50; color: white; }
        .btn-danger { background: #f44336; color: white; }
        .btn-secondary { background: #666; color: white; }
        .btn-warning { background: #ff9800; color: white; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .token-limit-warning { background: #ff9800; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center; }
        .chat-container { background: #2a2a2a; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .chat-history { background: #000; padding: 15px; border-radius: 8px; height: 300px; overflow-y: auto; margin-bottom: 15px; }
        .chat-message { margin-bottom: 15px; padding: 10px; border-radius: 8px; }
        .chat-message.user { background: #1e3a5f; text-align: right; }
        .chat-message.assistant { background: #1e5f1e; }
        .chat-message.error { background: #5f1e1e; }
        .chat-input { display: flex; gap: 10px; }
        .chat-input input { flex: 1; padding: 10px; border: none; border-radius: 5px; background: #333; color: white; }
        .pending-tasks { background: #2a2a2a; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .pending-item { background: #333; padding: 15px; margin-bottom: 10px; border-radius: 8px; border-left: 3px solid #ff9800; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; }
        .modal-content { background: #2a2a2a; margin: 5% auto; padding: 20px; border-radius: 8px; width: 80%; max-width: 800px; max-height: 80%; overflow-y: auto; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .close { font-size: 28px; cursor: pointer; }
        .claude-md-content { background: #000; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap; }
        @media (max-width: 768px) {
            body { margin: 10px; }
            .logs { height: 250px; }
            .chat-history { height: 200px; }
            .modal-content { width: 95%; margin: 2% auto; }
            .task-item { flex-direction: column; align-items: flex-start; }
            .task-actions { margin-top: 10px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌙 Overnight Task Runner</h1>
            <p>Enhanced with chat, individual task control, and system monitoring</p>
        </div>

        <!-- System Info -->
        <div class="system-info">
            <h4>🖥️ System Status</h4>
            <div id="system-info-content">Loading system information...</div>
            <button class="btn btn-secondary btn-small" onclick="showClaudeMd()">📖 View CLAUDE.md</button>
        </div>
        
        <div id="token-warning" class="token-limit-warning" style="display: none;">
            <strong>Token Limit Hit!</strong>
            <div id="countdown"></div>
        </div>

        <!-- Tabs -->
        <div class="tabs">
            <div class="tab active" onclick="showTab('dashboard')">📊 Dashboard</div>
            <div class="tab" onclick="showTab('chat')">💬 Chat & Create Tasks</div>
            <div class="tab" onclick="showTab('pending')">⏳ Pending Tasks</div>
        </div>

        <!-- Dashboard Tab -->
        <div id="dashboard-tab" class="tab-content active">
            <div class="status-card">
                <h3>Status: <span id="status">Loading...</span></h3>
                <div class="progress-bar">
                    <div id="progress" class="progress-fill" style="width: 0%"></div>
                </div>
                <p id="current-task">Initializing...</p>
                <p><strong>Started:</strong> <span id="start-time">-</span></p>
            </div>
            
            <div class="controls">
                <button id="start-btn" class="btn btn-primary" onclick="startWorkflow()">🚀 Start Overnight Run</button>
                <button id="pause-btn" class="btn btn-secondary" onclick="pauseWorkflow()" disabled>⏸️ Pause</button>
                <button id="stop-btn" class="btn btn-danger" onclick="stopWorkflow()" disabled>🛑 Stop</button>
            </div>
            
            <div class="task-list">
                <h3>📋 Tasks Queue (<span id="task-count">0</span> tasks)</h3>
                <div id="tasks"></div>
            </div>
            
            <div class="logs">
                <h4>📄 Live Task Output</h4>
                <div id="log-output">Connecting to task runner...</div>
            </div>
        </div>

        <!-- Chat Tab -->
        <div id="chat-tab" class="tab-content">
            <div class="chat-container">
                <h3>💬 Chat with Claude to Create Tasks</h3>
                <p>Ask Claude to create custom tasks for overnight execution. Examples:</p>
                <ul style="margin-bottom: 15px; font-size: 14px;">
                    <li>"Create a task to analyze PLUG's recent SEC filings"</li>
                    <li>"Build a backtest for gap-up strategies on NASDAQ stocks"</li>
                    <li>"Generate Django views for the insider trading detector"</li>
                </ul>
                
                <div class="chat-history" id="chat-history">
                    <div class="chat-message assistant">
                        <strong>Claude:</strong> Hi! I'm here to help you create tasks for overnight execution. What would you like to work on?
                    </div>
                </div>
                
                <div class="chat-input">
                    <input type="text" id="chat-input" placeholder="Describe what you want to accomplish..." 
                           onkeypress="if(event.key==='Enter') sendChatMessage()">
                    <button class="btn btn-primary" onclick="sendChatMessage()">Send</button>
                </div>
            </div>
        </div>

        <!-- Pending Tasks Tab -->
        <div id="pending-tab" class="tab-content">
            <div class="pending-tasks">
                <h3>⏳ Tasks Pending Approval</h3>
                <p>Review and approve tasks created via chat before adding them to the queue.</p>
                <div id="pending-tasks-list">
                    <p style="color: #888;">No pending tasks</p>
                </div>
            </div>
        </div>
    </div>

    <!-- CLAUDE.md Modal -->
    <div id="claude-md-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📖 CLAUDE.md</h3>
                <span class="close" onclick="hideClaudeMd()">&times;</span>
            </div>
            <div class="claude-md-content" id="claude-md-content">
                Loading CLAUDE.md...
            </div>
        </div>
    </div>

    <!-- Task JSON Modal -->
    <div id="task-json-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📄 Task JSON Output</h3>
                <span class="close" onclick="hideTaskJson()">&times;</span>
            </div>
            <div class="claude-md-content" id="task-json-content">
                Loading task JSON...
            </div>
        </div>
    </div>

    <script>
        let currentStatus = 'idle';
        let currentTab = 'dashboard';
        
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
            currentTab = tabName;
            
            // Load data for specific tabs
            if (tabName === 'pending') {
                loadPendingTasks();
            } else if (tabName === 'chat') {
                loadChatHistory();
            }
        }
        
        function initializeRedisMode() {
            addLog('Connected to task runner (Redis mode)', 'info');
            
            // Initial data load
            loadStatus();
            loadTasks();
            loadSystemInfo();
            
            // Regular polling for updates (reduced frequency)
            setInterval(() => {
                if (currentTab === 'dashboard') {
                    loadStatus();
                    loadTasks();
                    loadSystemInfo();
                }
            }, 10000); // Poll every 10 seconds
        }
        
        async function loadSystemInfo() {
            try {
                const response = await fetch('/api/system-info');
                const info = await response.json();
                updateSystemInfo(info);
            } catch (error) {
                console.error('Failed to load system info:', error);
            }
        }
        
        function updateSystemInfo(info) {
            const container = document.getElementById('system-info-content');
            const redisStatus = info.redis_status === 'running' ? 
                '<span class="status-good">✅ Running</span>' : 
                '<span class="status-bad">❌ Not Running</span>';
                
            container.innerHTML = `
                <div class="info-item"><strong>Working Dir:</strong> ${info.working_dir}</div>
                <div class="info-item"><strong>Python Env:</strong> ${info.venv}</div>
                <div class="info-item"><strong>Redis:</strong> ${redisStatus}</div>
            `;
        }
        
        async function showClaudeMd() {
            try {
                const response = await fetch('/api/claude-md');
                const data = await response.json();
                document.getElementById('claude-md-content').textContent = data.content;
                document.getElementById('claude-md-modal').style.display = 'block';
            } catch (error) {
                alert('Failed to load CLAUDE.md: ' + error.message);
            }
        }
        
        function hideClaudeMd() {
            document.getElementById('claude-md-modal').style.display = 'none';
        }
        
        async function viewTaskJson(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/json`);
                const data = await response.json();
                
                const task = data.task_info;
                let content = `=== TASK DETAILS ===\\n`;
                content += `ID: ${task.id}\\n`;
                content += `Name: ${task.name}\\n`;
                content += `Type: ${task.type}\\n`;
                content += `Status: ${task.status}\\n`;
                content += `Feature: ${task.feature || 'N/A'}\\n`;
                content += `Source: ${task.source}\\n`;
                content += `Estimated Time: ${task.estimated_time}\\n`;
                content += `Test File: ${task.test_file || 'N/A'}\\n`;
                content += `Created: ${task.created_at}\\n`;
                content += `Updated: ${task.updated_at}\\n\\n`;
                
                if (task.error_message) {
                    content += `=== ERROR MESSAGE ===\\n`;
                    content += `${task.error_message}\\n\\n`;
                }
                
                if (task.prompt) {
                    content += `=== FULL TASK PROMPT ===\\n`;
                    content += `${task.prompt}\\n\\n`;
                }
                
                if (task.result_json) {
                    try {
                        const resultObj = typeof task.result_json === 'string' ? JSON.parse(task.result_json) : task.result_json;
                        
                        // Extract success status if available
                        const successStatus = resultObj.success;
                        if (successStatus !== undefined) {
                            const statusIcon = successStatus ? "✅ SUCCESS" : "❌ FAILED";
                            content += `=== EXECUTION STATUS ===\\n${statusIcon}\\n\\n`;
                        }
                        
                        // Extract summary if available
                        if (resultObj.summary) {
                            content += `=== SUMMARY ===\\n${resultObj.summary}\\n\\n`;
                        }
                        
                        // Show files created/modified if available
                        if (resultObj.files_created && resultObj.files_created.length > 0) {
                            content += `=== FILES CREATED ===\\n`;
                            resultObj.files_created.forEach(file => {
                                content += `  - ${file}\\n`;
                            });
                            content += `\\n`;
                        }
                        
                        if (resultObj.files_modified && resultObj.files_modified.length > 0) {
                            content += `=== FILES MODIFIED ===\\n`;
                            resultObj.files_modified.forEach(file => {
                                content += `  - ${file}\\n`;
                            });
                            content += `\\n`;
                        }
                        
                        content += `=== FULL TASK RESULT JSON ===\\n`;
                        content += JSON.stringify(resultObj, null, 2) + '\\n\\n';
                    } catch(e) {
                        content += `=== TASK RESULT JSON (Raw) ===\\n`;
                        content += JSON.stringify(task.result_json, null, 2) + '\\n\\n';
                    }
                }
                
                if (data.execution_history && data.execution_history.length > 0) {
                    content += `=== EXECUTION HISTORY (${data.execution_history.length} executions) ===\\n`;
                    data.execution_history.forEach((exec, i) => {
                        content += `\\n--- Execution ${i + 1} ---\\n`;
                        content += `Type: ${exec.execution_type}\\n`;
                        content += `Started: ${exec.started_at}\\n`;
                        content += `Completed: ${exec.completed_at || 'N/A'}\\n`;
                        content += `Success: ${exec.success}\\n`;
                        
                        if (exec.error_message) {
                            content += `Error: ${exec.error_message}\\n`;
                        }
                        
                        if (exec.cc_executor_output) {
                            try {
                                const ccData = JSON.parse(exec.cc_executor_output);
                                
                                // Check if this is rich cc_executor data (has session_id, timestamp, etc.)
                                if (ccData.session_id && ccData.timestamp) {
                                    content += `\\n=== RICH CC_EXECUTOR DATA ===\\n`;
                                    
                                    if (ccData._note) {
                                        content += `⚠️ NOTE: ${ccData._note}\\n\\n`;
                                    }
                                    
                                    content += `Session ID: ${ccData.session_id}\\n`;
                                    content += `Timestamp: ${ccData.timestamp}\\n`;
                                    content += `Execution Time: ${ccData.execution_time ? ccData.execution_time.toFixed(2) + 's' : 'N/A'}\\n`;
                                    content += `Return Code: ${ccData.return_code || 0}\\n`;
                                    content += `Execution UUID: ${ccData.execution_uuid || 'N/A'}\\n`;
                                    
                                    if (ccData.task) {
                                        content += `\\n=== FULL TASK PROMPT ===\\n`;
                                        content += ccData.task + '\\n';
                                    }
                                    
                                    if (ccData.output) {
                                        content += `\\n=== RAW OUTPUT ===\\n`;
                                        content += ccData.output + '\\n';
                                    }
                                    
                                    if (ccData.error) {
                                        content += `\\n=== ERRORS ===\\n`;
                                        content += ccData.error + '\\n';
                                    }
                                } else {
                                    // Fallback to simple display
                                    content += `\\nCC Executor Output:\\n${exec.cc_executor_output}\\n`;
                                }
                            } catch (e) {
                                // Fallback if JSON parsing fails
                                content += `\\nCC Executor Output:\\n${exec.cc_executor_output}\\n`;
                            }
                        }
                        
                        if (exec.result_json) {
                            content += `\\nResult JSON:\\n${JSON.stringify(exec.result_json, null, 2)}\\n`;
                        }
                    });
                } else {
                    content += `=== EXECUTION HISTORY ===\\n`;
                    content += `No executions yet - task has not been run.\\n`;
                }
                
                document.getElementById('task-json-content').textContent = content;
                document.getElementById('task-json-modal').style.display = 'block';
            } catch (error) {
                alert('Failed to load task JSON: ' + error.message);
            }
        }
        
        function hideTaskJson() {
            document.getElementById('task-json-modal').style.display = 'none';
        }
        
        async function sendChatMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            if (!message) return;
            
            // Add user message to chat
            addChatMessage('user', message);
            input.value = '';
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const response = data.response;
                    addChatMessage('assistant', response.result || response.message || 'Task created successfully');
                    if (response.response_type === 'tasks' && response.tasks) {
                        addLog(`Created ${response.tasks.length} tasks via chat`, 'info');
                        loadPendingTasks();
                    }
                } else {
                    addChatMessage('error', 'Error: ' + data.error);
                }
            } catch (error) {
                addChatMessage('error', 'Failed to send message: ' + error.message);
            }
        }
        
        function addChatMessage(role, message) {
            const chatHistory = document.getElementById('chat-history');
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${role}`;
            
            const timestamp = new Date().toLocaleTimeString('en-GB', {timeZone: 'Europe/London'});
            const roleLabel = role === 'user' ? 'You' : role === 'assistant' ? 'Claude' : 'Error';
            
            messageDiv.innerHTML = `<strong>${roleLabel} (${timestamp}):</strong><br>${message}`;
            chatHistory.appendChild(messageDiv);
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }
        
        async function loadChatHistory() {
            try {
                const response = await fetch('/api/chat/history');
                const data = await response.json();
                
                const chatHistory = document.getElementById('chat-history');
                chatHistory.innerHTML = '<div class="chat-message assistant"><strong>Claude:</strong> Hi! I\\'m here to help you create tasks for overnight execution. What would you like to work on?</div>';
                
                data.chat_history.forEach(msg => {
                    addChatMessage(msg.role, msg.message);
                });
            } catch (error) {
                console.error('Failed to load chat history:', error);
            }
        }
        
        async function loadPendingTasks() {
            try {
                const response = await fetch('/api/pending-tasks');
                const data = await response.json();
                updatePendingTasksDisplay(data.pending_tasks);
            } catch (error) {
                console.error('Failed to load pending tasks:', error);
            }
        }
        
        function updatePendingTasksDisplay(pendingTasks) {
            const container = document.getElementById('pending-tasks-list');
            
            if (pendingTasks.length === 0) {
                container.innerHTML = '<p style="color: #888;">No pending tasks</p>';
                return;
            }
            
            container.innerHTML = pendingTasks.map((task, index) => `
                <div class="pending-item">
                    <h4>${task.name}</h4>
                    <p><strong>Type:</strong> ${task.type}</p>
                    <p><strong>Estimated Time:</strong> ${task.estimated_time}</p>
                    <p><strong>Description:</strong> ${task.description || 'No description'}</p>
                    <div class="task-actions" style="margin-top: 10px;">
                        <button class="btn btn-primary btn-small" onclick="approvePendingTask(${index})">✅ Approve</button>
                        <button class="btn btn-danger btn-small" onclick="rejectPendingTask(${index})">❌ Reject</button>
                    </div>
                </div>
            `).join('');
        }
        
        async function approvePendingTask(index) {
            try {
                const response = await fetch(`/api/pending-tasks/${index}/approve`, { method: 'POST' });
                const data = await response.json();
                addLog(`Approved task: ${data.task_id}`, 'info');
                loadPendingTasks();
                loadTasks(); // Refresh main task list
            } catch (error) {
                addLog('Failed to approve task', 'error');
            }
        }
        
        async function rejectPendingTask(index) {
            try {
                await fetch(`/api/pending-tasks/${index}`, { method: 'DELETE' });
                addLog('Rejected pending task', 'info');
                loadPendingTasks();
            } catch (error) {
                addLog('Failed to reject task', 'error');
            }
        }
        
        async function runIndividualTask(taskId) {
            try {
                // Start progress polling before running the task
                startProgressPolling(taskId);
                
                const response = await fetch(`/api/tasks/${taskId}/run`, { method: 'POST' });
                const data = await response.json();
                addLog(`Running individual task: ${taskId}`, 'info');
                
                // Stop polling when task completes (after a delay to catch final messages)
                setTimeout(() => {
                    stopProgressPolling(taskId);
                    loadTasks(); // Refresh task display
                }, 3000);
                
            } catch (error) {
                addLog(`Failed to run task ${taskId}: ${error.message}`, 'error');
                stopProgressPolling(taskId);
            }
        }
        
        async function deleteTask(taskId) {
            if (!confirm('Are you sure you want to delete this task?')) return;
            
            try {
                await fetch(`/api/tasks/${taskId}`, { method: 'DELETE' });
                addLog(`Deleted task: ${taskId}`, 'info');
                loadTasks();
            } catch (error) {
                addLog(`Failed to delete task: ${error.message}`, 'error');
            }
        }
        
        function handleStatusUpdate(data) {
            const timestamp = new Date().toLocaleTimeString('en-GB', {timeZone: 'Europe/London'});
            
            switch(data.type) {
                case 'task_started':
                    addLog(`[${timestamp}] Started: ${data.task}`, 'info');
                    updateCurrentTask(data.task);
                    if (data.task_id) {
                        addTaskOutput(data.task_id, `Task started: ${data.task}`, 'info');
                        updateTaskProgress(data.task_id, 'Starting execution...');
                    }
                    if (currentTab === 'dashboard') loadTasks(); // Refresh task display
                    break;
                case 'task_completed':
                    addLog(`[${timestamp}] ✅ Completed: ${data.task}`, 'progress');
                    if (data.task_id) {
                        addTaskOutput(data.task_id, `✅ Task completed successfully`, 'success');
                        updateTaskProgress(data.task_id, 'Completed');
                    }
                    if (currentTab === 'dashboard') loadTasks();
                    break;
                case 'task_error':
                    addLog(`[${timestamp}] ❌ Error in ${data.task}: ${data.error}`, 'error');
                    if (data.task_id) {
                        addTaskOutput(data.task_id, `❌ Task failed: ${data.error}`, 'error');
                        updateTaskProgress(data.task_id, 'Failed');
                    }
                    if (currentTab === 'dashboard') loadTasks();
                    break;
                case 'progress':
                    addLog(`[${timestamp}] ${data.message}`, 'progress');
                    // Route progress to specific task if task_id is provided
                    if (data.task_id) {
                        addTaskOutput(data.task_id, data.message, 'info');
                        updateTaskProgress(data.task_id, data.message);
                    }
                    break;
                case 'task_output':
                    // New message type for direct task output
                    if (data.task_id && data.message) {
                        addTaskOutput(data.task_id, data.message, data.output_type || 'info');
                    }
                    break;
                case 'token_limit_wait':
                    showTokenLimitWarning(data.message, data.retry_time);
                    break;
                case 'token_limit_countdown':
                    updateCountdown(data.remaining_seconds, data.message);
                    break;
                case 'token_limit_resumed':
                    hideTokenLimitWarning();
                    addLog(`[${timestamp}] ${data.message}`, 'info');
                    break;
            }
        }
        
        function addLog(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = message;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            
            // Keep only last 500 log entries
            if (logOutput.children.length > 500) {
                logOutput.removeChild(logOutput.firstChild);
            }
        }
        
        function showTokenLimitWarning(message, retryTime) {
            document.getElementById('token-warning').style.display = 'block';
            document.getElementById('countdown').textContent = message;
        }
        
        function hideTokenLimitWarning() {
            document.getElementById('token-warning').style.display = 'none';
        }
        
        function updateCountdown(seconds, message) {
            document.getElementById('countdown').textContent = message;
        }
        
        async function loadStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                updateStatusDisplay(status);
            } catch (error) {
                addLog('Failed to load status', 'error');
            }
        }
        
        async function loadTasks() {
            try {
                const response = await fetch('/api/tasks');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const tasks = await response.json();
                updateTasksDisplay(tasks);
            } catch (error) {
                console.error('Error loading tasks:', error);
                addLog(`Failed to load tasks: ${error.message}`, 'error');
                
                // Show error message in tasks container
                const tasksContainer = document.getElementById('tasks');
                if (tasksContainer) {
                    tasksContainer.innerHTML = `<div style="color: #f44336; padding: 10px;">Error loading tasks: ${error.message}</div>`;
                }
            }
        }
        
        function updateStatusDisplay(status) {
            document.getElementById('status').textContent = status.status;
            document.getElementById('current-task').textContent = status.current_task || 'Waiting...';
            document.getElementById('start-time').textContent = status.start_time || '-';
            
            const progress = (status.progress.completed / status.progress.total) * 100;
            document.getElementById('progress').style.width = progress + '%';
            
            // Update button states
            const isRunning = status.status === 'running';
            document.getElementById('start-btn').disabled = isRunning;
            document.getElementById('pause-btn').disabled = !isRunning;
            document.getElementById('stop-btn').disabled = status.status === 'idle';
        }
        
        function updateTasksDisplay(tasks) {
            const tasksContainer = document.getElementById('tasks');
            const taskCount = document.getElementById('task-count');
            taskCount.textContent = tasks.length;
            
            tasksContainer.innerHTML = tasks.map((task, index) => `
                <div class="task-item ${task.status || ''}" id="task-${task.id}">
                    <div class="task-header">
                        <div class="task-info">
                            <strong>${task.name}</strong>
                            <div style="font-size: 12px; color: #aaa; margin-top: 5px;">
                                Estimated: ${task.estimated_time} | Feature: ${task.feature || 'N/A'} | Source: ${task.source || 'workflow'}
                            </div>
                            ${task.status ? `<div style="font-size: 12px; color: #fff; margin-top: 3px;">Status: ${task.status}${task.status === 'completed' && task.success_status !== undefined ? (task.success_status ? ' ✅' : ' ❌') : ''}</div>` : ''}
                            ${task.status === 'running' ? `<div class="task-progress" id="progress-${task.id}">⚡ Executing...</div>` : ''}
                            ${task.status === 'completed' && task.task_summary ? `<div style="font-size: 11px; color: #aaa; margin-top: 3px; font-style: italic;">${task.task_summary}</div>` : ''}
                        </div>
                        <div class="task-actions">
                            <button class="btn btn-primary btn-small" onclick="runIndividualTask('${task.id}')" 
                                    ${task.status === 'running' ? 'disabled' : ''}>
                                ▶️ Run
                            </button>
                            <button class="btn btn-secondary btn-small" onclick="viewTaskJson('${task.id}')">
                                📄 JSON
                            </button>
                            ${task.status === 'running' || task.status === 'completed' || task.status === 'error' ? 
                                `<button class="terminal-toggle btn-small" onclick="toggleTaskTerminal('${task.id}')" id="toggle-${task.id}">
                                    📺 Terminal
                                </button>` : ''}
                            <button class="btn btn-danger btn-small" onclick="deleteTask('${task.id}')">
                                🗑️ Delete
                            </button>
                        </div>
                    </div>
                    <div class="task-terminal" id="terminal-${task.id}">
                        <div style="color: #888; margin-bottom: 5px; display: flex; justify-content: space-between;">
                            <span>--- Task Output for ${task.name} ---</span>
                            ${task.status === 'completed' ? '<button class="btn btn-small btn-warning" onclick="viewTaskJson(\\''+task.id+'\\')">📄 Show Result JSON</button>' : ''}
                        </div>
                        <div id="output-${task.id}">
                            ${task.status === 'running' ? 'Starting execution...' : 
                              task.status === 'completed' ? 'Task completed. Click "Show Result JSON" to view the final output.' :
                              task.status === 'error' ? 'Task failed. Check error details.' :
                              'Task not executed yet.'}
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        function updateCurrentTask(taskName) {
            document.getElementById('current-task').textContent = taskName;
        }
        
        function toggleTaskTerminal(taskId) {
            const terminal = document.getElementById(`terminal-${taskId}`);
            const toggle = document.getElementById(`toggle-${taskId}`);
            
            if (terminal.classList.contains('active')) {
                terminal.classList.remove('active');
                toggle.textContent = '📺 Terminal';
                toggle.classList.remove('active');
            } else {
                terminal.classList.add('active');
                toggle.textContent = '📺 Hide Terminal';
                toggle.classList.add('active');
            }
        }
        
        function addTaskOutput(taskId, message, type = 'info') {
            const output = document.getElementById(`output-${taskId}`);
            if (!output) return;
            
            const timestamp = new Date().toLocaleTimeString('en-GB', {timeZone: 'Europe/London'});
            const logLine = document.createElement('div');
            logLine.style.marginBottom = '2px';
            logLine.style.color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#0f0';
            logLine.textContent = `[${timestamp}] ${message}`;
            
            output.appendChild(logLine);
            output.scrollTop = output.scrollHeight;
            
            // Keep only last 100 lines
            if (output.children.length > 100) {
                output.removeChild(output.firstChild);
            }
        }
        
        function updateTaskProgress(taskId, message) {
            const progress = document.getElementById(`progress-${taskId}`);
            if (progress) {
                progress.textContent = `⚡ ${message}`;
            }
        }
        
        // Redis-based progress polling for running tasks
        let progressPollers = new Map();
        
        function startProgressPolling(taskId) {
            if (progressPollers.has(taskId)) {
                return; // Already polling
            }
            
            const poller = setInterval(async () => {
                try {
                    const response = await fetch(`/api/tasks/${taskId}/progress`);
                    const progressData = await response.json();
                    
                    if (progressData.latest_progress) {
                        updateTaskProgress(taskId, progressData.latest_progress);
                    }
                    
                    // Update terminal output if visible
                    const terminalOutput = document.getElementById(`output-${taskId}`);
                    if (terminalOutput && progressData.progress_log) {
                        // Show latest 10 messages in terminal
                        const recentMessages = progressData.progress_log.slice(0, 10);
                        terminalOutput.innerHTML = recentMessages.map(entry => 
                            `<div>[${new Date(entry.timestamp).toLocaleTimeString()}] ${entry.message}</div>`
                        ).join('');
                        terminalOutput.scrollTop = terminalOutput.scrollHeight;
                    }
                    
                } catch (error) {
                    console.error('Progress polling error:', error);
                }
            }, 1000); // Poll every second
            
            progressPollers.set(taskId, poller);
        }
        
        function stopProgressPolling(taskId) {
            const poller = progressPollers.get(taskId);
            if (poller) {
                clearInterval(poller);
                progressPollers.delete(taskId);
            }
        }
        
        async function startWorkflow() {
            try {
                const response = await fetch('/api/start', { method: 'POST' });
                const result = await response.json();
                addLog('Starting workflow...', 'info');
            } catch (error) {
                addLog('Failed to start workflow', 'error');
            }
        }
        
        async function pauseWorkflow() {
            try {
                const response = await fetch('/api/pause', { method: 'POST' });
                addLog('Pausing workflow...', 'info');
            } catch (error) {
                addLog('Failed to pause workflow', 'error');
            }
        }
        
        async function stopWorkflow() {
            try {
                const response = await fetch('/api/stop', { method: 'POST' });
                addLog('Stopping workflow...', 'info');
            } catch (error) {
                addLog('Failed to stop workflow', 'error');
            }
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const claudeModal = document.getElementById('claude-md-modal');
            const jsonModal = document.getElementById('task-json-modal');
            
            if (event.target === claudeModal) {
                hideClaudeMd();
            } else if (event.target === jsonModal) {
                hideTaskJson();
            }
        }
        
        // Initialize
        initializeRedisMode();
        
        // Fallback: load data immediately even if WebSocket isn't ready
        setTimeout(() => {
            loadStatus();
            loadTasks();
            loadSystemInfo();
        }, 500);
        
        // Consolidated polling handled by initializeRedisMode() above
    </script>
</body>
</html>
"""

@app.get("/api/status")
async def get_status():
    """Get current workflow status"""
    return current_workflow

@app.get("/api/tasks")
async def get_tasks():
    """Get list of all tasks with enriched status information"""
    enriched_tasks = []
    
    for task in task_runner.tasks:
        enriched_task = task.copy()
        
        # For completed tasks, extract success status from result JSON
        if task.get("status") == "completed" and task.get("result_json"):
            try:
                result_obj = task["result_json"]
                if isinstance(result_obj, dict):
                    # Check for explicit success field
                    if "success" in result_obj:
                        enriched_task["success_status"] = result_obj["success"]
                    else:
                        # Infer success from absence of errors and presence of result
                        # If task completed without error, assume success
                        has_error = task.get("error_message") is not None
                        has_result = "result" in result_obj or "summary" in result_obj
                        enriched_task["success_status"] = not has_error and has_result
                    
                    enriched_task["task_summary"] = result_obj.get("summary", "")
            except:
                # If we can't parse result, infer success from status
                enriched_task["success_status"] = task.get("error_message") is None
        
        enriched_tasks.append(enriched_task)
    
    return enriched_tasks

@app.post("/api/start")
async def start_workflow():
    """Start the overnight workflow"""
    if current_workflow["status"] == "running":
        raise HTTPException(status_code=400, detail="Workflow already running")
    
    current_workflow["status"] = "running"
    current_workflow["start_time"] = datetime.now().isoformat()
    current_workflow["progress"]["completed"] = 0
    
    # Start workflow in background
    asyncio.create_task(run_workflow_background())
    
    return {"message": "Workflow started", "total_tasks": len(task_runner.tasks)}

@app.post("/api/pause")
async def pause_workflow():
    """Pause the workflow"""
    current_workflow["status"] = "paused"
    task_runner.is_running = False
    return {"message": "Workflow paused"}

@app.post("/api/stop")
async def stop_workflow():
    """Stop the workflow"""
    current_workflow["status"] = "idle"
    task_runner.is_running = False
    task_runner.current_task_index = 0
    current_workflow["progress"]["completed"] = 0
    return {"message": "Workflow stopped"}

# New API endpoints for enhanced functionality

@app.get("/api/system-info")
async def get_system_info_endpoint():
    """Get current system information"""
    return get_system_info()

@app.get("/api/claude-md")
async def get_claude_md():
    """Get CLAUDE.md content for quick review"""
    return {"content": read_claude_md()}

@app.post("/api/tasks/{task_id}/run")
async def run_individual_task(task_id: str):
    """Run a specific task by ID"""
    if current_workflow["status"] == "running":
        raise HTTPException(status_code=400, detail="Cannot run individual task while workflow is running")
    
    result = await task_runner.run_task_by_id(task_id)
    return result

@app.delete("/api/tasks/{task_id}")
async def delete_task(task_id: str):
    """Delete a task by ID"""
    if current_workflow["status"] == "running":
        raise HTTPException(status_code=400, detail="Cannot delete tasks while workflow is running")
    
    success = task_runner.delete_task(task_id)
    if success:
        return {"message": f"Task {task_id} deleted"}
    else:
        raise HTTPException(status_code=404, detail="Task not found")

@app.post("/api/chat")
async def chat_with_claude(message: dict):
    """Chat with Claude Code to create tasks with session history management"""
    user_message = message.get("message", "")
    session_id = message.get("session_id", "default")
    
    if not user_message:
        raise HTTPException(status_code=400, detail="Message is required")
    
    # Store user message in database
    save_chat_message("user", user_message, session_id)
    
    # Get conversation history for context
    conversation_history = get_chat_history_from_db(session_id, limit=20)
    
    try:
        # Create a more conversational prompt with session history context
        conversation_context = "\n".join([
            f"{msg['role']}: {msg['message']}"
            for msg in conversation_history[-10:]  # Last 10 messages for context
        ])
        
        claude_prompt = f"""
You are Claude, an AI assistant helping with a trading strategy development project. 
The user is chatting with you to create tasks for overnight execution.

Conversation history:
{conversation_context}

Current user message: "{user_message}"

Context and Guidelines:
- This is a real-time chat interface for overnight task creation
- User wants help with algorithmic trading strategy research  
- Be conversational and helpful, not just a task generator
- Remember previous conversation context when responding
- Ask clarifying questions when needed
- Only create tasks when the user clearly wants something specific done
- Reference previous discussion topics naturally when relevant

Respond conversationally. If they want a specific task created, you can create it, but also engage in dialogue.

If creating tasks, use this JSON format:
{{
    "response_type": "tasks",
    "result": "Your conversational response explaining what you'll create",
    "tasks": [
        {{
            "name": "Task name",
            "type": "custom", 
            "prompt": "Detailed prompt for cc_execute. IMPORTANT: Always end your task prompts with: '\\n\\nReturn your response as JSON with these required fields:\\n{{\\n    \"success\": true/false,\\n    \"result\": \"main result or error description\",\\n    \"summary\": \"brief summary of what was accomplished\",\\n    \"files_created\": [\"list of files created\"],\\n    \"files_modified\": [\"list of files modified\"],\\n    \"execution_uuid\": \"unique identifier\"\\n}}'",
            "estimated_time": "X minutes",
            "description": "What this task does",
            "feature": "relevant_feature"
        }}
    ]
}}

Otherwise just chat:
{{
    "response_type": "conversation",
    "result": "Your conversational response"
}}

Project context: Gap-Up ATM Trading Strategy Research with Django UI development.
"""
        
        result = await cc_execute(
            claude_prompt,
            json_mode=True
        )
        
        if result is None:
            raise Exception("Claude Code returned None")
        
        # Store Claude's response in database
        response_message = result.get("result", "")
        save_chat_message("assistant", response_message, session_id, json.dumps(result))
        
        # If Claude created tasks, add them to pending tasks
        if result.get("response_type") == "tasks":
            import uuid
            for task_data in result.get("tasks", []):
                # Ensure required fields are present
                task_data["created_via_chat"] = True
                task_data["status"] = "pending_approval"
                task_data["source"] = "chat"
                task_data["id"] = str(uuid.uuid4())[:8]
                if "estimated_time" not in task_data:
                    task_data["estimated_time"] = "5-10 minutes"
                if "feature" not in task_data:
                    task_data["feature"] = "custom"
                pending_tasks.append(task_data)
        
        return {
            "success": True,
            "response": result,
            "session_id": session_id,
            "chat_history": get_chat_history_from_db(session_id, limit=10)
        }
        
    except Exception as e:
        error_message = f"Error communicating with Claude: {str(e)}"
        save_chat_message("error", error_message, session_id)
        
        return {
            "success": False,
            "error": str(e),
            "session_id": session_id,
            "chat_history": get_chat_history_from_db(session_id, limit=10)
        }

@app.get("/api/chat/history")
async def get_chat_history(session_id: str = "default", limit: int = 50):
    """Get chat history for a specific session"""
    return {
        "chat_history": get_chat_history_from_db(session_id, limit),
        "session_id": session_id
    }

@app.get("/api/pending-tasks")
async def get_pending_tasks():
    """Get tasks pending approval"""
    return {"pending_tasks": pending_tasks}

@app.post("/api/pending-tasks/{task_index}/approve")
async def approve_pending_task(task_index: int):
    """Approve a pending task and add it to the main task queue"""
    if task_index < 0 or task_index >= len(pending_tasks):
        raise HTTPException(status_code=404, detail="Pending task not found")
    
    task_data = pending_tasks.pop(task_index)
    task_id = task_runner.add_custom_task(task_data)
    
    return {"message": f"Task approved and added with ID {task_id}", "task_id": task_id}

@app.delete("/api/pending-tasks/{task_index}")
async def reject_pending_task(task_index: int):
    """Reject/delete a pending task"""
    if task_index < 0 or task_index >= len(pending_tasks):
        raise HTTPException(status_code=404, detail="Pending task not found")
    
    pending_tasks.pop(task_index)
    return {"message": "Pending task rejected"}

@app.get("/api/tasks/{task_id}/progress")
async def get_task_progress(task_id: str):
    """Get real-time progress for a specific task from Redis"""
    try:
        import redis
        r = redis.Redis(decode_responses=True)
        
        # Get latest progress message
        latest_progress = r.get(f"task_progress:{task_id}")
        
        # Get progress log (last 50 messages)
        progress_log = r.lrange(f"task_log:{task_id}", 0, 49)
        
        # Parse log entries
        parsed_log = []
        for entry in progress_log:
            if "|" in entry:
                timestamp, message = entry.split("|", 1)
                parsed_log.append({
                    "timestamp": timestamp,
                    "message": message
                })
        
        return {
            "task_id": task_id,
            "latest_progress": latest_progress,
            "progress_log": parsed_log,
            "log_count": len(parsed_log)
        }
        
    except Exception as e:
        return {
            "task_id": task_id,
            "latest_progress": None,
            "progress_log": [],
            "error": str(e)
        }

@app.get("/api/tasks/{task_id}/json")
async def get_task_json(task_id: str):
    """Get JSON output for a specific task"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Get full task details
    cursor.execute("""
        SELECT id, name, type, prompt, test_file, feature, estimated_time, source, status, 
               created_at, result_json, error_message, updated_at
        FROM tasks WHERE id = ?
    """, (task_id,))
    task_row = cursor.fetchone()
    
    if not task_row:
        conn.close()
        raise HTTPException(status_code=404, detail="Task not found")
    
    # Get execution history
    cursor.execute("""
        SELECT execution_type, started_at, completed_at, success, result_json, error_message, cc_executor_output
        FROM execution_history WHERE task_id = ? ORDER BY started_at DESC
    """, (task_id,))
    
    history_rows = cursor.fetchall()
    conn.close()
    
    # Build comprehensive task info
    task_info = {
        "id": task_row[0],
        "name": task_row[1],
        "type": task_row[2],
        "prompt": task_row[3],
        "test_file": task_row[4],
        "feature": task_row[5],
        "estimated_time": task_row[6],
        "source": task_row[7],
        "status": task_row[8],
        "created_at": task_row[9],
        "updated_at": task_row[12],
        "result_json": json.loads(task_row[10]) if task_row[10] else None,
        "error_message": task_row[11]
    }
    
    execution_history = []
    for row in history_rows:
        execution_history.append({
            "execution_type": row[0],
            "started_at": row[1],
            "completed_at": row[2],
            "success": bool(row[3]),
            "result_json": json.loads(row[4]) if row[4] else None,
            "error_message": row[5],
            "cc_executor_output": row[6]
        })
    
    return {
        "task_id": task_id,
        "task_info": task_info,
        "execution_history": execution_history
    }

# WebSocket removed - using Redis-based polling instead

async def run_workflow_background():
    """Run the workflow in the background"""
    task_runner.is_running = True
    task_runner.current_task_index = 0
    
    try:
        for i, task in enumerate(task_runner.tasks):
            if not task_runner.is_running:
                break
            
            # Skip tasks that are already completed or running
            if task.get("status") in ["completed", "running"]:
                await task_runner.broadcast_status({
                    "type": "task_skipped",
                    "task": task["name"],
                    "reason": f"Task already {task.get('status', 'unknown')}"
                })
                continue
                
            task_runner.current_task_index = i
            current_workflow["current_task"] = task["name"]
            
            result = await task_runner.run_single_task(task)
            
            if result["success"]:
                current_workflow["progress"]["completed"] = i + 1
                task["status"] = "completed"
            else:
                task["status"] = "error"
                # Continue to next task even if one fails
                
        # Workflow complete
        if task_runner.is_running:
            current_workflow["status"] = "completed"
            await task_runner.broadcast_status({
                "type": "workflow_completed",
                "message": "🎉 All tasks completed! Check your work in the morning.",
                "completed": current_workflow["progress"]["completed"],
                "total": current_workflow["progress"]["total"]
            })
        
    except Exception as e:
        current_workflow["status"] = "error"
        await task_runner.broadcast_status({
            "type": "workflow_error",
            "error": str(e)
        })
    
    task_runner.is_running = False

# WebSocket endpoint removed - using Redis-based polling instead

if __name__ == "__main__":
    print("🌙 Starting Overnight Task Runner")
    print("📱 Open http://localhost:8002 in your browser")
    print("💡 Mobile-friendly for checking status on phone")
    print("🔄 Includes automatic token limit handling")
    
    uvicorn.run(app, host="0.0.0.0", port=8002)