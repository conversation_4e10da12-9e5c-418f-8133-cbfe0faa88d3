#!/usr/bin/env python3
"""
Comprehensive Playwright Test Suite for Advanced Corporate Actions UI

Tests the Django UI implementation of the advanced_corp_actions feature to ensure:
1. Navigation and page loading works correctly
2. Real data display functionality
3. User interactions and form handling
4. Chart and visualization functionality
5. Real-time updates and auto-refresh
6. Error handling and edge cases

NO FAKES, NO MOCKS - Tests use real Django views and real corporate actions data.
MONEY IS ON THE LINE - Comprehensive testing for production-ready interface.
"""

import pytest
import asyncio
import os
import sys
from datetime import datetime, timedelta
from decimal import Decimal
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# Import Django setup
import django
from django.conf import settings
from django.test.utils import setup_test_environment, teardown_test_environment
from django.test.runner import DiscoverRunner
from django.core.management import execute_from_command_line

# Import Playwright
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext


class TestAdvancedCorpActionsUI:
    """
    Comprehensive test suite for Advanced Corporate Actions UI.
    
    Tests all user journeys and interactions for the advanced_corp_actions feature.
    """
    
    @pytest.fixture(scope="class")
    async def setup_django(self):
        """Set up Django environment for testing."""
        
        # Configure Django settings
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'seeking_alpha_research.django_prototype_v0.seeking_alpha.settings')
        django.setup()
        
        # Set up test environment
        setup_test_environment()
        
        # Create test data
        await self._create_test_corporate_actions()
        
        yield
        
        # Cleanup
        teardown_test_environment()
    
    @pytest.fixture(scope="class")
    async def browser_context(self):
        """Set up Playwright browser context."""
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=False,  # Set to True for CI/CD
                slow_mo=500     # Slow down for better observation
            )
            
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                permissions=['clipboard-read', 'clipboard-write']
            )
            
            yield context
            
            await context.close()
            await browser.close()
    
    @pytest.fixture
    async def page(self, browser_context):
        """Create a new page for each test."""
        
        page = await browser_context.new_page()
        
        # Wait for Django server to be ready
        await page.goto('http://localhost:8000/corp-actions/', wait_until='networkidle')
        
        yield page
        
        await page.close()
    
    async def test_corp_actions_list_page_loads(self, page, setup_django):
        """Test that the corporate actions list page loads correctly."""
        
        # Navigate to corporate actions list
        await page.goto('http://localhost:8000/corp-actions/')
        
        # Check page title and main elements
        await page.wait_for_selector('[data-test-id="total-actions"]', timeout=10000)
        
        # Verify page title
        title = await page.title()
        assert 'Advanced Corporate Actions Monitoring' in title
        
        # Check main header
        header = await page.text_content('h1')
        assert 'Advanced Corporate Actions Monitoring' in header
        
        # Verify monitoring statistics are displayed
        total_actions = await page.text_content('[data-test-id="total-actions"]')
        assert total_actions.isdigit()
        
        active_monitoring = await page.text_content('[data-test-id="active-monitoring"]')
        assert active_monitoring.isdigit()
        
        adjustments_triggered = await page.text_content('[data-test-id="adjustments-triggered"]')
        assert adjustments_triggered.isdigit()
        
        system_health = await page.text_content('[data-test-id="system-health"]')
        assert system_health in ['operational', 'degraded', 'failed', 'recovering', 'unknown']
        
        # Check navigation buttons
        dashboard_btn = page.locator('[data-test-id="dashboard-btn"]')
        await dashboard_btn.wait_for(state='visible')
        
        resilience_btn = page.locator('[data-test-id="resilience-btn"]')
        await resilience_btn.wait_for(state='visible')
        
        print("✅ Corporate actions list page loads correctly")
    
    async def test_corp_actions_cards_display(self, page, setup_django):
        """Test that corporate action cards display correctly with real data."""
        
        await page.goto('http://localhost:8000/corp-actions/')
        
        # Wait for corporate action cards to load
        corp_action_cards = page.locator('[data-test-id^="corp-action-"]')
        await corp_action_cards.first.wait_for(state='visible', timeout=10000)
        
        # Get all corporate action cards
        card_count = await corp_action_cards.count()
        assert card_count > 0, "Should have at least one corporate action card"
        
        # Test first few cards
        for i in range(min(3, card_count)):
            card = corp_action_cards.nth(i)
            
            # Check card visibility
            await card.wait_for(state='visible')
            
            # Verify card contains required elements
            symbol_element = card.locator('h6 strong')
            await symbol_element.wait_for(state='visible')
            
            symbol_text = await symbol_element.text_content()
            assert len(symbol_text) > 0, f"Card {i} should have a symbol"
            
            # Check for view details button
            view_btn = card.locator('[data-test-id^="view-detail-"]')
            await view_btn.wait_for(state='visible')
            
            print(f"✅ Corporate action card {i} displays correctly for {symbol_text}")
    
    async def test_corp_action_detail_navigation(self, page, setup_django):
        """Test navigation to corporate action detail page."""
        
        await page.goto('http://localhost:8000/corp-actions/')
        
        # Wait for first corporate action card
        first_card = page.locator('[data-test-id^="corp-action-"]').first
        await first_card.wait_for(state='visible')
        
        # Click on view details button
        view_btn = first_card.locator('[data-test-id^="view-detail-"]')
        await view_btn.click()
        
        # Wait for detail page to load
        await page.wait_for_selector('[data-test-id="corp-action-title"]', timeout=10000)
        
        # Verify we're on the detail page
        title_element = page.locator('[data-test-id="corp-action-title"]')
        title_text = await title_element.text_content()
        assert len(title_text) > 0, "Detail page should have a title"
        
        # Check breadcrumb navigation
        breadcrumb_list = page.locator('[data-test-id="breadcrumb-list"]')
        await breadcrumb_list.wait_for(state='visible')
        
        # Verify monitoring status is displayed
        monitoring_status = page.locator('[data-test-id="monitoring-status"]')
        await monitoring_status.wait_for(state='visible')
        
        print("✅ Navigation to corporate action detail page works")
    
    async def test_corp_action_detail_data_display(self, page, setup_django):
        """Test that corporate action detail page displays data correctly."""
        
        # Go to the first corporate action detail page
        await page.goto('http://localhost:8000/corp-actions/')
        
        first_card = page.locator('[data-test-id^="corp-action-"]').first
        await first_card.wait_for(state='visible')
        
        view_btn = first_card.locator('[data-test-id^="view-detail-"]')
        await view_btn.click()
        
        # Wait for detail page elements
        await page.wait_for_selector('[data-test-id="symbol"]', timeout=10000)
        
        # Check corporate action details table
        symbol = await page.text_content('[data-test-id="symbol"]')
        assert len(symbol) > 0, "Symbol should be displayed"
        
        action_type = await page.text_content('[data-test-id="action-type"]')
        assert action_type in ['Dividend', 'Stock Split', 'Spinoff', 'Merger', 'Delisting']
        
        effective_date = await page.text_content('[data-test-id="effective-date"]')
        assert len(effective_date) > 0, "Effective date should be displayed"
        
        detection_time = await page.text_content('[data-test-id="detection-time"]')
        assert len(detection_time) > 0, "Detection time should be displayed"
        
        data_adjustment = await page.text_content('[data-test-id="data-adjustment"]')
        assert data_adjustment in ['Triggered', 'Pending'], "Data adjustment status should be valid"
        
        # Check impact metrics if available
        raw_gap_element = page.locator('[data-test-id="raw-gap"]')
        if await raw_gap_element.count() > 0:
            raw_gap = await raw_gap_element.text_content()
            assert '%' in raw_gap, "Raw gap should be a percentage"
            
            adjusted_gap = await page.text_content('[data-test-id="adjusted-gap"]')
            assert '%' in adjusted_gap, "Adjusted gap should be a percentage"
        
        print(f"✅ Corporate action detail data displays correctly for {symbol}")
    
    async def test_impact_visualization_charts(self, page, setup_django):
        """Test that impact visualization charts load and function correctly."""
        
        # Navigate to a corporate action with impact data
        await page.goto('http://localhost:8000/corp-actions/')
        
        first_card = page.locator('[data-test-id^="corp-action-"]').first
        await first_card.wait_for(state='visible')
        
        view_btn = first_card.locator('[data-test-id^="view-detail-"]')
        await view_btn.click()
        
        # Wait for page to load
        await page.wait_for_selector('[data-test-id="corp-action-title"]')
        
        # Check for Chart.js canvas elements
        gap_chart = page.locator('#gapComparisonChart')
        impact_chart = page.locator('#impactMetricsChart')
        
        # Charts might not be visible if no impact data, so check conditionally
        if await gap_chart.count() > 0:
            await gap_chart.wait_for(state='visible')
            print("✅ Gap comparison chart is visible")
        
        if await impact_chart.count() > 0:
            await impact_chart.wait_for(state='visible')
            print("✅ Impact metrics chart is visible")
        
        # Test event timeline
        timeline = page.locator('[data-test-id="event-timeline"]')
        await timeline.wait_for(state='visible')
        
        print("✅ Impact visualization components load correctly")
    
    async def test_alerts_display(self, page, setup_django):
        """Test that alerts are displayed correctly on detail page."""
        
        # Navigate to corporate action detail
        await page.goto('http://localhost:8000/corp-actions/')
        
        first_card = page.locator('[data-test-id^="corp-action-"]').first
        await first_card.wait_for(state='visible')
        
        view_btn = first_card.locator('[data-test-id^="view-detail-"]')
        await view_btn.click()
        
        await page.wait_for_selector('[data-test-id="corp-action-title"]')
        
        # Check for alerts section
        alerts_section = page.locator('.card:has-text("Related Alerts")')
        await alerts_section.wait_for(state='visible')
        
        # Check if there are any alerts
        alert_elements = page.locator('[data-test-id^="alert-"]')
        alert_count = await alert_elements.count()
        
        if alert_count > 0:
            # Test first alert
            first_alert = alert_elements.first
            await first_alert.wait_for(state='visible')
            
            # Check alert has priority badge
            priority_badge = first_alert.locator('.badge')
            await priority_badge.wait_for(state='visible')
            
            priority_text = await priority_badge.text_content()
            assert priority_text.lower() in ['low', 'medium', 'high', 'critical']
            
            print(f"✅ Alerts display correctly - found {alert_count} alerts")
        else:
            # Should show "no alerts" message
            no_alerts_msg = alerts_section.locator('text=No alerts generated')
            await no_alerts_msg.wait_for(state='visible')
            print("✅ No alerts message displays correctly")
    
    async def test_data_adjustments_table(self, page, setup_django):
        """Test that data adjustments table displays correctly when available."""
        
        # Navigate to corporate action detail
        await page.goto('http://localhost:8000/corp-actions/')
        
        first_card = page.locator('[data-test-id^="corp-action-"]').first
        await first_card.wait_for(state='visible')
        
        view_btn = first_card.locator('[data-test-id^="view-detail-"]')
        await view_btn.click()
        
        await page.wait_for_selector('[data-test-id="corp-action-title"]')
        
        # Check if adjustments table exists
        adjustments_table = page.locator('[data-test-id="adjustments-table"]')
        
        if await adjustments_table.count() > 0:
            await adjustments_table.wait_for(state='visible')
            
            # Check table headers
            headers = adjustments_table.locator('thead th')
            expected_headers = [
                'Adjustment Type', 'Affected Periods', 'Consistency Check', 
                'Issues Found', 'Integrity Score', 'Timestamp'
            ]
            
            header_count = await headers.count()
            assert header_count == len(expected_headers)
            
            # Check adjustment rows
            adjustment_rows = adjustments_table.locator('[data-test-id^="adjustment-"]')
            adjustment_count = await adjustment_rows.count()
            
            if adjustment_count > 0:
                first_row = adjustment_rows.first
                await first_row.wait_for(state='visible')
                
                # Check consistency check badge
                consistency_badge = first_row.locator('.badge')
                await consistency_badge.wait_for(state='visible')
                
                badge_text = await consistency_badge.text_content()
                assert badge_text in ['Passed', 'Failed']
                
                print(f"✅ Data adjustments table displays correctly - {adjustment_count} adjustments")
            else:
                print("✅ Data adjustments table present but no adjustments")
        else:
            print("✅ No data adjustments table (no adjustments for this action)")
    
    async def test_refresh_data_functionality(self, page, setup_django):
        """Test the refresh data button functionality."""
        
        # Navigate to corporate action detail
        await page.goto('http://localhost:8000/corp-actions/')
        
        first_card = page.locator('[data-test-id^="corp-action-"]').first
        await first_card.wait_for(state='visible')
        
        view_btn = first_card.locator('[data-test-id^="view-detail-"]')
        await view_btn.click()
        
        await page.wait_for_selector('[data-test-id="refresh-data"]')
        
        # Test refresh button
        refresh_btn = page.locator('[data-test-id="refresh-data"]')
        await refresh_btn.wait_for(state='visible')
        
        # Click refresh button
        await refresh_btn.click()
        
        # Check for loading state
        await page.wait_for_selector('text=Refreshing...', timeout=2000)
        
        # Wait for success message or button to return to normal
        try:
            await page.wait_for_selector('.alert-success', timeout=5000)
            print("✅ Refresh data functionality shows success message")
        except:
            # Button should return to normal state
            await page.wait_for_selector('[data-test-id="refresh-data"]:not([disabled])', timeout=5000)
            print("✅ Refresh data functionality completes")
    
    async def test_navigation_buttons(self, page, setup_django):
        """Test navigation buttons work correctly."""
        
        # Navigate to corporate action detail
        await page.goto('http://localhost:8000/corp-actions/')
        
        first_card = page.locator('[data-test-id^="corp-action-"]').first
        await first_card.wait_for(state='visible')
        
        view_btn = first_card.locator('[data-test-id^="view-detail-"]')
        await view_btn.click()
        
        await page.wait_for_selector('[data-test-id="back-to-list"]')
        
        # Test back to list button
        back_btn = page.locator('[data-test-id="back-to-list"]')
        await back_btn.click()
        
        # Should be back on list page
        await page.wait_for_selector('[data-test-id="total-actions"]')
        
        # Navigate back to detail
        first_card = page.locator('[data-test-id^="corp-action-"]').first
        view_btn = first_card.locator('[data-test-id^="view-detail-"]')
        await view_btn.click()
        
        await page.wait_for_selector('[data-test-id="dashboard-link"]')
        
        # Test dashboard link
        dashboard_link = page.locator('[data-test-id="dashboard-link"]')
        await dashboard_link.click()
        
        # Should navigate to dashboard (might be different URL)
        await page.wait_for_load_state('networkidle')
        
        print("✅ Navigation buttons work correctly")
    
    async def test_pagination_functionality(self, page, setup_django):
        """Test pagination works if there are multiple pages."""
        
        await page.goto('http://localhost:8000/corp-actions/')
        
        # Check if pagination exists
        pagination = page.locator('.pagination')
        
        if await pagination.count() > 0:
            await pagination.wait_for(state='visible')
            
            # Check current page indicator
            current_page = page.locator('[data-test-id="current-page"]')
            await current_page.wait_for(state='visible')
            
            current_page_text = await current_page.text_content()
            assert 'Page' in current_page_text
            
            # Test next page if available
            next_page_btn = page.locator('[data-test-id="next-page"]')
            if await next_page_btn.count() > 0:
                await next_page_btn.click()
                
                # Wait for page to load
                await page.wait_for_load_state('networkidle')
                
                # Verify we're on a different page
                new_current_page = await page.text_content('[data-test-id="current-page"]')
                assert new_current_page != current_page_text
                
                print("✅ Pagination functionality works")
            else:
                print("✅ Pagination present but no next page available")
        else:
            print("✅ No pagination needed (single page)")
    
    async def test_auto_refresh_functionality(self, page, setup_django):
        """Test auto-refresh functionality doesn't break the page."""
        
        await page.goto('http://localhost:8000/corp-actions/')
        
        # Wait for initial load
        await page.wait_for_selector('[data-test-id="total-actions"]')
        
        # Check that auto-refresh JavaScript is loaded
        auto_refresh_exists = await page.evaluate("""
            typeof startAutoRefresh === 'function' && 
            typeof lastUserActivity !== 'undefined'
        """)
        
        assert auto_refresh_exists, "Auto-refresh functionality should be loaded"
        
        # Simulate waiting without interaction (shorter than 30 seconds for testing)
        await page.evaluate("""
            // Temporarily reduce refresh interval for testing
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = setInterval(() => {
                console.log('Auto-refresh check');
                // Don't actually refresh during test
            }, 1000);
        """)
        
        # Wait a bit to ensure no errors
        await page.wait_for_timeout(2000)
        
        # Clean up
        await page.evaluate("""
            clearInterval(autoRefreshInterval);
        """)
        
        print("✅ Auto-refresh functionality loads without errors")
    
    async def test_responsive_design(self, page, setup_django):
        """Test that the UI is responsive to different screen sizes."""
        
        await page.goto('http://localhost:8000/corp-actions/')
        
        # Test mobile viewport
        await page.set_viewport_size({'width': 375, 'height': 667})
        await page.wait_for_selector('[data-test-id="total-actions"]')
        
        # Check that statistics cards are still visible
        stats_cards = page.locator('.col-md-3')
        assert await stats_cards.count() == 4
        
        # Test tablet viewport
        await page.set_viewport_size({'width': 768, 'height': 1024})
        await page.wait_for_load_state('networkidle')
        
        # Check corporate action cards adapt
        corp_cards = page.locator('.col-md-6')
        if await corp_cards.count() > 0:
            await corp_cards.first.wait_for(state='visible')
        
        # Reset to desktop
        await page.set_viewport_size({'width': 1920, 'height': 1080})
        
        print("✅ Responsive design works across different screen sizes")
    
    async def test_error_handling(self, page, setup_django):
        """Test error handling for invalid URLs and missing data."""
        
        # Test invalid corporate action ID
        await page.goto('http://localhost:8000/corp-actions/99999/')
        
        # Should show 404 or handle gracefully
        # (Django will typically show a 404 page)
        await page.wait_for_load_state('networkidle')
        
        page_content = await page.content()
        
        # Should either be a 404 page or handle the error gracefully
        assert ('404' in page_content or 
                'Not Found' in page_content or 
                'does not exist' in page_content or
                'No corporate action' in page_content), "Should handle invalid ID gracefully"
        
        print("✅ Error handling works for invalid URLs")
    
    # Helper methods
    async def _create_test_corporate_actions(self):
        """Create test corporate actions data for UI testing."""
        
        try:
            from seeking_alpha_research.django_prototype_v0.strategy_viz.models import (
                CorporateAction, MonitoringAlert, DataAdjustment
            )
            
            # Create sample corporate actions
            test_actions = [
                {
                    'symbol': 'AAPL',
                    'action_type': 'split',
                    'effective_date': datetime.now().date() + timedelta(days=30),
                    'detection_time': datetime.now(),
                    'split_ratio': 4.0,
                    'monitoring_status': 'active',
                    'data_adjustment_triggered': True,
                    'impact_magnitude': 0.85,
                    'strategy_relevance': 'high',
                    'raw_gap_percentage': 45.2,
                    'adjusted_gap_percentage': 32.1,
                    'adjustment_quality': 0.95
                },
                {
                    'symbol': 'MSFT',
                    'action_type': 'dividend',
                    'effective_date': datetime.now().date() + timedelta(days=15),
                    'detection_time': datetime.now() - timedelta(hours=2),
                    'dividend_amount': Decimal('2.50'),
                    'ex_date': datetime.now().date() + timedelta(days=10),
                    'monitoring_status': 'active',
                    'data_adjustment_triggered': False,
                    'impact_magnitude': 0.15,
                    'strategy_relevance': 'low',
                    'raw_gap_percentage': 8.5,
                    'adjusted_gap_percentage': 6.2,
                    'adjustment_quality': 0.88
                },
                {
                    'symbol': 'TSLA',
                    'action_type': 'spinoff',
                    'effective_date': datetime.now().date() + timedelta(days=45),
                    'detection_time': datetime.now() - timedelta(days=1),
                    'spinoff_ratio': 0.5,
                    'monitoring_status': 'completed',
                    'data_adjustment_triggered': True,
                    'impact_magnitude': 0.67,
                    'strategy_relevance': 'medium',
                    'raw_gap_percentage': 28.3,
                    'adjusted_gap_percentage': 31.7,
                    'adjustment_quality': 0.72
                }
            ]
            
            for action_data in test_actions:
                action, created = CorporateAction.objects.get_or_create(
                    symbol=action_data['symbol'],
                    action_type=action_data['action_type'],
                    effective_date=action_data['effective_date'],
                    defaults=action_data
                )
                
                if created:
                    # Create sample alert
                    MonitoringAlert.objects.create(
                        corporate_action=action,
                        alert_type=f"{action_data['action_type']}_detected",
                        priority='high' if action_data['action_type'] == 'split' else 'medium',
                        alert_id=f"CORP_ACTION_{action.symbol}_{action.action_type.upper()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        channels_notified=['email', 'webhook', 'database'],
                        delivery_status={'email': 'delivered', 'webhook': 'delivered', 'database': 'delivered'},
                        message=f"{action_data['action_type'].title()} detected for {action.symbol}",
                        escalation_triggered=action_data['action_type'] == 'split'
                    )
                    
                    # Create sample data adjustment if triggered
                    if action_data['data_adjustment_triggered']:
                        DataAdjustment.objects.create(
                            corporate_action=action,
                            adjustment_type='price_volume_split' if action_data['action_type'] == 'split' else 'price_dividend_adjust',
                            affected_periods=504 if action_data['action_type'] == 'split' else 30,
                            adjustment_triggered=True,
                            rollback_capability=True,
                            consistency_check_passed=True,
                            checks_performed=['price_continuity', 'volume_consistency', 'ratio_validation'],
                            issues_found=0,
                            overall_integrity=0.96
                        )
            
            print(f"✅ Created {len(test_actions)} test corporate actions")
            
        except Exception as e:
            print(f"⚠️ Could not create test data: {e}")
            # Continue with testing using any existing data


if __name__ == "__main__":
    print("=" * 80)
    print("ADVANCED CORPORATE ACTIONS UI - COMPREHENSIVE PLAYWRIGHT TEST SUITE")
    print("Testing Django UI implementation with real user interactions...")
    print("=" * 80)
    
    # Run Django development server in background
    import subprocess
    import time
    import signal
    import threading
    
    def start_django_server():
        """Start Django development server."""
        os.chdir('/Users/<USER>/PycharmProjects/stk_v5/seeking_alpha_research/django_prototype_v0')
        subprocess.run([
            'python', 'manage.py', 'runserver', '8000'
        ])
    
    # Start server in background thread
    server_thread = threading.Thread(target=start_django_server, daemon=True)
    server_thread.start()
    
    # Wait for server to start
    time.sleep(5)
    
    try:
        # Run tests
        pytest.main([__file__, '-v', '-s', '--tb=short'])
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
    finally:
        print("\n🏁 UI testing completed")