#!/bin/bash
# Simple script to start the overnight task runner

echo "🌙 Starting Overnight Task Runner..."

# Install requirements if needed
if ! python -c "import fastapi" 2>/dev/null; then
    echo "📦 Installing requirements..."
    pip install -r overnight_requirements.txt
fi

# Make sure we're in the right directory
cd /Users/<USER>/PycharmProjects/stk_v5

# Start the server
echo "🚀 Starting server on http://localhost:8080"
echo "📱 Mobile-friendly - check on phone/tablet"
echo "🔄 Includes token limit auto-retry"
echo ""
echo "Press Ctrl+C to stop"

python overnight_ui.py