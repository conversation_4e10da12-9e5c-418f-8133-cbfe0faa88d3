#!/usr/bin/env python3
"""
Simplified Two-Phase Test Runner: Fix Tests + Build UIs + Playwright Testing
Uses cc_executor orchestration with good prompts - no overcomplication

========================================================================================
CONTEXT FOR LLM RECREATION:
========================================================================================

This script uses cc_executor's orchestration capabilities with simple, effective prompts.

🔄 TWO-PHASE WORKFLOW:
Phase 1: Test Fix → Five Whys debugging, multiple attempts
Phase 2: UI Development → Ultrathink analysis, Django UI, Playwright testing

Key insight: cc_executor handles the complexity, we just provide good prompts.

========================================================================================

# Minimal direct usage example:
# from cc_executor.client import cc_execute, cc_execute_list
# import asyncio
# async def main():
#     result = await cc_execute("Create a Python function to calculate prime numbers")
#     print(result)
# asyncio.run(main())
"""

import sys
import os
import json
import random
import time
from pathlib import Path
from typing import Dict, List
import asyncio
import argparse
import dotenv
dotenv.load_dotenv()


from cc_executor.client import cc_execute
from cc_executor.client.cc_execute import export_execution_history

class ComprehensiveTestRunner:
    """Simplified test runner using cc_executor orchestration"""
    
    def __init__(self):
        self.base_dir = Path("/Users/<USER>/PycharmProjects/stk_v5/seeking_alpha_research")
        self.test_dir = self.base_dir / "tests"
        self.django_dir = Path("/Users/<USER>/PycharmProjects/stk_v5/seeking_alpha_research/django_prototype_v0")
        self.results = {}
        self.history_dir = self.base_dir / "execution_history"
        self.history_dir.mkdir(exist_ok=True)
        
        # Documentation files for context
        self.claude_md = "/Users/<USER>/PycharmProjects/stk_v5/.claude/CLAUDE.md"
        self.five_whys_md = "/Users/<USER>/PycharmProjects/stk_v5/.claude/commands/five-whys.md"
        self.features_md = self.base_dir / "STANDALONE_FEATURES_DOCUMENTATION.md"
        self.project_claude_md = "/Users/<USER>/PycharmProjects/stk_v5/CLAUDE.md"
        self.specs_md = "/Users/<USER>/PycharmProjects/stk_v5/specs.md"
        
        # Get all real test files
        self.test_files = list(self.test_dir.glob("test_*_real.py"))
        random.shuffle(self.test_files)
        
        print(f"🔬 Found {len(self.test_files)} real test files to process")
        print(f"🎯 TWO-PHASE WORKFLOW: Test Fix + UI Development")
        print(f"📚 Using cc_executor orchestration with good prompts")
        print(f"🎨 Expected Output: {len(self.test_files) * 2} cc-executor tasks")
        print("=" * 70)
    
    def create_phase1_task(self, test_file: Path) -> str:
        """Create Phase 1 task prompt for cc_executor"""
        feature_name = test_file.stem.replace('test_', '').replace('_real', '')
        
        return f"""ORCHESTRATE Phase 1 - Fix Test with Five Whys Analysis:

**Test File**: {test_file}
**Feature**: {feature_name}
**Documentation Context**: 
- {self.claude_md}
- {self.five_whys_md} 
- {self.features_md}
- {self.project_claude_md}
- {self.specs_md}

**Phase 1 Workflow**:

Task 1: Run the test
- Execute: python -m pytest {test_file} -v --tb=short
- If PASSES: Move to Phase 2
- If FAILS: Continue to Task 2

Task 2: Five Whys Analysis (if test failed)
- Apply five-whys.md methodology
- Read all documentation for context
- Identify root cause (not symptoms)
- Follow \"Money is on the line\" standards

Task 3: Fix Implementation  
- Address root cause systematically
- Use real data only (NO FAKES, NO MOCKS)
- Follow existing code conventions
- Test fix works

Task 4: Retry Test
- Re-run: python -m pytest {test_file} -v --tb=short
- If still fails, repeat Tasks 2-4 (max 3 attempts)
- Don't give up easily - apply methodology persistently

**Success Criteria**: 
- ✅ Test passes 100%
- ✅ Root cause addressed
- ✅ Real data integration maintained
- ✅ Ready for Phase 2

Execute this workflow systematically. Only proceed when test is passing."""

    def create_phase2_task(self, test_file: Path) -> str:
        """Create Phase 2 task prompt for cc_executor"""
        feature_name = test_file.stem.replace('test_', '').replace('_real', '')
        
        return f"""ORCHESTRATE Phase 2 - UI Development for Passing Feature:

**Test File**: {test_file} (NOW PASSING from Phase 1)
**Feature**: {feature_name}
**Django Directory**: {self.django_dir}
**Documentation Context**: All 5 files from Phase 1

**Phase 2 Workflow**:

Task 1: Ultrathink Feature Utility
- Read specs.md to understand business value
- Analyze how {feature_name} fits Gap-Up ATM Trading Strategy
- Define user stories: \"As a trader, I want to...\"
- Plan Django UI components needed

Task 2: Build Django UI
- Examine existing django_prototype_v0 structure
- Create/modify models, views, templates, URLs for {feature_name}
- Integrate with existing navigation and styling
- Display real data from {feature_name} (NO FAKES, NO MOCKS)
- Add data-test-id attributes for testing

Task 3: Playwright Testing
- Create comprehensive test suite for the UI
- Test navigation, data display, user interactions
- Verify real {feature_name} data shows correctly
- Test error handling and edge cases
- Generate screenshots and reports

Task 4: Validation
- Start Django server and verify UI works
- Run Playwright tests and ensure they pass
- Fix any issues found
- Confirm production-ready UI

**Success Criteria**:
- ✅ Django UI integrated with django_prototype_v0
- ✅ Real {feature_name} data displayed correctly  
- ✅ Playwright tests passing
- ✅ Professional trader-ready interface

Build a complete, tested UI that traders can actually use for {feature_name}."""

    async def run_all_1(self):
        for i, test_file in enumerate(self.test_files, 1):
            feature_name = test_file.stem.replace('test_', '').replace('_real', '')
            print(f"\n[{i:2d}/{len(self.test_files)}] === {feature_name} ===")

            # Phase 1
            phase1_prompt = self.create_phase1_task(test_file)
            print(f"  🚀 Phase 1: Test Fix for {feature_name}")
            try:
                print(f"  🔄 Starting cc_execute for {feature_name}")
                phase1_result = await cc_execute(phase1_prompt, json_mode=True, stream=True, generate_report=True, progress_callback=lambda msg: print(f"[Progress] {msg}"))
                print(f"  📝 cc_execute completed, result type: {type(phase1_result)}")
                if phase1_result is None:
                    print(f"  ❌ Phase 1 failed for {feature_name}: cc_execute returned None")
                    print(f"  🔄 TOSH: Retrying in 1 hour...")
                    time.sleep(3600)
                    continue
                self.save_json(feature_name, 'phase1', phase1_prompt, phase1_result)
                print(f"  ✅ Phase 1 complete for {feature_name}")
            except Exception as e:
                print(f"  ❌ Phase 1 failed for {feature_name}: {e}")
                print(f"  🔄 TOSH: Retrying in 1 hour...")
                time.sleep(3600)
                continue

    async def run_all_2(self):       # Phase 2
        for i, test_file in enumerate(self.test_files, 1):
            feature_name = test_file.stem.replace('test_', '').replace('_real', '')
            phase2_prompt = self.create_phase2_task(test_file)
            print(f"  🚀 Phase 2: UI Build for {feature_name}")
            try:
                phase2_result = await cc_execute(phase2_prompt, json_mode=True, stream=True, generate_report=True, progress_callback=lambda msg: print(f"[Progress] {msg}"))
                if phase2_result is None:
                    print(f"  ❌ Phase 2 failed for {feature_name}: cc_execute returned None")
                    print(f"  🔄 TOSH: Retrying in 1 hour. claude limits hit most likely...")
                    time.sleep(3600)
                    continue
                self.save_json(feature_name, 'phase2', phase2_prompt, phase2_result)
                print(f"  ✅ Phase 2 complete for {feature_name}")
            except Exception as e:
                print(f"  ❌ Phase 2 failed for {feature_name}: {e}")
                print(f"  🔄 TOSH: Retrying in 1 hour. claude limits hit most likely...")
                time.sleep(3600)
                continue

    async def run_test(self):
        """Run a simple hello world test and save the prompt and result."""
        prompt = "Create a simple hello_world.py file that prints 'Hello from cc_execute!' and then run it to verify everything works."
        print("\n🧪 Running hello world test...")
        try:
            result = await cc_execute(prompt, generate_report=True, amend_prompt=True)
            if result is None:
                print("❌ Hello world test failed: cc_execute returned None")
                return
            self.save_json("hello_world", "test", prompt, result)
            print("✅ Hello world test completed and saved.")
        except Exception as e:
            print(f"❌ Hello world test failed: {e}")

    def save_json(self, feature_name, phase, prompt, result):
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        filename = self.history_dir / f"{feature_name}_{phase}_{timestamp}_{random.randint(1000,9999)}.json"
        data = {
            "feature": feature_name,
            "phase": phase,
            "prompt": prompt,
            "result": result
        }
        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            print(f"    💾 Saved: {filename}")
        except Exception as e:
            print(f"    ⚠️ Could not save result: {e}")

def main():
    parser = argparse.ArgumentParser(description="Two-Phase Test Runner with cc_executor")
    parser.add_argument("--test", action="store_true", help="Run a simple hello world test and save the result.")
    args = parser.parse_args()

    runner = ComprehensiveTestRunner()
    try:
        if args.test:
            asyncio.run(runner.run_test())
        else:
            asyncio.run(runner.run_all_1())
            asyncio.run(runner.run_all_2())
    except KeyboardInterrupt:
        print("\n⚠️ Execution interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n🚫 An unexpected error occurred: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()