# Testing Guide - STK_V5 Project

## Testing Philosophy
- **No mocks for external services** - Use real APIs with test data
- **Real database** - Separate test database, not in-memory
- **No silent failures** - Tests must catch all edge cases
- **Integration over isolation** - Test real workflows

## Test Structure

### Directory Layout
```
seeking_alpha_research/
├── tests/
│   ├── __init__.py
│   ├── conftest.py              # Pytest configuration and fixtures
│   ├── test_data_service.py     # Data fetching tests
│   ├── test_strategy.py         # Strategy logic tests
│   ├── test_database.py         # Database operations tests
│   ├── test_llm_agent.py        # LLM integration tests
│   ├── test_scrapers.py         # Web scraping tests
│   ├── test_ib_integration.py   # IB API tests
│   ├── test_universe.py         # Stock universe tests
│   ├── test_backtester.py       # Backtesting engine tests
│   └── integration/
│       ├── test_full_workflow.py
│       └── test_api_limits.py
└── test_data/
    ├── sample_bars.csv
    ├── sample_news.json
    ├── sample_filings/
    └── test_universe.csv
```

## Pytest Configuration

### conftest.py
```python
import pytest
import os
from sqlalchemy import create_engine
from database import metadata
from data_service import DataService

@pytest.fixture(scope='session')
def test_db():
    """Create a test database for the session"""
    db_path = 'test_stocks.db'
    if os.path.exists(db_path):
        os.remove(db_path)
    
    engine = create_engine(f'sqlite:///{db_path}')
    metadata.create_all(engine)
    yield engine
    
    # Cleanup
    engine.dispose()
    os.remove(db_path)

@pytest.fixture
def data_service(test_db):
    """Provide a DataService instance with test database"""
    service = DataService(db_conn=test_db)
    yield service
    service.close()

@pytest.fixture
def sample_symbol():
    """Provide a consistent test symbol"""
    return 'AAPL'  # Use a liquid stock for reliable data

@pytest.fixture
def test_date_range():
    """Provide a consistent date range for testing"""
    return {
        'start': '2023-01-01',
        'end': '2023-01-31'
    }
```

## Test Cases by Component

### 1. Data Service Tests

```python
# test_data_service.py
import pytest
from datetime import datetime
import pandas as pd

class TestDataService:
    def test_get_daily_bars_from_api(self, data_service, sample_symbol, test_date_range):
        """Test fetching daily bars from Alpaca API"""
        bars = data_service.get_daily_bars(
            sample_symbol, 
            test_date_range['start'], 
            test_date_range['end']
        )
        
        assert not bars.empty
        assert 'open' in bars.columns
        assert 'close' in bars.columns
        assert bars.index.name == 'timestamp'
        
    def test_get_daily_bars_from_cache(self, data_service, sample_symbol, test_date_range):
        """Test that second fetch uses cache"""
        # First fetch
        bars1 = data_service.get_daily_bars(
            sample_symbol, 
            test_date_range['start'], 
            test_date_range['end']
        )
        
        # Second fetch should be from cache
        bars2 = data_service.get_daily_bars(
            sample_symbol, 
            test_date_range['start'], 
            test_date_range['end']
        )
        
        pd.testing.assert_frame_equal(bars1, bars2)
        
    def test_get_minute_bars_not_implemented(self, data_service):
        """Test that minute bars raise NotImplementedError"""
        with pytest.raises(NotImplementedError):
            data_service.get_minute_bars('AAPL', '2023-01-01', '2023-01-01')
            
    def test_news_fetching_multiple_sources(self, data_service, sample_symbol):
        """Test news fetching from different sources"""
        alpaca_news = data_service.get_news(
            sample_symbol, '2023-01-01', '2023-01-31', source='alpaca'
        )
        
        assert 'headline' in alpaca_news.columns
        assert 'url' in alpaca_news.columns
        
    def test_sec_filings_fetch(self, data_service):
        """Test SEC filings retrieval"""
        filings = data_service.get_sec_filings('AAPL', '2023-01-01', '2023-03-31')
        
        assert 'form_type' in filings.columns
        assert 'filing_date' in filings.columns
```

### 2. Strategy Tests

```python
# test_strategy.py
import pytest
from strategy import find_gap_up_events, calculate_gap_percentage
import pandas as pd

class TestStrategy:
    def test_gap_calculation(self):
        """Test gap percentage calculation"""
        assert calculate_gap_percentage(100, 130) == 30.0
        assert calculate_gap_percentage(50, 60) == 20.0
        
    def test_find_gap_up_events(self, data_service):
        """Test gap detection with real data"""
        # Use a known volatile small-cap
        gaps = find_gap_up_events(
            'SNDL',  # Known volatile stock
            '2021-01-01',
            '2021-12-31',
            gap_percentage=20
        )
        
        # Should find at least some gaps in volatile stock
        assert isinstance(gaps, list)
        
    def test_gap_with_news_requirement(self, monkeypatch):
        """Test that gaps without news are filtered out"""
        # Create mock data with gap but no news
        mock_bars = pd.DataFrame({
            'open': [100, 140],  # 40% gap
            'close': [100, 130]
        }, index=pd.to_datetime(['2023-01-01', '2023-01-02']))
        
        # Mock to return no news
        def mock_get_news(*args, **kwargs):
            return pd.DataFrame()
            
        monkeypatch.setattr('data_service.DataService.get_news', mock_get_news)
        
        # Should return empty list due to no news
        gaps = find_gap_up_events('TEST', '2023-01-01', '2023-01-02')
        assert len(gaps) == 0
```

### 3. Database Tests

```python
# test_database.py
import pytest
from sqlalchemy import select
from database import stock_bars_daily, stock_news
import pandas as pd

class TestDatabase:
    def test_unique_constraint(self, test_db):
        """Test that duplicate bars are rejected"""
        # Insert a bar
        test_db.execute(
            stock_bars_daily.insert().values(
                symbol='TEST',
                timestamp='2023-01-01',
                open=100, high=110, low=95, close=105,
                volume=1000000
            )
        )
        
        # Try to insert duplicate
        with pytest.raises(Exception):  # Should raise integrity error
            test_db.execute(
                stock_bars_daily.insert().values(
                    symbol='TEST',
                    timestamp='2023-01-01',
                    open=100, high=110, low=95, close=105,
                    volume=1000000
                )
            )
            
    def test_news_storage(self, test_db):
        """Test news storage and retrieval"""
        # Insert news
        test_db.execute(
            stock_news.insert().values(
                symbol='TEST',
                headline='Test Company Announces Earnings',
                summary='Test summary',
                url='https://example.com',
                source='test_source',
                created_at='2023-01-01 09:00:00'
            )
        )
        
        # Retrieve
        result = test_db.execute(
            select(stock_news).where(stock_news.c.symbol == 'TEST')
        ).fetchall()
        
        assert len(result) == 1
        assert result[0].headline == 'Test Company Announces Earnings'
```

### 4. LLM Tests

```python
# test_llm_agent.py
import pytest
from llm_agent import analyze_filing_for_atm_risk

class TestLLMAgent:
    @pytest.mark.integration
    def test_atm_risk_analysis(self):
        """Test LLM analysis of SEC filing"""
        sample_filing = """
        The Company had cash and cash equivalents of $5.2 million as of December 31, 2023.
        Operating expenses for Q4 2023 were $2.1 million.
        The Company has an active shelf registration allowing for the sale of up to 
        $50 million in securities.
        """
        
        result = analyze_filing_for_atm_risk(sample_filing)
        
        assert 'cash_burn_rate' in result
        assert 'months_of_runway' in result
        assert 'atm_available' in result
        assert 'risk_assessment' in result
```

### 5. Integration Tests

```python
# integration/test_full_workflow.py
import pytest
from strategy import scan_universe_for_opportunities
from universe import generate_stock_universe

class TestFullWorkflow:
    @pytest.mark.integration
    @pytest.mark.slow
    def test_complete_scan_workflow(self):
        """Test the complete scanning workflow"""
        # Generate universe
        universe = generate_stock_universe(max_market_cap=100_000_000)
        
        # Scan for opportunities
        opportunities = scan_universe_for_opportunities(
            universe[:10],  # Test with small subset
            lookback_days=30,
            min_gap_pct=20
        )
        
        # Verify structure
        assert isinstance(opportunities, list)
        for opp in opportunities:
            assert 'symbol' in opp
            assert 'gap_date' in opp
            assert 'gap_percentage' in opp
            assert 'has_news' in opp
            assert 'atm_risk' in opp
```

## Testing Commands

### Run All Tests
```bash
cd seeking_alpha_research
pytest -v

# With coverage
pytest --cov=. --cov-report=html

# Run only unit tests (fast)
pytest -v -m "not integration"

# Run integration tests
pytest -v -m integration
```

### Continuous Testing
```bash
# Watch for changes and run tests
pip install pytest-watch
ptw -- -v
```

## Test Data Management

### Creating Test Fixtures
```python
# scripts/create_test_data.py
import json
from data_service import DataService

def create_test_fixtures():
    """Generate test data from real API calls"""
    service = DataService()
    
    # Get sample bars
    bars = service.get_daily_bars('AAPL', '2023-01-01', '2023-01-31')
    bars.to_csv('test_data/sample_bars.csv')
    
    # Get sample news
    news = service.get_news('AAPL', '2023-01-01', '2023-01-31')
    news.to_json('test_data/sample_news.json')
    
    print("Test fixtures created successfully")
```

## Performance Testing

### Load Testing
```python
# test_performance.py
import pytest
import time
from concurrent.futures import ThreadPoolExecutor

class TestPerformance:
    @pytest.mark.performance
    def test_concurrent_data_fetching(self, data_service):
        """Test concurrent API calls"""
        symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [
                executor.submit(
                    data_service.get_daily_bars, 
                    symbol, '2023-01-01', '2023-01-31'
                )
                for symbol in symbols
            ]
            
            results = [f.result() for f in futures]
            
        elapsed = time.time() - start_time
        
        assert all(not df.empty for df in results)
        assert elapsed < 10  # Should complete in under 10 seconds
```

## Debugging Failed Tests

### Common Issues and Solutions

1. **API Rate Limits**
   ```python
   # Add retry logic in conftest.py
   @pytest.fixture
   def rate_limited_service():
       service = DataService()
       service.set_rate_limit(calls_per_minute=10)
       return service
   ```

2. **Database Lock Errors**
   ```python
   # Use WAL mode for SQLite
   engine = create_engine(
       'sqlite:///test.db',
       connect_args={'check_same_thread': False},
       pool_pre_ping=True
   )
   ```

3. **Missing API Keys**
   ```python
   # Skip tests if API keys not available
   @pytest.mark.skipif(
       not os.getenv('ALPACA_API_KEY'),
       reason="Alpaca API key not set"
   )
   def test_alpaca_integration():
       pass
   ```

## CI/CD Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Run Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'
        
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
        
    - name: Run tests
      env:
        ALPACA_API_KEY: ${{ secrets.ALPACA_API_KEY }}
        ALPACA_SECRET_KEY: ${{ secrets.ALPACA_SECRET_KEY }}
      run: |
        pytest -v --cov=. --cov-report=xml
        
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

## Best Practices

1. **Test Independence**: Each test should set up its own data
2. **Deterministic Tests**: Use fixed dates/symbols for reproducibility  
3. **Clear Assertions**: Test one thing per test method
4. **Descriptive Names**: test_what_when_expected_result
5. **Fast Tests First**: Run unit tests before integration tests
6. **Real Data**: Use actual API responses, not mocked data
7. **Error Cases**: Test error handling explicitly