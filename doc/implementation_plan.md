# STK_V5 Implementation Plan

## Overview
This document outlines the comprehensive implementation plan for the STK_V5 project, breaking down all required functionality into ~50 actionable subtasks. The project aims to build a quantitative trading system focused on identifying small-cap stocks likely to gap up due to ATM offerings.

## Current Status Summary (Updated: July 11, 2025)
- **Core Infrastructure**: ~80% complete
- **Data Pipeline**: Daily bars, minute bars, news fetching, SEC filings, corporate actions, delisted stocks implemented
- **Testing**: ~70% - 81 tests passing (data_service, strategy, llm_agent, ib_connector, database, universe, delisted_stocks, premarket_scanner)
- **Strategy Implementation**: Gap detection, filing lookup, unusual volume detection, pre-market scanner
- **Visualization**: Django skeleton only
- **LLM Integration**: Gemini configured with LiteLLM, cash burn calculator, ATM detection
- **IB Integration**: Connection manager and data fetcher implemented with tests
- **Database**: All core tables created including corporate actions and delisted stocks
- **Error Handling**: Comprehensive error handling with retry logic for all API calls

## Completed Tasks
- ✅ Set up pytest framework with fixtures
- ✅ Write unit tests for data_service.py
- ✅ Implement minute bars fetching
- ✅ Create minute bars database table
- ✅ Implement find_latest_filing_before_date function
- ✅ Fix import errors in strategy.py
- ✅ Implement unusual volume detection
- ✅ Write unit tests for strategy.py
- ✅ Implement proper logging framework
- ✅ Configure Gemini with LiteLLM
- ✅ Implement cash burn calculator
- ✅ Create ATM detection logic
- ✅ Install IB Python client from lib folder
- ✅ Create IB connection manager
- ✅ Implement IB data fetcher (READ ONLY)
- ✅ Create IB to Alpaca data converter
- ✅ Write unit tests for ib_connector.py
- ✅ Write unit tests for database.py
- ✅ Write unit tests for universe.py
- ✅ Implement corporate actions fetching
- ✅ Create corporate actions database table
- ✅ Add delisted stocks integration
- ✅ Write unit tests for delisted_stocks.py
- ✅ Create pre-market scanner
- ✅ Write unit tests for premarket_scanner.py
- ✅ Add error handling for all API calls

## Implementation Tasks

### 1. Testing Infrastructure (Priority: CRITICAL)
1. **Set up pytest framework** - Configure pytest with proper directory structure
2. **Create test database setup** - Separate test SQLite database with fixtures
3. **Write unit tests for data_service.py** - Test all data fetching methods
4. **Write unit tests for database.py** - Test schema and CRUD operations
5. **Write unit tests for strategy.py** - Test gap detection logic
6. **Write unit tests for universe.py** - Test stock filtering logic
7. **Write integration tests for API calls** - Test real API responses with rate limit handling
8. **Create test data fixtures** - Mock data for consistent testing
9. **Add continuous testing script** - Watch files and run tests automatically

### 2. Data Pipeline Enhancement
10. **Implement minute bars fetching** - Add get_minute_bars() to data_service.py
11. **Create minute bars database table** - stock_bars_minute with proper schema
12. **Implement corporate actions fetching** - Use Alpaca CorporateActionsClient
13. **Create corporate actions table** - Track splits, dividends, delistings
14. **Add delisted stocks integration** - Process delisted_temp.csv and IssuersPendingSuspensionDelisting.csv
15. **Implement web scraping for StockLight** - Scrape historical delisted stocks
16. **Add Yahoo Finance news scraping** - Additional news source
17. **Implement news URL content scraping** - Extract full article text
18. **Create data validation layer** - Ensure data integrity across sources
19. **Add data synchronization logic** - Handle conflicts between data sources

### 3. Interactive Brokers Integration
20. **Install IB Python client** - Set up twsapi from lib folder
21. **Create IB connection manager** - Handle connection to port 4001
22. **Implement IB data fetcher** - Fetch historical data (READ ONLY)
23. **Create IB to Alpaca data converter** - Standardize to Alpaca schema
24. **Add IB error handling** - Handle disconnections and API limits
25. **Write IB integration tests** - Test with mock IB gateway

### 4. LLM Integration Enhancement
26. **Configure Gemini with LiteLLM** - Set up proper API keys and model selection
27. **Implement cash burn calculator** - Analyze financials from SEC filings
28. **Create ATM detection logic** - Identify active ATM programs
29. **Add filing summarization** - Extract key metrics from filings
30. **Implement find_latest_filing_before_date** - Missing critical function
31. **Create LLM response caching** - Store analysis results in database
32. **Add LLM error handling** - Handle API failures gracefully

### 5. Advanced Strategy Features
33. **Implement unusual volume detection** - Identify spikes in previous 5 days
34. **Create pre-market scanner** - Real-time gap detection
35. **Add insider buying detection** - Analyze Form 4 filings
36. **Implement dilution confirmation** - Check post-gap filings
37. **Create multi-timeframe analysis** - Convert 1-min to 5-min, 15-min candles
38. **Add market cap filtering** - Dynamic universe based on market cap
39. **Implement survivorship bias correction** - Properly handle delisted stocks

### 6. Portfolio Management
40. **Create position sizing logic** - Based on risk parameters
41. **Implement maximum holding period** - 5-day limit as specified
42. **Add portfolio risk calculator** - Track drawdowns and exposure
43. **Create trade execution simulator** - Realistic entry/exit with slippage
44. **Implement stop-loss logic** - Risk management rules
45. **Add portfolio performance metrics** - Sharpe ratio, max drawdown, etc.

### 7. Visualization & UI
46. **Integrate Plotly for candlestick charts** - Interactive chart library
47. **Create stock dashboard view** - Display OHLCV with gaps highlighted
48. **Add news timeline visualization** - Show news events on chart
49. **Implement strategy parameter UI** - Adjust gap %, holding period, etc.
50. **Create portfolio performance dashboard** - P&L charts and metrics
51. **Add real-time scanner dashboard** - Live gap detection display

### 8. Infrastructure & DevOps
52. **Implement proper logging framework** - Use Python logging module
53. **Add database migrations system** - Track schema changes
54. **Create data backup system** - Regular SQLite backups
55. **Implement parallel processing** - Thread-safe database operations
56. **Add monitoring and alerting** - Track API usage and errors
57. **Create deployment documentation** - Setup instructions
58. **Add performance profiling** - Identify bottlenecks

## Priority Order

### Phase 1: Foundation (Week 1-2)
- Testing infrastructure (Tasks 1-9)
- Missing data pipeline features (Tasks 10-13)
- Fix critical missing functions (Task 30)

### Phase 2: Data Completeness (Week 3-4)
- Delisted stocks handling (Tasks 14-16)
- Additional news sources (Tasks 17-18)
- IB integration (Tasks 20-25)

### Phase 3: Strategy Enhancement (Week 5-6)
- LLM integration (Tasks 26-32)
- Advanced strategy features (Tasks 33-39)

### Phase 4: Trading System (Week 7-8)
- Portfolio management (Tasks 40-45)
- Visualization (Tasks 46-51)

### Phase 5: Production Ready (Week 9-10)
- Infrastructure improvements (Tasks 52-57)
- Final testing and documentation

## Key Principles to Follow
1. **No silent failures** - All errors must be logged and raised
2. **Real data only** - No mocks except in unit tests
3. **Test everything** - Minimum 80% code coverage
4. **Data integrity** - Validate all external data
5. **Read-only IB** - Never place trades on real account

## Next Steps
1. Start with testing infrastructure (Task 1-9)
2. Implement minute bars functionality (Task 10-11)
3. Fix the missing find_latest_filing_before_date function (Task 30)

## Success Metrics
- All 57 tasks completed with tests
- Backtesting shows positive alpha
- System handles 1000+ stocks efficiently
- Zero critical bugs in production