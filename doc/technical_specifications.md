# Technical Specifications - STK_V5 Project

## 1. Architecture Overview

### System Components
```
┌─────────────────────────────────────────────────────────────┐
│                      Django Web Interface                     │
│                    (Visualization & Control)                  │
└─────────────────────────────────────────────────────────────┘
                               │
┌─────────────────────────────────────────────────────────────┐
│                      Strategy Engine                          │
│              (Gap Detection, ATM Risk Analysis)               │
└─────────────────────────────────────────────────────────────┘
                               │
┌─────────────────────────────────────────────────────────────┐
│                       Data Service Layer                      │
│                 (Caching, Standardization)                    │
└─────────────────────────────────────────────────────────────┘
                               │
        ┌──────────────┬───────────────┬──────────────┐
        │              │               │              │
┌───────▼──────┐ ┌────▼─────┐ ┌──────▼──────┐ ┌────▼─────┐
│ Alpaca API   │ │ IB API   │ │ Edgar/SEC   │ │ Scrapers │
└──────────────┘ └──────────┘ └─────────────┘ └──────────┘
```

## 2. Database Schema

### Tables Structure

#### stock_bars_daily
```sql
CREATE TABLE stock_bars_daily (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol VARCHAR(10) NOT NULL,
    timestamp DATETIME NOT NULL,
    open FLOAT NOT NULL,
    high FLOAT NOT NULL,
    low FLOAT NOT NULL,
    close FLOAT NOT NULL,
    volume INTEGER NOT NULL,
    trade_count INTEGER,
    vwap FLOAT,
    UNIQUE(symbol, timestamp)
);
```

#### stock_bars_minute (IMPLEMENTED)
```sql
CREATE TABLE stock_bars_minute (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol VARCHAR(10) NOT NULL,
    timestamp DATETIME NOT NULL,
    open FLOAT NOT NULL,
    high FLOAT NOT NULL,
    low FLOAT NOT NULL,
    close FLOAT NOT NULL,
    volume INTEGER NOT NULL,
    trade_count INTEGER,
    vwap FLOAT,
    UNIQUE(symbol, timestamp)
);
CREATE INDEX idx_minute_symbol_time ON stock_bars_minute(symbol, timestamp);
```

#### corporate_actions (TO BE IMPLEMENTED)
```sql
CREATE TABLE corporate_actions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol VARCHAR(10) NOT NULL,
    action_type VARCHAR(20) NOT NULL, -- 'split', 'dividend', 'delisting'
    ex_date DATE NOT NULL,
    record_date DATE,
    payment_date DATE,
    ratio FLOAT, -- for splits
    amount FLOAT, -- for dividends
    details TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, action_type, ex_date)
);
```

#### stock_universe
```sql
CREATE TABLE stock_universe (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(255),
    market_cap FLOAT,
    exchange VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    delisted_date DATE,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 3. API Integration Specifications

### Alpaca API
- **Base URL**: https://data.alpaca.markets/v2
- **Authentication**: API Key + Secret
- **Rate Limits**: 200 requests/minute
- **Data Types**:
  - Daily bars: `GET /stocks/{symbol}/bars`
  - Minute bars: `GET /stocks/{symbol}/bars?timeframe=1Min`
  - News: `GET /news`
  - Corporate Actions: Via CorporateActionsClient

### Interactive Brokers API (IMPLEMENTED)
- **Connection**: IB Gateway on port 4001 (REAL account - READ ONLY)
- **Mode**: READ-ONLY (Critical: Real account)
- **Data Limits**: ~4000 bars per request (was 1000, seems increased)
- **Data Types**:
  - Minute bars: 1 min resolution, up to 5 days
  - Daily bars: 1 day resolution, years of history
  - Pre-market/After-hours data included with use_rth=0
- **Implementation**:
```python
class IBConnector:
    def __init__(self, host='127.0.0.1', port=4001, client_id=1):
        # Connects to IB Gateway with proper error handling
        # Handles IB date formats: "******** 04:00:00 US/Eastern"
        
    def get_minute_bars(self, symbol: str, days: int = 1) -> pd.DataFrame:
        # Returns minute OHLCV data
        
    def get_daily_bars(self, symbol: str, days: int = 30) -> pd.DataFrame:
        # Returns daily OHLCV data
        
    def convert_to_alpaca_format(self, ib_df: pd.DataFrame) -> pd.DataFrame:
        # Standardizes IB data to match Alpaca format
```

### SEC/Edgar Integration
- **Library**: edgartools
- **Filings to Track**:
  - S-3 (Shelf registration)
  - 424B5 (Prospectus)
  - 8-K (Material events)
  - Form 4 (Insider trading)

## 4. Strategy Implementation Details

### Gap Detection Algorithm
```python
def calculate_gap_percentage(prev_close, current_open):
    """Calculate overnight gap percentage"""
    return ((current_open - prev_close) / prev_close) * 100

def find_gap_up_events(symbol, start_date, end_date, min_gap_pct=30):
    """
    Find days where stock gapped up by min_gap_pct
    Requirements:
    1. Gap up >= min_gap_pct
    2. Pre-market news exists
    3. Recent SEC filings indicate cash burn
    """
    pass
```

### Unusual Volume Detection
```python
def detect_unusual_volume(symbol, date, lookback_days=5, threshold_multiplier=3):
    """
    Detect if volume is unusual compared to recent average
    - Calculate average volume for lookback_days
    - Flag if current volume > avg_volume * threshold_multiplier
    """
    pass
```

### ATM Risk Analysis
```python
def analyze_atm_risk(filing_text):
    """
    Use LLM to analyze filing for ATM risk indicators:
    1. Cash burn rate
    2. Months of runway
    3. Active shelf registration amount
    4. Previous ATM usage
    """
    prompt = f"""
    Analyze this SEC filing and extract:
    1. Monthly cash burn rate
    2. Current cash position
    3. Active ATM program details (amount available)
    4. Risk of near-term dilution (High/Medium/Low)
    
    Filing text: {filing_text}
    """
    # Send to Gemini via LiteLLM
    pass
```

## 5. Testing Specifications

### Unit Test Structure
```
tests/
├── test_data_service.py
├── test_strategy.py
├── test_llm_agent.py
├── test_ib_integration.py
├── test_scrapers.py
├── test_database.py
└── fixtures/
    ├── sample_bars.json
    ├── sample_news.json
    └── sample_filings.json
```

### Test Requirements
- **Coverage**: Minimum 80%
- **Real API Tests**: Use @pytest.mark.integration
- **Database Tests**: Use separate test.db
- **No Mocks**: Except for external API responses in unit tests

## 6. Performance Requirements

### Data Processing
- Process 1000 stocks in < 5 minutes
- Cache hit rate > 90% for historical data
- Concurrent API requests: 10 threads max

### Database Performance
- Index all timestamp columns
- Batch inserts for bulk data
- Connection pooling for Django

## 7. Error Handling Strategy

### Principles
1. **No silent failures** - All exceptions logged and re-raised
2. **Graceful degradation** - Continue with available data
3. **Retry logic** - 3 retries with exponential backoff

### Implementation
```python
import logging
from retry import retry

logger = logging.getLogger(__name__)

@retry(tries=3, delay=1, backoff=2)
def fetch_with_retry(func, *args, **kwargs):
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"Error in {func.__name__}: {e}")
        raise
```

## 8. Deployment Configuration

### Environment Variables
```bash
# .env file
ALPACA_API_KEY=your_key
ALPACA_SECRET_KEY=your_secret
IB_GATEWAY_PORT=4001
IB_ACCOUNT_TYPE=REAL  # CRITICAL: Read-only
GEMINI_API_KEY=your_gemini_key
DATABASE_PATH=./seeking_alpha_research/data/stocks.db
LOG_LEVEL=INFO
```

### Django Settings
- **DEBUG**: False in production
- **ALLOWED_HOSTS**: ['localhost', '127.0.0.1']
- **Database**: SQLite with WAL mode for concurrency

## 9. Monitoring & Logging

### Log Format
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'stk_v5.log',
            'maxBytes': ********,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
}
```

### Metrics to Track
- API call counts and response times
- Cache hit/miss ratios
- Strategy signal generation rate
- Error rates by component

## 10. Future Enhancements

### Phase 2 Features
- WebSocket real-time data
- Multi-strategy support
- Machine learning for gap prediction
- Automated trading execution (paper account only)

### Scalability Considerations
- PostgreSQL migration path
- Redis for caching
- Celery for async task processing
- Docker containerization