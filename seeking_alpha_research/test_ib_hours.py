#!/usr/bin/env python3
"""
Test what hours IB is actually returning for minute data.
"""

import sys
import os
from datetime import datetime, timedelta

# Add project to path
sys.path.insert(0, os.path.dirname(__file__))

from core.ib_connector import IBConnector
from core.ib_connection_manager import get_ib_connection
from core.logger import get_logger

logger = get_logger(__name__)


def test_ib_hours():
    """Test what hours IB returns for minute data."""
    print("🕐 Testing IB Trading Hours Coverage...")
    print("=" * 50)
    
    # Get IB connection
    ib = get_ib_connection()
    if not ib or not ib.is_connected():
        print("❌ No IB connection available")
        return
    
    symbol = "AAPL"
    
    # Test 1: Direct IB call with different parameters
    print(f"\n📊 Testing Direct IB Calls for {symbol}:")
    
    try:
        # Test with empty end_date (current data)
        print("   Testing with end_date='' (current data)...")
        bars1 = ib.get_minute_bars(symbol, days=1, end_date="")
        
        if not bars1.empty:
            print(f"   Records: {len(bars1)}")
            print(f"   Time range: {bars1.index[0]} to {bars1.index[-1]}")
            print(f"   Hours: {bars1.index[0].hour}:00 to {bars1.index[-1].hour}:59")
            
            # Check hour distribution
            hours = bars1.index.hour.unique()
            print(f"   Hours present: {sorted(hours)}")
            
            if 4 in hours:
                print("   ✅ Pre-market (4 AM) included")
            else:
                print("   ❌ Pre-market (4 AM) missing")
                
            if any(h >= 16 for h in hours):
                max_hour = max(hours)
                print(f"   ✅ After-hours data until {max_hour}:00")
            else:
                print("   ❌ After-hours data missing")
        else:
            print("   ❌ No data returned")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Try with specific end_date
    print(f"\n   Testing with specific end_date (yesterday 8 PM)...")
    try:
        yesterday = datetime.now() - timedelta(days=1)
        end_date_8pm = yesterday.replace(hour=20, minute=0, second=0).strftime("%Y%m%d 20:00:00")
        
        bars2 = ib.get_minute_bars(symbol, days=1, end_date=end_date_8pm)
        
        if not bars2.empty:
            print(f"   Records: {len(bars2)}")
            print(f"   Time range: {bars2.index[0]} to {bars2.index[-1]}")
            print(f"   Hours: {bars2.index[0].hour}:00 to {bars2.index[-1].hour}:59")
            
            hours = bars2.index.hour.unique()
            print(f"   Hours present: {sorted(hours)}")
            
            if any(h >= 19 for h in hours):  # 7 PM or later
                print("   ✅ After-hours data included")
            else:
                print("   ❌ After-hours data still missing")
        else:
            print("   ❌ No data returned")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Check IB historical data parameters
    print(f"\n🔧 Testing IB Historical Data Parameters:")
    try:
        # Test with different useRTH settings
        print("   Testing useRTH=0 (all hours) vs useRTH=1 (regular hours)...")
        
        # This would require modifying the get_historical_data method temporarily
        # For now, just report what we found
        print("   Current setting: useRTH=0 (should include all hours)")
        
        # Check if the issue is with the data source
        print(f"\n📋 Analysis:")
        print("   - IB connector is set to useRTH=0 (all hours)")
        print("   - Data might be limited by:")
        print("     1. Market data subscription level")
        print("     2. IB's delayed data limitations")
        print("     3. Symbol-specific trading hours")
        print("     4. Weekend/holiday restrictions")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n✅ IB hours test complete!")


if __name__ == "__main__":
    test_ib_hours()
