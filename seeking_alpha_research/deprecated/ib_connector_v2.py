"""
Enhanced IB Connector using Official TWS API Samples
Based on /Users/<USER>/PycharmProjects/stk_v5/lib/twsapi_macunix.1038.01/IBJts/samples/Python/Testbed/

This integrates:
1. Historical news from IB
2. Market calendar operations  
3. Enhanced tick data collection
4. Fundamental data integration
"""

import sys
import os
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
import pandas as pd
import numpy as np

# Add TWS API path
tws_api_path = "/Users/<USER>/PycharmProjects/stk_v5/lib/twsapi_macunix.1038.01/IBJts/source/pythonclient"
if tws_api_path not in sys.path:
    sys.path.append(tws_api_path)

from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.order import Order
from ibapi.ticktype import TickType, TickTypeEnum
from ibapi.common import TickerId, <PERSON>Data, HistoricalTickBidAsk, HistoricalTickLast, HistoricalTick
from ibapi.tag_value import TagValue

from core.logger import get_logger

logger = get_logger(__name__)


class EnhancedIBWrapper(EWrapper):
    """Enhanced IB Wrapper based on official samples with news and calendar data."""
    
    def __init__(self):
        EWrapper.__init__(self)
        self.tick_data = {}
        self.historical_data = {}
        self.news_data = {}
        self.calendar_data = {}
        self.fundamental_data = {}
        self.contract_details = {}
        self.market_depth = {}
        self.error_messages = []
        self.data_ready_events = {}
        
    def error(self, reqId: TickerId, errorCode: int, errorString: str):
        """Handle errors from TWS."""
        error_msg = f"IB Error {errorCode} for reqId {reqId}: {errorString}"
        logger.error(error_msg)
        self.error_messages.append({
            'reqId': reqId,
            'errorCode': errorCode,
            'errorString': errorString,
            'timestamp': datetime.now()
        })
        
        # Signal completion for certain error codes
        if errorCode in [200, 201, 202, 354]:  # Data farm connection errors
            if reqId in self.data_ready_events:
                self.data_ready_events[reqId].set()
    
    def contractDetails(self, reqId: int, contractDetails):
        """Receive contract details."""
        if reqId not in self.contract_details:
            self.contract_details[reqId] = []
        self.contract_details[reqId].append(contractDetails)
        logger.info(f"Contract details received for reqId {reqId}: {contractDetails.contract.symbol}")
    
    def contractDetailsEnd(self, reqId: int):
        """Signal end of contract details."""
        if reqId in self.data_ready_events:
            self.data_ready_events[reqId].set()
    
    # Historical News Methods (from IB samples)
    def historicalNews(self, reqId: int, time: str, providerCode: str,
                      articleId: str, headline: str):
        """Receive historical news."""
        if reqId not in self.news_data:
            self.news_data[reqId] = []
        
        news_item = {
            'time': time,
            'provider': providerCode,
            'article_id': articleId,
            'headline': headline,
            'timestamp': datetime.now()
        }
        
        self.news_data[reqId].append(news_item)
        logger.info(f"Historical News for {reqId}: {headline[:50]}...")
    
    def historicalNewsEnd(self, reqId: int, hasMore: bool):
        """Signal end of historical news."""
        logger.info(f"Historical news end for reqId {reqId}, hasMore: {hasMore}")
        if reqId in self.data_ready_events:
            self.data_ready_events[reqId].set()
    
    def newsArticle(self, reqId: int, articleType: int, articleText: str):
        """Receive full news article text."""
        if reqId in self.news_data:
            # Find the latest news item and add article text
            for item in reversed(self.news_data[reqId]):
                if 'article_text' not in item:
                    item['article_text'] = articleText
                    break
    
    # Market Calendar Methods
    def historicalSchedule(self, reqId: int, startDateTime: str, endDateTime: str, 
                          timeZone: str, sessions):
        """Receive historical schedule data."""
        if reqId not in self.calendar_data:
            self.calendar_data[reqId] = []
        
        schedule_data = {
            'start': startDateTime,
            'end': endDateTime,
            'timezone': timeZone,
            'sessions': []
        }
        
        for session in sessions:
            schedule_data['sessions'].append({
                'start': session.startDateTime,
                'end': session.endDateTime,
                'ref_date': session.refDate
            })
        
        self.calendar_data[reqId].append(schedule_data)
        logger.info(f"Historical schedule for {reqId}: {startDateTime} to {endDateTime}")
        
        if reqId in self.data_ready_events:
            self.data_ready_events[reqId].set()
    
    # Enhanced Tick Data Methods
    def tickPrice(self, reqId: TickerId, tickType: TickType, price: float,
                  attrib):
        """Receive tick price data."""
        if reqId not in self.tick_data:
            self.tick_data[reqId] = []
        
        tick_data = {
            'type': 'price',
            'tick_type': TickTypeEnum.to_str(tickType),
            'price': price,
            'timestamp': datetime.now(),
            'can_auto_execute': attrib.canAutoExecute,
            'past_limit': attrib.pastLimit,
            'pre_open': attrib.preOpen
        }
        
        self.tick_data[reqId].append(tick_data)
    
    def tickSize(self, reqId: TickerId, tickType: TickType, size: int):
        """Receive tick size data."""
        if reqId not in self.tick_data:
            self.tick_data[reqId] = []
        
        tick_data = {
            'type': 'size',
            'tick_type': TickTypeEnum.to_str(tickType),
            'size': size,
            'timestamp': datetime.now()
        }
        
        self.tick_data[reqId].append(tick_data)
    
    def historicalTicksLast(self, reqId: int, ticks: List[HistoricalTickLast], done: bool):
        """Receive historical last ticks."""
        if reqId not in self.tick_data:
            self.tick_data[reqId] = []
        
        for tick in ticks:
            tick_data = {
                'type': 'last',
                'time': tick.time,
                'price': tick.price,
                'size': tick.size,
                'exchange': tick.exchange,
                'special_conditions': tick.specialConditions
            }
            self.tick_data[reqId].append(tick_data)
        
        if done and reqId in self.data_ready_events:
            self.data_ready_events[reqId].set()
    
    def historicalTicksBidAsk(self, reqId: int, ticks: List[HistoricalTickBidAsk], done: bool):
        """Receive historical bid/ask ticks."""
        if reqId not in self.tick_data:
            self.tick_data[reqId] = []
        
        for tick in ticks:
            tick_data = {
                'type': 'bidask',
                'time': tick.time,
                'bid_price': tick.priceBid,
                'ask_price': tick.priceAsk,
                'bid_size': tick.sizeBid,
                'ask_size': tick.sizeAsk,
                'bid_past_low': tick.bidPastLow,
                'ask_past_high': tick.askPastHigh
            }
            self.tick_data[reqId].append(tick_data)
        
        if done and reqId in self.data_ready_events:
            self.data_ready_events[reqId].set()
    
    # Market Depth Methods for Insider Detection
    def updateMktDepth(self, reqId: TickerId, position: int, operation: int,
                      side: int, price: float, size: int):
        """Receive market depth updates."""
        if reqId not in self.market_depth:
            self.market_depth[reqId] = []
        
        depth_data = {
            'position': position,
            'operation': operation,  # 0=insert, 1=update, 2=delete
            'side': side,  # 0=ask, 1=bid
            'price': price,
            'size': size,
            'timestamp': datetime.now()
        }
        
        self.market_depth[reqId].append(depth_data)
    
    # Fundamental Data Methods
    def fundamentalData(self, reqId: TickerId, data: str):
        """Receive fundamental data."""
        self.fundamental_data[reqId] = {
            'xml_data': data,
            'timestamp': datetime.now()
        }
        
        if reqId in self.data_ready_events:
            self.data_ready_events[reqId].set()


class EnhancedIBConnector(EClient):
    """Enhanced IB Connector with news, calendar, and sophisticated tick analysis."""
    
    def __init__(self, host='localhost', port=4001, client_id=1):
        self.wrapper = EnhancedIBWrapper()
        EClient.__init__(self, self.wrapper)
        
        self.host = host
        self.port = port
        self.client_id = client_id
        self.connected = False
        self.next_req_id = 1
        self.api_thread = None
        
    def connect_to_tws(self) -> bool:
        """Connect to TWS with enhanced error handling."""
        try:
            self.connect(self.host, self.port, self.client_id)
            
            # Start API thread
            self.api_thread = threading.Thread(target=self.run, daemon=True)
            self.api_thread.start()
            
            # Wait for connection
            timeout = 10
            start_time = time.time()
            while not self.isConnected() and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if self.isConnected():
                logger.info(f"Connected to IB TWS at {self.host}:{self.port}")
                self.connected = True
                return True
            else:
                logger.error("Failed to connect to IB TWS")
                return False
                
        except Exception as e:
            logger.error(f"IB connection error: {e}")
            return False
    
    def get_next_req_id(self) -> int:
        """Get next request ID."""
        req_id = self.next_req_id
        self.next_req_id += 1
        return req_id
    
    def create_stock_contract(self, symbol: str, exchange: str = "SMART") -> Contract:
        """Create stock contract."""
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "STK"
        contract.exchange = exchange
        contract.currency = "USD"
        return contract
    
    def get_historical_news(self, symbol: str, days_back: int = 30, 
                           providers: List[str] = None) -> pd.DataFrame:
        """Get historical news for symbol using IB news feed."""
        if not self.connected:
            logger.error("Not connected to IB")
            return pd.DataFrame()
        
        req_id = self.get_next_req_id()
        contract = self.create_stock_contract(symbol)
        
        # Set up event for data completion
        self.wrapper.data_ready_events[req_id] = threading.Event()
        
        # Default providers
        if providers is None:
            providers = ["BZ", "FLY", "MT"]  # Benzinga, TheFly, MarketWatch
        
        end_time = datetime.now().strftime("%Y%m%d %H:%M:%S")
        
        try:
            # Request historical news
            self.reqHistoricalNews(
                reqId=req_id,
                conId=contract.conId if hasattr(contract, 'conId') else 0,
                providerCodes="+".join(providers),
                startDateTime="",
                endDateTime=end_time,
                totalResults=100,
                historicalNewsOptions=[]
            )
            
            # Wait for data
            if self.wrapper.data_ready_events[req_id].wait(timeout=30):
                news_list = self.wrapper.news_data.get(req_id, [])
                
                if news_list:
                    df = pd.DataFrame(news_list)
                    df['symbol'] = symbol
                    df['timestamp'] = pd.to_datetime(df['time'])
                    logger.info(f"Retrieved {len(df)} news items for {symbol}")
                    return df
                else:
                    logger.warning(f"No news data received for {symbol}")
                    return pd.DataFrame()
            else:
                logger.error(f"Timeout waiting for news data for {symbol}")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Error getting historical news for {symbol}: {e}")
            return pd.DataFrame()
        finally:
            # Cleanup
            if req_id in self.wrapper.data_ready_events:
                del self.wrapper.data_ready_events[req_id]
    
    def get_market_calendar(self, symbol: str, days_ahead: int = 30) -> Dict:
        """Get market calendar/schedule for symbol."""
        if not self.connected:
            logger.error("Not connected to IB")
            return {}
        
        req_id = self.get_next_req_id()
        contract = self.create_stock_contract(symbol)
        
        # Set up event for data completion
        self.wrapper.data_ready_events[req_id] = threading.Event()
        
        start_date = datetime.now().strftime("%Y%m%d")
        end_date = (datetime.now() + timedelta(days=days_ahead)).strftime("%Y%m%d")
        
        try:
            self.reqHistoricalSchedule(
                reqId=req_id,
                contract=contract,
                numDays=days_ahead,
                endDateTime=end_date,
                useRTH=True
            )
            
            # Wait for data
            if self.wrapper.data_ready_events[req_id].wait(timeout=15):
                calendar_data = self.wrapper.calendar_data.get(req_id, [])
                logger.info(f"Retrieved calendar data for {symbol}")
                return calendar_data[0] if calendar_data else {}
            else:
                logger.error(f"Timeout waiting for calendar data for {symbol}")
                return {}
                
        except Exception as e:
            logger.error(f"Error getting calendar for {symbol}: {e}")
            return {}
        finally:
            # Cleanup
            if req_id in self.wrapper.data_ready_events:
                del self.wrapper.data_ready_events[req_id]
    
    def detect_insider_accumulation_advanced(self, symbol: str, 
                                           analysis_date: datetime = None) -> Dict[str, Any]:
        """
        Advanced insider accumulation detection using tick data and market depth.
        Based on IB samples for sophisticated analysis.
        """
        if not self.connected:
            logger.error("Not connected to IB")
            return {'insider_detected': False, 'reason': 'No IB connection'}
        
        if analysis_date is None:
            analysis_date = datetime.now()
        
        contract = self.create_stock_contract(symbol)
        
        # Get multiple data types
        tick_data = self._get_historical_ticks(contract, analysis_date)
        depth_data = self._get_market_depth_snapshot(contract)
        
        # Analysis metrics
        analysis = {
            'symbol': symbol,
            'analysis_date': analysis_date.isoformat(),
            'insider_detected': False,
            'confidence': 0.0,
            'patterns': [],
            'tick_imbalance': 0.0,
            'large_order_ratio': 0.0,
            'price_improvement': 0.0,
            'volume_profile': {},
            'timestamp': datetime.now().isoformat()
        }
        
        if not tick_data:
            analysis['reason'] = 'No tick data available'
            return analysis
        
        # Convert to DataFrame for analysis
        df = pd.DataFrame(tick_data)
        
        # 1. Tick Imbalance Analysis
        bid_volume = df[df['type'] == 'bidask']['bid_size'].sum()
        ask_volume = df[df['type'] == 'bidask']['ask_size'].sum()
        
        if bid_volume + ask_volume > 0:
            analysis['tick_imbalance'] = (bid_volume - ask_volume) / (bid_volume + ask_volume)
        
        # 2. Large Order Detection
        last_trades = df[df['type'] == 'last']
        if not last_trades.empty:
            avg_size = last_trades['size'].mean()
            large_trades = last_trades[last_trades['size'] > avg_size * 3]
            analysis['large_order_ratio'] = len(large_trades) / len(last_trades)
        
        # 3. Price Improvement Analysis
        bidask_data = df[df['type'] == 'bidask']
        if not bidask_data.empty:
            spread = (bidask_data['ask_price'] - bidask_data['bid_price']).mean()
            mid_price = (bidask_data['ask_price'] + bidask_data['bid_price']) / 2
            
            # Check for trades inside the spread (price improvement)
            inside_trades = 0
            for _, trade in last_trades.iterrows():
                # Find corresponding bid/ask at that time
                closest_quote = bidask_data.iloc[0]  # Simplified
                if (trade['price'] > closest_quote['bid_price'] and 
                    trade['price'] < closest_quote['ask_price']):
                    inside_trades += 1
            
            if len(last_trades) > 0:
                analysis['price_improvement'] = inside_trades / len(last_trades)
        
        # 4. Volume Profile Analysis
        if not last_trades.empty:
            # Hourly volume distribution
            last_trades['hour'] = pd.to_datetime(last_trades['time']).dt.hour
            hourly_volume = last_trades.groupby('hour')['size'].sum()
            analysis['volume_profile'] = hourly_volume.to_dict()
        
        # 5. Pattern Detection
        patterns = []
        
        # Large tick imbalance suggests accumulation
        if abs(analysis['tick_imbalance']) > 0.3:
            patterns.append(f"High tick imbalance: {analysis['tick_imbalance']:.2f}")
        
        # High large order ratio
        if analysis['large_order_ratio'] > 0.2:
            patterns.append(f"High large order ratio: {analysis['large_order_ratio']:.2f}")
        
        # Significant price improvement
        if analysis['price_improvement'] > 0.1:
            patterns.append(f"Price improvement: {analysis['price_improvement']:.2f}")
        
        analysis['patterns'] = patterns
        
        # 6. Overall Confidence Score
        confidence = 0.0
        confidence += min(abs(analysis['tick_imbalance']) * 2, 0.4)  # Max 0.4
        confidence += min(analysis['large_order_ratio'] * 2, 0.3)    # Max 0.3
        confidence += min(analysis['price_improvement'] * 3, 0.3)    # Max 0.3
        
        analysis['confidence'] = confidence
        analysis['insider_detected'] = confidence > 0.6
        
        logger.info(f"Insider analysis for {symbol}: confidence={confidence:.2f}, "
                   f"patterns={len(patterns)}")
        
        return analysis
    
    def _get_historical_ticks(self, contract: Contract, date: datetime, 
                             num_ticks: int = 1000) -> List[Dict]:
        """Get historical tick data for analysis."""
        req_id = self.get_next_req_id()
        self.wrapper.data_ready_events[req_id] = threading.Event()
        
        end_time = date.strftime("%Y%m%d %H:%M:%S")
        
        try:
            # Request last ticks
            self.reqHistoricalTicks(
                reqId=req_id,
                contract=contract,
                startDateTime="",
                endDateTime=end_time,
                numberOfTicks=num_ticks,
                whatToShow="TRADES",
                useRth=0,
                ignoreSize=False,
                miscOptions=[]
            )
            
            # Wait for data
            if self.wrapper.data_ready_events[req_id].wait(timeout=30):
                return self.wrapper.tick_data.get(req_id, [])
            else:
                logger.warning(f"Timeout getting historical ticks")
                return []
                
        except Exception as e:
            logger.error(f"Error getting historical ticks: {e}")
            return []
        finally:
            if req_id in self.wrapper.data_ready_events:
                del self.wrapper.data_ready_events[req_id]
    
    def _get_market_depth_snapshot(self, contract: Contract) -> List[Dict]:
        """Get current market depth snapshot."""
        req_id = self.get_next_req_id()
        
        try:
            # Request market depth
            self.reqMktDepth(reqId=req_id, contract=contract, numRows=10, isSmartDepth=True)
            
            # Let it collect for a few seconds
            time.sleep(3)
            
            # Cancel the subscription
            self.cancelMktDepth(reqId=req_id, isSmartDepth=True)
            
            return self.wrapper.market_depth.get(req_id, [])
            
        except Exception as e:
            logger.error(f"Error getting market depth: {e}")
            return []
    
    def disconnect_from_tws(self):
        """Disconnect from TWS."""
        if self.connected:
            self.disconnect()
            self.connected = False
            logger.info("Disconnected from IB TWS")


def test_enhanced_ib_connector():
    """Test the enhanced IB connector."""
    print("Testing Enhanced IB Connector with News and Calendar...")
    
    connector = EnhancedIBConnector()
    
    if connector.connect_to_tws():
        try:
            # Test historical news
            print("\n1. Testing Historical News:")
            news_df = connector.get_historical_news('AAPL', days_back=7)
            if not news_df.empty:
                print(f"   Retrieved {len(news_df)} news items for AAPL")
                print(f"   Latest headline: {news_df.iloc[0]['headline']}")
            else:
                print("   No news data retrieved")
            
            # Test market calendar
            print("\n2. Testing Market Calendar:")
            calendar_data = connector.get_market_calendar('AAPL')
            if calendar_data:
                print(f"   Calendar data retrieved: {calendar_data.get('start', 'N/A')}")
            else:
                print("   No calendar data retrieved")
            
            # Test insider detection
            print("\n3. Testing Advanced Insider Detection:")
            insider_analysis = connector.detect_insider_accumulation_advanced('AAPL')
            print(f"   Insider detected: {insider_analysis['insider_detected']}")
            print(f"   Confidence: {insider_analysis['confidence']:.2f}")
            print(f"   Patterns: {insider_analysis['patterns']}")
            
        except Exception as e:
            print(f"Test error: {e}")
        finally:
            connector.disconnect_from_tws()
    else:
        print("Failed to connect to IB TWS")


if __name__ == '__main__':
    test_enhanced_ib_connector()