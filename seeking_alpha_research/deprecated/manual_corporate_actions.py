"""
Manual corporate actions data for testing and critical split tracking.
Since Alpaca free tier doesn't provide corporate actions API,
we maintain critical split data manually.

CRITICAL: This is a temporary workaround - money is on the line!
"""

from datetime import datetime
from core.database import corporate_actions, engine
from sqlalchemy import insert
import pandas as pd

# Known major splits for testing
KNOWN_SPLITS = [
    {
        'symbol': 'TSLA',
        'action_type': 'forward_split',
        'ex_date': datetime(2022, 8, 25).date(),
        'ratio': 3.0,  # 3:1 split
        'description': 'TSLA 3:1 forward split'
    },
    {
        'symbol': 'NVDA',
        'action_type': 'forward_split', 
        'ex_date': datetime(2021, 7, 20).date(),
        'ratio': 4.0,  # 4:1 split
        'description': 'NVDA 4:1 forward split'
    },
    {
        'symbol': 'NVDA',
        'action_type': 'forward_split',
        'ex_date': datetime(2024, 6, 10).date(),
        'ratio': 10.0,  # 10:1 split
        'description': 'NVDA 10:1 forward split'
    },
    {
        'symbol': 'AAPL',
        'action_type': 'forward_split',
        'ex_date': datetime(2020, 8, 31).date(),
        'ratio': 4.0,  # 4:1 split
        'description': 'AAPL 4:1 forward split'
    },
    {
        'symbol': 'GOOGL',
        'action_type': 'forward_split',
        'ex_date': datetime(2022, 7, 18).date(),
        'ratio': 20.0,  # 20:1 split
        'description': 'GOOGL 20:1 forward split'
    },
    {
        'symbol': 'AMZN',
        'action_type': 'forward_split',
        'ex_date': datetime(2022, 6, 6).date(),
        'ratio': 20.0,  # 20:1 split
        'description': 'AMZN 20:1 forward split'
    }
]

def populate_manual_splits():
    """Populate database with known splits for testing."""
    conn = engine.connect()
    
    try:
        for split in KNOWN_SPLITS:
            try:
                insert_stmt = insert(corporate_actions).values(**split)
                conn.execute(insert_stmt)
                conn.commit()
                print(f"Inserted split: {split['description']}")
            except Exception as e:
                # Ignore duplicates
                if 'UNIQUE constraint failed' not in str(e):
                    print(f"Error inserting {split['symbol']}: {e}")
        
        # Verify insertions
        result = conn.execute(corporate_actions.select())
        splits_df = pd.DataFrame(result.fetchall())
        print(f"\nTotal corporate actions in database: {len(splits_df)}")
        if not splits_df.empty:
            print("\nStored splits:")
            print(splits_df[['symbol', 'action_type', 'ex_date', 'ratio', 'description']].to_string())
            
    finally:
        conn.close()

if __name__ == '__main__':
    print("Populating manual corporate actions data...")
    print("CRITICAL: This is a workaround for missing Alpaca corporate actions API")
    print("Money is on the line - these splits must be accurate!\n")
    
    populate_manual_splits()