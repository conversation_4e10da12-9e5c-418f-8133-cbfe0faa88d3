import os
import pandas as pd
from dotenv import load_dotenv
from sqlalchemy import create_engine, select, text, insert
import requests
from alpaca.data.historical import StockHistoricalDataClient
from alpaca.data.requests import StockBarsRequest, CorporateActionsRequest, NewsRequest
from alpaca.data.timeframe import TimeFrame
from alpaca.trading.client import TradingClient
# News is fetched through StockHistoricalDataClient with NewsRequest
from datetime import datetime, timedelta
import edgar

# Import logging
from logger import get_logger, LogContext, log_api_call, log_data_fetch, log_error_with_context
import functools
import time
from typing import Any, Callable
from alpaca.common.exceptions import APIError

# Import IBConnector
from ib_connector import IBConnector

# Set up logger for this module
logger = get_logger(__name__)


def api_error_handler(max_retries: int = 3, delay: float = 1.0):
    """
    Decorator for API calls with retry logic and comprehensive error handling.
    NO SILENT FAILURES - errors are logged and re-raised after retries.
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_error = None
            
            for attempt in range(max_retries):
                try:
                    # Log API call
                    func_name = func.__name__
                    symbol = kwargs.get('symbol') or (args[1] if len(args) > 1 else 'N/A')
                    logger.info(f"API call: {func_name} for {symbol} (attempt {attempt + 1}/{max_retries})")
                    
                    # Make the actual call
                    result = func(*args, **kwargs)
                    
                    # Log success
                    if hasattr(result, '__len__'):
                        logger.info(f"API call successful: {func_name} returned {len(result)} items")
                    else:
                        logger.info(f"API call successful: {func_name}")
                    
                    return result
                    
                except requests.exceptions.HTTPError as e:
                    last_error = e
                    if e.response.status_code == 429:  # Rate limit
                        wait_time = delay * (2 ** attempt)  # Exponential backoff
                        logger.warning(f"Rate limited on {func_name}. Waiting {wait_time}s before retry...")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"HTTP error in {func_name}: {e}")
                        raise  # Don't retry on non-rate-limit HTTP errors
                        
                except requests.exceptions.ConnectionError as e:
                    last_error = e
                    logger.error(f"Connection error in {func_name}: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(delay)
                
                except APIError as e:
                    last_error = e
                    error_msg = str(e)
                    if "invalid symbol" in error_msg.lower():
                        logger.warning(f"Invalid symbol error in {func_name}: {e}")
                        # Don't retry for invalid symbols
                        return pd.DataFrame() if 'pd' in globals() else None
                    else:
                        logger.error(f"Alpaca API error in {func_name}: {e}")
                        if attempt < max_retries - 1:
                            time.sleep(delay)
                    
                except Exception as e:
                    last_error = e
                    logger.error(f"Unexpected error in {func_name}: {type(e).__name__}: {e}")
                    # For unexpected errors, don't retry unless it's a timeout
                    if "timeout" not in str(e).lower():
                        raise
                    
            # All retries exhausted
            logger.error(f"All {max_retries} attempts failed for {func_name}")
            raise last_error
            
        return wrapper
    return decorator

# Import database tables
from database import stock_bars_daily, stock_bars_minute, stock_news, sec_filings, llm_filing_analysis, corporate_actions

# Import news sources
from news_sources import alpaca as alpaca_news
from news_sources import finviz as finviz_news

# Build the path to the .env file located in the same directory as the script
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(dotenv_path=dotenv_path)

# Standardize on the .env variable names
API_KEY = os.getenv("ALPACA_API_KEY")
API_SECRET = os.getenv("ALPACA_SECRET_KEY")
EDGAR_IDENTITY = os.getenv("EDGAR_IDENTITY")
DATABASE_URL = "sqlite:///seeking_alpha_research/seeking_alpha.db"

# Set the Edgar identity
if EDGAR_IDENTITY:
    edgar.set_identity(EDGAR_IDENTITY)
    logger.info(f"Edgar identity set: {EDGAR_IDENTITY}")
else:
    logger.warning("EDGAR_IDENTITY not found in .env file. SEC filing requests may be blocked.")


def get_db_connection():
    """Returns a SQLAlchemy engine connection."""
    engine = create_engine(DATABASE_URL)
    return engine.connect()


def get_alpaca_data_client():
    """Returns an Alpaca StockHistoricalDataClient."""
    return StockHistoricalDataClient(API_KEY, API_SECRET)

def get_alpaca_trading_client():
    """Returns an Alpaca TradingClient."""
    return TradingClient(API_KEY, API_SECRET, paper=True)

# News is fetched through the data client, not a separate news client


class DataService:
    def __init__(self):
        self.db_conn = get_db_connection()
        self.alpaca_data_client = get_alpaca_data_client()
        self.alpaca_trading_client = get_alpaca_trading_client()
        # News fetched through alpaca_data_client using NewsRequest
        self.ib_connector = None
        try:
            logger.info("Attempting to connect to Interactive Brokers Gateway...")
            self.ib_connector = IBConnector(
                host=os.getenv("IB_HOST", "127.0.0.1"),
                port=int(os.getenv("IB_PORT", 4001)),
                client_id=int(os.getenv("IB_CLIENT_ID", 1))
            )
            logger.info("Successfully connected to Interactive Brokers Gateway.")
        except ConnectionError as e:
            logger.warning(f"Could not connect to IB Gateway: {e}. IB features will be unavailable.")
        except Exception as e:
            logger.error(f"An unexpected error occurred during IB connection: {e}", exc_info=True)

    @api_error_handler(max_retries=3, delay=1.0)
    def get_daily_bars(self, symbol: str, start: str, end: str, source: str = 'alpaca'):
        """
        Retrieves daily stock bars for a given symbol and date range.
        First checks the local DB, then fetches missing data from Alpaca API.
        """
        with LogContext(logger, f"get_daily_bars for {symbol}"):
            start_date = datetime.strptime(start, "%Y-%m-%d")
            end_date = datetime.strptime(end, "%Y-%m-%d")

            query = select(stock_bars_daily).where(
                stock_bars_daily.c.symbol == symbol,
                stock_bars_daily.c.timestamp >= start_date,
                stock_bars_daily.c.timestamp <= end_date
            ).order_by(stock_bars_daily.c.timestamp)
            
            existing_data = pd.read_sql(query, self.db_conn, index_col='timestamp')
            
            if not existing_data.empty:
                logger.info(f"Found {len(existing_data)} bars for {symbol} in local DB.")
                return existing_data

            if source == 'ib' and self.ib_connector:
                return self._get_bars_from_ib(symbol, start, end, 'daily')
            else:
                return self._get_bars_from_alpaca(symbol, start, end, 'daily')

    def get_news(self, symbol: str, start: str, end: str) -> pd.DataFrame:
        """
        Fetches news for a symbol from all available sources, combines them,
        and caches the result in the database.
        """
        # 1. Fetch from Alpaca
        alpaca_news_df = self._fetch_from_source(
            symbol=symbol,
            start=start,
            end=end,
            source_name='alpaca',
            fetch_function=self.alpaca_data_client.get_news
        )

        # 2. Fetch from Finviz
        finviz_news_df = self._fetch_from_finviz(symbol)

        # 3. Combine and de-duplicate
        combined_news = pd.concat([alpaca_news_df, finviz_news_df], ignore_index=True)

        if combined_news.empty:
            return pd.DataFrame()

        combined_news['timestamp_hour'] = combined_news['timestamp'].dt.round('h')
        combined_news = combined_news.drop_duplicates(subset=['headline', 'timestamp_hour'], keep='first')
        del combined_news['timestamp_hour']

        start_date = pd.to_datetime(start).date()
        end_date = pd.to_datetime(end).date()
        mask = (combined_news['timestamp'].dt.date >= start_date) & \
               (combined_news['timestamp'].dt.date <= end_date)
        
        final_news = combined_news.loc[mask]
        
        print(f"Combined and filtered news for {symbol}. Found {len(final_news)} articles.")
        return final_news

    def _fetch_from_finviz(self, symbol: str) -> pd.DataFrame:
        """
        Internal method to fetch news from Finviz.
        """
        print(f"Fetching news for {symbol} from Finviz...")
        try:
            news_df = finviz_news.get_finviz_news(symbol)

            if news_df.empty:
                print(f"No news found for {symbol} from Finviz.")
                return pd.DataFrame()
            
            print(f"Found {len(news_df)} news articles for {symbol} from Finviz.")
            return news_df # This now correctly returns a df with a 'timestamp' column
            
        except Exception as e:
            print(f"Error fetching/processing news from Finviz for {symbol}: {e}")
            return pd.DataFrame()


    def _fetch_from_source(self, symbol: str, start: str, end: str, source_name: str, fetch_function) -> pd.DataFrame:
        """
        A generic helper to fetch news from a specific source, handling DB cache.
        Returns a DataFrame with a 'timestamp' column for consistency.
        """
        table_name = 'stock_news'
        print(f"Checking local news for {symbol} from '{source_name}'...")
        
        try:
            query = f"SELECT * FROM {table_name} WHERE symbol = ? AND source = ?"
            cached_df = pd.read_sql(query, self.db_conn, params=(symbol, source_name), parse_dates=['created_at'])

            if not cached_df.empty:
                print(f"Found {len(cached_df)} cached news articles for {symbol} from '{source_name}'.")
                return cached_df.rename(columns={'created_at': 'timestamp'})

            print(f"No local news for {symbol} from '{source_name}'. Fetching from source...")
            
            request_params = NewsRequest(
                symbols=symbol,
                start=start,
                end=end
            )
            news = fetch_function(request_params)

            if not news:
                print(f"No news found for {symbol} from {source_name}.")
                return pd.DataFrame()

            news_df = news.df
            # The fetch functions must return a df with 'created_at' for DB storage.
            news_df_to_save = news_df.copy()
            news_df_to_save['source'] = source_name
            news_df_to_save.to_sql(table_name, self.db_conn, if_exists='append', index=False)
            print(f"Saved {len(news_df_to_save)} new news articles for {symbol} from '{source_name}' to DB.")
            
            return news_df.rename(columns={'created_at': 'timestamp'})

        except Exception as e:
            print(f"An error occurred while fetching news for {symbol} from {source_name}: {e}")
            return pd.DataFrame()


    @api_error_handler(max_retries=2, delay=2.0)  # SEC API is slower, use longer delay
    def get_sec_filings(self, symbol: str, start: str, end: str):
        """
        Retrieves SEC filings for a given symbol using the edgartools library.
        Caches the filings in the database.
        """
        with LogContext(logger, f"get_sec_filings for {symbol}"):
            start_date = datetime.strptime(start, "%Y-%m-%d").date()
            end_date = datetime.strptime(end, "%Y-%m-%d").date()

            query = select(sec_filings).where(
                sec_filings.c.symbol == symbol,
                sec_filings.c.filed_at >= start_date,
                sec_filings.c.filed_at <= end_date
            )
            existing_filings = pd.read_sql(query, self.db_conn)
            if not existing_filings.empty:
                logger.info(f"Found {len(existing_filings)} SEC filings for {symbol} in local DB.")
                return existing_filings

            logger.info(f"No local SEC filings for {symbol}. Fetching from EDGAR...")
            company = edgar.Company(symbol)
            all_filings = company.get_filings().to_pandas()
            
            if all_filings.empty:
                logger.warning(f"No filings found for {symbol} on EDGAR.")
                return pd.DataFrame()
            
            # Convert filing_date to datetime
            all_filings['filing_date'] = pd.to_datetime(all_filings['filing_date']).dt.date
            filings_df = all_filings[
                (all_filings['filing_date'] >= start_date) & 
                (all_filings['filing_date'] <= end_date)
            ]

            if filings_df.empty:
                logger.warning(f"No filings found for {symbol} in the date range {start} to {end}.")
                return pd.DataFrame()

            filings_to_save = filings_df.rename(columns={
                'accession_number': 'accession_number',  # Keep as is
                'form': 'form_type',
                'filing_date': 'filed_at'  # Rename filing_date to filed_at for DB
            })
            filings_to_save['symbol'] = symbol
            filings_to_save['filing_url'] = None
            filings_to_save['report_url'] = None  # Add report_url column 
            
            columns_to_save = [c.name for c in sec_filings.columns if c.name in filings_to_save.columns]
            
            filings_to_save[columns_to_save].to_sql(
                sec_filings.name,
                self.db_conn,
                if_exists='append',
                index=False
            )
            
            logger.info(f"Saved {len(filings_to_save)} new SEC filings for {symbol} to DB.")
            
            # Return with renamed columns to match expected schema
            return filings_to_save[columns_to_save]

    @api_error_handler(max_retries=3, delay=1.0)
    def get_minute_bars(self, symbol: str, start: str, end: str, source: str = 'alpaca'):
        """
        Retrieves minute-level stock bars for a given symbol and date range.
        First checks the local DB, then fetches missing data from Alpaca API.
        Note: IB limits to 1000 data points per request.
        """
        with LogContext(logger, f"get_minute_bars for {symbol}"):
            start_date = datetime.strptime(start, "%Y-%m-%d")
            end_date = datetime.strptime(end, "%Y-%m-%d")

            query = select(stock_bars_minute).where(
                stock_bars_minute.c.symbol == symbol,
                stock_bars_minute.c.timestamp >= start_date,
                stock_bars_minute.c.timestamp <= end_date
            ).order_by(stock_bars_minute.c.timestamp)
            
            existing_bars = pd.read_sql(query, self.db_conn, index_col='timestamp', parse_dates=['timestamp'])
            
            if not existing_bars.empty:
                logger.info(f"Found {len(existing_bars)} minute bars for {symbol} in local DB.")
                return existing_bars

            if source == 'ib' and self.ib_connector:
                return self._get_bars_from_ib(symbol, start, end, 'minute')
            else:
                return self._get_bars_from_alpaca(symbol, start, end, 'minute')

    @api_error_handler(max_retries=3, delay=1.0)
    def get_corporate_actions(self, symbol: str, start: str, end: str) -> pd.DataFrame:
        """
        Retrieves corporate actions (splits, dividends, delistings) for a symbol.
        First checks the local DB, then fetches missing data from Alpaca API.
        """
        with LogContext(logger, f"corporate actions fetch for {symbol}"):
            start_date = datetime.strptime(start, "%Y-%m-%d").date()
            end_date = datetime.strptime(end, "%Y-%m-%d").date()
            
            # Check local database first
            query = select(corporate_actions).where(
                corporate_actions.c.symbol == symbol,
                corporate_actions.c.ex_date >= start_date,
                corporate_actions.c.ex_date <= end_date
            ).order_by(corporate_actions.c.ex_date)
            
            existing_actions = pd.read_sql(query, self.db_conn)
            
            if not existing_actions.empty:
                logger.info(f"Found {len(existing_actions)} corporate actions for {symbol} in local DB.")
                return existing_actions
            
            # Fetch from Alpaca corporate actions API using direct REST endpoint
            logger.info(f"No local corporate actions for {symbol}. Fetching from Alpaca API...")
            
            try:
                import requests
                
                # Use the correct REST endpoint for corporate actions
                base_url = "https://data.alpaca.markets/v1/corporate-actions"
                
                # Build query parameters
                params = {
                    'symbols': symbol,
                    'start': start_date.strftime('%Y-%m-%d'),
                    'end': end_date.strftime('%Y-%m-%d'),
                    'types': 'reverse_split,forward_split,cash_dividend,stock_dividend,spin_off',
                    'limit': 1000,
                    'sort': 'asc'
                }
                
                # Set authentication headers
                headers = {
                    'accept': 'application/json',
                    'APCA-API-KEY-ID': API_KEY,
                    'APCA-API-SECRET-KEY': API_SECRET
                }
                
                # Make the request
                response = requests.get(base_url, params=params, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    actions_list = []
                    
                    # Process the corporate actions - they're grouped by type
                    if 'corporate_actions' in data:
                        corp_actions = data['corporate_actions']
                        
                        # Process forward splits
                        if 'forward_splits' in corp_actions:
                            for split in corp_actions['forward_splits']:
                                ratio = split['new_rate'] / split['old_rate']
                                action_dict = {
                                    'symbol': split.get('symbol', symbol),
                                    'action_type': 'forward_split',
                                    'ex_date': datetime.strptime(split['ex_date'], '%Y-%m-%d').date(),
                                    'record_date': datetime.strptime(split['record_date'], '%Y-%m-%d').date() if split.get('record_date') else None,
                                    'payment_date': datetime.strptime(split['payable_date'], '%Y-%m-%d').date() if split.get('payable_date') else None,
                                    'ratio': ratio,
                                    'amount': None,
                                    'cash_amount': None,
                                    'description': f"Forward split {split['new_rate']}:{split['old_rate']}"
                                }
                                actions_list.append(action_dict)
                        
                        # Process reverse splits
                        if 'reverse_splits' in corp_actions:
                            for split in corp_actions['reverse_splits']:
                                ratio = split['new_rate'] / split['old_rate']
                                action_dict = {
                                    'symbol': split.get('symbol', symbol),
                                    'action_type': 'reverse_split',
                                    'ex_date': datetime.strptime(split['ex_date'], '%Y-%m-%d').date(),
                                    'record_date': datetime.strptime(split['record_date'], '%Y-%m-%d').date() if split.get('record_date') else None,
                                    'payment_date': datetime.strptime(split['payable_date'], '%Y-%m-%d').date() if split.get('payable_date') else None,
                                    'ratio': ratio,
                                    'amount': None,
                                    'cash_amount': None,
                                    'description': f"Reverse split {split['new_rate']}:{split['old_rate']}"
                                }
                                actions_list.append(action_dict)
                        
                        # Process cash dividends
                        if 'cash_dividends' in corp_actions:
                            for div in corp_actions['cash_dividends']:
                                action_dict = {
                                    'symbol': div.get('symbol', symbol),
                                    'action_type': 'cash_dividend',
                                    'ex_date': datetime.strptime(div['ex_date'], '%Y-%m-%d').date(),
                                    'record_date': datetime.strptime(div['record_date'], '%Y-%m-%d').date() if div.get('record_date') else None,
                                    'payment_date': datetime.strptime(div['payable_date'], '%Y-%m-%d').date() if div.get('payable_date') else None,
                                    'ratio': None,
                                    'amount': div.get('cash_amount'),
                                    'cash_amount': div.get('cash_amount'),
                                    'description': f"Cash dividend ${div.get('cash_amount', 0)}"
                                }
                                actions_list.append(action_dict)
                    
                    if actions_list:
                        actions_df = pd.DataFrame(actions_list)
                        
                        # Insert into database
                        for _, row in actions_df.iterrows():
                            try:
                                insert_stmt = insert(corporate_actions).values(**row.to_dict())
                                self.db_conn.execute(insert_stmt)
                                self.db_conn.commit()
                            except Exception as e:
                                if 'UNIQUE constraint failed' not in str(e):
                                    logger.warning(f"Could not insert corporate action: {e}")
                        
                        logger.info(f"Saved {len(actions_df)} corporate actions for {symbol}")
                        
                        # Query back to return consistent format
                        return pd.read_sql(query, self.db_conn)
                    else:
                        logger.info(f"No corporate actions found for {symbol}")
                        return pd.DataFrame()
                        
                elif response.status_code == 403:
                    logger.error("Authentication failed. Check API keys.")
                    raise ValueError("Corporate actions API authentication failed")
                elif response.status_code == 429:
                    logger.error("Rate limit hit for corporate actions API")
                    raise ValueError("Rate limit exceeded")
                else:
                    logger.error(f"Corporate actions API error: {response.status_code} - {response.text}")
                    raise ValueError(f"Corporate actions API failed with status {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error fetching corporate actions: {e}")
                raise  # No silent failures - money is on the line!
    
    def _get_bars_from_alpaca(self, symbol: str, start_date: str, end_date: str, timeframe_str: str) -> pd.DataFrame:
        """Helper to fetch bars from Alpaca API."""
        logger.info(f"No cache found for {symbol}, fetching {timeframe_str} bars from Alpaca API.")
        try:
            timeframe = TimeFrame.Day if timeframe_str == 'daily' else TimeFrame.Minute
            request_params = StockBarsRequest(
                symbol_or_symbols=[symbol],
                timeframe=timeframe,
                start=start_date,
                end=end_date,
                adjustment='split'
            )
            bars = self.alpaca_data_client.get_stock_bars(request_params)
            multi_index_df = bars.df

            if multi_index_df.empty:
                logger.warning(f"No data found for {symbol} from Alpaca.")
                return pd.DataFrame()

            bars_df = multi_index_df.loc[symbol]
            bars_df.index = bars_df.index.tz_localize(None)
            
            bars_to_save = bars_df.reset_index().rename(columns={'index': 'timestamp'})
            bars_to_save['symbol'] = symbol
            
            bars_to_save.to_sql(
                stock_bars_daily.name if timeframe_str == 'daily' else stock_bars_minute.name,
                self.db_conn,
                if_exists='append',
                index=False
            )
            logger.info(f"Saved {len(bars_to_save)} new bars for {symbol} to DB.")
            
            return bars_df
        except Exception as e:
            logger.error(f"Error fetching {timeframe_str} bars for {symbol} from Alpaca: {e}")
            return pd.DataFrame()

    def _get_bars_from_ib(self, symbol: str, start_date: str, end_date: str, timeframe_str: str) -> pd.DataFrame:
        """Helper to fetch bars from Interactive Brokers API."""
        logger.info(f"No cache found for {symbol}, fetching {timeframe_str} bars from IB API.")
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            days = (end - start).days + 1 # IB uses duration in days

            if timeframe_str == 'daily':
                bars = self.ib_connector.get_daily_bars(symbol, days=days)
            else: # minute
                bars = self.ib_connector.get_minute_bars(symbol, days=days)

            if not bars.empty:
                # Convert to Alpaca format before saving
                alpaca_formatted_bars = self.ib_connector.convert_to_alpaca_format(bars)
                self._save_bars_to_db(alpaca_formatted_bars, timeframe_str)
                logger.info(f"Fetched and cached {len(alpaca_formatted_bars)} new {timeframe_str} bars for {symbol} from IB")
                return alpaca_formatted_bars
            else:
                logger.warning(f"No {timeframe_str} bars returned from IB for {symbol}")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error fetching {timeframe_str} bars for {symbol} from IB: {e}", exc_info=True)
            return pd.DataFrame()

    def _save_bars_to_db(self, bars_df: pd.DataFrame, timeframe: str):
        """Helper to save bars to the database."""
        table = stock_bars_daily if timeframe == 'daily' else stock_bars_minute
        
        # Prepare data for insertion
        records = bars_df.reset_index().to_dict('records')
        
        # Use a transaction for bulk insert
        with self.db_conn.begin():
            for record in records:
                try:
                    self.db_conn.execute(insert(table).values(record))
                except IntegrityError:
                    # Ignore duplicates (already cached)
                    pass

    def invalidate_cache_for_split(self, symbol: str, split_date: datetime.date, split_ratio: float):
        """
        Invalidates cached price data for a symbol after a split occurs.
        This is critical to prevent mixing pre-split and post-split data.
        
        Args:
            symbol: Stock symbol
            split_date: Date of the split
            split_ratio: Split ratio (e.g., 2.0 for 2:1 split)
        """
        logger.warning(f"CRITICAL: Invalidating cache for {symbol} due to {split_ratio}:1 split on {split_date}")
        
        # Delete all cached data for this symbol on or after the split date
        # This forces re-fetching of properly adjusted data
        try:
            # Delete daily bars
            delete_daily = text("""
                DELETE FROM stock_bars_daily 
                WHERE symbol = :symbol AND timestamp >= :split_date
            """)
            result_daily = self.db_conn.execute(delete_daily, {'symbol': symbol, 'split_date': split_date})
            self.db_conn.commit()
            logger.info(f"Deleted {result_daily.rowcount} daily bars for {symbol} after split date")
            
            # Delete minute bars
            delete_minute = text("""
                DELETE FROM stock_bars_minute 
                WHERE symbol = :symbol AND timestamp >= :split_date
            """)
            result_minute = self.db_conn.execute(delete_minute, {'symbol': symbol, 'split_date': split_date})
            self.db_conn.commit()
            logger.info(f"Deleted {result_minute.rowcount} minute bars for {symbol} after split date")
        except Exception as e:
            logger.error(f"Error during cache invalidation: {e}")
            self.db_conn.rollback()
            raise
    
    def check_and_update_corporate_actions(self, symbol: str):
        """
        Checks for new corporate actions and invalidates cache if necessary.
        Should be called before any price data operations.
        """
        # Get the latest cached data date
        query = select(stock_bars_daily.c.timestamp).where(
            stock_bars_daily.c.symbol == symbol
        ).order_by(stock_bars_daily.c.timestamp.desc()).limit(1)
        
        result = self.db_conn.execute(query).fetchone()
        if not result:
            return  # No cached data
        
        latest_cached_date = result[0].date()
        
        # Check for corporate actions since last cached date
        actions = self.get_corporate_actions(
            symbol, 
            latest_cached_date.strftime('%Y-%m-%d'),
            datetime.now().strftime('%Y-%m-%d')
        )
        
        if not actions.empty:
            splits = actions[actions['action_type'] == 'split']
            for _, split in splits.iterrows():
                if split['ratio'] and split['ratio'] != 1.0:
                    self.invalidate_cache_for_split(
                        symbol,
                        split['ex_date'],
                        split['ratio']
                    )
                    
    def get_daily_bars_with_split_check(self, symbol: str, start: str, end: str, source: str = 'alpaca') -> pd.DataFrame:
        """
        Enhanced version of get_daily_bars that checks for splits before returning data.
        This ensures data consistency across split dates.
        """
        # First check for any new corporate actions
        self.check_and_update_corporate_actions(symbol)
        
        # Then proceed with normal data fetching
        return self.get_daily_bars(symbol, start, end, source)

    def get_tick_data(self, symbol: str, start_time: str = None, duration_seconds: int = 30, source: str = 'ib') -> pd.DataFrame:
        """
        Get tick data for sophisticated price action analysis.
        
        Args:
            symbol: Stock symbol
            start_time: Historical start time (format: "YYYYMMDD HH:MM:SS") or None for real-time
            duration_seconds: Duration for real-time collection
            source: Data source ('ib' only for now)
            
        Returns:
            DataFrame with tick data
        """
        if source != 'ib':
            raise ValueError("Tick data only available from Interactive Brokers")
        
        if not self.ib_connector:
            raise ConnectionError("IB Gateway not connected. Tick data requires IB connection.")
        
        with LogContext(logger, f"get_tick_data for {symbol}"):
            if start_time:
                # Historical ticks
                logger.info(f"Fetching historical ticks for {symbol} from {start_time}")
                return self.ib_connector.get_historical_ticks(symbol, start_time)
            else:
                # Real-time ticks
                logger.info(f"Collecting real-time ticks for {symbol} for {duration_seconds}s")
                return self.ib_connector.get_tick_data(symbol, duration_seconds)
    
    def analyze_tick_patterns(self, symbol: str, tick_df: pd.DataFrame = None, lookback_hours: int = 24) -> dict:
        """
        Analyze tick patterns for insider trading signals.
        
        Args:
            symbol: Stock symbol
            tick_df: Tick data DataFrame (if None, fetches from database)
            lookback_hours: Hours to look back for analysis
            
        Returns:
            Dictionary with insider trading signals
        """
        from database import stock_ticks
        
        if tick_df is None:
            # Fetch from database
            start_time = datetime.now() - timedelta(hours=lookback_hours)
            query = select(stock_ticks).where(
                stock_ticks.c.symbol == symbol,
                stock_ticks.c.timestamp >= start_time
            ).order_by(stock_ticks.c.timestamp)
            
            tick_df = pd.read_sql(query, self.db_conn)
        
        if tick_df.empty:
            logger.warning(f"No tick data available for {symbol}")
            return {}
        
        # Use IB connector's analysis
        if self.ib_connector:
            analysis = self.ib_connector.analyze_tick_patterns(tick_df)
        else:
            # Basic analysis without IB
            analysis = {
                'tick_count': len(tick_df),
                'period_hours': lookback_hours
            }
        
        # Add insider trading indicators
        analysis['insider_indicators'] = []
        
        # Check for unusual patterns
        if analysis.get('large_trades', 0) > 5:
            analysis['insider_indicators'].append('Multiple large block trades detected')
        
        if analysis.get('rapid_trade_ratio', 0) > 0.2:
            analysis['insider_indicators'].append('High frequency trading detected')
        
        if analysis.get('price_volatility', 0) < 0.1 and analysis.get('total_volume', 0) > 10000:
            analysis['insider_indicators'].append('High volume with low volatility (accumulation)')
        
        # Calculate overall insider probability
        analysis['insider_probability'] = len(analysis['insider_indicators']) / 5.0
        
        return analysis

    def close(self):
        """Closes all connections."""
        if self.db_conn:
            self.db_conn.close()
            logger.info("Database connection closed.")
        if self.ib_connector:
            self.ib_connector.disconnect()
            logger.info("IBConnector disconnected.")

if __name__ == '__main__':
    # Example Usage
    service = DataService()
    try:
        print("DataService initialized.")
        # Test IB if connected
        if service.ib_connector:
            print("\n--- Testing IB Integration ---")
            ib_bars = service.get_daily_bars("TSLA", "2023-01-01", "2023-01-05", source='ib')
            if not ib_bars.empty:
                print("Successfully fetched daily bars for TSLA from IB:")
                print(ib_bars.head())
        else:
            print("\n--- IB Gateway not connected, skipping IB tests ---")

        aapl_bars = service.get_daily_bars("AAPL", "2023-01-01", "2023-01-07")
        if not aapl_bars.empty:
            print("\nAAPL Daily Bars (2023-01-01 to 2023-01-07):")
            print(aapl_bars)
        
        print("\n--- Running again to test cache ---")
        aapl_bars_cached = service.get_daily_bars("AAPL", "2023-01-01", "2023-01-07")
        if not aapl_bars_cached.empty:
            print(aapl_bars_cached)
        
        print("\n--- Fetching news for AAPL (Alpaca & Finviz) ---")
        aapl_news = service.get_news("AAPL", "2023-01-01", "2023-01-07")
        if not aapl_news.empty:
            print(aapl_news.head())

        print("\n--- Fetching SEC Filings for AAPL ---")
        aapl_filings = service.get_sec_filings("AAPL", "2023-01-01", "2023-01-07")
        if not aapl_filings.empty:
            print(aapl_filings.head())

        print("\n--- Fetching Corporate Actions for AAPL ---")
        aapl_actions = service.get_corporate_actions("AAPL", "2022-01-01", "2023-12-31")
        if not aapl_actions.empty:
            print(aapl_actions.head())
        else:
            print("No corporate actions found for AAPL in the specified date range.")

    finally:
        service.close()
        print("\nDataService connection closed.")
 