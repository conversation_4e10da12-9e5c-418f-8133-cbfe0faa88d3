"""
# TODO: DUPLICATE - This is a duplicate of backtester.py
# TODO: REMOVE OR CONSOLIDATE - Has overlapping functionality
# TODO: FIX IMPORTS - Uses non-existent functions

Comprehensive backtesting engine for the gap-up ATM strategy.
This is CRITICAL for proving the strategy isn't random.
NO SILENT FAILURES - money is on the line.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
import json
from collections import defaultdict

from core.data_service import DataService
from scanners.premarket_scanner import PremarketScanner
from analysis.llm_agent import analyze_filing_for_atm_risk, calculate_cash_burn
from universe.delisted_stocks import DelistedStocksManager
from strategy.strategy import find_latest_filing_before_date, calculate_gap_percentage
from core.logger import get_logger, LogContext
from core.database import engine, metadata
from sqlalchemy import create_engine
import sqlalchemy

logger = get_logger(__name__)


@dataclass
class Position:
    """Represents a single position in the portfolio."""
    symbol: str
    entry_date: datetime
    entry_price: float
    shares: int
    gap_percentage: float
    has_news: bool
    unusual_volume_days: int
    filing_risk_score: float = 0.0
    cash_burn_months: float = 0.0
    exit_date: Optional[datetime] = None
    exit_price: Optional[float] = None
    exit_reason: Optional[str] = None
    pnl: float = 0.0
    pnl_percentage: float = 0.0
    max_gain_pct: float = 0.0
    max_loss_pct: float = 0.0
    held_days: int = 0


@dataclass
class BacktestResults:
    """Contains comprehensive backtest results and metrics."""
    positions: List[Position] = field(default_factory=list)
    daily_portfolio_value: pd.Series = field(default_factory=pd.Series)
    total_return: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    avg_holding_period: float = 0.0
    
    # Strategy-specific metrics
    gaps_with_news_win_rate: float = 0.0
    gaps_without_news_win_rate: float = 0.0
    high_filing_risk_win_rate: float = 0.0
    unusual_volume_correlation: float = 0.0
    
    # Risk metrics
    var_95: float = 0.0  # Value at Risk
    expected_shortfall: float = 0.0
    calmar_ratio: float = 0.0
    
    # Monthly breakdown
    monthly_returns: pd.Series = field(default_factory=pd.Series)
    
    def to_dict(self) -> Dict:
        """Convert results to dictionary for JSON serialization."""
        return {
            'total_return': self.total_return,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'win_rate': self.win_rate,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'profit_factor': self.profit_factor,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'avg_holding_period': self.avg_holding_period,
            'gaps_with_news_win_rate': self.gaps_with_news_win_rate,
            'gaps_without_news_win_rate': self.gaps_without_news_win_rate,
            'high_filing_risk_win_rate': self.high_filing_risk_win_rate,
            'unusual_volume_correlation': self.unusual_volume_correlation,
            'var_95': self.var_95,
            'expected_shortfall': self.expected_shortfall,
            'calmar_ratio': self.calmar_ratio
        }


class ComprehensiveBacktester:
    """
    Comprehensive backtesting engine for gap-up ATM strategy.
    
    Key features:
    - Realistic simulation with slippage and fees
    - Survivorship bias correction
    - Multiple exit strategies
    - Risk management
    - Detailed performance metrics
    """
    
    def __init__(self, 
                 initial_capital: float = 100000,
                 position_size_pct: float = 0.02,  # 2% per position
                 max_positions: int = 10,
                 max_holding_days: int = 5,
                 stop_loss_pct: float = -10.0,
                 take_profit_pct: float = 50.0,
                 commission_per_share: float = 0.005,
                 slippage_pct: float = 0.5):
        
        self.initial_capital = initial_capital
        self.position_size_pct = position_size_pct
        self.max_positions = max_positions
        self.max_holding_days = max_holding_days
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        self.commission_per_share = commission_per_share
        self.slippage_pct = slippage_pct
        
        # Initialize services
        self.data_service = DataService()
        self.scanner = PremarketScanner(self.data_service)
        self.delisted_manager = DelistedStocksManager()
        
        # Initialize database for storing results
        self._init_results_table()
        
    def _init_results_table(self):
        """Initialize database table for backtest results."""
        self.backtest_results_table = sqlalchemy.Table(
            "backtest_results",
            metadata,
            sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
            sqlalchemy.Column("run_date", sqlalchemy.DateTime, default=sqlalchemy.func.now()),
            sqlalchemy.Column("start_date", sqlalchemy.Date),
            sqlalchemy.Column("end_date", sqlalchemy.Date),
            sqlalchemy.Column("initial_capital", sqlalchemy.Float),
            sqlalchemy.Column("final_value", sqlalchemy.Float),
            sqlalchemy.Column("total_return", sqlalchemy.Float),
            sqlalchemy.Column("sharpe_ratio", sqlalchemy.Float),
            sqlalchemy.Column("max_drawdown", sqlalchemy.Float),
            sqlalchemy.Column("win_rate", sqlalchemy.Float),
            sqlalchemy.Column("total_trades", sqlalchemy.Integer),
            sqlalchemy.Column("parameters", sqlalchemy.JSON),
            sqlalchemy.Column("detailed_results", sqlalchemy.JSON),
            extend_existing=True
        )
        
        engine = create_engine("sqlite:///seeking_alpha_research/seeking_alpha.db")
        metadata.create_all(engine)
    
    def run_backtest(self, 
                    start_date: str, 
                    end_date: str,
                    gap_threshold: float = 20.0,
                    require_news: bool = False,
                    require_filing_analysis: bool = True,
                    survivorship_bias_correction: bool = True) -> BacktestResults:
        """
        Run comprehensive backtest over date range.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            gap_threshold: Minimum gap percentage to consider
            require_news: Only trade gaps with news
            require_filing_analysis: Analyze SEC filings for ATM risk
            survivorship_bias_correction: Include delisted stocks in universe
            
        Returns:
            BacktestResults with all metrics
        """
        with LogContext(logger, f"backtest from {start_date} to {end_date}"):
            logger.info(f"Starting backtest with ${self.initial_capital:,.0f} capital")
            
            # Initialize portfolio state
            cash = self.initial_capital
            positions: Dict[str, Position] = {}
            closed_positions: List[Position] = []
            daily_values = []
            
            # Generate trading days
            current_date = pd.to_datetime(start_date)
            end = pd.to_datetime(end_date)
            
            trading_days = pd.bdate_range(start=current_date, end=end)
            
            for trading_day in trading_days:
                date_str = trading_day.strftime('%Y-%m-%d')
                
                # Update existing positions
                positions, cash, newly_closed = self._update_positions(
                    positions, date_str, cash
                )
                closed_positions.extend(newly_closed)
                
                # Scan for new opportunities if we have capacity
                if len(positions) < self.max_positions:
                    new_positions = self._find_new_positions(
                        date_str, 
                        gap_threshold,
                        require_news,
                        require_filing_analysis,
                        cash,
                        len(positions)
                    )
                    
                    # Enter new positions
                    for pos in new_positions:
                        if len(positions) >= self.max_positions:
                            break
                            
                        position_value = cash * self.position_size_pct
                        shares = int(position_value / (pos.entry_price * (1 + self.slippage_pct/100)))
                        
                        if shares > 0:
                            actual_cost = shares * pos.entry_price * (1 + self.slippage_pct/100)
                            actual_cost += shares * self.commission_per_share
                            
                            if actual_cost <= cash:
                                pos.shares = shares
                                positions[pos.symbol] = pos
                                cash -= actual_cost
                                logger.info(f"Entered {pos.symbol} at ${pos.entry_price:.2f} "
                                          f"({shares} shares) on {date_str}")
                
                # Calculate daily portfolio value
                portfolio_value = cash
                for symbol, pos in positions.items():
                    current_price = self._get_current_price(symbol, date_str)
                    if current_price:
                        portfolio_value += pos.shares * current_price
                
                daily_values.append({
                    'date': trading_day,
                    'value': portfolio_value,
                    'cash': cash,
                    'positions': len(positions)
                })
            
            # Close any remaining positions
            for symbol, pos in positions.items():
                exit_price = self._get_current_price(symbol, end_date)
                if exit_price:
                    pos.exit_date = pd.to_datetime(end_date)
                    pos.exit_price = exit_price
                    pos.exit_reason = "Backtest End"
                    pos = self._calculate_position_pnl(pos)
                    closed_positions.append(pos)
                    cash += pos.shares * exit_price * (1 - self.slippage_pct/100)
                    cash -= pos.shares * self.commission_per_share
            
            # Calculate final metrics
            results = self._calculate_metrics(
                closed_positions,
                pd.DataFrame(daily_values),
                self.initial_capital
            )
            
            # Save results to database
            self._save_results(start_date, end_date, results)
            
            logger.info(f"Backtest complete: {results.total_trades} trades, "
                       f"{results.win_rate:.1f}% win rate, "
                       f"{results.total_return:.1f}% return")
            
            return results
    
    def _update_positions(self, 
                         positions: Dict[str, Position], 
                         date_str: str,
                         cash: float) -> Tuple[Dict[str, Position], float, List[Position]]:
        """Update existing positions and check for exits."""
        remaining_positions = {}
        closed_positions = []
        
        for symbol, pos in positions.items():
            # Check if stock was delisted (forced exit)
            forced_exit_date = self.delisted_manager.get_forced_exit_date(symbol, pos.entry_date.strftime('%Y-%m-%d'))
            if forced_exit_date and pd.to_datetime(forced_exit_date) <= pd.to_datetime(date_str):
                pos.exit_date = pd.to_datetime(forced_exit_date)
                pos.exit_price = 0.01  # Assume total loss on delisting
                pos.exit_reason = "Delisted"
                pos = self._calculate_position_pnl(pos)
                closed_positions.append(pos)
                cash -= pos.shares * self.commission_per_share
                logger.warning(f"{symbol} delisted - total loss")
                continue
            
            # Get current price
            current_price = self._get_current_price(symbol, date_str)
            if not current_price:
                remaining_positions[symbol] = pos
                continue
            
            # Calculate current P&L
            entry_total = pos.entry_price * (1 + self.slippage_pct/100)
            current_pnl_pct = ((current_price - entry_total) / entry_total) * 100
            
            # Update max gain/loss
            pos.max_gain_pct = max(pos.max_gain_pct, current_pnl_pct)
            pos.max_loss_pct = min(pos.max_loss_pct, current_pnl_pct)
            
            # Check exit conditions
            days_held = (pd.to_datetime(date_str) - pos.entry_date).days
            exit_reason = None
            
            if current_pnl_pct <= self.stop_loss_pct:
                exit_reason = "Stop Loss"
            elif current_pnl_pct >= self.take_profit_pct:
                exit_reason = "Take Profit"
            elif days_held >= self.max_holding_days:
                exit_reason = "Max Hold Time"
            
            if exit_reason:
                pos.exit_date = pd.to_datetime(date_str)
                pos.exit_price = current_price * (1 - self.slippage_pct/100)
                pos.exit_reason = exit_reason
                pos.held_days = days_held
                pos = self._calculate_position_pnl(pos)
                closed_positions.append(pos)
                
                # Update cash
                cash += pos.shares * pos.exit_price
                cash -= pos.shares * self.commission_per_share
                
                logger.info(f"Exited {symbol} at ${current_price:.2f} "
                          f"({exit_reason}) - P&L: {pos.pnl_percentage:.1f}%")
            else:
                remaining_positions[symbol] = pos
        
        return remaining_positions, cash, closed_positions
    
    def _find_new_positions(self,
                           date_str: str,
                           gap_threshold: float,
                           require_news: bool,
                           require_filing_analysis: bool,
                           available_cash: float,
                           current_positions: int) -> List[Position]:
        """Find new position candidates for the day."""
        try:
            # Run pre-market scanner
            gaps = self.scanner.scan_premarket_gaps(
                date_str,
                min_gap_pct=gap_threshold,
                min_volume_ratio=2.0,
                market_cap_limit=100_000_000
            )
            
            if gaps.empty:
                return []
            
            # Filter by news requirement
            if require_news:
                gaps = gaps[gaps['has_news'] == True]
            
            # Sort by gap percentage and unusual volume days
            gaps['score'] = gaps['gap_pct'] + (gaps['unusual_volume_days'] * 5)
            gaps = gaps.sort_values('score', ascending=False)
            
            # Analyze top candidates
            positions = []
            max_new_positions = min(3, self.max_positions - current_positions)
            
            for _, gap in gaps.head(max_new_positions * 2).iterrows():
                symbol = gap['symbol']
                
                # Skip if we don't have enough cash
                position_value = available_cash * self.position_size_pct
                if position_value < 1000:  # Minimum position size
                    break
                
                # Apply survivorship bias correction - skip if stock was delisted before this date
                if not self.delisted_manager.should_include_in_backtest(symbol, date_str):
                    logger.debug(f"Skipping {symbol} - delisted before trade date")
                    continue
                
                # Get entry price (gap open)
                entry_price = gap['curr_open']
                
                # Analyze SEC filings if required
                filing_risk_score = 0.0
                cash_burn_months = 0.0
                
                if require_filing_analysis:
                    filing_analysis = self._analyze_filing_risk(symbol, date_str)
                    if filing_analysis:
                        filing_risk_score = filing_analysis.get('atm_probability', 0.0)
                        cash_burn_months = filing_analysis.get('months_until_cash_out', 999)
                        
                        # Skip if no ATM risk
                        if filing_risk_score < 0.5:
                            continue
                
                # Create position
                position = Position(
                    symbol=symbol,
                    entry_date=pd.to_datetime(date_str),
                    entry_price=entry_price,
                    shares=0,  # Will be set when entering
                    gap_percentage=gap['gap_pct'],
                    has_news=gap['has_news'],
                    unusual_volume_days=gap['unusual_volume_days'],
                    filing_risk_score=filing_risk_score,
                    cash_burn_months=cash_burn_months
                )
                
                positions.append(position)
                
                if len(positions) >= max_new_positions:
                    break
            
            return positions
            
        except Exception as e:
            logger.error(f"Error finding positions for {date_str}: {e}")
            return []
    
    def _analyze_filing_risk(self, symbol: str, date_str: str) -> Optional[Dict]:
        """Analyze SEC filings for ATM offering risk."""
        try:
            # Get latest filing before gap
            filing = find_latest_filing_before_date(
                symbol, 
                date_str,
                self.data_service.db_conn
            )
            
            if not filing:
                return None
            
            # Analyze with LLM
            filing_text = f"Filing {filing['form_type']} from {filing['filed_at']}"
            
            # Get cash burn analysis
            cash_burn = calculate_cash_burn(filing_text, save_to_db=False)
            
            # Get ATM risk analysis
            atm_risk = analyze_filing_for_atm_risk(filing_text, filing_id=None)
            
            return {
                'atm_probability': atm_risk.get('atm_probability', 0.0),
                'months_until_cash_out': cash_burn.get('months_until_cash_out', 999),
                'has_active_atm': atm_risk.get('has_active_atm', False)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing filing for {symbol}: {e}")
            return None
    
    def _get_current_price(self, symbol: str, date_str: str) -> Optional[float]:
        """Get closing price for symbol on date."""
        try:
            bars = self.data_service.get_daily_bars(symbol, date_str, date_str)
            if not bars.empty:
                return bars.iloc[0]['close']
            return None
        except Exception as e:
            logger.error(f"Error getting price for {symbol} on {date_str}: {e}")
            return None
    
    def _calculate_position_pnl(self, pos: Position) -> Position:
        """Calculate P&L for a closed position."""
        if pos.exit_price is None:
            return pos
        
        # Include slippage and commissions
        entry_cost = pos.shares * pos.entry_price * (1 + self.slippage_pct/100)
        entry_cost += pos.shares * self.commission_per_share
        
        exit_proceeds = pos.shares * pos.exit_price * (1 - self.slippage_pct/100)
        exit_proceeds -= pos.shares * self.commission_per_share
        
        pos.pnl = exit_proceeds - entry_cost
        pos.pnl_percentage = (pos.pnl / entry_cost) * 100
        
        return pos
    
    def _calculate_metrics(self, 
                          positions: List[Position],
                          daily_values: pd.DataFrame,
                          initial_capital: float) -> BacktestResults:
        """Calculate comprehensive performance metrics."""
        results = BacktestResults()
        results.positions = positions
        
        if not positions:
            return results
        
        # Basic metrics
        results.total_trades = len(positions)
        results.winning_trades = sum(1 for p in positions if p.pnl > 0)
        results.losing_trades = sum(1 for p in positions if p.pnl <= 0)
        results.win_rate = (results.winning_trades / results.total_trades) * 100
        
        # P&L metrics
        wins = [p.pnl for p in positions if p.pnl > 0]
        losses = [p.pnl for p in positions if p.pnl <= 0]
        
        results.avg_win = np.mean(wins) if wins else 0
        results.avg_loss = np.mean(losses) if losses else 0
        results.profit_factor = abs(sum(wins) / sum(losses)) if losses else float('inf')
        
        # Holding period
        holding_days = [p.held_days for p in positions if p.held_days > 0]
        results.avg_holding_period = np.mean(holding_days) if holding_days else 0
        
        # Portfolio metrics
        if not daily_values.empty:
            daily_values = daily_values.set_index('date')
            results.daily_portfolio_value = daily_values['value']
            
            # Returns
            final_value = daily_values['value'].iloc[-1]
            results.total_return = ((final_value - initial_capital) / initial_capital) * 100
            
            # Daily returns for risk metrics
            daily_returns = daily_values['value'].pct_change().dropna()
            
            if len(daily_returns) > 1:
                # Sharpe ratio (assuming 252 trading days)
                results.sharpe_ratio = np.sqrt(252) * daily_returns.mean() / daily_returns.std()
                
                # Max drawdown
                cumulative = (1 + daily_returns).cumprod()
                running_max = cumulative.expanding().max()
                drawdown = (cumulative - running_max) / running_max
                results.max_drawdown = drawdown.min() * 100
                
                # VaR and Expected Shortfall
                results.var_95 = daily_returns.quantile(0.05) * 100
                results.expected_shortfall = daily_returns[daily_returns <= daily_returns.quantile(0.05)].mean() * 100
                
                # Calmar ratio
                if results.max_drawdown != 0:
                    annual_return = results.total_return * (252 / len(daily_returns))
                    results.calmar_ratio = annual_return / abs(results.max_drawdown)
        
        # Strategy-specific metrics
        with_news = [p for p in positions if p.has_news]
        without_news = [p for p in positions if not p.has_news]
        
        if with_news:
            results.gaps_with_news_win_rate = (sum(1 for p in with_news if p.pnl > 0) / len(with_news)) * 100
        
        if without_news:
            results.gaps_without_news_win_rate = (sum(1 for p in without_news if p.pnl > 0) / len(without_news)) * 100
        
        # Filing risk correlation
        high_risk = [p for p in positions if p.filing_risk_score > 0.7]
        if high_risk:
            results.high_filing_risk_win_rate = (sum(1 for p in high_risk if p.pnl > 0) / len(high_risk)) * 100
        
        # Unusual volume correlation
        if positions:
            volume_days = [p.unusual_volume_days for p in positions]
            pnls = [p.pnl_percentage for p in positions]
            if len(set(volume_days)) > 1:  # Need variance for correlation
                results.unusual_volume_correlation = np.corrcoef(volume_days, pnls)[0, 1]
        
        return results
    
    def _save_results(self, start_date: str, end_date: str, results: BacktestResults):
        """Save backtest results to database."""
        try:
            engine = create_engine("sqlite:///seeking_alpha_research/seeking_alpha.db")
            
            # Prepare data for insertion
            data = {
                'start_date': pd.to_datetime(start_date).date(),
                'end_date': pd.to_datetime(end_date).date(),
                'initial_capital': self.initial_capital,
                'final_value': results.daily_portfolio_value.iloc[-1] if not results.daily_portfolio_value.empty else self.initial_capital,
                'total_return': results.total_return,
                'sharpe_ratio': results.sharpe_ratio,
                'max_drawdown': results.max_drawdown,
                'win_rate': results.win_rate,
                'total_trades': results.total_trades,
                'parameters': {
                    'position_size_pct': self.position_size_pct,
                    'max_positions': self.max_positions,
                    'max_holding_days': self.max_holding_days,
                    'stop_loss_pct': self.stop_loss_pct,
                    'take_profit_pct': self.take_profit_pct
                },
                'detailed_results': results.to_dict()
            }
            
            with engine.connect() as conn:
                conn.execute(self.backtest_results_table.insert(), data)
                conn.commit()
                
            logger.info("Backtest results saved to database")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def generate_report(self, results: BacktestResults) -> str:
        """Generate comprehensive backtest report."""
        report = f"""
# Backtest Report

## Summary
- Total Return: {results.total_return:.2f}%
- Sharpe Ratio: {results.sharpe_ratio:.2f}
- Max Drawdown: {results.max_drawdown:.2f}%
- Win Rate: {results.win_rate:.1f}%

## Trade Statistics
- Total Trades: {results.total_trades}
- Winning Trades: {results.winning_trades}
- Losing Trades: {results.losing_trades}
- Average Win: ${results.avg_win:.2f}
- Average Loss: ${results.avg_loss:.2f}
- Profit Factor: {results.profit_factor:.2f}
- Avg Holding Period: {results.avg_holding_period:.1f} days

## Risk Metrics
- Value at Risk (95%): {results.var_95:.2f}%
- Expected Shortfall: {results.expected_shortfall:.2f}%
- Calmar Ratio: {results.calmar_ratio:.2f}

## Strategy Insights
- Gaps with News Win Rate: {results.gaps_with_news_win_rate:.1f}%
- Gaps without News Win Rate: {results.gaps_without_news_win_rate:.1f}%
- High Filing Risk Win Rate: {results.high_filing_risk_win_rate:.1f}%
- Unusual Volume Correlation: {results.unusual_volume_correlation:.2f}

## Top Trades
"""
        # Add top winning and losing trades
        sorted_positions = sorted(results.positions, key=lambda x: x.pnl_percentage, reverse=True)
        
        report += "\n### Best Trades\n"
        for pos in sorted_positions[:5]:
            report += f"- {pos.symbol}: {pos.pnl_percentage:.1f}% ({pos.held_days} days)\n"
        
        report += "\n### Worst Trades\n"
        for pos in sorted_positions[-5:]:
            report += f"- {pos.symbol}: {pos.pnl_percentage:.1f}% ({pos.exit_reason})\n"
        
        return report


if __name__ == "__main__":
    # Test the backtester
    logger.info("Starting comprehensive backtester test run...")
    
    backtester = ComprehensiveBacktester(
        initial_capital=100000,
        position_size_pct=0.02,  # 2% per position
        max_positions=10,
        max_holding_days=5,
        stop_loss_pct=-10.0,
        take_profit_pct=50.0
    )
    
    # Run a short backtest
    results = backtester.run_backtest(
        start_date="2024-01-01",
        end_date="2024-01-31",
        gap_threshold=20.0,
        require_news=True,
        require_filing_analysis=False  # Disable for speed in test
    )
    
    # Generate and print report
    report = backtester.generate_report(results)
    print(report)
    
    # Save detailed results
    with open("backtest_results.json", "w") as f:
        json.dump(results.to_dict(), f, indent=2)
    
    logger.info("Comprehensive backtester test complete!")