#!/usr/bin/env python3
"""
# TODO: DUPLICATE - Multiple backtest runners exist
# TODO: CONSOLIDATE - Merge with backtester.py or remove
# TODO: FIX IMPORTS - Uses comprehensive_backtester.py which has broken imports

Full 3-5 Year Backtest Execution

This script runs comprehensive backtests for the gap-up ATM strategy
across multiple time periods to validate performance and consistency.

NO MOCKS - Uses real Alpaca API, real Edgar API, real database.
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta
import logging
from .comprehensive_backtester import ComprehensiveBacktester
from core.data_service import DataService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('full_backtest.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def run_yearly_backtests():
    """Run backtests for each year from 2021-2024"""
    
    # Initialize backtester with realistic parameters
    backtester = ComprehensiveBacktester(
        initial_capital=100000,  # $100K starting capital
        position_size_pct=0.02,  # 2% per position
        max_positions=10,        # Max 10 concurrent positions
        stop_loss_pct=-10.0,     # 10% stop loss
        take_profit_pct=50.0,    # 50% take profit
        max_holding_days=30      # Max 30 day hold
    )
    
    # Define backtest periods
    periods = [
        ('2021-01-01', '2021-12-31', 'Full Year 2021'),
        ('2022-01-01', '2022-12-31', 'Full Year 2022'),
        ('2023-01-01', '2023-12-31', 'Full Year 2023'),
        ('2024-01-01', '2024-06-30', 'H1 2024'),
        ('2021-01-01', '2024-06-30', 'Full 3.5 Year Period')
    ]
    
    results_summary = []
    
    for start_date, end_date, period_name in periods:
        logger.info(f"\n=== Running backtest for {period_name} ===")
        logger.info(f"Period: {start_date} to {end_date}")
        
        try:
            # Run backtest with real APIs
            results = backtester.run_backtest(
                start_date=start_date,
                end_date=end_date,
                gap_threshold=20.0,           # Min 20% gap
                min_volume_ratio=2.0,         # 2x normal volume
                require_news=False,           # Don't require news (test both)
                require_filing_analysis=False, # Disable LLM for initial run
                min_market_cap=1e6,          # Min $1M market cap
                max_market_cap=100e6         # Max $100M market cap (small cap focus)
            )
            
            # Log detailed results
            logger.info(f"Backtest completed for {period_name}")
            logger.info(f"Total Return: {results.total_return:.2f}%")
            logger.info(f"Sharpe Ratio: {results.sharpe_ratio:.3f}")
            logger.info(f"Max Drawdown: {results.max_drawdown:.2f}%")
            logger.info(f"Win Rate: {results.win_rate:.1f}%")
            logger.info(f"Total Trades: {results.total_trades}")
            logger.info(f"Avg Days Held: {results.avg_hold_days:.1f}")
            logger.info(f"Profit Factor: {results.profit_factor:.2f}")
            
            # Strategy-specific metrics
            logger.info(f"Gaps with News Win Rate: {results.gaps_with_news_win_rate:.1f}%")
            logger.info(f"Gaps without News Win Rate: {results.gaps_without_news_win_rate:.1f}%")
            logger.info(f"Dilution Confirmation Rate: {results.dilution_confirmation_rate:.1f}%")
            logger.info(f"High Risk Score Win Rate: {results.high_risk_win_rate:.1f}%")
            logger.info(f"Low Risk Score Win Rate: {results.low_risk_win_rate:.1f}%")
            
            # Store results
            results_summary.append({
                'period': period_name,
                'start_date': start_date,
                'end_date': end_date,
                'total_return': results.total_return,
                'annualized_return': results.annualized_return,
                'sharpe_ratio': results.sharpe_ratio,
                'max_drawdown': results.max_drawdown,
                'win_rate': results.win_rate,
                'total_trades': results.total_trades,
                'avg_hold_days': results.avg_hold_days,
                'profit_factor': results.profit_factor,
                'gaps_with_news_win_rate': results.gaps_with_news_win_rate,
                'gaps_without_news_win_rate': results.gaps_without_news_win_rate,
                'dilution_confirmation_rate': results.dilution_confirmation_rate,
                'high_risk_win_rate': results.high_risk_win_rate,
                'low_risk_win_rate': results.low_risk_win_rate,
                'final_portfolio_value': results.final_portfolio_value,
                'total_fees': results.total_fees,
                'largest_win': results.largest_win_pct,
                'largest_loss': results.largest_loss_pct
            })
            
            # Save detailed positions for analysis
            if hasattr(results, 'positions') and results.positions:
                positions_df = pd.DataFrame([
                    {
                        'symbol': pos.symbol,
                        'entry_date': pos.entry_date,
                        'exit_date': pos.exit_date,
                        'entry_price': pos.entry_price,
                        'exit_price': pos.exit_price,
                        'pnl_pct': pos.pnl_percentage,
                        'held_days': pos.held_days,
                        'exit_reason': pos.exit_reason,
                        'max_gain_pct': pos.max_gain_pct,
                        'max_loss_pct': pos.max_loss_pct,
                        'had_news': getattr(pos, 'had_news', False),
                        'filing_risk_score': getattr(pos, 'filing_risk_score', 0),
                        'dilution_confirmed': getattr(pos, 'dilution_confirmed', False)
                    }
                    for pos in results.positions
                ])
                
                # Save positions to CSV
                filename = f"backtest_positions_{period_name.replace(' ', '_').lower()}.csv"
                positions_df.to_csv(filename, index=False)
                logger.info(f"Saved {len(positions_df)} positions to {filename}")
            
        except Exception as e:
            logger.error(f"Backtest failed for {period_name}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            # Add failed result to summary
            results_summary.append({
                'period': period_name,
                'start_date': start_date,
                'end_date': end_date,
                'error': str(e),
                'total_return': None,
                'annualized_return': None,
                'sharpe_ratio': None,
                'max_drawdown': None,
                'win_rate': None,
                'total_trades': 0
            })
    
    return results_summary


def run_parameter_sensitivity_analysis():
    """Test different parameter combinations to validate robustness"""
    
    logger.info("\n=== Running Parameter Sensitivity Analysis ===")
    
    # Test different gap thresholds
    gap_thresholds = [15.0, 20.0, 25.0, 30.0]
    
    # Test different position sizes
    position_sizes = [0.01, 0.02, 0.03, 0.05]  # 1%, 2%, 3%, 5%
    
    # Test different stop losses
    stop_losses = [-0.05, -0.10, -0.15, -0.20]  # 5%, 10%, 15%, 20%
    
    # Use 2023 as test period (good mix of market conditions)
    test_start = '2023-01-01'
    test_end = '2023-12-31'
    
    sensitivity_results = []
    
    # Test gap thresholds
    for gap_threshold in gap_thresholds:
        logger.info(f"Testing gap threshold: {gap_threshold}%")
        
        backtester = ComprehensiveBacktester(
            initial_capital=100000,
            position_size_pct=0.02,  # Keep other params constant
            max_positions=10,
            stop_loss_pct=-0.10,
            take_profit_pct=0.50
        )
        
        try:
            results = backtester.run_backtest(
                start_date=test_start,
                end_date=test_end,
                gap_threshold=gap_threshold,
                require_filing_analysis=False
            )
            
            sensitivity_results.append({
                'parameter': 'gap_threshold',
                'value': gap_threshold,
                'total_return': results.total_return,
                'sharpe_ratio': results.sharpe_ratio,
                'win_rate': results.win_rate,
                'total_trades': results.total_trades
            })
            
        except Exception as e:
            logger.error(f"Sensitivity test failed for gap_threshold {gap_threshold}: {e}")
    
    # Test position sizes
    for position_size in position_sizes:
        logger.info(f"Testing position size: {position_size*100}%")
        
        backtester = ComprehensiveBacktester(
            initial_capital=100000,
            position_size_pct=position_size,  # Vary this parameter
            max_positions=10,
            stop_loss_pct=-0.10,
            take_profit_pct=0.50
        )
        
        try:
            results = backtester.run_backtest(
                start_date=test_start,
                end_date=test_end,
                gap_threshold=20.0,  # Keep other params constant
                require_filing_analysis=False
            )
            
            sensitivity_results.append({
                'parameter': 'position_size_pct',
                'value': position_size,
                'total_return': results.total_return,
                'sharpe_ratio': results.sharpe_ratio,
                'win_rate': results.win_rate,
                'total_trades': results.total_trades
            })
            
        except Exception as e:
            logger.error(f"Sensitivity test failed for position_size {position_size}: {e}")
    
    return sensitivity_results


def analyze_results(results_summary, sensitivity_results):
    """Analyze and summarize all backtest results"""
    
    logger.info("\n=== COMPREHENSIVE BACKTEST ANALYSIS ===")
    
    # Create summary DataFrame
    df = pd.DataFrame([r for r in results_summary if 'error' not in r])
    
    if df.empty:
        logger.error("No successful backtest results to analyze")
        return
    
    logger.info("\n--- PERFORMANCE SUMMARY ---")
    logger.info(f"Periods Tested: {len(df)}")
    logger.info(f"Profitable Periods: {len(df[df['total_return'] > 0])}")
    logger.info(f"Success Rate: {len(df[df['total_return'] > 0]) / len(df) * 100:.1f}%")
    
    logger.info(f"\nAverage Annual Return: {df['annualized_return'].mean():.2f}%")
    logger.info(f"Best Year: {df.loc[df['annualized_return'].idxmax(), 'period']} ({df['annualized_return'].max():.2f}%)")
    logger.info(f"Worst Year: {df.loc[df['annualized_return'].idxmin(), 'period']} ({df['annualized_return'].min():.2f}%)")
    
    logger.info(f"\nAverage Sharpe Ratio: {df['sharpe_ratio'].mean():.3f}")
    logger.info(f"Average Max Drawdown: {df['max_drawdown'].mean():.2f}%")
    logger.info(f"Average Win Rate: {df['win_rate'].mean():.1f}%")
    
    logger.info(f"\nTotal Trades Executed: {df['total_trades'].sum()}")
    logger.info(f"Average Trades per Year: {df['total_trades'].mean():.1f}")
    
    # Strategy validation metrics
    logger.info("\n--- STRATEGY VALIDATION ---")
    logger.info(f"News Impact: {df['gaps_with_news_win_rate'].mean():.1f}% vs {df['gaps_without_news_win_rate'].mean():.1f}%")
    logger.info(f"News Advantage: +{df['gaps_with_news_win_rate'].mean() - df['gaps_without_news_win_rate'].mean():.1f}%")
    
    logger.info(f"Dilution Confirmation Rate: {df['dilution_confirmation_rate'].mean():.1f}%")
    logger.info(f"High Risk Score Performance: {df['high_risk_win_rate'].mean():.1f}%")
    logger.info(f"Low Risk Score Performance: {df['low_risk_win_rate'].mean():.1f}%")
    logger.info(f"Risk Score Advantage: +{df['high_risk_win_rate'].mean() - df['low_risk_win_rate'].mean():.1f}%")
    
    # Statistical significance
    logger.info("\n--- STATISTICAL SIGNIFICANCE ---")
    logger.info(f"Consistency: {len(df[df['total_return'] > 0]) / len(df) * 100:.0f}% of periods profitable")
    logger.info(f"Dilution Accuracy: {df['dilution_confirmation_rate'].mean():.0f}% (vs ~5% random chance)")
    logger.info(f"Sharpe Ratio: {df['sharpe_ratio'].mean():.2f} (>1.0 indicates strong risk-adjusted returns)")
    
    # Save comprehensive results
    df.to_csv('full_backtest_summary.csv', index=False)
    logger.info("\nSaved comprehensive results to full_backtest_summary.csv")
    
    # Save sensitivity analysis
    if sensitivity_results:
        sens_df = pd.DataFrame(sensitivity_results)
        sens_df.to_csv('parameter_sensitivity_analysis.csv', index=False)
        logger.info("Saved sensitivity analysis to parameter_sensitivity_analysis.csv")
        
        logger.info("\n--- PARAMETER SENSITIVITY ---")
        for param in sens_df['parameter'].unique():
            param_data = sens_df[sens_df['parameter'] == param]
            logger.info(f"{param}: Best value = {param_data.loc[param_data['total_return'].idxmax(), 'value']}")


def main():
    """Main execution function"""
    
    logger.info("=== STARTING COMPREHENSIVE 3-5 YEAR BACKTEST ===")
    logger.info("Using REAL APIs: Alpaca, Edgar, Gemini")
    logger.info("NO MOCKS - Real data, real latency, real issues")
    
    # Check environment
    required_env_vars = ['ALPACA_API_KEY', 'ALPACA_SECRET_KEY']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        return
    
    # Check if Google AI API key is available for LLM analysis
    google_ai_available = bool(os.getenv('GOOGLE_AI_API_KEY'))
    if not google_ai_available:
        logger.warning("Google AI API key not found - running without LLM filing analysis")
    else:
        logger.info("Google AI API key found - will use LLM filing analysis")
    
    # Test data service connection
    try:
        data_service = DataService()
        account = data_service.alpaca_trading_client.get_account()
        logger.info(f"Connected to Alpaca - Account: {account.status}")
    except Exception as e:
        logger.error(f"Failed to connect to Alpaca API: {e}")
        return
    
    # Run comprehensive backtests
    try:
        # Main yearly backtests
        logger.info("Starting yearly backtests...")
        results_summary = run_yearly_backtests()
        
        # Parameter sensitivity analysis
        logger.info("Starting parameter sensitivity analysis...")
        sensitivity_results = run_parameter_sensitivity_analysis()
        
        # Comprehensive analysis
        analyze_results(results_summary, sensitivity_results)
        
        logger.info("\n=== BACKTEST EXECUTION COMPLETED ===")
        logger.info("Check output files for detailed results:")
        logger.info("- full_backtest_summary.csv")
        logger.info("- parameter_sensitivity_analysis.csv") 
        logger.info("- backtest_positions_*.csv")
        logger.info("- full_backtest.log")
        
    except Exception as e:
        logger.error(f"Backtest execution failed: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main()