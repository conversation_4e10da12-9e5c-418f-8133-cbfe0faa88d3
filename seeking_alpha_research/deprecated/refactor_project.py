#!/usr/bin/env python3
"""
COMPREHENSIVE PROJECT REFACTORING
- Remove deprecated files
- Rename files to clean names
- Update all imports
- Organize into proper structure
"""

import os
import re
import shutil
from pathlib import Path

# Define what needs to be renamed (old -> new)
FILE_RENAMES = {
    # Remove _fixed suffix - these are the real versions
    'strategy_fixed.py': 'strategy.py',
    'react_agents_fixed.py': 'react_agents.py',
    'insider_accumulation_detector_fixed.py': 'insider_accumulation_detector.py',
    'predictive_watchlist_fixed.py': 'predictive_watchlist.py',
    'focused_validation_fixed.py': 'focused_validation.py',
    
    # Remove enhanced_ prefix
    'enhanced_sec_analyzer.py': 'sec_analyzer.py',
    'enhanced_ib_connector.py': 'ib_connector_v2.py',  # Keep v2 to distinguish from current
    'enhanced_delisted_integration.py': 'delisted_integration.py',
    
    # Clean up analyzer names
    'comprehensive_analyzer_final.py': 'comprehensive_analyzer.py',
    'final_analyzer.py': 'analyzer.py',
    'real_filing_analyzer.py': 'filing_analyzer.py',
    'real_gap_scanner.py': 'gap_scanner.py',
    'real_price_exit_engine.py': 'price_exit_engine.py',
    
    # Clean test names
    'final_system_test.py': 'system_test.py',
    'test_ib_comprehensive.py': 'test_ib_api.py',
}

# Files to definitely remove
FILES_TO_DELETE = [
    # Deprecated with fake data
    'rapid_alpha_validation.py',
    'statistical_validation.py',
    'statistical_validation_proper.py',
    'simple_alpha_proof.py',
    'realistic_alpha_test.py',
    'final_alpha_test.py',
    'real_alpha_test_v2.py',
    'daily_backtesting_engine.py',
    'django_integration_engine.py',
    'run_quick_validation.py',
    'parallel_processor.py',
    'production_integration_test.py',
    
    # Old versions (replaced by _fixed versions)
    'strategy.py',  # Will be replaced by strategy_fixed.py
    'strategy_enhanced.py',
    'insider_accumulation_detector.py',  # Will be replaced by _fixed
    'predictive_watchlist.py',  # Will be replaced by _fixed
    'react_agents.py',  # Will be replaced by _fixed
    'focused_validation.py',  # Will be replaced by _fixed
    'enhanced_react_agents.py',
    
    # Test files with fake data
    'test_no_fakes.py',
    'test_real_vs_fake.py',
    'test_all_real_functionality.py',
]

# Define final folder structure
FOLDER_STRUCTURE = {
    'core': [
        'data_service.py',
        'database.py',
        'config.py',
        'logger.py',
        'ib_connector.py',
    ],
    'scanners': [
        'gap_scanner.py',  # renamed from gap_scanner.py
        'premarket_scanner.py',
        'insider_accumulation_detector.py',  # renamed from _fixed
    ],
    'analysis': [
        'llm_agent.py',
        'filing_analyzer.py',  # renamed from filing_analyzer.py
        'react_agents.py',  # renamed from _fixed
        'sec_analyzer.py',  # renamed from enhanced_
        'dilution_confirmation.py',
    ],
    'strategy': [
        'strategy.py',  # renamed from _fixed
        'price_exit_engine.py',  # renamed from real_
        'focused_validation.py',  # renamed from _fixed
        'predictive_watchlist.py',  # renamed from _fixed
    ],
    'backtesting': [
        'comprehensive_backtester.py',
        'comprehensive_analyzer.py',  # renamed from _final
        'run_full_backtest.py',
    ],
    'universe': [
        'universe.py',
        'delisted_stocks.py',
        'delisted_integration.py',  # renamed from enhanced_
    ],
    'utils': [
        'news_scraper.py',
        'llm_cache.py',
        'sophisticated_filing_cache.py',
        'filing_cache_manager.py',
    ],
    'tests': [
        'system_test.py',  # renamed from system_test.py
        'test_ib_api.py',  # renamed from test_ib_api.py
        'test_ib_minute_data.py',
        'test_ib_connection.py',
        'test_single_trade.py',
        'verify_no_fakes.py',
    ],
}

def update_imports_in_file(filepath, renames):
    """Update imports in a file based on rename mappings."""
    with open(filepath, 'r') as f:
        content = f.read()
    
    original = content
    
    for old_name, new_name in renames.items():
        # Remove .py extension for imports
        old_module = old_name.replace('.py', '')
        new_module = new_name.replace('.py', '')
        
        # Update various import patterns
        patterns = [
            (f'from {old_module} import', f'from {new_module} import'),
            (f'import {old_module}', f'import {new_module}'),
            (f'from {old_module}', f'from {new_module}'),
        ]
        
        for old_pattern, new_pattern in patterns:
            content = content.replace(old_pattern, new_pattern)
    
    if content != original:
        with open(filepath, 'w') as f:
            f.write(content)
        return True
    return False

def main():
    print("🔧 COMPREHENSIVE PROJECT REFACTORING")
    print("="*60)
    
    # Step 1: Delete deprecated files
    print("\n1️⃣ REMOVING DEPRECATED FILES")
    print("-"*40)
    deleted_count = 0
    for file in FILES_TO_DELETE:
        if Path(file).exists():
            print(f"   🗑️  Deleting {file}")
            os.remove(file)
            deleted_count += 1
    print(f"   Deleted {deleted_count} deprecated files")
    
    # Step 2: Rename files
    print("\n2️⃣ RENAMING FILES")
    print("-"*40)
    renamed_count = 0
    import_updates = {}
    
    for old_name, new_name in FILE_RENAMES.items():
        if Path(old_name).exists():
            print(f"   📝 {old_name} → {new_name}")
            shutil.move(old_name, new_name)
            renamed_count += 1
            import_updates[old_name] = new_name
    print(f"   Renamed {renamed_count} files")
    
    # Step 3: Update all imports
    print("\n3️⃣ UPDATING IMPORTS")
    print("-"*40)
    py_files = list(Path('.').rglob('*.py'))
    updated_files = 0
    
    for file in py_files:
        if '__pycache__' in str(file):
            continue
        if update_imports_in_file(file, import_updates):
            updated_files += 1
    
    print(f"   Updated imports in {updated_files} files")
    
    # Step 4: Create folder structure
    print("\n4️⃣ CREATING FOLDER STRUCTURE")
    print("-"*40)
    
    for folder in FOLDER_STRUCTURE.keys():
        Path(folder).mkdir(exist_ok=True)
        print(f"   📁 Created {folder}/")
    
    # Step 5: Move files to folders
    print("\n5️⃣ ORGANIZING FILES")
    print("-"*40)
    moved_count = 0
    
    move_script = []
    for folder, files in FOLDER_STRUCTURE.items():
        for file in files:
            if Path(file).exists():
                move_script.append(f"mv {file} {folder}/")
                moved_count += 1
    
    # Create move script
    with open('move_to_folders.sh', 'w') as f:
        f.write("#!/bin/bash\n")
        f.write("# Move files to organized folders\n\n")
        for cmd in move_script:
            f.write(cmd + "\n")
        f.write(f"\necho 'Moved {moved_count} files to folders'\n")
    
    os.chmod('move_to_folders.sh', 0o755)
    
    # Step 6: Create import update script
    print("\n6️⃣ CREATING IMPORT UPDATE SCRIPT")
    print("-"*40)
    
    with open('update_folder_imports.py', 'w') as f:
        f.write('''#!/usr/bin/env python3
"""Update imports to use folder structure"""
import os
from pathlib import Path

def update_imports():
    # Update imports to include folder paths
    folders = ['core', 'scanners', 'analysis', 'strategy', 'backtesting', 'universe', 'utils']
    
    for folder in folders:
        files = list(Path(folder).glob('*.py'))
        
        for file in files:
            with open(file, 'r') as f:
                content = f.read()
            
            # Update imports from other folders
            for other_folder in folders:
                if other_folder != folder:
                    # Add folder prefix to imports
                    content = re.sub(
                        r'from ([a-zA-Z_]+) import',
                        lambda m: f'from {other_folder}.{m.group(1)} import' 
                        if Path(f'{other_folder}/{m.group(1)}.py').exists() 
                        else m.group(0),
                        content
                    )
            
            with open(file, 'w') as f:
                f.write(content)

if __name__ == '__main__':
    update_imports()
''')
    
    os.chmod('update_folder_imports.py', 0o755)
    
    # Final summary
    print("\n" + "="*60)
    print("✅ REFACTORING COMPLETE!")
    print("="*60)
    print(f"\n📊 Summary:")
    print(f"   - Deleted {deleted_count} deprecated files")
    print(f"   - Renamed {renamed_count} files to clean names")
    print(f"   - Updated imports in {updated_files} files")
    print(f"   - Ready to organize {moved_count} files into folders")
    
    print("\n📋 Next Steps:")
    print("   1. Run: ./move_to_folders.sh")
    print("   2. Run: python update_folder_imports.py")
    print("   3. Run: python verify_no_fakes.py")
    
    # List remaining uncategorized files
    all_files = set(Path('.').glob('*.py'))
    organized_files = set()
    for files in FOLDER_STRUCTURE.values():
        organized_files.update(Path(f) for f in files)
    
    remaining = all_files - organized_files
    remaining = [f for f in remaining if '__pycache__' not in str(f) 
                 and f.name not in ['refactor_project.py', 'move_to_folders.sh', 
                                   'update_folder_imports.py', '__init__.py']]
    
    if remaining:
        print(f"\n⚠️ {len(remaining)} files still need categorization:")
        for f in sorted(remaining)[:10]:
            print(f"   - {f.name}")
        if len(remaining) > 10:
            print(f"   ... and {len(remaining) - 10} more")

if __name__ == '__main__':
    main()