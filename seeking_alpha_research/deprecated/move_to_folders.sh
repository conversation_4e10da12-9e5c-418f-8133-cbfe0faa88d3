#!/bin/bash
# Move files to organized folders

mv data_service.py core/
mv database.py core/
mv logger.py core/
mv ib_connector.py core/
mv gap_scanner.py scanners/
mv premarket_scanner.py scanners/
mv insider_accumulation_detector.py scanners/
mv llm_agent.py analysis/
mv filing_analyzer.py analysis/
mv react_agents.py analysis/
mv sec_analyzer.py analysis/
mv dilution_confirmation.py analysis/
mv strategy.py strategy/
mv price_exit_engine.py strategy/
mv focused_validation.py strategy/
mv predictive_watchlist.py strategy/
mv comprehensive_backtester.py backtesting/
mv comprehensive_analyzer.py backtesting/
mv run_full_backtest.py backtesting/
mv universe.py universe/
mv delisted_stocks.py universe/
mv delisted_integration.py universe/
mv news_scraper.py utils/
mv llm_cache.py utils/
mv sophisticated_filing_cache.py utils/
mv filing_cache_manager.py utils/
mv system_test.py tests/
mv test_ib_api.py tests/
mv test_ib_minute_data.py tests/
mv test_ib_connection.py tests/
mv test_single_trade.py tests/
mv verify_no_fakes.py tests/

echo 'Moved 32 files to folders'
