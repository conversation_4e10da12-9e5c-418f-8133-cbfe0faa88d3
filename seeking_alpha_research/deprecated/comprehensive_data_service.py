"""
Comprehensive Data Service - Final Implementation

Combines the best of all data sources:
1. Alpaca for reliable market data and some SEC filings
2. edgartools for small-cap SEC filings and raw data access
3. ib_fundamental for comprehensive fundamental data  
4. IB TWS for tick data and news
5. Sophisticated caching and error handling

This replaces all the fragmented approaches with one comprehensive service.
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union
import time
import requests

# Data source imports
from core.data_service import DataService  # Our existing Alpaca-based service

try:
    from edgar import Company, set_identity
    EDGAR_AVAILABLE = True
    set_identity("Seeking <NAME_EMAIL>")
except ImportError:
    EDGAR_AVAILABLE = False

try:
    import ib_async
    from ib_fundamental import CompanyFinancials
    IB_FUNDAMENTAL_AVAILABLE = True
except ImportError:
    IB_FUNDAMENTAL_AVAILABLE = False

from ib_connector_v2 import EnhancedIBConnector
from utils.filing_cache_manager import FilingCacheManager
from core.logger import get_logger
import litellm

logger = get_logger(__name__)

# Configure LiteLLM
os.environ["GEMINI_API_KEY"] = os.getenv("GEMINI_API_KEY", "")
litellm.set_verbose = False


class ComprehensiveDataService:
    """
    Unified data service combining all sources for maximum coverage.
    
    Data Source Priority:
    1. Alpaca (reliable, good coverage for large caps)
    2. edgartools (excellent for SEC filings, especially small caps)
    3. IB fundamental (comprehensive fundamental data)
    4. IB TWS (real-time tick data, news)
    5. Cache for performance
    """
    
    def __init__(self, ib_host='localhost', ib_port=4001):
        # Initialize all data sources
        self.alpaca_service = DataService()
        self.cache_manager = FilingCacheManager()
        self.ib_connector = None
        self.ib_fundamental = None
        
        # Connection parameters
        self.ib_host = ib_host
        self.ib_port = ib_port
        
        # Track data source availability
        self.data_sources = {
            'alpaca': True,
            'edgar': EDGAR_AVAILABLE,
            'ib_fundamental': IB_FUNDAMENTAL_AVAILABLE,
            'ib_tick': False,  # Will check on connection
        }
        
        # Connect to IB if available
        self._connect_to_ib()
        
        logger.info(f"Comprehensive Data Service initialized. Sources: {self.data_sources}")
    
    def _connect_to_ib(self):
        """Connect to Interactive Brokers for tick data and fundamentals."""
        try:
            # Enhanced IB Connector for tick data and news
            self.ib_connector = EnhancedIBConnector(self.ib_host, self.ib_port)
            if self.ib_connector.connect_to_tws():
                self.data_sources['ib_tick'] = True
                logger.info("Connected to IB TWS for tick data and news")
            
            # IB Fundamental data connection
            if IB_FUNDAMENTAL_AVAILABLE:
                self.ib = ib_async.IB().connect(self.ib_host, self.ib_port)
                self.data_sources['ib_fundamental'] = True
                logger.info("Connected to IB for fundamental data")
            
        except Exception as e:
            logger.warning(f"Could not connect to IB: {e}")
            self.data_sources['ib_tick'] = False
            self.data_sources['ib_fundamental'] = False
    
    def get_comprehensive_filings(self, symbol: str, date_before: str, 
                                lookback_days: int = 730) -> pd.DataFrame:
        """
        Get SEC filings from multiple sources with fallback hierarchy.
        
        1. Try Alpaca first (reliable for large caps)
        2. Fallback to edgartools (better for small caps)
        3. Combine and deduplicate
        """
        all_filings = []
        
        # Source 1: Alpaca (existing data_service.py)
        try:
            start_date = (pd.to_datetime(date_before) - timedelta(days=lookback_days)).strftime('%Y-%m-%d')
            alpaca_filings = self.alpaca_service.get_sec_filings(symbol, start_date, date_before)
            
            if not alpaca_filings.empty:
                alpaca_filings['source'] = 'alpaca'
                all_filings.append(alpaca_filings)
                logger.info(f"Retrieved {len(alpaca_filings)} filings from Alpaca for {symbol}")
            
        except Exception as e:
            logger.warning(f"Alpaca filings failed for {symbol}: {e}")
        
        # Source 2: edgartools (especially good for small caps)
        if EDGAR_AVAILABLE:
            try:
                edgar_filings = self._get_edgar_filings(symbol, date_before, lookback_days)
                if not edgar_filings.empty:
                    edgar_filings['source'] = 'edgar'
                    all_filings.append(edgar_filings)
                    logger.info(f"Retrieved {len(edgar_filings)} filings from Edgar for {symbol}")
                
            except Exception as e:
                logger.warning(f"Edgar filings failed for {symbol}: {e}")
        
        # Combine and deduplicate
        if all_filings:
            combined = pd.concat(all_filings, ignore_index=True)
            
            # Deduplicate by accession number (keep Alpaca version if both exist)
            combined = combined.sort_values('source').drop_duplicates(
                subset=['accession_number'], keep='first'
            ).sort_values('filed_at', ascending=False)
            
            logger.info(f"Combined filings for {symbol}: {len(combined)} total")
            return combined
        
        else:
            logger.warning(f"No filings found for {symbol} from any source")
            return pd.DataFrame()
    
    def _get_edgar_filings(self, symbol: str, date_before: str, 
                          lookback_days: int) -> pd.DataFrame:
        """Get filings using edgartools."""
        try:
            company = Company(symbol)
            
            # Get all filings (edgartools handles the complexity)
            all_filings = company.get_filings(trigger_full_load=True)
            
            # Filter by date range
            end_date = pd.to_datetime(date_before)
            start_date = end_date - timedelta(days=lookback_days)
            
            filing_data = []
            for filing in all_filings:
                filing_date = pd.to_datetime(filing.filing_date)
                if start_date <= filing_date <= end_date:
                    filing_data.append({
                        'symbol': symbol,
                        'form_type': filing.form,
                        'filed_at': filing.filing_date.strftime("%Y-%m-%d"),
                        'accession_number': filing.accession_no,
                        'filing_url': f"https://www.sec.gov/Archives/edgar/data/{company.cik}/{filing.accession_no.replace('-', '')}/{filing.accession_no}-index.htm",
                        'edgar_filing': filing,  # Keep reference for text access
                        'cik': company.cik
                    })
            
            return pd.DataFrame(filing_data)
            
        except Exception as e:
            logger.error(f"Edgar filing retrieval failed for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_comprehensive_fundamental_data(self, symbol: str) -> Dict[str, Any]:
        """
        Get fundamental data from multiple sources.
        
        Combines:
        1. IB fundamental data (most comprehensive)
        2. Alpaca fundamental data (if available)
        3. Edgar filing-derived data
        """
        fundamental_data = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'sources': [],
            'income_statement': {},
            'balance_sheet': {},
            'cash_flow': {},
            'ratios': {},
            'estimates': {}
        }
        
        # Source 1: IB Fundamental (most comprehensive for large caps)
        if self.data_sources['ib_fundamental']:
            try:
                ib_data = self._get_ib_fundamental_data(symbol)
                if ib_data:
                    fundamental_data.update(ib_data)
                    fundamental_data['sources'].append('ib_fundamental')
                    logger.info(f"Retrieved IB fundamental data for {symbol}")
            
            except Exception as e:
                logger.warning(f"IB fundamental data failed for {symbol}: {e}")
        
        # Source 2: Alpaca fundamental (backup)
        try:
            # Note: Alpaca doesn't have fundamental API, but we could add it
            pass
        except Exception as e:
            logger.warning(f"Alpaca fundamental data failed for {symbol}: {e}")
        
        # Source 3: Edgar-derived fundamental data (for small caps)
        if EDGAR_AVAILABLE:
            try:
                edgar_fundamental = self._derive_fundamental_from_filings(symbol)
                if edgar_fundamental:
                    # Merge with existing data
                    for key, value in edgar_fundamental.items():
                        if key not in fundamental_data or not fundamental_data[key]:
                            fundamental_data[key] = value
                    fundamental_data['sources'].append('edgar_derived')
                    logger.info(f"Derived fundamental data from Edgar for {symbol}")
            
            except Exception as e:
                logger.warning(f"Edgar fundamental derivation failed for {symbol}: {e}")
        
        return fundamental_data
    
    def _get_ib_fundamental_data(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive fundamental data from IB."""
        try:
            company_financials = CompanyFinancials(ib=self.ib, symbol=symbol)
            
            data = {
                'company_info': {},
                'income_annual': {},
                'income_quarter': {},
                'balance_annual': {},
                'balance_quarter': {},
                'cash_flow_annual': {},
                'cash_flow_quarter': {},
                'eps_ttm': {},
                'ratios': {}
            }
            
            # Company information
            if hasattr(company_financials, 'company_information'):
                info = company_financials.company_information
                if not info.empty:
                    data['company_info'] = info.to_dict()
            
            # Income statements
            if hasattr(company_financials, 'income_annual'):
                income_annual = company_financials.income_annual
                if not income_annual.empty:
                    data['income_annual'] = income_annual.to_dict()
            
            if hasattr(company_financials, 'income_quarter'):
                income_quarter = company_financials.income_quarter
                if not income_quarter.empty:
                    data['income_quarter'] = income_quarter.to_dict()
            
            # EPS TTM
            if hasattr(company_financials, 'eps_ttm'):
                eps_ttm = company_financials.eps_ttm
                if not eps_ttm.empty:
                    data['eps_ttm'] = eps_ttm.to_dict()
                    
                    # Extract latest EPS
                    latest_eps = eps_ttm.iloc[-1]['eps'] if not eps_ttm.empty else None
                    data['latest_eps_ttm'] = latest_eps
            
            # Balance sheet
            if hasattr(company_financials, 'balance_annual'):
                balance_annual = company_financials.balance_annual
                if not balance_annual.empty:
                    data['balance_annual'] = balance_annual.to_dict()
            
            logger.info(f"Retrieved comprehensive IB fundamental data for {symbol}")
            return data
            
        except Exception as e:
            logger.error(f"IB fundamental data retrieval failed for {symbol}: {e}")
            return {}
    
    def _derive_fundamental_from_filings(self, symbol: str) -> Dict[str, Any]:
        """Derive basic fundamental data from SEC filings for small caps."""
        try:
            # Get recent filings
            filings = self.get_comprehensive_filings(symbol, 
                                                   datetime.now().strftime('%Y-%m-%d'), 
                                                   lookback_days=365)
            
            if filings.empty:
                return {}
            
            # Find most recent 10-Q or 10-K
            quarterly = filings[filings['form_type'].isin(['10-Q', '10-K'])]
            
            if quarterly.empty:
                return {}
            
            latest_filing = quarterly.iloc[0]
            
            # Extract text if we have edgar filing object
            if 'edgar_filing' in latest_filing and latest_filing['edgar_filing'] is not None:
                filing_text = latest_filing['edgar_filing'].text
                
                # Use LLM to extract key financial metrics
                extracted = self._extract_fundamentals_with_llm(filing_text, symbol)
                
                return {
                    'derived_cash_position': extracted.get('cash_position'),
                    'derived_revenue': extracted.get('revenue'),
                    'derived_operating_income': extracted.get('operating_income'),
                    'derived_total_assets': extracted.get('total_assets'),
                    'derived_total_debt': extracted.get('total_debt'),
                    'filing_source': latest_filing['accession_number'],
                    'filing_date': latest_filing['filed_at']
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"Fundamental derivation failed for {symbol}: {e}")
            return {}
    
    def _extract_fundamentals_with_llm(self, filing_text: str, symbol: str) -> Dict[str, Any]:
        """Use LLM to extract fundamental metrics from filing text."""
        try:
            prompt = f"""
Extract key financial metrics from this SEC filing for {symbol}.

Filing text (first 10000 chars):
{filing_text[:10000]}

Extract and return JSON:
{{
  "cash_position": [cash and cash equivalents in USD],
  "revenue": [total revenue for period in USD],
  "operating_income": [operating income/loss in USD],
  "total_assets": [total assets in USD],
  "total_debt": [total debt in USD],
  "shares_outstanding": [shares outstanding],
  "market_cap_estimate": [revenue * reasonable_multiple]
}}

Return only the JSON, no markdown formatting.
"""
            
            response = litellm.completion(
                model="gemini/gemini-1.5-flash",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=400,
                temperature=0.1
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Parse JSON
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
            return {}
            
        except Exception as e:
            logger.error(f"LLM fundamental extraction failed: {e}")
            return {}
    
    def get_comprehensive_news(self, symbol: str, days_back: int = 7) -> pd.DataFrame:
        """
        Get news from multiple sources.
        
        1. IB historical news (high quality)
        2. Alpaca news (good coverage)
        3. Combine and deduplicate
        """
        all_news = []
        
        # Source 1: IB Historical News
        if self.data_sources['ib_tick']:
            try:
                ib_news = self.ib_connector.get_historical_news(symbol, days_back)
                if not ib_news.empty:
                    ib_news['source'] = 'ib_tws'
                    all_news.append(ib_news)
                    logger.info(f"Retrieved {len(ib_news)} news items from IB for {symbol}")
            
            except Exception as e:
                logger.warning(f"IB news failed for {symbol}: {e}")
        
        # Source 2: Alpaca News
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            alpaca_news = self.alpaca_service.get_news(symbol, start_date, end_date)
            
            if not alpaca_news.empty:
                alpaca_news['source'] = 'alpaca'
                all_news.append(alpaca_news)
                logger.info(f"Retrieved {len(alpaca_news)} news items from Alpaca for {symbol}")
        
        except Exception as e:
            logger.warning(f"Alpaca news failed for {symbol}: {e}")
        
        # Combine and deduplicate
        if all_news:
            combined = pd.concat(all_news, ignore_index=True)
            
            # Deduplicate by headline similarity
            combined = combined.drop_duplicates(subset=['headline'], keep='first')
            combined = combined.sort_values('timestamp', ascending=False)
            
            logger.info(f"Combined news for {symbol}: {len(combined)} unique articles")
            return combined
        
        return pd.DataFrame()
    
    def detect_insider_accumulation(self, symbol: str, 
                                  analysis_date: datetime = None) -> Dict[str, Any]:
        """Advanced insider detection using IB tick data."""
        if not self.data_sources['ib_tick']:
            return {
                'insider_detected': False,
                'reason': 'IB tick data not available',
                'confidence': 0.0
            }
        
        return self.ib_connector.detect_insider_accumulation_advanced(symbol, analysis_date)
    
    def get_market_data(self, symbol: str, start: str, end: str, 
                       timeframe: str = 'daily') -> pd.DataFrame:
        """Get market data with fallback to multiple sources."""
        
        # Primary: Alpaca (reliable)
        try:
            if timeframe == 'daily':
                data = self.alpaca_service.get_daily_bars(symbol, start, end)
            else:
                data = self.alpaca_service.get_minute_bars(symbol, start, end)
            
            if not data.empty:
                data['source'] = 'alpaca'
                return data
        
        except Exception as e:
            logger.warning(f"Alpaca market data failed for {symbol}: {e}")
        
        # Fallback: Could add other sources here
        logger.error(f"No market data available for {symbol}")
        return pd.DataFrame()
    
    def close(self):
        """Clean up all connections."""
        try:
            if self.alpaca_service:
                self.alpaca_service.close()
            
            if self.cache_manager:
                self.cache_manager.close()
            
            if self.ib_connector:
                self.ib_connector.disconnect_from_tws()
            
            if hasattr(self, 'ib') and self.ib:
                self.ib.disconnect()
            
            logger.info("Comprehensive Data Service closed")
            
        except Exception as e:
            logger.error(f"Error closing data service: {e}")


def test_comprehensive_data_service():
    """Test the comprehensive data service."""
    print("Testing Comprehensive Data Service...")
    
    service = ComprehensiveDataService()
    
    try:
        symbol = 'AAPL'  # Start with a reliable large cap
        
        print(f"\n1. Testing comprehensive filings for {symbol}:")
        filings = service.get_comprehensive_filings(symbol, '2023-12-31', lookback_days=365)
        print(f"   Retrieved {len(filings)} filings")
        if not filings.empty:
            print(f"   Sources: {filings['source'].value_counts().to_dict()}")
            print(f"   Forms: {filings['form_type'].value_counts().to_dict()}")
        
        print(f"\n2. Testing fundamental data for {symbol}:")
        fundamentals = service.get_comprehensive_fundamental_data(symbol)
        print(f"   Sources: {fundamentals.get('sources', [])}")
        print(f"   Latest EPS TTM: {fundamentals.get('latest_eps_ttm', 'N/A')}")
        
        print(f"\n3. Testing news for {symbol}:")
        news = service.get_comprehensive_news(symbol, days_back=3)
        print(f"   Retrieved {len(news)} news articles")
        if not news.empty:
            print(f"   Latest headline: {news.iloc[0]['headline'][:60]}...")
        
        print(f"\n4. Testing insider detection for {symbol}:")
        insider = service.detect_insider_accumulation(symbol)
        print(f"   Insider detected: {insider['insider_detected']}")
        print(f"   Confidence: {insider.get('confidence', 0):.2f}")
        
        print(f"\n5. Testing market data for {symbol}:")
        market_data = service.get_market_data(symbol, '2024-01-01', '2024-01-31')
        print(f"   Retrieved {len(market_data)} daily bars")
        
        print("\n✅ Comprehensive Data Service test completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        service.close()


if __name__ == '__main__':
    test_comprehensive_data_service()