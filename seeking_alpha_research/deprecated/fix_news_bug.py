"""
Quick fix script for the news fetching bug in data_service.py

Run this to apply the fix:
python fix_news_bug.py
"""

import os
import re

def fix_news_fetching_bug():
    """Fix the news fetching bug in data_service.py"""
    
    file_path = 'data_service.py'
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # The broken line to find
    broken_pattern = r'fetch_function=self\.alpaca_data_client\.get_news\b'
    
    # Check if the bug exists
    if not re.search(broken_pattern, content):
        print("❌ Could not find the bug pattern. It may have already been fixed.")
        return False
    
    # Create the fix - we need to define a proper fetch function
    fix_code = '''def fetch_alpaca_news(symbol, start, end):
            from alpaca.data.requests import NewsRequest
            import pytz
            news_request = NewsRequest(
                symbols=[symbol],
                start=pd.Timestamp(start).replace(tzinfo=pytz.UTC),
                end=pd.Timestamp(end).replace(tzinfo=pytz.UTC),
                limit=50
            )
            return self.alpaca_data_client.get_news(news_request)
        
        fetch_function = fetch_alpaca_news'''
    
    # Replace the broken line
    fixed_content = re.sub(
        broken_pattern,
        'fetch_function=fetch_alpaca_news',
        content
    )
    
    # Find where to insert the function definition
    # Look for the get_news method definition
    get_news_match = re.search(r'def get_news\(self.*?\):\s*\n.*?""".*?"""', fixed_content, re.DOTALL)
    
    if get_news_match:
        # Insert the fetch function after the docstring
        insert_pos = get_news_match.end()
        
        # Add proper indentation
        indented_fix = '\n        '.join(fix_code.split('\n'))
        
        fixed_content = (
            fixed_content[:insert_pos] + 
            '\n        ' + indented_fix + '\n' +
            fixed_content[insert_pos:]
        )
    
    # Backup the original file
    backup_path = file_path + '.backup'
    with open(backup_path, 'w') as f:
        f.write(content)
    print(f"✅ Created backup: {backup_path}")
    
    # Write the fixed file
    with open(file_path, 'w') as f:
        f.write(fixed_content)
    
    print(f"✅ Fixed news fetching bug in {file_path}")
    print("\nThe fix adds a proper fetch function that creates NewsRequest objects.")
    print("You can now test with: python -m pytest tests/test_fake_implementations.py::test_data_service_news_bug -v")
    
    return True


if __name__ == '__main__':
    print("🔧 Fixing news fetching bug in data_service.py...")
    success = fix_news_fetching_bug()
    
    if success:
        print("\n✅ Fix applied successfully!")
        print("\nNext steps:")
        print("1. Run: python -m pytest tests/test_fake_implementations.py::test_data_service_news_bug -v")
        print("2. If the test passes, the news fetching should work correctly")
    else:
        print("\n❌ Fix could not be applied. Please check data_service.py manually.")