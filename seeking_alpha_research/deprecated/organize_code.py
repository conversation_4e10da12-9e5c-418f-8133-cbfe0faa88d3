#!/usr/bin/env python3
"""
Organize code into proper subfolders based on specs.md requirements
"""

import os
import shutil
from pathlib import Path

# Define folder structure based on specs.md
FOLDER_STRUCTURE = {
    'core': {
        'description': 'Core data fetching and standardization',
        'files': [
            'data_service.py',
            'database.py',
            'config.py',
            'logger.py',
            'ib_connector.py',  # IB integration
        ]
    },
    'scanners': {
        'description': 'Gap detection and pre-market scanning',
        'files': [
            'real_gap_scanner.py',
            'premarket_scanner.py',
            'insider_accumulation_detector_fixed.py',
        ]
    },
    'analysis': {
        'description': 'SEC filing and LLM analysis',
        'files': [
            'llm_agent.py',
            'real_filing_analyzer.py',
            'react_agents_fixed.py',
            'dilution_confirmation.py',
            'enhanced_sec_analyzer.py',
        ]
    },
    'strategy': {
        'description': 'Trading strategy and validation',
        'files': [
            'strategy_fixed.py',
            'real_price_exit_engine.py',
            'focused_validation_fixed.py',
            'predictive_watchlist_fixed.py',
        ]
    },
    'backtesting': {
        'description': 'Backtesting and validation engines',
        'files': [
            'comprehensive_backtester.py',
            'run_full_backtest.py',
        ]
    },
    'universe': {
        'description': 'Stock universe management',
        'files': [
            'universe.py',
            'delisted_stocks.py',
        ]
    },
    'utils': {
        'description': 'Utilities and helpers',
        'files': [
            'news_scraper.py',
            'llm_cache.py',
            'sophisticated_filing_cache.py',
            'filing_cache_manager.py',
        ]
    },
    'tests': {
        'description': 'Test files (REAL tests only)',
        'files': [
            'final_system_test.py',
            'test_ib_comprehensive.py',
            'test_ib_minute_data.py',
            'test_ib_connection.py',
            'test_single_trade.py',
            'find_all_fakes.py',
        ]
    },
    'deprecated': {
        'description': 'Old files with fake/random data',
        'files': [
            # Files with np.random trading logic
            'rapid_alpha_validation.py',
            'statistical_validation.py',
            'statistical_validation_proper.py',
            'simple_alpha_proof.py',
            'realistic_alpha_test.py',
            'final_alpha_test.py',
            'real_alpha_test_v2.py',
            'daily_backtesting_engine.py',
            'django_integration_engine.py',
            'run_quick_validation.py',
            'parallel_processor.py',
            'production_integration_test.py',
            
            # Replaced versions
            'strategy.py',  # Replaced by strategy_fixed.py
            'strategy_enhanced.py',
            'insider_accumulation_detector.py',  # Replaced by fixed version
            'predictive_watchlist.py',  # Replaced by fixed version
            'react_agents.py',  # Replaced by fixed version
            'focused_validation.py',  # Replaced by fixed version
            'enhanced_react_agents.py',  # Old version
            
            # Test files with fake data
            'test_no_fakes.py',
            'test_real_vs_fake.py',
            'test_all_real_functionality.py',
        ]
    }
}

def create_folder_structure():
    """Create organized folder structure."""
    print("🏗️ ORGANIZING CODE INTO PROPER STRUCTURE")
    print("="*60)
    
    # Create folders
    for folder, info in FOLDER_STRUCTURE.items():
        Path(folder).mkdir(exist_ok=True)
        print(f"\n📁 {folder}/ - {info['description']}")
        
        # Create README in each folder
        readme_content = f"# {folder.upper()}\n\n{info['description']}\n\n## Files\n\n"
        for file in info['files']:
            if Path(file).exists():
                readme_content += f"- `{file}`\n"
        
        with open(f"{folder}/README.md", 'w') as f:
            f.write(readme_content)
    
    # Create move script
    with open('reorganize.sh', 'w') as f:
        f.write("#!/bin/bash\n")
        f.write("# Reorganize code into proper structure\n\n")
        
        moved_count = 0
        for folder, info in FOLDER_STRUCTURE.items():
            f.write(f"\n# {info['description']}\n")
            for file in info['files']:
                if Path(file).exists():
                    f.write(f"mv {file} {folder}/\n")
                    moved_count += 1
        
        f.write(f"\necho 'Moved {moved_count} files into organized folders'\n")
        f.write("echo 'Run: tree -d to see new structure'\n")
    
    os.chmod('reorganize.sh', 0o755)
    
    print(f"\n{'='*60}")
    print("✅ Created folder structure and reorganize.sh")
    print("\nNext steps:")
    print("1. Review the folder assignments")
    print("2. Run: ./reorganize.sh to move files")
    print("3. Update imports in all files")
    
    # Identify remaining files
    all_py_files = set(Path('.').glob('*.py'))
    organized_files = set()
    for info in FOLDER_STRUCTURE.values():
        for file in info['files']:
            organized_files.add(Path(file))
    
    remaining = all_py_files - organized_files
    remaining = [f for f in remaining if '__pycache__' not in str(f) and f.name != 'organize_code.py']
    
    if remaining:
        print(f"\n⚠️ {len(remaining)} files not categorized:")
        for f in sorted(remaining):
            print(f"  - {f.name}")

if __name__ == '__main__':
    create_folder_structure()