"""Diagnose gap detection issues"""

import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta

def diagnose_gap_detection():
    """Compare yfinance data with what the scanner should find"""
    
    # Test parameters matching the scanner
    min_gap_pct = 30.0
    min_volume_ratio = 2.0
    
    # Test stocks
    test_stocks = ['GME', 'AMC', 'NVAX', 'TSLA', 'SPCE', 'RIVN', 'LCID', 'PLTR', 
                   'SOFI', 'NIO', 'XPEV', 'COIN', 'HOOD', 'DWAC']
    
    # Time period
    end_date = datetime.now()
    start_date = end_date - timedelta(days=540)  # 18 months
    
    print(f"Gap Detection Analysis")
    print(f"Period: {start_date.date()} to {end_date.date()}")
    print(f"Criteria: {min_gap_pct}% gap with {min_volume_ratio}x volume")
    print("="*80)
    
    total_gaps = 0
    gaps_by_stock = {}
    
    for symbol in test_stocks:
        try:
            # Get data from yfinance
            ticker = yf.Ticker(symbol)
            df = ticker.history(start=start_date, end=end_date)
            
            if df.empty:
                print(f"{symbol}: No data available")
                continue
            
            # Calculate gaps the same way as the scanner
            df = df.sort_index()
            df['prev_close'] = df['Close'].shift(1)
            df['gap_pct'] = ((df['Open'] - df['prev_close']) / df['prev_close'] * 100)
            
            # Calculate volume ratio
            df['volume_ma20'] = df['Volume'].rolling(20).mean()
            df['volume_ratio'] = df['Volume'] / df['volume_ma20']
            
            # Find gaps meeting criteria
            gap_mask = (
                (df['gap_pct'] >= min_gap_pct) &
                (df['volume_ratio'] >= min_volume_ratio) &
                (df['volume_ma20'].notna())
            )
            
            gaps = df[gap_mask]
            num_gaps = len(gaps)
            total_gaps += num_gaps
            
            if num_gaps > 0:
                gaps_by_stock[symbol] = num_gaps
                print(f"\n{symbol}: Found {num_gaps} gaps")
                
                # Show details
                for idx in gaps.index[:3]:  # Show up to 3 examples
                    print(f"  {idx.date()}: {gaps.loc[idx, 'gap_pct']:.1f}% gap, "
                          f"{gaps.loc[idx, 'volume_ratio']:.1f}x volume "
                          f"(${gaps.loc[idx, 'prev_close']:.2f} -> ${gaps.loc[idx, 'Open']:.2f})")
            else:
                # Check if there were any big gaps without volume requirement
                big_gaps = df[df['gap_pct'] >= min_gap_pct]
                if len(big_gaps) > 0:
                    print(f"\n{symbol}: No gaps with volume requirement, but {len(big_gaps)} gaps without volume filter")
                    # Check volume ratios on gap days
                    vol_ratios = big_gaps['volume_ratio'].dropna()
                    if len(vol_ratios) > 0:
                        print(f"  Volume ratios on gap days: min={vol_ratios.min():.1f}, max={vol_ratios.max():.1f}, avg={vol_ratios.mean():.1f}")
                
        except Exception as e:
            print(f"{symbol}: Error - {e}")
    
    print(f"\n{'='*80}")
    print(f"SUMMARY:")
    print(f"Total gaps found across {len(test_stocks)} stocks: {total_gaps}")
    print(f"Stocks with gaps: {len(gaps_by_stock)}")
    if gaps_by_stock:
        print(f"Breakdown: {gaps_by_stock}")
    
    # Test with relaxed criteria
    print(f"\n{'='*80}")
    print("Testing with relaxed criteria to understand the data:")
    
    for gap_threshold in [20, 15, 10]:
        total_relaxed = 0
        for symbol in ['GME', 'AMC', 'NVAX']:
            try:
                ticker = yf.Ticker(symbol)
                df = ticker.history(start=start_date, end=end_date)
                
                if not df.empty:
                    df['prev_close'] = df['Close'].shift(1)
                    df['gap_pct'] = ((df['Open'] - df['prev_close']) / df['prev_close'] * 100)
                    df['volume_ma20'] = df['Volume'].rolling(20).mean()
                    df['volume_ratio'] = df['Volume'] / df['volume_ma20']
                    
                    gaps = df[(df['gap_pct'] >= gap_threshold) & 
                             (df['volume_ratio'] >= min_volume_ratio) & 
                             (df['volume_ma20'].notna())]
                    total_relaxed += len(gaps)
            except:
                pass
        
        print(f"{gap_threshold}% threshold: {total_relaxed} gaps found in GME/AMC/NVAX")

if __name__ == '__main__':
    diagnose_gap_detection()