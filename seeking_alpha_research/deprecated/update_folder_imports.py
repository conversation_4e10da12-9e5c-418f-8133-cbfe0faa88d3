#!/usr/bin/env python3
"""Update imports to use folder structure"""
import os
import re
from pathlib import Path

def update_imports():
    # Update imports to include folder paths
    folders = ['core', 'scanners', 'analysis', 'strategy', 'backtesting', 'universe', 'utils']
    
    for folder in folders:
        files = list(Path(folder).glob('*.py'))
        
        for file in files:
            with open(file, 'r') as f:
                content = f.read()
            
            # Update imports from other folders
            for other_folder in folders:
                if other_folder != folder:
                    # Add folder prefix to imports
                    content = re.sub(
                        r'from ([a-zA-Z_]+) import',
                        lambda m: f'from {other_folder}.{m.group(1)} import' 
                        if Path(f'{other_folder}/{m.group(1)}.py').exists() 
                        else m.group(0),
                        content
                    )
            
            with open(file, 'w') as f:
                f.write(content)

if __name__ == '__main__':
    update_imports()
