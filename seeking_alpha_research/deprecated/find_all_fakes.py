#!/usr/bin/env python3
"""
FIND ALL FAKES - Comprehensive scan for fake implementations
"""

import os
import re
from pathlib import Path

def scan_for_fakes():
    """Scan entire codebase for fake patterns."""
    
    fake_patterns = {
        'random_trading': r'np\.random.*(?:uniform|randint|choice).*(?:pnl|profit|loss|return|price|exit)',
        'hardcoded_values': r'(?:cash_position|burn_rate|market_cap)\s*=\s*\d+(?:000000|e6)',
        'placeholder_returns': r'return\s+(?:None|{}|\[\]|""|0\.0).*#.*(?:TODO|FIXME|placeholder)',
        'mock_data': r'mock.*data|dummy.*data|fake.*data|test.*data',
        'random_probabilities': r'probability\s*=.*random\.uniform',
        'fixed_multipliers': r'(?:exit_price|gap)\s*=.*\*\s*(?:1\.\d+|0\.\d+)',
        'todo_implement': r'TODO.*implement|FIXME.*implement',
        'raise_not_implemented': r'raise NotImplementedError',
        'sample_data': r'sample_data|test_data|dummy_filing',
        'random_accuracy': r'accuracy.*random\.uniform'
    }
    
    results = {}
    py_files = list(Path('.').rglob('*.py'))
    
    # Skip test files and cache
    py_files = [f for f in py_files if '__pycache__' not in str(f) and 'test_' not in f.name]
    
    print(f"Scanning {len(py_files)} Python files...")
    
    for file_path in py_files:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                lines = content.split('\n')
            
            file_issues = []
            
            for pattern_name, pattern in fake_patterns.items():
                matches = list(re.finditer(pattern, content, re.IGNORECASE))
                if matches:
                    for match in matches:
                        # Find line number
                        line_num = content[:match.start()].count('\n') + 1
                        line_content = lines[line_num - 1].strip()
                        file_issues.append({
                            'type': pattern_name,
                            'line': line_num,
                            'content': line_content[:100]
                        })
            
            if file_issues:
                results[str(file_path)] = file_issues
                
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
    
    return results

def main():
    print("🔍 COMPREHENSIVE FAKE DETECTION SCAN")
    print("="*60)
    
    results = scan_for_fakes()
    
    if not results:
        print("✅ No fake patterns found!")
        return
    
    print(f"\n❌ Found fake patterns in {len(results)} files:\n")
    
    # Group by type
    by_type = {}
    total_issues = 0
    
    for file_path, issues in results.items():
        for issue in issues:
            issue_type = issue['type']
            if issue_type not in by_type:
                by_type[issue_type] = []
            by_type[issue_type].append({
                'file': file_path,
                'line': issue['line'],
                'content': issue['content']
            })
            total_issues += 1
    
    # Print summary by type
    print("SUMMARY BY TYPE:")
    print("-" * 60)
    for issue_type, issues in sorted(by_type.items()):
        print(f"\n{issue_type.upper()} ({len(issues)} instances):")
        for issue in issues[:5]:  # Show first 5
            print(f"  {issue['file']}:{issue['line']}")
            print(f"    {issue['content']}")
        if len(issues) > 5:
            print(f"  ... and {len(issues) - 5} more")
    
    print(f"\n{'='*60}")
    print(f"TOTAL FAKE PATTERNS FOUND: {total_issues}")
    print(f"{'='*60}")
    
    # List critical files
    critical_files = [
        'rapid_alpha_validation.py',
        'statistical_validation.py', 
        'simple_alpha_proof.py',
        'django_integration_engine.py',
        'realistic_alpha_test.py'
    ]
    
    print("\nCRITICAL FILES WITH FAKES:")
    for file in critical_files:
        for path, issues in results.items():
            if file in path:
                print(f"\n❌ {path}: {len(issues)} fake patterns")
                for issue in issues[:3]:
                    print(f"   Line {issue['line']}: {issue['type']}")

if __name__ == '__main__':
    main()