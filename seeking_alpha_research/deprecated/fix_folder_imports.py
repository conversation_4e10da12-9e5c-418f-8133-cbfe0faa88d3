#!/usr/bin/env python3
"""
Fix all imports after moving files to folders
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(filepath, file_to_folder_map):
    """Fix imports in a single file."""
    with open(filepath, 'r') as f:
        content = f.read()
    
    original = content
    
    # Fix imports based on where files now live
    for filename, folder in file_to_folder_map.items():
        module_name = filename.replace('.py', '')
        
        # Pattern 1: from module import X
        pattern1 = f'from {module_name} import'
        replacement1 = f'from {folder}.{module_name} import'
        content = content.replace(pattern1, replacement1)
        
        # Pattern 2: import module
        pattern2 = f'import {module_name}\n'
        replacement2 = f'import {folder}.{module_name}\n'
        content = content.replace(pattern2, replacement2)
        
        # Pattern 3: from .module import X (relative)
        pattern3 = f'from .{module_name} import'
        replacement3 = f'from {folder}.{module_name} import'
        content = content.replace(pattern3, replacement3)
    
    # Fix imports within same folder (make them relative)
    folder_name = filepath.parent.name
    if folder_name in ['core', 'scanners', 'analysis', 'strategy', 'backtesting', 'universe', 'utils']:
        # Find all imports from same folder
        same_folder_files = list(filepath.parent.glob('*.py'))
        for file in same_folder_files:
            if file.name != filepath.name and file.name != '__init__.py':
                module = file.stem
                # Convert absolute imports to relative within same folder
                content = re.sub(
                    f'from {folder_name}\\.{module} import',
                    f'from .{module} import',
                    content
                )
    
    if content != original:
        with open(filepath, 'w') as f:
            f.write(content)
        return True
    return False

def main():
    print("🔧 FIXING IMPORTS AFTER FOLDER REORGANIZATION")
    print("="*60)
    
    # Map of files to their new folders
    file_to_folder = {
        'data_service.py': 'core',
        'database.py': 'core',
        'logger.py': 'core',
        'ib_connector.py': 'core',
        'gap_scanner.py': 'scanners',
        'premarket_scanner.py': 'scanners',
        'insider_accumulation_detector.py': 'scanners',
        'llm_agent.py': 'analysis',
        'filing_analyzer.py': 'analysis',
        'react_agents.py': 'analysis',
        'sec_analyzer.py': 'analysis',
        'dilution_confirmation.py': 'analysis',
        'strategy.py': 'strategy',
        'price_exit_engine.py': 'strategy',
        'focused_validation.py': 'strategy',
        'predictive_watchlist.py': 'strategy',
        'comprehensive_backtester.py': 'backtesting',
        'comprehensive_analyzer.py': 'backtesting',
        'run_full_backtest.py': 'backtesting',
        'universe.py': 'universe',
        'delisted_stocks.py': 'universe',
        'delisted_integration.py': 'universe',
        'news_scraper.py': 'utils',
        'llm_cache.py': 'utils',
        'sophisticated_filing_cache.py': 'utils',
        'filing_cache_manager.py': 'utils',
    }
    
    # Process all Python files
    folders = ['core', 'scanners', 'analysis', 'strategy', 'backtesting', 'universe', 'utils', 'tests']
    total_updated = 0
    
    for folder in folders:
        folder_path = Path(folder)
        if folder_path.exists():
            print(f"\nProcessing {folder}/...")
            files = list(folder_path.glob('*.py'))
            
            for file in files:
                if file.name != '__init__.py':
                    if fix_imports_in_file(file, file_to_folder):
                        print(f"   ✅ Updated {file.name}")
                        total_updated += 1
    
    # Also process root level files
    print("\nProcessing root level files...")
    root_files = list(Path('.').glob('*.py'))
    for file in root_files:
        if fix_imports_in_file(file, file_to_folder):
            print(f"   ✅ Updated {file.name}")
            total_updated += 1
    
    print(f"\n✅ Updated imports in {total_updated} files")
    
    # Create __init__.py files
    print("\nCreating __init__.py files...")
    for folder in ['core', 'scanners', 'analysis', 'strategy', 'backtesting', 'universe', 'utils']:
        init_file = Path(folder) / '__init__.py'
        if not init_file.exists():
            init_file.touch()
            print(f"   ✅ Created {folder}/__init__.py")

if __name__ == '__main__':
    main()