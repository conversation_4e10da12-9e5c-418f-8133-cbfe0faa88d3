#!/usr/bin/env python3
"""
Identify deprecated files based on the active components in README
"""

import os
from pathlib import Path

# Active files from README.md
ACTIVE_CORE = {
    'data_service.py',
    'database.py', 
    'strategy.py',
    'strategy_fixed.py',  # Fixed version in use
    'universe.py',
    'premarket_scanner.py',
    'dilution_confirmation.py',
    'comprehensive_backtester.py',
    'llm_agent.py',
    'ib_connector.py',
    'delisted_stocks.py',
    'logger.py',
    'config.py',
    'run_full_backtest.py',
    # Real implementations
    'real_gap_scanner.py',
    'real_price_exit_engine.py', 
    'real_filing_analyzer.py',
    'focused_validation_fixed.py',
    'insider_accumulation_detector_fixed.py',
    'react_agents_fixed.py',
    'predictive_watchlist_fixed.py',
    # Tests and utilities
    'final_system_test.py',
    'test_ib_comprehensive.py',
    'test_ib_minute_data.py',
    'test_single_trade.py',
    'test_ib_connection.py',
    'find_all_fakes.py',
    'identify_deprecated.py'
}

# Django files - keep all
DJANGO_PREFIX = 'django_prototype_v0/'

# Test files - keep
TEST_PREFIX = ('test_', 'tests/')

# Documentation files - keep
DOC_FILES = {'.md', '.txt', '.pdf'}

def main():
    """Identify files that should be moved to deprecated."""
    
    deprecated_files = []
    py_files = list(Path('.').glob('*.py'))
    
    for file_path in py_files:
        filename = file_path.name
        
        # Skip if in active list
        if filename in ACTIVE_CORE:
            continue
            
        # Skip test files
        if filename.startswith('test_'):
            continue
            
        # Skip __pycache__
        if '__pycache__' in str(file_path):
            continue
            
        # Check if it's a validation/test file with fake data
        fake_files = [
            'rapid_alpha_validation.py',
            'statistical_validation.py',
            'statistical_validation_proper.py',
            'simple_alpha_proof.py',
            'realistic_alpha_test.py',
            'final_alpha_test.py',
            'real_alpha_test_v2.py',
            'daily_backtesting_engine.py',  # Has fake multipliers
            'django_integration_engine.py',  # Full of random data
            'run_quick_validation.py',  # Has fake exits
            'parallel_processor.py',  # Test data
            'production_integration_test.py',  # Old test
            'enhanced_react_agents.py',  # Replaced by react_agents_fixed
            'insider_accumulation_detector.py',  # Replaced by fixed version
            'predictive_watchlist.py',  # Replaced by fixed version
            'react_agents.py',  # Replaced by fixed version
            'strategy_enhanced.py',  # Old version
            'focused_validation.py',  # Replaced by fixed version
        ]
        
        if filename in fake_files:
            deprecated_files.append(filename)
    
    print("🔍 DEPRECATED FILE ANALYSIS")
    print("="*60)
    print(f"\nFound {len(deprecated_files)} deprecated files with fake data:")
    print("-"*60)
    
    for f in sorted(deprecated_files):
        print(f"  {f}")
    
    print(f"\n{'='*60}")
    print("RECOMMENDATION: Move these files to deprecated/ folder")
    print("They contain random trading logic and should not be used")
    
    # Create move script
    with open('move_deprecated.sh', 'w') as f:
        f.write("#!/bin/bash\n")
        f.write("# Move deprecated files with fake data\n\n")
        f.write("mkdir -p deprecated\n\n")
        for file in deprecated_files:
            f.write(f"mv {file} deprecated/\n")
        f.write("\necho 'Moved {} files to deprecated/'\n".format(len(deprecated_files)))
    
    os.chmod('move_deprecated.sh', 0o755)
    print("\nCreated move_deprecated.sh - run it to clean up")

if __name__ == '__main__':
    main()