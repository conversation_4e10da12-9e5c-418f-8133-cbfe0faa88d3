"""Debug the gap scanner issue"""

import pandas as pd
import yfinance as yf
from datetime import datetime, <PERSON><PERSON><PERSON>

def debug_gap_scanner():
    """Debug why scanner is finding fewer gaps than expected"""
    
    # Test with GME for May 2024 gaps
    symbol = 'GME'
    
    # Get data around known gap dates
    start_date = '2024-05-01'
    end_date = '2024-06-30'
    
    print(f"Debugging {symbol} gap detection")
    print(f"Period: {start_date} to {end_date}")
    print("="*80)
    
    # Get data from yfinance
    ticker = yf.Ticker(symbol)
    df = ticker.history(start=start_date, end=end_date)
    
    if df.empty:
        print("No data retrieved!")
        return
    
    # Convert to lowercase columns to match scanner format
    df_scanner_format = pd.DataFrame({
        'timestamp': df.index,
        'open': df['Open'],
        'high': df['High'], 
        'low': df['Low'],
        'close': df['Close'],
        'volume': df['Volume']
    })
    
    # Apply the exact logic from _detect_real_gaps
    print("\nApplying scanner logic:")
    
    # Sort and calculate gaps
    df_scanner_format = df_scanner_format.sort_values('timestamp').reset_index(drop=True)
    df_scanner_format['prev_close'] = df_scanner_format['close'].shift(1)
    df_scanner_format['gap_pct'] = ((df_scanner_format['open'] - df_scanner_format['prev_close']) / df_scanner_format['prev_close'] * 100)
    
    # Calculate volume ratio
    df_scanner_format['volume_ma20'] = df_scanner_format['volume'].rolling(20).mean()
    df_scanner_format['volume_ratio'] = df_scanner_format['volume'] / df_scanner_format['volume_ma20']
    
    # Show all rows with calculations
    print("\nAll data with calculations:")
    print(df_scanner_format[['timestamp', 'prev_close', 'open', 'gap_pct', 'volume', 'volume_ma20', 'volume_ratio']].to_string())
    
    # Apply gap criteria
    min_gap_pct = 30.0
    min_volume_ratio = 2.0
    
    gap_mask = (
        (df_scanner_format['gap_pct'] >= min_gap_pct) &
        (df_scanner_format['volume_ratio'] >= min_volume_ratio) &
        (df_scanner_format['volume_ma20'].notna())
    )
    
    gaps_found = df_scanner_format[gap_mask]
    
    print(f"\nGaps found with 30% + 2x volume criteria: {len(gaps_found)}")
    
    if len(gaps_found) > 0:
        print("\nGap details:")
        for _, gap in gaps_found.iterrows():
            print(f"  {gap['timestamp'].date()}: {gap['gap_pct']:.1f}% gap, {gap['volume_ratio']:.1f}x volume")
    
    # Check what happens with different criteria
    print("\n\nTesting different criteria:")
    
    # Just gap percentage
    big_gaps = df_scanner_format[df_scanner_format['gap_pct'] >= 30]
    print(f"\nGaps >= 30% (no volume filter): {len(big_gaps)}")
    for _, gap in big_gaps.iterrows():
        print(f"  {gap['timestamp'].date()}: {gap['gap_pct']:.1f}% gap, volume_ratio={gap['volume_ratio']:.1f} (ma20_exists={pd.notna(gap['volume_ma20'])})")
    
    # Check volume_ma20 availability
    print(f"\nRows with volume_ma20 available: {df_scanner_format['volume_ma20'].notna().sum()} out of {len(df_scanner_format)}")
    
    # Check specific dates
    print("\n\nChecking specific known gap dates:")
    known_gaps = ['2024-05-13', '2024-05-14', '2024-06-03']
    
    for date_str in known_gaps:
        rows = df_scanner_format[df_scanner_format['timestamp'].astype(str).str.startswith(date_str)]
        if not rows.empty:
            row = rows.iloc[0]
            print(f"\n{date_str}:")
            print(f"  Gap %: {row['gap_pct']:.1f}")
            print(f"  Volume ratio: {row['volume_ratio']:.1f}")
            print(f"  Volume MA20 exists: {pd.notna(row['volume_ma20'])}")
            print(f"  Meets gap criteria: {row['gap_pct'] >= min_gap_pct}")
            print(f"  Meets volume criteria: {row['volume_ratio'] >= min_volume_ratio}")
            print(f"  Meets MA20 criteria: {pd.notna(row['volume_ma20'])}")
            print(f"  Overall qualification: {row['gap_pct'] >= min_gap_pct and row['volume_ratio'] >= min_volume_ratio and pd.notna(row['volume_ma20'])}")

if __name__ == '__main__':
    debug_gap_scanner()