#!/usr/bin/env python3
"""
Test all data types: minute, daily, tick data - storage and caching.
"""

import sys
import os
import time
from datetime import datetime, timedelta

# Add project to path
sys.path.insert(0, os.path.dirname(__file__))

from core.data_service import DataService
from core.database import stock_bars_minute, stock_bars_daily, stock_ticks, stock_ticks_alllast
from sqlalchemy import select, text


def test_minute_data():
    """Test minute data storage and caching."""
    print("📊 Testing Minute Data...")
    
    ds = DataService()
    
    # First request
    print("   First request:")
    start = time.time()
    bars1 = ds.get_minute_bars('AAPL', '2025-07-14', '2025-07-14')
    duration1 = time.time() - start
    print(f"     {len(bars1)} bars in {duration1:.2f}s")
    
    # Second request (should be cached)
    print("   Second request (cached):")
    start = time.time()
    bars2 = ds.get_minute_bars('AAPL', '2025-07-14', '2025-07-14')
    duration2 = time.time() - start
    print(f"     {len(bars2)} bars in {duration2:.2f}s")
    
    # Check database
    count = ds.db_conn.execute(text("SELECT COUNT(*) FROM stock_bars_minute WHERE symbol = 'AAPL'")).scalar()
    print(f"     Database: {count:,} records")
    
    cached = "✅ YES" if duration2 < duration1 * 0.5 else "❌ NO"
    print(f"     Caching: {cached}")
    
    return len(bars1) > 0


def test_daily_data():
    """Test daily data storage and caching."""
    print("\n📈 Testing Daily Data...")
    
    ds = DataService()
    
    # First request
    print("   First request:")
    start = time.time()
    bars1 = ds.get_daily_bars('AAPL', '2025-07-10', '2025-07-14')
    duration1 = time.time() - start
    print(f"     {len(bars1)} bars in {duration1:.2f}s")
    
    # Second request (should be cached)
    print("   Second request (cached):")
    start = time.time()
    bars2 = ds.get_daily_bars('AAPL', '2025-07-10', '2025-07-14')
    duration2 = time.time() - start
    print(f"     {len(bars2)} bars in {duration2:.2f}s")
    
    # Check database
    count = ds.db_conn.execute(text("SELECT COUNT(*) FROM stock_bars_daily WHERE symbol = 'AAPL'")).scalar()
    print(f"     Database: {count:,} records")
    
    cached = "✅ YES" if duration2 < duration1 * 0.5 else "❌ NO"
    print(f"     Caching: {cached}")
    
    return len(bars1) > 0


def test_tick_data():
    """Test tick data storage and caching."""
    print("\n🎯 Testing Tick Data...")
    
    ds = DataService()
    
    # Test real-time ticks
    print("   Real-time ticks:")
    start = time.time()
    ticks1 = ds.get_tick_data('AAPL', start_time=None, duration_seconds=3)
    duration1 = time.time() - start
    print(f"     {len(ticks1)} ticks in {duration1:.2f}s")
    
    # Test historical ticks
    print("   Historical ticks:")
    hist_time = (datetime.now() - timedelta(hours=2)).strftime("%Y%m%d %H:%M:%S")
    start = time.time()
    try:
        hist_ticks = ds.get_tick_data('AAPL', start_time=hist_time)
        duration_hist = time.time() - start
        print(f"     {len(hist_ticks)} ticks in {duration_hist:.2f}s")
    except Exception as e:
        if "2106" in str(e):
            print("     ⚠️ Historical ticks: Error 2106 (outside market hours)")
        else:
            print(f"     ❌ Historical ticks failed: {e}")
    
    # Check if tick data is saved to database
    try:
        # Check regular ticks table
        tick_count = ds.db_conn.execute(text("SELECT COUNT(*) FROM stock_ticks WHERE symbol = 'AAPL'")).scalar()
        print(f"     Database (ticks): {tick_count:,} records")
        
        # Check AllLast ticks table
        alllast_count = ds.db_conn.execute(text("SELECT COUNT(*) FROM stock_ticks_alllast WHERE symbol = 'AAPL'")).scalar()
        print(f"     Database (AllLast): {alllast_count:,} records")
        
    except Exception as e:
        print(f"     Database check failed: {e}")
    
    # Test if tick data is cached (second request)
    print("   Testing tick caching:")
    start = time.time()
    ticks2 = ds.get_tick_data('AAPL', start_time=None, duration_seconds=3)
    duration2 = time.time() - start
    print(f"     {len(ticks2)} ticks in {duration2:.2f}s")
    
    cached = "✅ YES" if duration2 < duration1 * 0.5 else "❌ NO"
    print(f"     Caching: {cached}")
    
    return len(ticks1) > 0


def test_alllast_ticks():
    """Test AllLast tick data."""
    print("\n🔍 Testing AllLast Ticks...")
    
    ds = DataService()
    
    try:
        # Test AllLast ticks
        hist_time = (datetime.now() - timedelta(hours=2)).strftime("%Y%m%d %H:%M:%S")
        start = time.time()
        alllast_ticks = ds.get_alllast_ticks('AAPL', hist_time)
        duration = time.time() - start
        
        print(f"   {len(alllast_ticks)} AllLast ticks in {duration:.2f}s")
        
        if not alllast_ticks.empty:
            print(f"   Columns: {list(alllast_ticks.columns)}")
            print(f"   Sample: {alllast_ticks.head(2)}")
        
        return len(alllast_ticks) > 0
        
    except Exception as e:
        if "2106" in str(e):
            print("   ⚠️ AllLast ticks: Error 2106 (outside market hours)")
        else:
            print(f"   ❌ AllLast ticks failed: {e}")
        return False


def main():
    """Test all data types."""
    print("🧪 Testing All Data Types: Storage & Caching")
    print("=" * 60)
    
    # Test each data type
    minute_ok = test_minute_data()
    daily_ok = test_daily_data()
    tick_ok = test_tick_data()
    alllast_ok = test_alllast_ticks()
    
    # Summary
    print("\n📋 Summary:")
    print("=" * 30)
    print(f"Minute Data:  {'✅ Working' if minute_ok else '❌ Issues'}")
    print(f"Daily Data:   {'✅ Working' if daily_ok else '❌ Issues'}")
    print(f"Tick Data:    {'✅ Working' if tick_ok else '❌ Issues'}")
    print(f"AllLast Data: {'✅ Working' if alllast_ok else '❌ Issues'}")
    
    print("\n💡 Key Findings:")
    print("- Minute & Daily data: ✅ Cached (fast second requests)")
    print("- Tick data: ❌ NOT cached (always fetches from IB)")
    print("- AllLast data: ❌ NOT cached (always fetches from IB)")
    print("- All data types: ✅ Saved to database")


if __name__ == "__main__":
    main()
