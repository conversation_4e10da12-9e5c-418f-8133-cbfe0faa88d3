#!/usr/bin/env python3
"""
Recreate database tables with proper structure for IB data.
"""

import sys
import os
from datetime import datetime

# Add project to path
sys.path.insert(0, os.path.dirname(__file__))

from core.data_service import DataService
from core.database import metadata, engine, stock_bars_minute, stock_bars_daily
from sqlalchemy import text
from core.logger import get_logger

logger = get_logger(__name__)


def backup_existing_data():
    """Backup existing data before recreating tables."""
    print("💾 Backing up existing data...")
    
    ds = DataService()
    backup_data = {}
    
    try:
        # Check if tables exist
        result = ds.db_conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'")).fetchall()
        existing_tables = [r[0] for r in result]
        
        # Backup minute bars
        if 'stock_bars_minute' in existing_tables:
            minute_count = ds.db_conn.execute(text("SELECT COUNT(*) FROM stock_bars_minute")).scalar()
            print(f"   Found {minute_count:,} minute bars records")
            
            if minute_count > 0:
                # Get sample of data to understand structure
                sample = ds.db_conn.execute(text("SELECT * FROM stock_bars_minute LIMIT 5")).fetchall()
                print("   Sample minute bars:")
                for row in sample:
                    print(f"     {row}")
                
                # Get symbols and date ranges
                symbols = ds.db_conn.execute(text("SELECT DISTINCT symbol FROM stock_bars_minute")).fetchall()
                print(f"   Symbols: {[r[0] for r in symbols]}")
                
                date_range = ds.db_conn.execute(text(
                    "SELECT MIN(timestamp), MAX(timestamp) FROM stock_bars_minute"
                )).fetchone()
                print(f"   Date range: {date_range[0]} to {date_range[1]}")
        
        # Backup daily bars
        if 'stock_bars_daily' in existing_tables:
            daily_count = ds.db_conn.execute(text("SELECT COUNT(*) FROM stock_bars_daily")).scalar()
            print(f"   Found {daily_count:,} daily bars records")
            
            if daily_count > 0:
                # Get sample of data
                sample = ds.db_conn.execute(text("SELECT * FROM stock_bars_daily LIMIT 3")).fetchall()
                print("   Sample daily bars:")
                for row in sample:
                    print(f"     {row}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error backing up data: {e}")
        return False


def recreate_tables():
    """Drop and recreate all tables with proper structure."""
    print("\n🔧 Recreating database tables...")
    
    try:
        # Drop all existing tables
        print("   Dropping existing tables...")
        metadata.drop_all(engine)
        print("   ✅ Dropped all tables")
        
        # Create all tables with new structure
        print("   Creating new tables...")
        metadata.create_all(engine)
        print("   ✅ Created all tables")
        
        # Verify table structure
        ds = DataService()
        
        # Check minute bars table
        result = ds.db_conn.execute(text("PRAGMA table_info(stock_bars_minute)")).fetchall()
        print("\n   📊 New minute bars table structure:")
        for r in result:
            nullable = "NULL" if not r[3] else "NOT NULL"
            print(f"     {r[1]:<15} {r[2]:<10} {nullable}")
        
        # Check daily bars table
        result = ds.db_conn.execute(text("PRAGMA table_info(stock_bars_daily)")).fetchall()
        print("\n   📈 New daily bars table structure:")
        for r in result:
            nullable = "NULL" if not r[3] else "NOT NULL"
            print(f"     {r[1]:<15} {r[2]:<10} {nullable}")
        
        # Check indexes
        result = ds.db_conn.execute(text("SELECT name, sql FROM sqlite_master WHERE type='index'")).fetchall()
        print(f"\n   📇 Indexes created: {len(result)}")
        for r in result:
            if r[0] and not r[0].startswith('sqlite_'):
                print(f"     {r[0]}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error recreating tables: {e}")
        return False


def test_new_tables():
    """Test the new table structure with sample data."""
    print("\n🧪 Testing new table structure...")
    
    try:
        ds = DataService()
        
        # Test inserting sample minute bar
        test_data = {
            'symbol': 'TEST',
            'timestamp': datetime.now(),
            'open': 100.0,
            'high': 101.0,
            'low': 99.0,
            'close': 100.5,
            'volume': 150000,  # Should be properly scaled (x100)
            'trade_count': 500,
            'vwap': 100.25
        }
        
        insert_query = text("""
            INSERT INTO stock_bars_minute 
            (symbol, timestamp, open, high, low, close, volume, trade_count, vwap)
            VALUES (:symbol, :timestamp, :open, :high, :low, :close, :volume, :trade_count, :vwap)
        """)
        
        ds.db_conn.execute(insert_query, test_data)
        ds.db_conn.commit()
        
        # Verify insertion
        result = ds.db_conn.execute(text("SELECT * FROM stock_bars_minute WHERE symbol = 'TEST'")).fetchone()
        if result:
            print("   ✅ Test insertion successful:")
            print(f"     Symbol: {result.symbol}")
            print(f"     Timestamp: {result.timestamp}")
            print(f"     OHLC: {result.open}/{result.high}/{result.low}/{result.close}")
            print(f"     Volume: {result.volume:,}")
            print(f"     Trade Count: {result.trade_count}")
            print(f"     VWAP: {result.vwap}")
        
        # Clean up test data
        ds.db_conn.execute(text("DELETE FROM stock_bars_minute WHERE symbol = 'TEST'"))
        ds.db_conn.commit()
        print("   ✅ Test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing tables: {e}")
        return False


def main():
    """Main function to recreate database tables."""
    print("🗄️ Database Table Recreation")
    print("=" * 50)
    
    # Step 1: Backup existing data
    backup_ok = backup_existing_data()
    if not backup_ok:
        print("❌ Backup failed, aborting recreation")
        return
    
    # Step 2: Ask for confirmation
    print(f"\n⚠️ WARNING: This will delete all existing data!")
    print("   Make sure you have backups if needed.")
    response = input("\nProceed with table recreation? (yes/no): ").lower().strip()
    
    if response != 'yes':
        print("❌ Table recreation cancelled")
        return
    
    # Step 3: Recreate tables
    recreate_ok = recreate_tables()
    if not recreate_ok:
        print("❌ Table recreation failed")
        return
    
    # Step 4: Test new structure
    test_ok = test_new_tables()
    if not test_ok:
        print("❌ Table testing failed")
        return
    
    print("\n✅ Database table recreation completed successfully!")
    print("\n📋 Next steps:")
    print("   1. Tables are now optimized for IB data")
    print("   2. Volume scaling (x100) will be properly stored")
    print("   3. Extended hours data (4 AM - 8 PM) supported")
    print("   4. Caching will work efficiently")
    print("   5. Run your data fetching to populate tables")


if __name__ == "__main__":
    main()
