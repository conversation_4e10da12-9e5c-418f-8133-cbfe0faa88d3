#!/usr/bin/env python3
"""Quick test for caching and tick data."""

import sys
import os
import time
from datetime import datetime, timedelta

# Add project to path
sys.path.insert(0, os.path.dirname(__file__))

from core.data_service import DataService


def test_caching():
    """Test caching system."""
    print("💾 Testing Caching...")
    
    ds = DataService()
    
    # Test caching
    print("First request:")
    start = time.time()
    bars1 = ds.get_minute_bars('AAPL', '2025-07-14', '2025-07-14')
    duration1 = time.time() - start
    print(f"  {len(bars1)} bars in {duration1:.2f}s")
    
    print("Second request (should be cached):")
    start = time.time()
    bars2 = ds.get_minute_bars('AAPL', '2025-07-14', '2025-07-14')
    duration2 = time.time() - start
    print(f"  {len(bars2)} bars in {duration2:.2f}s")
    
    if duration2 < duration1 * 0.5:
        print("✅ Caching is working!")
    else:
        print("⚠️ Caching might not be working")


def test_tick_data():
    """Test tick data."""
    print("\n🎯 Testing Tick Data...")
    
    ds = DataService()
    
    # Test real-time ticks
    print("Real-time ticks (5 seconds):")
    try:
        ticks = ds.get_tick_data('AAPL', start_time=None, duration_seconds=5)
        if not ticks.empty:
            print(f"✅ Got {len(ticks)} real-time ticks")
            print(f"Columns: {list(ticks.columns)}")
        else:
            print("⚠️ No real-time ticks (outside market hours)")
    except Exception as e:
        print(f"⚠️ Real-time ticks failed: {e}")
    
    # Test historical ticks
    print("Historical ticks (2 hours ago):")
    try:
        hist_time = (datetime.now() - timedelta(hours=2)).strftime("%Y%m%d %H:%M:%S")
        hist_ticks = ds.get_tick_data('AAPL', start_time=hist_time)
        if not hist_ticks.empty:
            print(f"✅ Got {len(hist_ticks)} historical ticks")
        else:
            print("⚠️ No historical ticks")
    except Exception as e:
        if "2106" in str(e):
            print("⚠️ Historical ticks: Error 2106 (outside market hours)")
        else:
            print(f"⚠️ Historical ticks failed: {e}")


if __name__ == "__main__":
    test_caching()
    test_tick_data()
    print("\n✅ Tests complete!")
