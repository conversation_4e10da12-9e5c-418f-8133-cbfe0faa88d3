import pandas as pd
from strategy.strategy import find_gap_up_candidates_properly
from core.data_service import DataService
from datetime import datetime, timedelta

# from universe.universe import generate_stock_universe, OUTPUT_FILE
import os


class Backtester:
    """
    A simple backtesting engine to simulate the trading strategy and evaluate its performance.
    """

    def __init__(
        self, symbols: list, start_date: str, end_date: str, hold_period: int = 5
    ):
        self.symbols = symbols
        self.start_date = start_date
        self.end_date = end_date
        self.hold_period = hold_period
        self.trades = []
        self.data_service = DataService()

    def run(self):
        """
        Runs the backtest across all symbols.
        """
        print("--- Starting Backtest ---")
        for symbol in self.symbols:
            print(f"\nProcessing symbol: {symbol}")
            # 1. Find trade opportunities using the core strategy logic
            # We need to get daily bars for the strategy to work
            daily_bars = self.data_service.get_daily_bars(
                symbol, start=self.start_date, end=self.end_date
            )

            if daily_bars.empty:
                print(
                    f"No daily bar data found for {symbol} from {self.start_date} to {self.end_date}."
                )
                continue

            # Find gap-up events with our strategy logic
            # Use the proper strategy function that analyzes filings
            from datetime import datetime

            scan_date = datetime.strptime(self.start_date, "%Y-%m-%d").strftime(
                "%Y-%m-%d"
            )

            candidates = find_gap_up_candidates_properly(
                scan_date=scan_date,
                universe=[symbol],
                min_gap_pct=30.0,
                max_market_cap=100_000_000,
            )

            # Extract trade dates from candidates
            trade_dates = []
            for candidate in candidates:
                if candidate.get("atm_probability", 0) > 0.7:  # High ATM probability
                    trade_dates.append(scan_date)

            if not trade_dates:
                print(f"No tradeable events found for {symbol}.\n")
                continue

            # 2. Simulate trades for each opportunity
            for trade_date_str in trade_dates:
                self.simulate_trade(symbol, trade_date_str)

        print("\n--- Backtest Finished ---")
        # 3. Calculate and report performance
        self.report_performance()

    def simulate_trade(self, symbol: str, entry_date_str: str):
        """
        Simulates a single trade: entering at the open and exiting after the hold period.
        """
        print(f"  -> Simulating trade for {symbol} on {entry_date_str}")

        try:
            entry_date = datetime.strptime(entry_date_str, "%Y-%m-%d").date()

            # We need a buffer to get the exit date's data
            trade_data_end_date = entry_date + timedelta(days=self.hold_period + 5)

            bars = self.data_service.get_daily_bars(
                symbol,
                start=entry_date.strftime("%Y-%m-%d"),
                end=trade_data_end_date.strftime("%Y-%m-%d"),
            )

            if bars.empty or entry_date not in bars.index.date:
                print(
                    f"    [!] Could not find bar data for entry date {entry_date_str}."
                )
                return

            # 1. Get entry price
            entry_price = bars.loc[bars.index.date == entry_date, "open"].iloc[0]

            # 2. Calculate exit date
            # We use pandas' BDay to handle weekends and holidays
            exit_date = (entry_date + pd.tseries.offsets.BDay(self.hold_period)).date()

            if exit_date not in bars.index.date:
                # If the calculated exit date is not a trading day, find the next available one.
                # This is a simple approach; a more advanced one might have different rules.
                actual_exit_date_row = bars[bars.index.date > exit_date]
                if actual_exit_date_row.empty:
                    print(
                        f"    [!] Could not find an exit date for trade entered on {entry_date_str}."
                    )
                    return
                exit_price = actual_exit_date_row["open"].iloc[0]
                actual_exit_date = actual_exit_date_row.index.date[0]
            else:
                exit_price = bars.loc[bars.index.date == exit_date, "open"].iloc[0]
                actual_exit_date = exit_date

            # 4. Record the trade
            pnl = exit_price - entry_price
            self.trades.append(
                {
                    "symbol": symbol,
                    "entry_date": entry_date_str,
                    "entry_price": entry_price,
                    "exit_date": actual_exit_date.strftime("%Y-%m-%d"),
                    "exit_price": exit_price,
                    "pnl": pnl,
                }
            )
            print(
                f"    [+] Trade simulated: Entered at {entry_price:.2f}, Exited at {exit_price:.2f}, P&L: {pnl:.2f}"
            )

        except Exception as e:
            print(
                f"    [!] Error simulating trade for {symbol} on {entry_date_str}: {e}"
            )

    def report_performance(self):
        """
        Calculates and prints performance metrics for the backtest.
        """
        print("\n--- Performance Report ---")
        if not self.trades:
            print("No trades were executed.")
            return

        trades_df = pd.DataFrame(self.trades)

        total_pnl = trades_df["pnl"].sum()
        num_trades = len(trades_df)

        wins = trades_df[trades_df["pnl"] > 0]
        losses = trades_df[trades_df["pnl"] <= 0]

        win_rate = (len(wins) / num_trades) * 100 if num_trades > 0 else 0
        avg_gain = wins["pnl"].mean() if not wins.empty else 0
        avg_loss = losses["pnl"].mean() if not losses.empty else 0

        print(f"Total Trades: {num_trades}")
        print(f"Total P/L: ${total_pnl:,.2f}")
        print(f"Win Rate: {win_rate:.2f}%")
        print(f"Average Gain: ${avg_gain:,.2f}")
        print(f"Average Loss: ${avg_loss:,.2f}")

        print("\n--- All Trades ---")
        print(trades_df.to_string())

    def close(self):
        """Closes the database connection."""
        self.data_service.close()


if __name__ == "__main__":
    # 1. Generate the universe of small-cap stocks first.
    # This ensures we are working with a fresh list based on the latest market data.
    # generate_stock_universe()

    # 2. Load the universe from the generated file.
    UNIVERSE_FILE = "/Users/<USER>/PycharmProjects/stk_v5/data/universe/nasdaq_screener_1752224820914.csv"

    if not os.path.exists(UNIVERSE_FILE):
        print(f"Stock universe file '{UNIVERSE_FILE}' not found. Exiting.")
    else:
        universe_df = pd.read_csv(UNIVERSE_FILE)
        if universe_df.empty:
            print(f"Stock universe file '{UNIVERSE_FILE}' is empty. Exiting.")
        else:
            test_symbols = universe_df["Symbol"].tolist()

            # For testing, let's just run on the first 10 symbols to keep it fast.
            # Remove the line below to run on the full universe.
            test_symbols = test_symbols[:10]

            print(
                f"\n--- Running Backtest on {len(test_symbols)} symbols from '{UNIVERSE_FILE}' ---"
            )

            backtester = Backtester(
                symbols=test_symbols,
                start_date="2021-01-01",
                end_date="2023-12-31",
                hold_period=5,
            )
            try:
                backtester.run()
            finally:
                backtester.close()
