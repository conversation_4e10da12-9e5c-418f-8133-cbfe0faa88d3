{"cells": [{"cell_type": "markdown", "id": "0b65ef79", "metadata": {}, "source": ["# Usage\n", "<hr/>\n", "\n", "<a target=\"_blank\" href=\"https://colab.research.google.com/github/dgunning/edgartools/blob/main/Usage.ipynb\">\n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/>\n", "</a>"]}, {"cell_type": "markdown", "id": "dcb71bc6", "metadata": {}, "source": ["# Install\n", "<hr/>"]}, {"cell_type": "code", "execution_count": 1, "id": "081ed4f8", "metadata": {}, "source": ["!pip install -U edgartools"], "outputs": []}, {"cell_type": "markdown", "id": "6973612d", "metadata": {}, "source": ["# Imports\n", "<hr/>"]}, {"cell_type": "code", "execution_count": 1, "id": "8702904d", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:33.216847Z", "end_time": "2023-04-10T14:14:38.271615Z"}}, "source": ["from edgar import *\n", "import pandas as pd"], "outputs": []}, {"cell_type": "markdown", "id": "723cc193", "metadata": {}, "source": ["# Set identity"]}, {"cell_type": "code", "execution_count": 2, "id": "68614b4f", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:37.870676Z", "end_time": "2023-04-10T14:14:38.365045Z"}}, "source": ["set_identity('<PERSON><PERSON><PERSON> <EMAIL>')"], "outputs": []}, {"cell_type": "markdown", "id": "f24d6abc", "metadata": {}, "source": ["# Get Filings\n", "<hr/>"]}, {"cell_type": "markdown", "id": "64d90b0f", "metadata": {}, "source": ["## Get filings with default values"]}, {"cell_type": "code", "execution_count": 3, "id": "4c46179a", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:37.909742Z", "end_time": "2023-04-10T14:14:38.959738Z"}}, "source": ["filings = get_filings()"], "outputs": []}, {"cell_type": "code", "execution_count": 4, "id": "80c6aec0", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:38.849019Z", "end_time": "2023-04-10T14:14:39.244210Z"}}, "source": ["filings"], "outputs": []}, {"cell_type": "markdown", "id": "7ac27af3", "metadata": {}, "source": ["## Get next page of filings"]}, {"cell_type": "code", "execution_count": 5, "id": "a7255a86", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:39.120604Z", "end_time": "2023-04-10T14:14:39.495245Z"}}, "source": ["filings.next()"], "outputs": []}, {"cell_type": "markdown", "id": "5c8b9211", "metadata": {}, "source": ["## Get previous page of filings"]}, {"cell_type": "code", "execution_count": 6, "id": "c1e3f860", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:39.324650Z", "end_time": "2023-04-10T14:14:39.938416Z"}}, "source": "filings.previous()", "outputs": []}, {"cell_type": "markdown", "id": "d1af015e", "metadata": {}, "source": ["## Get filings for year"]}, {"cell_type": "code", "execution_count": 7, "id": "29ba5473", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:39.508716Z", "end_time": "2023-04-10T14:14:47.687278Z"}}, "source": ["filings = get_filings(year=2022)\n", "filings"], "outputs": []}, {"cell_type": "markdown", "id": "c7971628", "metadata": {}, "source": ["## Get filings for specific form or forms"]}, {"cell_type": "code", "execution_count": 8, "id": "be5fbb8a", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:47.563169Z", "end_time": "2023-04-10T14:14:48.412980Z"}}, "source": ["filings_6k = get_filings(form=\"6-K\")\n", "filings_6k"], "outputs": []}, {"cell_type": "markdown", "id": "1e0d6802", "metadata": {}, "source": ["### Getting filings for a form not including amendments"]}, {"cell_type": "markdown", "id": "0c9432ae", "metadata": {}, "source": ["By default getting filings for a form type also includes amendments to that form type"]}, {"cell_type": "code", "execution_count": 9, "id": "dbca49ab", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:48.400315Z", "end_time": "2023-04-10T14:14:48.830311Z"}}, "source": ["filings_6k.filter(form=\"6-K/A\")"], "outputs": []}, {"cell_type": "markdown", "id": "0c19bd83", "metadata": {}, "source": ["To omit amendments set `amendments=False`"]}, {"cell_type": "code", "execution_count": 10, "id": "4592916b", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:48.495081Z", "end_time": "2023-04-10T14:14:49.186421Z"}}, "source": ["filings_6k = get_filings(form=\"6-K\", amendments=False)\n", "filings_6k.filter(form=\"6-K/A\")"], "outputs": []}, {"cell_type": "markdown", "id": "ec2bd12b", "metadata": {}, "source": ["### Getting filings for a list of forms"]}, {"cell_type": "markdown", "id": "1ecb475f", "metadata": {}, "source": ["The `form` parameter also accepts a list of forms "]}, {"cell_type": "code", "execution_count": 11, "id": "c94354ca", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:49.001338Z", "end_time": "2023-04-10T14:14:49.692243Z"}}, "source": ["filings = get_filings(form=[\"S-3\", \"424B4\"])\n", "filings"], "outputs": []}, {"cell_type": "markdown", "id": "7714bf89", "metadata": {}, "source": ["## Get the filings as a pandas dataframe\n", "You can get the filings data as a pandas dataframe using `to_pandas()`"]}, {"cell_type": "code", "execution_count": 12, "id": "660bdc30", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:49.658183Z", "end_time": "2023-04-10T14:14:49.856869Z"}}, "source": ["filings.to_pandas()"], "outputs": []}, {"cell_type": "markdown", "id": "8e504f4d", "metadata": {}, "source": ["### Select the columns included in to_pandas\n", "You can select the columns of the resulting pandas dataframe by passing it into `to_pandas`"]}, {"cell_type": "code", "execution_count": 13, "id": "8e9478a3", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:49.726897Z", "end_time": "2023-04-10T14:14:50.140018Z"}}, "source": ["filings.to_pandas('form', 'company')"], "outputs": []}, {"cell_type": "markdown", "id": "632a69ef", "metadata": {}, "source": ["## Get the underlying filings data as a pyarrow Table\n", "\n", "The filings data is kept as a `pyarrow.Table` which you can access using `.data`"]}, {"cell_type": "code", "execution_count": 14, "id": "3440ea85", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:49.787945Z", "end_time": "2023-04-10T14:14:50.170005Z"}}, "source": ["filings.data"], "outputs": []}, {"cell_type": "markdown", "id": "e7319254", "metadata": {}, "source": ["# Get Filing\n", "<hr/>"]}, {"cell_type": "code", "execution_count": 15, "id": "5e552182", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:49.815645Z", "end_time": "2023-04-10T14:14:50.329794Z"}}, "source": ["filing = filings[10]\n", "filing"], "outputs": []}, {"cell_type": "markdown", "id": "e6622f10", "metadata": {}, "source": ["## Get filing homepage"]}, {"cell_type": "code", "execution_count": 16, "id": "da82d706", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:50.050123Z", "end_time": "2023-04-10T14:14:51.724787Z"}}, "source": ["filing.homepage"], "outputs": []}, {"cell_type": "markdown", "id": "8faee529", "metadata": {}, "source": ["## Get filing Xbrl"]}, {"cell_type": "code", "execution_count": 17, "id": "d51e4043", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:51.050223Z", "end_time": "2023-04-10T14:14:52.957852Z"}}, "source": ["filings_8k = get_filings(form=\"8-K\")\n", "eight_k_filing =  filings_8k[0]\n", "eight_k_filing.xbrl()"], "outputs": []}, {"cell_type": "markdown", "id": "03aef2a2", "metadata": {}, "source": ["## Get the filing markdown"]}, {"cell_type": "code", "execution_count": 18, "id": "013c87df", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:52.290879Z", "end_time": "2023-04-10T14:14:53.023987Z"}}, "source": ["md = eight_k_filing.markdown()\n", "\n", "for line in md.split(\"\\n\")[:40]:\n", "    print(line)"], "outputs": []}, {"cell_type": "markdown", "id": "07308a19", "metadata": {}, "source": ["## View the filing markdown"]}, {"cell_type": "code", "execution_count": 19, "id": "8cb73b17", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:53.536495Z", "end_time": "2023-04-10T14:14:54.555649Z"}}, "source": ["eight_k_filing.view()"], "outputs": []}, {"cell_type": "markdown", "id": "e35ef2dc", "metadata": {}, "source": ["# Company\n", "<hr/>"]}, {"cell_type": "markdown", "id": "9354e375", "metadata": {}, "source": ["## Get company by cik"]}, {"cell_type": "code", "execution_count": 20, "id": "34308a3b", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:54.939744Z", "end_time": "2023-04-10T14:14:55.824254Z"}}, "source": ["Company(311337)"], "outputs": []}, {"cell_type": "markdown", "id": "276d97f4", "metadata": {}, "source": ["## Get company by ticker"]}, {"cell_type": "code", "execution_count": 21, "id": "33631a44", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:57.067797Z", "end_time": "2023-04-10T14:14:57.748457Z"}}, "source": ["company = Company(\"SNOW\")\n", "company"], "outputs": []}, {"cell_type": "code", "execution_count": 22, "id": "c3d64d97", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:14:57.498358Z", "end_time": "2023-04-10T14:14:57.866962Z"}}, "source": ["company.get_filings()"], "outputs": []}, {"cell_type": "markdown", "source": ["## Company Financials"], "metadata": {"collapsed": false}, "id": "da493f68cdfcea2e"}, {"cell_type": "code", "execution_count": 23, "source": ["financials = company.financials\n", "financials"], "metadata": {"collapsed": false, "ExecuteTime": {"start_time": "2023-04-10T14:15:06.138381Z", "end_time": "2023-04-10T14:15:07.763916Z"}}, "id": "e2224246663ed69a", "outputs": []}, {"cell_type": "markdown", "id": "1dc2db35", "metadata": {}, "source": ["### Properties of the company"]}, {"cell_type": "code", "execution_count": 24, "id": "212d6dde", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:16.802921Z", "end_time": "2023-04-10T14:15:17.005652Z"}}, "source": ["pd.DataFrame([\n", "    {'property': f\"company.{property}\", 'value':getattr(company, property)} \n", "         for property in ['name', 'cik', 'tickers', 'exchanges', 'category', 'industry', 'sic', 'entity_type'  ]]\n", ").set_index(\"property\")"], "outputs": []}, {"cell_type": "markdown", "id": "0c2049a2", "metadata": {}, "source": ["# Effect filings\n", "<hr/>"]}, {"cell_type": "code", "execution_count": 25, "id": "aaa196cd", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:18.454481Z", "end_time": "2023-04-10T14:15:19.269825Z"}}, "source": ["effect_filings = get_filings(form=\"EFFECT\")\n", "effect_filings"], "outputs": []}, {"cell_type": "markdown", "id": "00b4ded1", "metadata": {}, "source": ["## Show effect filing"]}, {"cell_type": "code", "execution_count": 26, "id": "37ce8b75", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:20.403165Z", "end_time": "2023-04-10T14:15:20.697992Z"}}, "source": ["effect_filing = effect_filings[0]\n", "effect_filing"], "outputs": []}, {"cell_type": "markdown", "id": "ea77bbaa", "metadata": {}, "source": ["## Effect class"]}, {"cell_type": "code", "execution_count": 27, "id": "e3e94295", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:24.145121Z", "end_time": "2023-04-10T14:15:25.326686Z"}}, "source": ["Effect.from_xml(effect_filing.xml())"], "outputs": []}, {"cell_type": "markdown", "id": "96c87b98", "metadata": {}, "source": ["# Ownership filings\n", "<hr/>"]}, {"cell_type": "code", "execution_count": 28, "id": "77ff20de", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:25.330820Z", "end_time": "2023-04-10T14:15:26.524869Z"}}, "source": ["ownership_filings = get_filings(form=[\"3\", 4, \"5\"])\n", "form_4_filings = ownership_filings.filter(form=\"4\")"], "outputs": []}, {"cell_type": "code", "execution_count": 29, "id": "278e54b1", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:25.919688Z", "end_time": "2023-04-10T14:15:26.528866Z"}}, "source": ["form_4_filing = form_4_filings[4]\n", "form_4_filing"], "outputs": []}, {"cell_type": "markdown", "id": "ca878804", "metadata": {}, "source": ["## Show Ownership"]}, {"cell_type": "code", "execution_count": 30, "id": "d181bfc5", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:26.193899Z", "end_time": "2023-04-10T14:15:27.258720Z"}}, "source": ["Ownership.from_xml(form_4_filing.xml())"], "outputs": []}, {"cell_type": "markdown", "id": "411b7135", "metadata": {}, "source": ["# Fund Filings\n", "\n", "<hr/>"]}, {"cell_type": "code", "execution_count": 31, "id": "97cc7167", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:27.011856Z", "end_time": "2023-04-10T14:15:27.928227Z"}}, "source": ["funds = get_funds()\n", "funds"], "outputs": []}, {"cell_type": "markdown", "id": "6b2a3ce0", "metadata": {}, "source": ["## Fund Report"]}, {"cell_type": "code", "execution_count": 32, "id": "cb7df50e", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:27.704714Z", "end_time": "2023-04-10T14:15:32.200644Z"}}, "source": ["FundReport.from_xml(funds[1].xml())"], "outputs": []}, {"cell_type": "markdown", "id": "7cfb9ae2", "metadata": {}, "source": ["# Offering"]}, {"cell_type": "code", "execution_count": 33, "id": "285c724a", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:32.184997Z", "end_time": "2023-04-10T14:15:32.901934Z"}}, "source": ["offering_filings = get_filings(form=\"D\")\n", "offering_filings"], "outputs": []}, {"cell_type": "code", "execution_count": 34, "id": "4fcf7ccf", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:34.454132Z", "end_time": "2023-04-10T14:15:34.479365Z"}}, "source": ["offering_filings[0]"], "outputs": []}, {"cell_type": "code", "execution_count": 35, "id": "52f06c6c", "metadata": {"ExecuteTime": {"start_time": "2023-04-10T14:15:34.482790Z", "end_time": "2023-04-10T14:15:35.234139Z"}}, "source": ["Offering.from_xml(offering_filings[0].xml())"], "outputs": []}, {"cell_type": "code", "execution_count": 35, "source": [], "metadata": {"collapsed": false, "ExecuteTime": {"start_time": "2023-04-10T14:15:35.167774Z", "end_time": "2023-04-10T14:15:35.280214Z"}}, "id": "ceee4f53b6e25258", "outputs": []}, {"cell_type": "code", "execution_count": null, "source": [], "metadata": {"collapsed": false}, "id": "3884e4b6d4585c06", "outputs": []}], "metadata": {"kernelspec": {"display_name": "<PERSON>", "language": "python", "name": "edgartools"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}}, "nbformat": 4, "nbformat_minor": 5}