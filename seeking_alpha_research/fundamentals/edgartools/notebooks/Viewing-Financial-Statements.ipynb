{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["# Financials Statements\n", "\n", "This notebook demonstrates how to get financial statements from the SEC Edgar database using the `edgar` package.\n", "\n", "**[Open this notebook in Google Colab](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/Viewing-Financial-Statements.ipynb)**"], "id": "83e75eb6785bc307"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "!pip install edgartools", "id": "3757be49b8baba13"}, {"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2024-11-14T14:53:43.341472Z", "start_time": "2024-11-14T14:53:43.335571Z"}}, "source": ["from edgar import *\n", "\n", "set_identity('<EMAIL>')"], "outputs": [{"data": {"text/plain": ["\u001b[2;36m[09:53:43]\u001b[0m\u001b[2;36m \u001b[0m\u001b[34mINFO    \u001b[0m Identity of the Edgar REST client set to \u001b[1m[\u001b[<EMAIL>\u001b[1m]\u001b[0m                            \u001b]8;id=49368;file:///Users/<USER>/PycharmProjects/edgartools/edgar/core.py\u001b\\\u001b[2mcore.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=579254;file:///Users/<USER>/PycharmProjects/edgartools/edgar/core.py#163\u001b\\\u001b[2m163\u001b[0m\u001b]8;;\u001b\\\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[09:53:43] </span><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> Identity of the Edgar REST client set to <span style=\"font-weight: bold\">[</span><EMAIL><span style=\"font-weight: bold\">]</span>                            <a href=\"file:///Users/<USER>/PycharmProjects/edgartools/edgar/core.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">core.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///Users/<USER>/PycharmProjects/edgartools/edgar/core.py#163\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">163</span></a>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 4}, {"metadata": {}, "cell_type": "markdown", "source": ["## Get a company by ticker\n", "\n", "To start let's get a company by ticker"], "id": "ebf7dcd8f569e536"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-14T14:55:06.207760Z", "start_time": "2024-11-14T14:55:06.202992Z"}}, "cell_type": "code", "source": ["c = Company(\"TSLA\")\n", "c"], "id": "29c4db0645860fbb", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────────────────────────────────────\u001b[1;38;5;71m Tesla, Inc. (TSLA) \u001b[0m───────────────────────────────────────────────╮\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mCIK    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCategory               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIndustry                             \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIncorporated\u001b[0m\u001b[1m \u001b[0m                     │\n", "│  ──────────────────────────────────────────────────────────────────────────────────────────                     │\n", "│  \u001b[1;35m \u001b[0m\u001b[1;35m1318605\u001b[0m\u001b[1;35m \u001b[0m  Large accelerated filer   Motor Vehicles & Passenger Car Bodies   Delaware                          │\n", "│                                                                                                                 │\n", "│ ╭───────── ✉ Mailing Address ──────────╮                 ╭──────── 🏢 Business Address ─────────╮               │\n", "│ │ 3500 DEER CREEK RD                   │                 │ 3500 DEER CREEK RD                   │               │\n", "│ │ PALO ALTO, CA 94304                  │                 │ PALO ALTO, CA 94304                  │               │\n", "│ ╰──────────────────────────────────────╯                 ╰──────────────────────────────────────╯               │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"metadata": {}, "cell_type": "markdown", "source": ["### Get the latest Financials\n", "Financial data is included with **10-K** and **10-Q** filings as XBRL attachments. **edgartools** allows you to access the latest 10-K financials directly from the company\n"], "id": "14a06380e401e770"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-14T14:59:19.592685Z", "start_time": "2024-11-14T14:59:18.539647Z"}}, "cell_type": "code", "source": ["financials = c.financials\n", "financials"], "id": "3c827af01d94cf7b", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["               \u001b[1;38;5;39mTesla, Inc.\u001b[0m Financials                \n", "╭───┬───────────────────────────────────────────────╮\n", "│ 1 │ \u001b[1mCover Page                                   \u001b[0m │\n", "│ 2 │ \u001b[1mConsolidated Balance Sheets                  \u001b[0m │\n", "│ 3 │ \u001b[1mIncome Statements                            \u001b[0m │\n", "│ 4 │ \u001b[1mConsolidated Statement of Cash Flows         \u001b[0m │\n", "│ 5 │ \u001b[1mConsolidated Statement of Shareholders Equity\u001b[0m │\n", "│ 6 │ \u001b[1mComprehensive Income Statement               \u001b[0m │\n", "╰───┴───────────────────────────────────────────────╯\n", "           \u001b[38;5;249m<PERSON>eriod ending \u001b[0m\u001b[1mDecember 31, 2023\u001b[0m           "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "execution_count": 8}, {"metadata": {}, "cell_type": "markdown", "source": "The `Financials` object gives you quick access to the most common standard financial statements for our company. ", "id": "1cc8ae35323b48af"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-14T15:10:43.011350Z", "start_time": "2024-11-14T15:10:42.997631Z"}}, "cell_type": "code", "source": "financials.balance_sheet", "id": "9e9977ccf47fbb7b", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["                                      \u001b[1;38;5;38mTesla, Inc.\u001b[0m                                       \n", "                              \u001b[1mConsolidated Balance Sheets\u001b[0m                               \n", "                                         \u001b[3;38;5;250mAnnual\u001b[0m                                         \n", "                                                                                        \n", " \u001b[1m \u001b[0m\u001b[1m                                                  \u001b[0m\u001b[1m \u001b[0m \u001b[1m        \u001b[0m\u001b[1m \u001b[0m \u001b[1m      2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m      2022\u001b[0m\u001b[1m \u001b[0m \n", " ────────────────────────────────────────────────────────────────────────────────────── \n", "  \u001b[37m    Cash and cash equivalents                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      16,398      16,253  \n", "  \u001b[37m    Short-term investments                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      12,696       5,932  \n", "  \u001b[37m    Accounts receivable, net                      \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       3,508       2,952  \n", "  \u001b[37m    Inventory                                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      13,626      12,839  \n", "  \u001b[37m    Prepaid expenses and other current assets     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       3,388       2,941  \n", "  \u001b[1;38;5;32m    Total current assets                          \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    49,616\u001b[0m  \u001b[1;38;5;32m    40,917\u001b[0m  \n", "  \u001b[37m  Operating lease vehicles, net                   \u001b[0m  \u001b[2;38;5;249m        \u001b[0m                          \n", "  \u001b[37m    Operating Lease Vehicles                      \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       5,989       5,035  \n", "  \u001b[37m  Solar energy systems, net                       \u001b[0m  \u001b[2;38;5;249m        \u001b[0m                          \n", "  \u001b[37m    Solar energy systems, net                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       5,229       5,489  \n", "  \u001b[37m  Property, plant and equipment, net              \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      29,725      23,548  \n", "  \u001b[37m  Operating lease right-of-use assets             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       4,180       2,563  \n", "  \u001b[37m  Digital assets, net                             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         184         184  \n", "  \u001b[37m  Intangible assets, net                          \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         178         215  \n", "  \u001b[37m  <PERSON><PERSON>                                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         253         194  \n", "  \u001b[37m  Deferred tax assets                             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       6,733         328  \n", "  \u001b[37m  Other non-current assets                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       4,531       3,865  \n", "  \u001b[1;38;5;32m  Total assets                                    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m   106,618\u001b[0m  \u001b[1;38;5;32m    82,338\u001b[0m  \n", "  \u001b[37m    Accounts payable                              \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      14,431      15,255  \n", "  \u001b[37m    Accrued liabilities and other                 \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       9,080       8,205  \n", "  \u001b[37m    Deferred revenue                              \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       2,864       1,747  \n", "  \u001b[37m    Current portion of debt and finance leases    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       2,373       1,502  \n", "  \u001b[1;38;5;32m    Total current liabilities                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    28,748\u001b[0m  \u001b[1;38;5;32m    26,709\u001b[0m  \n", "  \u001b[37m  Debt and finance leases, net of current portion \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       2,857       1,597  \n", "  \u001b[37m  Deferred revenue, net of current portion        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       3,251       2,804  \n", "  \u001b[37m  Other long-term liabilities                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       8,153       5,330  \n", "  \u001b[1;38;5;32m  Total liabilities                               \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    43,009\u001b[0m  \u001b[1;38;5;32m    36,440\u001b[0m  \n", "  \u001b[37mCommitments and contingencies (Note 15)           \u001b[0m  \u001b[2;38;5;249m        \u001b[0m                          \n", "  \u001b[37mRedeemable noncontrolling interests in            \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         242         409  \n", "  \u001b[37msubsidiaries                                      \u001b[0m                                    \n", "  \u001b[37m    Preferred stock; $0.001 par value; 100 shares \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m           0           0  \n", "  \u001b[37mauthorized; no shares issued and outstanding      \u001b[0m                                    \n", "  \u001b[37m    Common stock; $0.001 par value; 6,000 shares  \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m           3           3  \n", "  \u001b[37mauthorized; 3,185 and 3,164 shares issued and     \u001b[0m                                    \n", "  \u001b[37moutstanding as of December 31, 2023 and 2022,     \u001b[0m                                    \n", "  \u001b[37mrespectively                                      \u001b[0m                                    \n", "  \u001b[37m    Additional paid-in capital                    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      34,892      32,177  \n", "  \u001b[37m    Accumulated other comprehensive loss          \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (     143)  (     361)  \n", "  \u001b[37m    Retained earnings                             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      27,882      12,885  \n", "  \u001b[1;38;5;32m    Total stockholders’ equity                    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    62,634\u001b[0m  \u001b[1;38;5;32m    44,704\u001b[0m  \n", "  \u001b[37m  Noncontrolling interests in subsidiaries        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         733         785  \n", "  \u001b[1;38;5;32mTotal liabilities and equity                      \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m   106,618\u001b[0m  \u001b[1;38;5;32m    82,338\u001b[0m  \n", "                                                                                        "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "execution_count": 12}, {"metadata": {}, "cell_type": "markdown", "source": "#### Balance Sheet", "id": "30ae02ba78d7c91c"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-14T15:10:55.965077Z", "start_time": "2024-11-14T15:10:55.951864Z"}}, "cell_type": "code", "source": "financials.balance_sheet", "id": "d42ba84013a8bed0", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["                                      \u001b[1;38;5;38mTesla, Inc.\u001b[0m                                       \n", "                              \u001b[1mConsolidated Balance Sheets\u001b[0m                               \n", "                                         \u001b[3;38;5;250mAnnual\u001b[0m                                         \n", "                                                                                        \n", " \u001b[1m \u001b[0m\u001b[1m                                                  \u001b[0m\u001b[1m \u001b[0m \u001b[1m        \u001b[0m\u001b[1m \u001b[0m \u001b[1m      2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m      2022\u001b[0m\u001b[1m \u001b[0m \n", " ────────────────────────────────────────────────────────────────────────────────────── \n", "  \u001b[37m    Cash and cash equivalents                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      16,398      16,253  \n", "  \u001b[37m    Short-term investments                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      12,696       5,932  \n", "  \u001b[37m    Accounts receivable, net                      \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       3,508       2,952  \n", "  \u001b[37m    Inventory                                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      13,626      12,839  \n", "  \u001b[37m    Prepaid expenses and other current assets     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       3,388       2,941  \n", "  \u001b[1;38;5;32m    Total current assets                          \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    49,616\u001b[0m  \u001b[1;38;5;32m    40,917\u001b[0m  \n", "  \u001b[37m  Operating lease vehicles, net                   \u001b[0m  \u001b[2;38;5;249m        \u001b[0m                          \n", "  \u001b[37m    Operating Lease Vehicles                      \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       5,989       5,035  \n", "  \u001b[37m  Solar energy systems, net                       \u001b[0m  \u001b[2;38;5;249m        \u001b[0m                          \n", "  \u001b[37m    Solar energy systems, net                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       5,229       5,489  \n", "  \u001b[37m  Property, plant and equipment, net              \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      29,725      23,548  \n", "  \u001b[37m  Operating lease right-of-use assets             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       4,180       2,563  \n", "  \u001b[37m  Digital assets, net                             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         184         184  \n", "  \u001b[37m  Intangible assets, net                          \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         178         215  \n", "  \u001b[37m  <PERSON><PERSON>                                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         253         194  \n", "  \u001b[37m  Deferred tax assets                             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       6,733         328  \n", "  \u001b[37m  Other non-current assets                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       4,531       3,865  \n", "  \u001b[1;38;5;32m  Total assets                                    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m   106,618\u001b[0m  \u001b[1;38;5;32m    82,338\u001b[0m  \n", "  \u001b[37m    Accounts payable                              \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      14,431      15,255  \n", "  \u001b[37m    Accrued liabilities and other                 \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       9,080       8,205  \n", "  \u001b[37m    Deferred revenue                              \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       2,864       1,747  \n", "  \u001b[37m    Current portion of debt and finance leases    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       2,373       1,502  \n", "  \u001b[1;38;5;32m    Total current liabilities                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    28,748\u001b[0m  \u001b[1;38;5;32m    26,709\u001b[0m  \n", "  \u001b[37m  Debt and finance leases, net of current portion \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       2,857       1,597  \n", "  \u001b[37m  Deferred revenue, net of current portion        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       3,251       2,804  \n", "  \u001b[37m  Other long-term liabilities                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       8,153       5,330  \n", "  \u001b[1;38;5;32m  Total liabilities                               \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    43,009\u001b[0m  \u001b[1;38;5;32m    36,440\u001b[0m  \n", "  \u001b[37mCommitments and contingencies (Note 15)           \u001b[0m  \u001b[2;38;5;249m        \u001b[0m                          \n", "  \u001b[37mRedeemable noncontrolling interests in            \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         242         409  \n", "  \u001b[37msubsidiaries                                      \u001b[0m                                    \n", "  \u001b[37m    Preferred stock; $0.001 par value; 100 shares \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m           0           0  \n", "  \u001b[37mauthorized; no shares issued and outstanding      \u001b[0m                                    \n", "  \u001b[37m    Common stock; $0.001 par value; 6,000 shares  \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m           3           3  \n", "  \u001b[37mauthorized; 3,185 and 3,164 shares issued and     \u001b[0m                                    \n", "  \u001b[37moutstanding as of December 31, 2023 and 2022,     \u001b[0m                                    \n", "  \u001b[37mrespectively                                      \u001b[0m                                    \n", "  \u001b[37m    Additional paid-in capital                    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      34,892      32,177  \n", "  \u001b[37m    Accumulated other comprehensive loss          \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (     143)  (     361)  \n", "  \u001b[37m    Retained earnings                             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      27,882      12,885  \n", "  \u001b[1;38;5;32m    Total stockholders’ equity                    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    62,634\u001b[0m  \u001b[1;38;5;32m    44,704\u001b[0m  \n", "  \u001b[37m  Noncontrolling interests in subsidiaries        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         733         785  \n", "  \u001b[1;38;5;32mTotal liabilities and equity                      \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m   106,618\u001b[0m  \u001b[1;38;5;32m    82,338\u001b[0m  \n", "                                                                                        "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "execution_count": 13}, {"metadata": {}, "cell_type": "markdown", "source": "#### Income Statement", "id": "be85cc82d79c4662"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-14T15:12:01.912912Z", "start_time": "2024-11-14T15:12:01.900630Z"}}, "cell_type": "code", "source": "financials.income", "id": "3cd2edbb6b2cd909", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["                                            \u001b[1;38;5;38mTesla, Inc.\u001b[0m                                             \n", "                                         \u001b[1mIncome Statements\u001b[0m                                          \n", "                                               \u001b[3;38;5;250mAnnual\u001b[0m                                               \n", "                                                                                                    \n", " \u001b[1m \u001b[0m\u001b[1m                                                  \u001b[0m\u001b[1m \u001b[0m \u001b[1m        \u001b[0m\u001b[1m \u001b[0m \u001b[1m      2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m      2022\u001b[0m\u001b[1m \u001b[0m \u001b[1m      2021\u001b[0m\u001b[1m \u001b[0m \n", " ────────────────────────────────────────────────────────────────────────────────────────────────── \n", "  \u001b[37m  Revenues                                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      96,773      81,462      53,823  \n", "  \u001b[37m    Automotive Revenues                           \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      82,419      71,462      47,232  \n", "  \u001b[37m    Energy generation and storage                 \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       6,035       3,909       2,789  \n", "  \u001b[37m    Services and other                            \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       8,319       6,091       3,802  \n", "  \u001b[37m  Total cost of revenues                          \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (  79,113)  (  60,609)  (  40,217)  \n", "  \u001b[37m    Automotive Revenues                           \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (  66,389)  (  51,108)  (  33,393)  \n", "  \u001b[37m    Energy generation and storage                 \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (   4,894)  (   3,621)  (   2,918)  \n", "  \u001b[37m    Services and other                            \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (   7,830)  (   5,880)  (   3,906)  \n", "  \u001b[1;38;5;32mGross profit                                      \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    17,660\u001b[0m  \u001b[1;38;5;32m    20,853\u001b[0m  \u001b[1;38;5;32m    13,606\u001b[0m  \n", "  \u001b[37m  Research and development                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       3,969       3,075       2,593  \n", "  \u001b[37m  Selling, general and administrative             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       4,800       3,946       4,517  \n", "  \u001b[37m  Restructuring and other                         \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m           0         176  (      27)  \n", "  \u001b[1;38;5;32m  Total operating expenses                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m(   8,769)\u001b[0m  \u001b[1;38;5;32m(   7,197)\u001b[0m  \u001b[1;38;5;32m(   7,083)\u001b[0m  \n", "  \u001b[1;38;5;32m<PERSON><PERSON><PERSON> from operations                            \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m     8,891\u001b[0m  \u001b[1;38;5;32m    13,656\u001b[0m  \u001b[1;38;5;32m     6,523\u001b[0m  \n", "  \u001b[37mInterest income                                   \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       1,066         297          56  \n", "  \u001b[37mInterest expense                                  \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (     156)  (     191)  (     371)  \n", "  \u001b[37mOther income (expense), net                       \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         172  (      43)         135  \n", "  \u001b[1;38;5;32mIncome before income taxes                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m     9,973\u001b[0m  \u001b[1;38;5;32m    13,719\u001b[0m  \u001b[1;38;5;32m     6,343\u001b[0m  \n", "  \u001b[37m(Benefit from) provision for income taxes         \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (   5,000)  (   1,130)  (     699)  \n", "  \u001b[1;38;5;32mNet income                                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    14,974\u001b[0m  \u001b[1;38;5;32m    12,587\u001b[0m  \u001b[1;38;5;32m     5,644\u001b[0m  \n", "  \u001b[37mNet (loss) income attributable to noncontrolling  \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (      23)  (      31)  (     125)  \n", "  \u001b[37minterests and redeemable noncontrolling interests \u001b[0m                                                \n", "  \u001b[37min subsidiaries                                   \u001b[0m                                                \n", "  \u001b[1;38;5;32mNet income attributable to common stockholders    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    14,997\u001b[0m  \u001b[1;38;5;32m    12,556\u001b[0m  \u001b[1;38;5;32m     5,519\u001b[0m  \n", "  \u001b[37m  Basic (in dollars per share)                    \u001b[0m  \u001b[2;38;5;249m        \u001b[0m        4.73        4.02        1.87  \n", "  \u001b[37m  Diluted (in dollars per share)                  \u001b[0m  \u001b[2;38;5;249m        \u001b[0m         4.3        3.62        1.63  \n", "  \u001b[37m  Basic (in shares)                               \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       3,174       3,130       2,959  \n", "  \u001b[37m  Diluted (in shares)                             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       3,485       3,475       3,386  \n", "                                                                                                    "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "execution_count": 15}, {"metadata": {}, "cell_type": "markdown", "source": "#### Cash Flow", "id": "5a875791edf2ecc3"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-14T15:11:06.939827Z", "start_time": "2024-11-14T15:11:06.763783Z"}}, "cell_type": "code", "source": "financials.cashflow", "id": "febcc6053aa873aa", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["                                            \u001b[1;38;5;38mTesla, Inc.\u001b[0m                                             \n", "                                \u001b[1mConsolidated Statement of Cash Flows\u001b[0m                                \n", "                                               \u001b[3;38;5;250mAnnual\u001b[0m                                               \n", "                                                                                                    \n", " \u001b[1m \u001b[0m\u001b[1m                                                  \u001b[0m\u001b[1m \u001b[0m \u001b[1m        \u001b[0m\u001b[1m \u001b[0m \u001b[1m      2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m      2022\u001b[0m\u001b[1m \u001b[0m \u001b[1m      2021\u001b[0m\u001b[1m \u001b[0m \n", " ────────────────────────────────────────────────────────────────────────────────────────────────── \n", "  \u001b[37m  Net income                                      \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      14,974      12,587       5,644  \n", "  \u001b[37m    Depreciation, amortization and impairment     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       4,667       3,747       2,911  \n", "  \u001b[37m    Stock-based compensation                      \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       1,812       1,560       2,121  \n", "  \u001b[37m    Inventory and purchase commitments write-downs\u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         463         177         140  \n", "  \u001b[37m    Foreign currency transaction net unrealized   \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (     144)  (      81)  (      55)  \n", "  \u001b[37m(gain) loss                                       \u001b[0m                                                \n", "  \u001b[37m    Deferred income taxes                         \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (   6,349)  (     196)  (     149)  \n", "  \u001b[37m    Non-cash interest and other operating         \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (      81)  (     340)  (     245)  \n", "  \u001b[37mactivities                                        \u001b[0m                                                \n", "  \u001b[37m    Digital assets loss (gain), net               \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m          -0  (     140)  (      27)  \n", "  \u001b[37m      Accounts receivable                         \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (     586)  (   1,124)  (     130)  \n", "  \u001b[37m      Inventory                                   \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (   1,195)  (   6,465)  (   1,709)  \n", "  \u001b[37m      Operating lease vehicles                    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (   1,952)  (   1,570)  (   2,114)  \n", "  \u001b[37m      Prepaid expenses and other assets           \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (   2,652)  (   3,713)  (   1,540)  \n", "  \u001b[37m      Accounts payable, accrued and other         \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       2,605       8,029       5,367  \n", "  \u001b[37mliabilities                                       \u001b[0m                                                \n", "  \u001b[37m      Deferred revenue                            \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       1,532       1,131         793  \n", "  \u001b[1;38;5;32m  Net cash provided by operating activities       \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    13,256\u001b[0m  \u001b[1;38;5;32m    14,724\u001b[0m  \u001b[1;38;5;32m    11,497\u001b[0m  \n", "  \u001b[37m  Purchases of property and equipment excluding   \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (   8,898)  (   7,158)  (   6,482)  \n", "  \u001b[37mfinance leases, net of sales                      \u001b[0m                                                \n", "  \u001b[37m  Purchases of solar energy systems, net of sales \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (       1)  (       5)  (      32)  \n", "  \u001b[37m  Purchases of digital assets                     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m          -0          -0  (   1,500)  \n", "  \u001b[37m  Proceeds from sales of digital assets           \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m           0         936         272  \n", "  \u001b[37m  Purchase of intangible assets                   \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m          -0  (       9)          -0  \n", "  \u001b[37m  Purchases of investments                        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (  19,112)  (   5,835)  (     132)  \n", "  \u001b[37m  Proceeds from maturities of investments         \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m      12,353          22           0  \n", "  \u001b[37m  Proceeds from sales of investments              \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         138           0           0  \n", "  \u001b[37m  Receipt of government grants                    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m           0          76           6  \n", "  \u001b[37m  Business combinations, net of cash acquired     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (      64)          -0          -0  \n", "  \u001b[1;38;5;32m  Net cash used in investing activities           \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m(  15,584)\u001b[0m  \u001b[1;38;5;32m(  11,973)\u001b[0m  \u001b[1;38;5;32m(   7,868)\u001b[0m  \n", "  \u001b[37m  Proceeds from issuances of debt                 \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       3,931           0       8,883  \n", "  \u001b[37m  Repayments of debt                              \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (   1,351)  (   3,364)  (  14,167)  \n", "  \u001b[37m  Collateralized lease repayments                 \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m           0           0  (       9)  \n", "  \u001b[37m  Proceeds from exercises of stock options and    \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         700         541         707  \n", "  \u001b[37mother stock issuances                             \u001b[0m                                                \n", "  \u001b[37m  Principal payments on finance leases            \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (     464)  (     502)  (     439)  \n", "  \u001b[37m  Debt issuance costs                             \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (      29)          -0  (       9)  \n", "  \u001b[37m  Proceeds from investments by noncontrolling     \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m           0           0           2  \n", "  \u001b[37minterests in subsidiaries                         \u001b[0m                                                \n", "  \u001b[37m  Distributions paid to noncontrolling interests  \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (     144)  (     157)  (     161)  \n", "  \u001b[37min subsidiaries                                   \u001b[0m                                                \n", "  \u001b[37m  Payments for buy-outs of noncontrolling         \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  (      54)  (      45)  (      10)  \n", "  \u001b[37minterests in subsidiaries                         \u001b[0m                                                \n", "  \u001b[1;38;5;32m  Net cash provided by (used in) financing        \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m     2,589\u001b[0m  \u001b[1;38;5;32m(   3,527)\u001b[0m  \u001b[1;38;5;32m(   5,203)\u001b[0m  \n", "  \u001b[1;38;5;32mactivities                                        \u001b[0m                                                \n", "  \u001b[37mEffect of exchange rate changes on cash and cash  \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m           4  (     444)  (     183)  \n", "  \u001b[37mequivalents and restricted cash                   \u001b[0m                                                \n", "  \u001b[1;38;5;32mNet increase (decrease) in cash and cash          \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m       265\u001b[0m  \u001b[1;38;5;32m(   1,220)\u001b[0m  \u001b[1;38;5;32m(   1,757)\u001b[0m  \n", "  \u001b[1;38;5;32mequivalents and restricted cash                   \u001b[0m                                                \n", "  \u001b[1;38;5;32mCash and cash equivalents and restricted cash, end\u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m  \u001b[1;38;5;32m    17,189\u001b[0m  \u001b[1;38;5;32m    16,924\u001b[0m  \u001b[1;38;5;32m    18,144\u001b[0m  \n", "  \u001b[1;38;5;32mof period                                         \u001b[0m                                                \n", "  \u001b[37m  Acquisitions of property and equipment included \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       2,272       2,148       2,251  \n", "  \u001b[37min liabilities                                    \u001b[0m                                                \n", "  \u001b[37m  Cash paid during the period for interest, net of\u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m         126         152         266  \n", "  \u001b[37mamounts capitalized                               \u001b[0m                                                \n", "  \u001b[37m  Cash paid during the period for income taxes,   \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m       1,119       1,203         561  \n", "  \u001b[37mnet of refunds                                    \u001b[0m                                                \n", "                                                                                                    "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "execution_count": 14}, {"metadata": {}, "cell_type": "markdown", "source": ["### XBRL Data Object\n", "\n", "The Financials is a shorthand way to get access to the common financial statements, but XBRL data can also have many more statements that you may need to access. For this, you can use the XBRL data object. "], "id": "c30ba85a0a641267"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-14T15:13:10.256797Z", "start_time": "2024-11-14T15:13:10.235318Z"}}, "cell_type": "code", "source": ["xb = financials.xbrl_data\n", "xb"], "id": "8eca6ef94b026756", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭───────────────────────────────────────────────────── XBRL ──────────────────────────────────────────────────────╮\n", "│ \u001b[3m                  XBRL Instance                   \u001b[0m                                                              │\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mCompany    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mForm\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mPeriod           \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mFacts\u001b[0m\u001b[1m \u001b[0m                                                               │\n", "│  ────────────────────────────────────────────────                                                               │\n", "│   \u001b[1;38;5;32mTesla, Inc.\u001b[0m   10-K   December 31, 2023   1,583                                                                │\n", "│                                                                                                                 │\n", "│ \u001b[3m                          Financial Statements                          \u001b[0m                                        │\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mName                              \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mAccessor                       \u001b[0m\u001b[1m \u001b[0m                                         │\n", "│  ──────────────────────────────────────────────────────────────────────                                         │\n", "│  \u001b[1m \u001b[0m\u001b[1mBalance Sheet                     \u001b[0m\u001b[1m \u001b[0m  \u001b[3mfinancials.balance_sheet       \u001b[0m                                          │\n", "│  \u001b[1m \u001b[0m\u001b[1mIncome Statement                  \u001b[0m\u001b[1m \u001b[0m  \u001b[3mfinancials.income              \u001b[0m                                          │\n", "│  \u001b[1m \u001b[0m\u001b[1mCash Flow Statement               \u001b[0m\u001b[1m \u001b[0m  \u001b[3mfinancials.cashflow            \u001b[0m                                          │\n", "│  \u001b[1m \u001b[0m\u001b[1mStatement of Changes in Equity    \u001b[0m\u001b[1m \u001b[0m  \u001b[3mfinancials.equity              \u001b[0m                                          │\n", "│  \u001b[1m \u001b[0m\u001b[1mStatement of Comprehensive Income \u001b[0m\u001b[1m \u001b[0m  \u001b[3mfinancials.comprehensive_income\u001b[0m                                          │\n", "│                                                                                                                 │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "execution_count": 16}, {"metadata": {}, "cell_type": "markdown", "source": ["### All Statements\n", "\n", "To see all the statements that are included in the XBRL filing use the `statements` accessor. "], "id": "6244a135200c48"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-14T15:13:34.953699Z", "start_time": "2024-11-14T15:13:34.935032Z"}}, "cell_type": "code", "source": "xb.statements", "id": "e5dfef0013959114", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭─────┬───────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│\u001b[1m \u001b[0m\u001b[1m   \u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mStatements                                                                                               \u001b[0m\u001b[1m \u001b[0m│\n", "├─────┼───────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ 0   │ \u001b[38;5;32mCover\u001b[0m                                                                                                     │\n", "│ 1   │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mInformation\u001b[0m                                                                                          │\n", "│ 2   │ \u001b[38;5;32m<PERSON>onsolidated\u001b[0m\u001b[38;5;160mBalance\u001b[0m\u001b[38;5;71mSheets\u001b[0m                                                                                 │\n", "│ 3   │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mBalance\u001b[0m\u001b[38;5;71mSheets\u001b[0m\u001b[38;5;32mParenthetical\u001b[0m                                                                    │\n", "│ 4   │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mStatementsof\u001b[0m\u001b[38;5;71mOperations\u001b[0m                                                                        │\n", "│ 5   │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mStatementsof\u001b[0m\u001b[38;5;71mComprehensive\u001b[0m\u001b[38;5;32mI<PERSON><PERSON>\u001b[0m                                                               │\n", "│ 6   │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mStatementsof\u001b[0m\u001b[38;5;71mRedeemable\u001b[0m\u001b[38;5;32mNoncontrolling\u001b[0m\u001b[38;5;160mInterestand\u001b[0m\u001b[38;5;71mEquity\u001b[0m                                         │\n", "│ 7   │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mStatementsof\u001b[0m\u001b[38;5;71mCash\u001b[0m\u001b[38;5;32mFlows\u001b[0m                                                                         │\n", "│ 8   │ \u001b[38;5;32mOverview\u001b[0m                                                                                                  │\n", "│ 9   │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m                                                                    │\n", "│ 10  │ \u001b[38;5;32mDigital\u001b[0m\u001b[38;5;160mAssets\u001b[0m\u001b[38;5;71mNet\u001b[0m                                                                                          │\n", "│ 11  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mIntangible\u001b[0m\u001b[38;5;71mAssets\u001b[0m                                                                               │\n", "│ 12  │ \u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mFinancial\u001b[0m\u001b[38;5;32mInstruments\u001b[0m                                                                           │\n", "│ 13  │ \u001b[38;5;32mInventory\u001b[0m                                                                                                 │\n", "│ 14  │ \u001b[38;5;32mSolar\u001b[0m\u001b[38;5;160mEnergy\u001b[0m\u001b[38;5;71mSystems\u001b[0m\u001b[38;5;32mNet\u001b[0m                                                                                     │\n", "│ 15  │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m\u001b[38;5;32mNet\u001b[0m                                                                              │\n", "│ 16  │ \u001b[38;5;32mAccrued\u001b[0m\u001b[38;5;160mLiabilitiesand\u001b[0m\u001b[38;5;71mOther\u001b[0m                                                                                │\n", "│ 17  │ \u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160mLong\u001b[0m\u001b[38;5;71mTerm\u001b[0m\u001b[38;5;32mLiabilities\u001b[0m                                                                                  │\n", "│ 18  │ \u001b[38;5;32mDebt\u001b[0m                                                                                                      │\n", "│ 19  │ \u001b[38;5;32mLeases\u001b[0m                                                                                                    │\n", "│ 20  │ \u001b[38;5;32mEquity\u001b[0m\u001b[38;5;160mIncentive\u001b[0m\u001b[38;5;71mPlans\u001b[0m                                                                                      │\n", "│ 21  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m                                                                                               │\n", "│ 22  │ \u001b[38;5;32mCommitmentsand\u001b[0m\u001b[38;5;160mContingencies\u001b[0m                                                                               │\n", "│ 23  │ \u001b[38;5;32mVariable\u001b[0m\u001b[38;5;160mInterest\u001b[0m\u001b[38;5;71mEntity\u001b[0m\u001b[38;5;32mArrangements\u001b[0m                                                                        │\n", "│ 24  │ \u001b[38;5;32m<PERSON><PERSON>ted\u001b[0m\u001b[38;5;160m<PERSON><PERSON>y\u001b[0m\u001b[38;5;71mTransactions\u001b[0m                                                                                  │\n", "│ 25  │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mReportingand\u001b[0m\u001b[38;5;71mInformationabout\u001b[0m\u001b[38;5;32mGeographic\u001b[0m\u001b[38;5;160mAreas\u001b[0m                                                        │\n", "│ 26  │ \u001b[38;5;32mRestructuringand\u001b[0m\u001b[38;5;160mOther\u001b[0m                                                                                     │\n", "│ 27  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mPolicies\u001b[0m                                                            │\n", "│ 28  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                              │\n", "│ 29  │ \u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mFinancial\u001b[0m\u001b[38;5;32mInstruments\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                     │\n", "│ 30  │ \u001b[38;5;32mInventory\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                                           │\n", "│ 31  │ \u001b[38;5;32mSolar\u001b[0m\u001b[38;5;160mEnergy\u001b[0m\u001b[38;5;71mSystems\u001b[0m\u001b[38;5;32mNet\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                               │\n", "│ 32  │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m\u001b[38;5;32mNet\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                        │\n", "│ 33  │ \u001b[38;5;32mAccrued\u001b[0m\u001b[38;5;160mLiabilitiesand\u001b[0m\u001b[38;5;71mOther\u001b[0m\u001b[38;5;32mTables\u001b[0m                                                                          │\n", "│ 34  │ \u001b[38;5;32mOther\u001b[0m\u001b[38;5;160mLong\u001b[0m\u001b[38;5;71mTerm\u001b[0m\u001b[38;5;32mLiabilities\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                            │\n", "│ 35  │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                                                │\n", "│ 36  │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                                              │\n", "│ 37  │ \u001b[38;5;32mEquity\u001b[0m\u001b[38;5;160mIncentive\u001b[0m\u001b[38;5;71mPlans\u001b[0m\u001b[38;5;32mTables\u001b[0m                                                                                │\n", "│ 38  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mTables\u001b[0m                                                                                         │\n", "│ 39  │ \u001b[38;5;32mVariable\u001b[0m\u001b[38;5;160mInterest\u001b[0m\u001b[38;5;71mEntity\u001b[0m\u001b[38;5;32mArrangements\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                  │\n", "│ 40  │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mReportingand\u001b[0m\u001b[38;5;71mInformationabout\u001b[0m\u001b[38;5;32mGeographic\u001b[0m\u001b[38;5;160mAreas\u001b[0m\u001b[38;5;71mTables\u001b[0m                                                  │\n", "│ 41  │ \u001b[38;5;32mOverview\u001b[0m\u001b[38;5;160mDetail\u001b[0m                                                                                            │\n", "│ 42  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mDisaggregationof\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mMajor\u001b[0m\u001b[38;5;71mSource\u001b[0m\u001b[38;5;32mDetail\u001b[0m                │\n", "│ 43  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetail\u001b[0m                                         │\n", "│ 44  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71m<PERSON><PERSON><PERSON>red\u001b[0m\u001b[38;5;32mR<PERSON><PERSON>e\u001b[0m\u001b[38;5;160mActivity\u001b[0m\u001b[38;5;71mDetails\u001b[0m                            │\n", "│ 45  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mReconciliationof\u001b[0m\u001b[38;5;32mNet\u001b[0m\u001b[38;5;160mIncome\u001b[0m\u001b[38;5;71mUsedin\u001b[0m\u001b[38;5;32mComputing\u001b[0m\u001b[38;5;160mBasicand\u001b[0m\u001b[38;5;71mDiluted\u001b[0m\u001b[38;5;32mN…\u001b[0m │\n", "│ 46  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mReconciliationof\u001b[0m\u001b[38;5;32mBasicto\u001b[0m\u001b[38;5;160mDiluted\u001b[0m\u001b[38;5;71mWeighted\u001b[0m\u001b[38;5;32mAverage\u001b[0m\u001b[38;5;160mShares\u001b[0m\u001b[38;5;71mUsedi…\u001b[0m │\n", "│ 47  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mPotentially\u001b[0m\u001b[38;5;32mDilutive\u001b[0m\u001b[38;5;160mSharesthatwere\u001b[0m\u001b[38;5;71mExcludedfrom\u001b[0m\u001b[38;5;32mComputation…\u001b[0m │\n", "│ 48  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mCashand\u001b[0m\u001b[38;5;32mCash\u001b[0m\u001b[38;5;160mEquivalentsand\u001b[0m\u001b[38;5;71mRestricted\u001b[0m\u001b[38;5;32mCash\u001b[0m\u001b[38;5;160mDetail\u001b[0m             │\n", "│ 49  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mEstimated\u001b[0m\u001b[38;5;71mUseful\u001b[0m\u001b[38;5;32mLivesof\u001b[0m\u001b[38;5;160mRespective\u001b[0m\u001b[38;5;71mAssets\u001b[0m\u001b[38;5;32mDetails\u001b[0m                       │\n", "│ 50  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mEstimated\u001b[0m\u001b[38;5;32mUseful\u001b[0m\u001b[38;5;160mLivesof\u001b[0m\u001b[38;5;71mRelated\u001b[0m\u001b[38;5;32mAssets\u001b[0m\u001b[38;5;160mDetails\u001b[0m                │\n", "│ 51  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mAccrued\u001b[0m\u001b[38;5;32mWarranty\u001b[0m\u001b[38;5;160mActivity\u001b[0m\u001b[38;5;71mDetail\u001b[0m                             │\n", "│ 52  │ \u001b[38;5;32mDigital\u001b[0m\u001b[38;5;160mAssets\u001b[0m\u001b[38;5;71mNet\u001b[0m\u001b[38;5;32mDetail\u001b[0m                                                                                    │\n", "│ 53  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mIntangible\u001b[0m\u001b[38;5;71mAssets\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                                        │\n", "│ 54  │ \u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mFinancial\u001b[0m\u001b[38;5;32mInstruments\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mAssetsand\u001b[0m\u001b[38;5;32mLiabilities\u001b[0m\u001b[38;5;160mMeasuredat\u001b[0m\u001b[38;5;71mFair\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mR<PERSON>urring\u001b[0m\u001b[38;5;71mB<PERSON>\u001b[0m\u001b[38;5;32mDetail\u001b[0m    │\n", "│ 55  │ \u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mFinancial\u001b[0m\u001b[38;5;32mInstruments\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mCash\u001b[0m\u001b[38;5;32mCash\u001b[0m\u001b[38;5;160mEquivalentsand\u001b[0m\u001b[38;5;71mMarketable\u001b[0m\u001b[38;5;32mSecurities\u001b[0m\u001b[38;5;160mDetails\u001b[0m                │\n", "│ 56  │ \u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mFinancial\u001b[0m\u001b[38;5;32mInstruments\u001b[0m\u001b[38;5;160mS<PERSON>mar<PERSON><PERSON>\u001b[0m\u001b[38;5;71m<PERSON><PERSON>\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mMarketable\u001b[0m\u001b[38;5;71mSecuritiesby\u001b[0m\u001b[38;5;32mContractual\u001b[0m\u001b[38;5;160mMaturities\u001b[0m\u001b[38;5;71mDetails\u001b[0m     │\n", "│ 57  │ \u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mFinancial\u001b[0m\u001b[38;5;32mInstruments\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetail\u001b[0m                                                │\n", "│ 58  │ \u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mFinancial\u001b[0m\u001b[38;5;32mInstruments\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mEstimated\u001b[0m\u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160mValuesand\u001b[0m\u001b[38;5;71m<PERSON>arrying\u001b[0m\u001b[38;5;32mValues\u001b[0m\u001b[38;5;160mDetail\u001b[0m                       │\n", "│ 59  │ \u001b[38;5;32mInventory\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mInventory\u001b[0m\u001b[38;5;32mDetail\u001b[0m                                                                        │\n", "│ 60  │ \u001b[38;5;32mInventory\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetail\u001b[0m                                                                      │\n", "│ 61  │ \u001b[38;5;32mSolar\u001b[0m\u001b[38;5;160mEnergy\u001b[0m\u001b[38;5;71mSystems\u001b[0m\u001b[38;5;32mNet\u001b[0m\u001b[38;5;160mComponentsof\u001b[0m\u001b[38;5;71mSolar\u001b[0m\u001b[38;5;32mEnergy\u001b[0m\u001b[38;5;160mSystems\u001b[0m\u001b[38;5;71mNet\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                             │\n", "│ 62  │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m\u001b[38;5;32mNet\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mProperty\u001b[0m\u001b[38;5;32mPlantand\u001b[0m\u001b[38;5;160mEquipment\u001b[0m\u001b[38;5;71mNet\u001b[0m\u001b[38;5;32mDetail\u001b[0m                                  │\n", "│ 63  │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m\u001b[38;5;32mNet\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetail\u001b[0m                                                   │\n", "│ 64  │ \u001b[38;5;32mAccrued\u001b[0m\u001b[38;5;160mLiabilitiesand\u001b[0m\u001b[38;5;71mOther\u001b[0m\u001b[38;5;32mScheduleof\u001b[0m\u001b[38;5;160mAccrued\u001b[0m\u001b[38;5;71mLiabilitiesand\u001b[0m\u001b[38;5;32mOther\u001b[0m\u001b[38;5;160mCurrent\u001b[0m\u001b[38;5;71mLiabilities\u001b[0m\u001b[38;5;32mDetail\u001b[0m                    │\n", "│ 65  │ \u001b[38;5;32mOther\u001b[0m\u001b[38;5;160mLong\u001b[0m\u001b[38;5;71mTerm\u001b[0m\u001b[38;5;32mLiabilities\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mOther\u001b[0m\u001b[38;5;32mLongterm\u001b[0m\u001b[38;5;160mLiabilities\u001b[0m\u001b[38;5;71mDetail\u001b[0m                                          │\n", "│ 66  │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mDebtand\u001b[0m\u001b[38;5;32mFinance\u001b[0m\u001b[38;5;160mLeases\u001b[0m\u001b[38;5;71mDetail\u001b[0m                                                                   │\n", "│ 67  │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                                          │\n", "│ 68  │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160m<PERSON>rincipalof\u001b[0m\u001b[38;5;71mMaturitiesof\u001b[0m\u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                                    │\n", "│ 69  │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetail\u001b[0m                                                                         │\n", "│ 70  │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mOperatingand\u001b[0m\u001b[38;5;32mFinancing\u001b[0m\u001b[38;5;160mLeases\u001b[0m\u001b[38;5;71mPresentedin\u001b[0m\u001b[38;5;32mB<PERSON>ce\u001b[0m\u001b[38;5;160mSheets\u001b[0m\u001b[38;5;71mDetail\u001b[0m                                 │\n", "│ 71  │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mComponentsof\u001b[0m\u001b[38;5;32mLease\u001b[0m\u001b[38;5;160mExpenseand\u001b[0m\u001b[38;5;71mOther\u001b[0m\u001b[38;5;32mInformation\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mLeases\u001b[0m\u001b[38;5;32mDetail\u001b[0m                          │\n", "│ 72  │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mSupplemental\u001b[0m\u001b[38;5;71mCash\u001b[0m\u001b[38;5;32mFlow\u001b[0m\u001b[38;5;160mInformation\u001b[0m\u001b[38;5;71m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mDetail\u001b[0m                                                │\n", "│ 73  │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mMaturitiesof\u001b[0m\u001b[38;5;32mOperatingand\u001b[0m\u001b[38;5;160mFinance\u001b[0m\u001b[38;5;71mLease\u001b[0m\u001b[38;5;32mLiabilities\u001b[0m\u001b[38;5;160mDetail\u001b[0m                                     │\n", "│ 74  │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mMaturitiesof\u001b[0m\u001b[38;5;71mOperating\u001b[0m\u001b[38;5;32mLeaseand\u001b[0m\u001b[38;5;160mSales\u001b[0m\u001b[38;5;71mType\u001b[0m\u001b[38;5;32mLease\u001b[0m\u001b[38;5;160mReceivablesfrom\u001b[0m\u001b[38;5;71mCustomers\u001b[0m\u001b[38;5;32mDetail\u001b[0m                           │\n", "│ 75  │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mScheduleof\u001b[0m\u001b[38;5;71mLease\u001b[0m\u001b[38;5;32mReceivables\u001b[0m\u001b[38;5;160mRelatingto\u001b[0m\u001b[38;5;71mSales\u001b[0m\u001b[38;5;32mType\u001b[0m\u001b[38;5;160mLeases\u001b[0m\u001b[38;5;71mDetail\u001b[0m                                           │\n", "│ 76  │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mScheduleoffutureminimummasterleasepaymentstobereceivedfrominvestors\u001b[0m\u001b[38;5;71mDetail\u001b[0m                           │\n", "│ 77  │ \u001b[38;5;32mEquity\u001b[0m\u001b[38;5;160mIncentive\u001b[0m\u001b[38;5;71mPlans\u001b[0m\u001b[38;5;32mAdditional\u001b[0m\u001b[38;5;160mInformation\u001b[0m\u001b[38;5;71mDetail\u001b[0m                                                           │\n", "│ 78  │ \u001b[38;5;32mEquity\u001b[0m\u001b[38;5;160mIncentive\u001b[0m\u001b[38;5;71mP<PERSON>s\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mStock\u001b[0m\u001b[38;5;71mOptionand\u001b[0m\u001b[38;5;32mRSU\u001b[0m\u001b[38;5;160mActivity\u001b[0m\u001b[38;5;71mDetail\u001b[0m                                              │\n", "│ 79  │ \u001b[38;5;32mE<PERSON>ty\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON>ve\u001b[0m\u001b[38;5;71m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;32mS<PERSON>uleof\u001b[0m\u001b[38;5;160mFair\u001b[0m\u001b[38;5;71m<PERSON><PERSON><PERSON>of\u001b[0m\u001b[38;5;32mStock\u001b[0m\u001b[38;5;160mOption\u001b[0m\u001b[38;5;71mAwardand\u001b[0m\u001b[38;5;32mESP\u001b[0m\u001b[38;5;160mPon\u001b[0m\u001b[38;5;71mGrant\u001b[0m\u001b[38;5;32mDate\u001b[0m\u001b[38;5;160mDetail\u001b[0m                         │\n", "│ 80  │ \u001b[38;5;32mEquity\u001b[0m\u001b[38;5;160mIncentive\u001b[0m\u001b[38;5;71m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mOperational\u001b[0m\u001b[38;5;71mMilestone\u001b[0m\u001b[38;5;32mB<PERSON><PERSON>\u001b[0m\u001b[38;5;160mRevenueor\u001b[0m\u001b[38;5;71mAdjusted\u001b[0m\u001b[38;5;32mEBITDA\u001b[0m\u001b[38;5;160mDetail\u001b[0m                     │\n", "│ 81  │ \u001b[38;5;32mEquity\u001b[0m\u001b[38;5;160mIncentive\u001b[0m\u001b[38;5;71m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mStock\u001b[0m\u001b[38;5;71mBased\u001b[0m\u001b[38;5;32mCompensation\u001b[0m\u001b[38;5;160mExpense\u001b[0m\u001b[38;5;71mDetail\u001b[0m                                          │\n", "│ 82  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mScheduleof\u001b[0m\u001b[38;5;32mIncomebefore\u001b[0m\u001b[38;5;160mProvision\u001b[0m\u001b[38;5;71mFor\u001b[0m\u001b[38;5;32mI<PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                           │\n", "│ 83  │ \u001b[38;5;32mI<PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mAdditional\u001b[0m\u001b[38;5;32mInformation\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                                   │\n", "│ 84  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mComponentsof\u001b[0m\u001b[38;5;32mProvisionfor\u001b[0m\u001b[38;5;160mIncome\u001b[0m\u001b[38;5;71mTaxes\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                     │\n", "│ 85  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mScheduleof\u001b[0m\u001b[38;5;32mReconciliationof\u001b[0m\u001b[38;5;160mTaxesat\u001b[0m\u001b[38;5;71mFederal\u001b[0m\u001b[38;5;32mStatutory\u001b[0m\u001b[38;5;160mRateto\u001b[0m\u001b[38;5;71mProvisionfor\u001b[0m\u001b[38;5;32mIncome\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mDetails\u001b[0m          │\n", "│ 86  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mScheduleof\u001b[0m\u001b[38;5;32mD<PERSON><PERSON>red\u001b[0m\u001b[38;5;160mTax\u001b[0m\u001b[38;5;71mAssets\u001b[0m\u001b[38;5;32mLiabilities\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                  │\n", "│ 87  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mS<PERSON>uleof\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON>\u001b[0m\u001b[38;5;71mUnrecognized\u001b[0m\u001b[38;5;32mTax\u001b[0m\u001b[38;5;160mBenefits\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                         │\n", "│ 88  │ \u001b[38;5;32mCommitmentsand\u001b[0m\u001b[38;5;160mContingencies\u001b[0m\u001b[38;5;71mDetail\u001b[0m                                                                         │\n", "│ 89  │ \u001b[38;5;32mVariable\u001b[0m\u001b[38;5;160mInterest\u001b[0m\u001b[38;5;71mEntity\u001b[0m\u001b[38;5;32mArrangements\u001b[0m\u001b[38;5;160mCarrying\u001b[0m\u001b[38;5;71mValuesof\u001b[0m\u001b[38;5;32mAssetsand\u001b[0m\u001b[38;5;160mLiabilitiesof\u001b[0m\u001b[38;5;71mSubsidiaryin\u001b[0m\u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mBalance\u001b[0m\u001b[38;5;71mS…\u001b[0m │\n", "│ 90  │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mReportingand\u001b[0m\u001b[38;5;71mInformationabout\u001b[0m\u001b[38;5;32mGeographic\u001b[0m\u001b[38;5;160mAreas\u001b[0m\u001b[38;5;71mAdditional\u001b[0m\u001b[38;5;32mInformation\u001b[0m\u001b[38;5;160mDetail\u001b[0m                             │\n", "│ 91  │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mReportingand\u001b[0m\u001b[38;5;71mInformationabout\u001b[0m\u001b[38;5;32mGeographic\u001b[0m\u001b[38;5;160mAreas\u001b[0m\u001b[38;5;71mScheduleof\u001b[0m\u001b[38;5;32mTotal\u001b[0m\u001b[38;5;160mRevenuesand\u001b[0m\u001b[38;5;71m<PERSON><PERSON>\u001b[0m\u001b[38;5;32mProfitby\u001b[0m\u001b[38;5;160mReportable\u001b[0m\u001b[38;5;71mSegme…\u001b[0m │\n", "│ 92  │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mReportingand\u001b[0m\u001b[38;5;71mInformationabout\u001b[0m\u001b[38;5;32mGeographic\u001b[0m\u001b[38;5;160mAreas\u001b[0m\u001b[38;5;71mScheduleof\u001b[0m\u001b[38;5;32mReve<PERSON>esby\u001b[0m\u001b[38;5;160mGeographic\u001b[0m\u001b[38;5;71mArea\u001b[0m\u001b[38;5;32mDetail\u001b[0m                │\n", "│ 93  │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mReportingand\u001b[0m\u001b[38;5;71mInformationabout\u001b[0m\u001b[38;5;32mGeographic\u001b[0m\u001b[38;5;160mAreas\u001b[0m\u001b[38;5;71mScheduleof\u001b[0m\u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160mLived\u001b[0m\u001b[38;5;71mAssetsby\u001b[0m\u001b[38;5;32mGeographic\u001b[0m\u001b[38;5;160mArea\u001b[0m\u001b[38;5;71mDetail\u001b[0m         │\n", "│ 94  │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mReportingand\u001b[0m\u001b[38;5;71mInformationabout\u001b[0m\u001b[38;5;32mGeographic\u001b[0m\u001b[38;5;160mAreas\u001b[0m\u001b[38;5;71mScheduleofinventorybyreportablesegment\u001b[0m\u001b[38;5;32mDetail\u001b[0m            │\n", "│ 95  │ \u001b[38;5;32mRestructuringand\u001b[0m\u001b[38;5;160mOther\u001b[0m\u001b[38;5;71mDetail\u001b[0m                                                                               │\n", "│ 96  │ \u001b[38;5;32mAward\u001b[0m\u001b[38;5;160mTiming\u001b[0m\u001b[38;5;71mDisclosure\u001b[0m                                                                                     │\n", "│ 97  │ \u001b[38;5;32mInsider\u001b[0m\u001b[38;5;160mTrading\u001b[0m\u001b[38;5;71mArrangements\u001b[0m                                                                                │\n", "│ 98  │ \u001b[38;5;32mErr\u001b[0m\u001b[38;5;160mComp\u001b[0m\u001b[38;5;71mDisclosure\u001b[0m                                                                                         │\n", "│ 99  │ \u001b[38;5;32mPvp\u001b[0m\u001b[38;5;160mDisclosure\u001b[0m                                                                                             │\n", "│ 100 │ \u001b[38;5;32mInsider\u001b[0m\u001b[38;5;160mTrading\u001b[0m\u001b[38;5;71mPolicies\u001b[0m\u001b[38;5;32mProc\u001b[0m                                                                                │\n", "╰─────┴───────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "execution_count": 17}, {"metadata": {}, "cell_type": "markdown", "source": "To see an individual statement, use the bracket `[]` operator. ", "id": "ba095dcf6a557c52"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-14T15:15:28.345845Z", "start_time": "2024-11-14T15:15:28.300668Z"}}, "cell_type": "code", "source": "xb.statements[91]", "id": "652a502ac194f2f4", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["                                                  \u001b[1;38;5;38mTesla, Inc.\u001b[0m                                                   \n", "                                               \u001b[1mSegment Reporting\u001b[0m                                                \n", "                                                     \u001b[3;38;5;250mAnnual\u001b[0m                                                     \n", "                                                                                                                \n", " \u001b[1m \u001b[0m\u001b[1m                                                  \u001b[0m\u001b[1m \u001b[0m \u001b[1m        \u001b[0m\u001b[1m \u001b[0m \u001b[1m          2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m          2022\u001b[0m\u001b[1m \u001b[0m \u001b[1m          2021\u001b[0m\u001b[1m \u001b[0m \n", " ────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "  \u001b[37mRevenues                                          \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m          96,773          81,462          53,823  \n", "  \u001b[37m  Automotive segment                              \u001b[0m  \u001b[2;38;5;249m        \u001b[0m  90,738,000,000  77,553,000,000  51,034,000,000  \n", "  \u001b[37m  Energy generation and storage segment           \u001b[0m  \u001b[2;38;5;249m        \u001b[0m   6,035,000,000   3,909,000,000   2,789,000,000  \n", "  \u001b[37mGross profit                                      \u001b[0m  \u001b[2;38;5;249mmillions\u001b[0m          17,660          20,853          13,606  \n", "  \u001b[37m  Automotive segment                              \u001b[0m  \u001b[2;38;5;249m        \u001b[0m  16,519,000,000  20,565,000,000  13,735,000,000  \n", "  \u001b[37m  Energy generation and storage segment           \u001b[0m  \u001b[2;38;5;249m        \u001b[0m   1,141,000,000     288,000,000   (129,000,000)  \n", "                                                                                                                "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "execution_count": 23}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}