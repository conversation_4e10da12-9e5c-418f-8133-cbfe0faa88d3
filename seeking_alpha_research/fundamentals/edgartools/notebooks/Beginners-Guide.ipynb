{"cells": [{"cell_type": "markdown", "id": "f02d4f2e-c44b-43f0-92fe-1c2d2f94d366", "metadata": {}, "source": ["# Beginners Guide\n", "\n", "This is the beginner's guide to **edgartools** - the easiest way to navigate **SEC Edgar** data."]}, {"cell_type": "markdown", "id": "22fb98d1bf5bac29", "metadata": {}, "source": ["**[Open this notebook in Google Colab](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/Beginners-Guide.ipynb)**"]}, {"cell_type": "markdown", "id": "5a7e5e88-1d32-4486-9be3-a3cc316dc636", "metadata": {}, "source": ["Edgartools is a python library which means you install using pip"]}, {"cell_type": "code", "execution_count": null, "id": "829bcf7b-7b0e-4bfd-a68f-31b48b53cd1f", "metadata": {}, "outputs": [], "source": ["!pip install -U edgartools"]}, {"cell_type": "markdown", "id": "a6a2ec39-a1b7-4664-b4df-885ed3516cf4", "metadata": {}, "source": ["## Import\n", "\n", "To get started import the library using `from edgar import *`. This will get you a comprehensive set of the most useful functions.\n", "\n", "This includes `set_identity` which you will use to identify yourself to the **SEC**. You can use `set_identity('your email address')`."]}, {"cell_type": "code", "execution_count": 2, "id": "7c077924-65c8-4f4c-897c-cce7fce98170", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[21:12:07] </span><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> Identity of the Edgar REST client set to <span style=\"font-weight: bold\">[</span><EMAIL><span style=\"font-weight: bold\">]</span>                       <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">core.py:158</span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[21:12:07]\u001b[0m\u001b[2;36m \u001b[0m\u001b[34mINFO    \u001b[0m Identity of the Edgar REST client set to \u001b[1m[\u001b[<EMAIL>\u001b[1m]\u001b[0m                       \u001b[2mcore.py\u001b[0m\u001b[2m:\u001b[0m\u001b[2m158\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from edgar import *\n", "\n", "set_identity(\"<EMAIL>\")"]}, {"cell_type": "markdown", "id": "72216c79-8ed9-4061-bd59-8ac9caae281c", "metadata": {}, "source": ["Alternatively you can set the environment variable **EDGAR_IDENTITY**"]}, {"cell_type": "markdown", "id": "f4fae47d-b32c-497c-9bc5-26b52bfdfd7e", "metadata": {}, "source": ["## Getting SEC Filings\n", "\n", "To get a set of filings use `get_filings`"]}, {"cell_type": "code", "execution_count": 14, "id": "3f846609-84ea-4ae5-a0fb-8acbf66ab906", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────────────────────────────────────────── Filings ────────────────────────────────────────────────────╮\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1m  \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71mform    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mcompany                                     \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mcik    \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39mfiling_date\u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1maccession_number    \u001b[0m\u001b[1m \u001b[0m  │\n", "│  ─────────────────────────────────────────────────────────────────────────────────────────────────────────────  │\n", "│  \u001b[1m \u001b[0m\u001b[1m0 \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m1-A POS \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mArmed Forces Brewing Company, Inc.          \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1832987\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001832987-24-000007\u001b[0m\u001b[1m \u001b[0m  │\n", "│   1   \u001b[38;5;71m \u001b[0m\u001b[38;5;71m1-A/A   \u001b[0m\u001b[38;5;71m \u001b[0m  LELANTOS HOLDINGS INC.                         2006925  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001493152-24-033802   │\n", "│  \u001b[1m \u001b[0m\u001b[1m2 \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m1-K     \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mHouse Hack, Inc.                            \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1945278\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001104659-24-092687\u001b[0m\u001b[1m \u001b[0m  │\n", "│   3   \u001b[38;5;71m \u001b[0m\u001b[38;5;71m1-K/A   \u001b[0m\u001b[38;5;71m \u001b[0m  Armed Forces Brewing Company, Inc.             1832987  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001832987-24-000005   │\n", "│  \u001b[1m \u001b[0m\u001b[1m4 \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m1-U     \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mHere Collection LLC                         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1876769\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001096906-24-001801\u001b[0m\u001b[1m \u001b[0m  │\n", "│   5   \u001b[38;5;71m \u001b[0m\u001b[38;5;71m1-<PERSON>     \u001b[0m\u001b[38;5;71m \u001b[0m  Miso Robotics, Inc.                            1710670  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001104659-24-092511   │\n", "│  \u001b[1m \u001b[0m\u001b[1m6 \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-12B/A\u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mAmazon Holdco Inc.                          \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2011286\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001193125-24-207090\u001b[0m\u001b[1m \u001b[0m  │\n", "│   7   \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  BENCHMARK 2018-B3 COMMERCIAL MORTGAGE TRUST    1734103  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000555   │\n", "│  \u001b[1m \u001b[0m\u001b[1m8 \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mBENCHMARK 2018-B6 MORTGAGE TRUST            \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1751874\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000568\u001b[0m\u001b[1m \u001b[0m  │\n", "│   9   \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  CFCRE 2016-C4 Mortgage Trust                   1671048  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001853620-24-000215   │\n", "│  \u001b[1m \u001b[0m\u001b[1m10\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCITIGROUP COMMERCIAL MORTGAGE TRUST 2016-C2 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1681031\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000546\u001b[0m\u001b[1m \u001b[0m  │\n", "│   11  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  CITIGROUP COMMERCIAL MORTGAGE TRUST 2016-P4    1677913  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000548   │\n", "│  \u001b[1m \u001b[0m\u001b[1m12\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCITIGROUP COMMERCIAL MORTGAGE TRUST 2016-P5 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1684093\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000549\u001b[0m\u001b[1m \u001b[0m  │\n", "│   13  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  CITIGROUP COMMERCIAL MORTGAGE TRUST 2016-P6    1690255  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000551   │\n", "│  \u001b[1m \u001b[0m\u001b[1m14\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCITIGROUP COMMERCIAL MORTGAGE TRUST 2017-C4 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1718304\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000553\u001b[0m\u001b[1m \u001b[0m  │\n", "│   15  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  CITIGROUP COMMERCIAL MORTGAGE TRUST 2018-B2    1731044  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000557   │\n", "│  \u001b[1m \u001b[0m\u001b[1m16\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCITIGROUP COMMERCIAL MORTGAGE TRUST 2018-C5 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1740450\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000559\u001b[0m\u001b[1m \u001b[0m  │\n", "│   17  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  CITIGROUP COMMERCIAL MORTGAGE TRUST 2018-C6    1757925  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000562   │\n", "│  \u001b[1m \u001b[0m\u001b[1m18\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCITIGROUP COMMERCIAL MORTGAGE TRUST         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1783287\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000564\u001b[0m\u001b[1m \u001b[0m  │\n", "│  \u001b[1m    \u001b[0m \u001b[1;38;5;71m          \u001b[0m \u001b[1m \u001b[0m\u001b[1m2019-GC41                                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m         \u001b[0m \u001b[1;38;5;39m             \u001b[0m \u001b[1m                      \u001b[0m  │\n", "│   19  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  CITIGROUP COMMERCIAL MORTGAGE TRUST            1791183  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000566   │\n", "│       \u001b[38;5;71m          \u001b[0m  2019-GC43                                               \u001b[38;5;39m             \u001b[0m                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m20\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCOMM 2012-LC4 Mortgage Trust                \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1543042\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001020242-24-000101\u001b[0m\u001b[1m \u001b[0m  │\n", "│   21  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  COMM 2013-CCRE13 Mortgage Trust                1589804  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001020242-24-000102   │\n", "│  \u001b[1m \u001b[0m\u001b[1m22\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCOMM 2014-CCRE14 Mortgage Trust             \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1594100\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001020242-24-000106\u001b[0m\u001b[1m \u001b[0m  │\n", "│   23  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  COMM 2014-CCRE17 Mortgage Trust                1603669  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001020242-24-000107   │\n", "│  \u001b[1m \u001b[0m\u001b[1m24\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCOMM 2014-UBS2 Mortgage Trust               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1599714\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001020242-24-000103\u001b[0m\u001b[1m \u001b[0m  │\n", "│   25  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  COMM 2014-UBS4 Mortgage Trust                  1612126  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001020242-24-000104   │\n", "│  \u001b[1m \u001b[0m\u001b[1m26\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCOMM 2015-CCRE22 Mortgage Trust             \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1634976\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001020242-24-000109\u001b[0m\u001b[1m \u001b[0m  │\n", "│   27  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  COMM 2015-CCRE25 Mortgage Trust                1648195  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001020242-24-000110   │\n", "│  \u001b[1m \u001b[0m\u001b[1m28\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCOMM 2015-LC21 Mortgage Trust               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1643293\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001020242-24-000108\u001b[0m\u001b[1m \u001b[0m  │\n", "│   29  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  COMM 2016-DC2 Mortgage Trust                   1663244  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001020242-24-000105   │\n", "│  \u001b[1m \u001b[0m\u001b[1m30\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCitigroup Commercial Mortgage Trust         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1590058\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000537\u001b[0m\u001b[1m \u001b[0m  │\n", "│  \u001b[1m    \u001b[0m \u001b[1;38;5;71m          \u001b[0m \u001b[1m \u001b[0m\u001b[1m2013-GC17                                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m         \u001b[0m \u001b[1;38;5;39m             \u001b[0m \u001b[1m                      \u001b[0m  │\n", "│   31  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  Citigroup Commercial Mortgage Trust            1612518  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000538   │\n", "│       \u001b[38;5;71m          \u001b[0m  2014-GC23                                               \u001b[38;5;39m             \u001b[0m                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m32\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCitigroup Commercial Mortgage Trust         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1619616\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000539\u001b[0m\u001b[1m \u001b[0m  │\n", "│  \u001b[1m    \u001b[0m \u001b[1;38;5;71m          \u001b[0m \u001b[1m \u001b[0m\u001b[1m2014-GC25                                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m         \u001b[0m \u001b[1;38;5;39m             \u001b[0m \u001b[1m                      \u001b[0m  │\n", "│   33  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  Citigroup Commercial Mortgage Trust            1629716  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000540   │\n", "│       \u001b[38;5;71m          \u001b[0m  2015-GC27                                               \u001b[38;5;39m             \u001b[0m                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m34\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCitigroup Commercial Mortgage Trust         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1636708\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000541\u001b[0m\u001b[1m \u001b[0m  │\n", "│  \u001b[1m    \u001b[0m \u001b[1;38;5;71m          \u001b[0m \u001b[1m \u001b[0m\u001b[1m2015-GC29                                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m         \u001b[0m \u001b[1;38;5;39m             \u001b[0m \u001b[1m                      \u001b[0m  │\n", "│   35  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  Citigroup Commercial Mortgage Trust            1643661  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000542   │\n", "│       \u001b[38;5;71m          \u001b[0m  2015-GC31                                               \u001b[38;5;39m             \u001b[0m                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m36\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCitigroup Commercial Mortgage Trust         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1651588\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000543\u001b[0m\u001b[1m \u001b[0m  │\n", "│  \u001b[1m    \u001b[0m \u001b[1;38;5;71m          \u001b[0m \u001b[1m \u001b[0m\u001b[1m2015-GC33                                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m         \u001b[0m \u001b[1;38;5;39m             \u001b[0m \u001b[1m                      \u001b[0m  │\n", "│   37  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  Citigroup Commercial Mortgage Trust            1657325  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000544   │\n", "│       \u001b[38;5;71m          \u001b[0m  2015-GC35                                               \u001b[38;5;39m             \u001b[0m                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m38\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCitigroup Commercial Mortgage Trust 2016-C1 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1673255\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001628297-24-000545\u001b[0m\u001b[1m \u001b[0m  │\n", "│   39  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  Citigroup Commercial Mortgage Trust            1670601  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628297-24-000547   │\n", "│       \u001b[38;5;71m          \u001b[0m  2016-GC37                                               \u001b[38;5;39m             \u001b[0m                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m40\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mFORD CREDIT FLOORPLAN MASTER OWNER TRUST A  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1159408\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001159408-24-000019\u001b[0m\u001b[1m \u001b[0m  │\n", "│   41  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  GM Financial Consumer Automobile Receivables   1823097  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001213900-24-072366   │\n", "│       \u001b[38;5;71m          \u001b[0m  Trust 2020-4                                            \u001b[38;5;39m             \u001b[0m                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m42\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mGM Financial Consumer Automobile Receivables\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1834699\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001213900-24-072367\u001b[0m\u001b[1m \u001b[0m  │\n", "│  \u001b[1m    \u001b[0m \u001b[1;38;5;71m          \u001b[0m \u001b[1m \u001b[0m\u001b[1mTrust 2021-1                                \u001b[0m\u001b[1m \u001b[0m \u001b[1m         \u001b[0m \u001b[1;38;5;39m             \u001b[0m \u001b[1m                      \u001b[0m  │\n", "│   43  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  GM Financial Consumer Automobile Receivables   1850024  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001213900-24-072368   │\n", "│       \u001b[38;5;71m          \u001b[0m  Trust 2021-2                                            \u001b[38;5;39m             \u001b[0m                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m44\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mGM Financial Consumer Automobile Receivables\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1867993\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001213900-24-072369\u001b[0m\u001b[1m \u001b[0m  │\n", "│  \u001b[1m    \u001b[0m \u001b[1;38;5;71m          \u001b[0m \u001b[1m \u001b[0m\u001b[1mTrust 2021-3                                \u001b[0m\u001b[1m \u001b[0m \u001b[1m         \u001b[0m \u001b[1;38;5;39m             \u001b[0m \u001b[1m                      \u001b[0m  │\n", "│   45  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  GM Financial Consumer Automobile Receivables   1882961  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001213900-24-072370   │\n", "│       \u001b[38;5;71m          \u001b[0m  Trust 2021-4                                            \u001b[38;5;39m             \u001b[0m                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m46\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mGM Financial Consumer Automobile Receivables\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1896310\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001213900-24-072372\u001b[0m\u001b[1m \u001b[0m  │\n", "│  \u001b[1m    \u001b[0m \u001b[1;38;5;71m          \u001b[0m \u001b[1m \u001b[0m\u001b[1mTrust 2022-1                                \u001b[0m\u001b[1m \u001b[0m \u001b[1m         \u001b[0m \u001b[1;38;5;39m             \u001b[0m \u001b[1m                      \u001b[0m  │\n", "│   47  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  GM Financial Consumer Automobile Receivables   1916232  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001213900-24-072373   │\n", "│       \u001b[38;5;71m          \u001b[0m  Trust 2022-2                                            \u001b[38;5;39m             \u001b[0m                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m48\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-D    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mGM Financial Consumer Automobile Receivables\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1932377\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001213900-24-072374\u001b[0m\u001b[1m \u001b[0m  │\n", "│  \u001b[1m    \u001b[0m \u001b[1;38;5;71m          \u001b[0m \u001b[1m \u001b[0m\u001b[1mTrust 2022-3                                \u001b[0m\u001b[1m \u001b[0m \u001b[1m         \u001b[0m \u001b[1;38;5;39m             \u001b[0m \u001b[1m                      \u001b[0m  │\n", "│   49  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-D    \u001b[0m\u001b[38;5;71m \u001b[0m  GM Financial Consumer Automobile Receivables   1944019  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001213900-24-072376   │\n", "│       \u001b[38;5;71m          \u001b[0m  Trust 2022-4                                            \u001b[38;5;39m             \u001b[0m                         │\n", "│                                                                                                                 │\n", "│ Showing 50 of 167,798 filings                                                                                   │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["filings = get_filings()\n", "filings"]}, {"cell_type": "markdown", "id": "1244596a-4512-4e3f-a3fc-7cf3b554ebeb", "metadata": {}, "source": ["## Select a single filing\n", "\n", "Once you have the filings you can select a single filing using the `[]` notation."]}, {"cell_type": "code", "execution_count": 15, "id": "c01b7d14-2b27-4c65-982a-8a1822081797", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭────────────────────────────\u001b[1m Armed Forces Brewing Company, Inc. [1832987] 1-A POS 📄 \u001b[0m────────────────────────────╮\n", "│ ╭──────────────────────┬────────────╮                                                                           │\n", "│ │\u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m0001832987-24-000007\u001b[0m\u001b[1;38;5;39m \u001b[0m│ 2024-08-26 │                                                                           │\n", "│ ╰──────────────────────┴────────────╯                                                                           │\n", "│ ╭──────────────────────────────────────────────────────────────────────────────────────────────╮                │\n", "│ │\u001b[1m \u001b[0m\u001b[1mLinks\u001b[0m\u001b[1m: 🏠 Homepage 📄 Primary Document 📜 Full Submission Text                              \u001b[0m\u001b[1m \u001b[0m│                │\n", "│ ├──────────────────────────────────────────────────────────────────────────────────────────────┤                │\n", "│ │ 🏠 https://sec.gov/Archives/edgar/data/1832987/0001832987-24-000007-index.html               │                │\n", "│ │ 📄 https://sec.gov/Archives/edgar/data/1832987/000183298724000007/xsl1-A_X01/primary_doc.xml │                │\n", "│ │ 📜 https://sec.gov/Archives/edgar/data/1832987/000183298724000007/0001832987-24-000007.txt   │                │\n", "│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯                │\n", "╰──────────────────────────────────── Form 1-A POS: Reg A Offering Amendment ─────────────────────────────────────╯"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["filing = filings[0]\n", "filing"]}, {"cell_type": "markdown", "id": "735117a6-ac45-46d2-8019-bf14296808f4", "metadata": {}, "source": ["## Select the top or bottom n filings "]}, {"cell_type": "code", "execution_count": 16, "id": "a6e71294-d0e6-4ed3-9602-75ac5dad1453", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────────────────────────────────────────── Filings ────────────────────────────────────────────────────╮\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1m \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71mform   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mcompany                           \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mcik    \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39mfiling_date\u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1maccession_number    \u001b[0m\u001b[1m \u001b[0m              │\n", "│  ─────────────────────────────────────────────────────────────────────────────────────────────────              │\n", "│  \u001b[1m \u001b[0m\u001b[1m0\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m1-A POS\u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mArmed Forces Brewing Company, Inc.\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1832987\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001832987-24-000007\u001b[0m\u001b[1m \u001b[0m              │\n", "│   1  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m1-A/A  \u001b[0m\u001b[38;5;71m \u001b[0m  LELANTOS HOLDINGS INC.               2006925  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001493152-24-033802               │\n", "│  \u001b[1m \u001b[0m\u001b[1m2\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m1-K    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mHouse Hack, Inc.                  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1945278\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001104659-24-092687\u001b[0m\u001b[1m \u001b[0m              │\n", "│   3  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m1-K/A  \u001b[0m\u001b[38;5;71m \u001b[0m  Armed Forces Brewing Company, Inc.   1832987  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-26 \u001b[0m\u001b[38;5;39m \u001b[0m  0001832987-24-000005               │\n", "│  \u001b[1m \u001b[0m\u001b[1m4\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m1-U    \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mHere Collection LLC               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1876769\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001096906-24-001801\u001b[0m\u001b[1m \u001b[0m              │\n", "│                                                                                                                 │\n", "│ Showing 5 of 5 filings                                                                                          │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["filings.head(5)"]}, {"cell_type": "code", "execution_count": 17, "id": "b2842f55-b5e9-4341-951d-a066ad352e5a", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────────────────────────────────────────── Filings ────────────────────────────────────────────────────╮\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1m \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71mform   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mcompany                           \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mcik    \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39mfiling_date\u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1maccession_number    \u001b[0m\u001b[1m \u001b[0m              │\n", "│  ─────────────────────────────────────────────────────────────────────────────────────────────────              │\n", "│  \u001b[1m \u001b[0m\u001b[1m0\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71mX-17A-5\u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mKINGFISHER SECURITIES, LLC        \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1103008\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-07-01 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m9999999997-24-003176\u001b[0m\u001b[1m \u001b[0m              │\n", "│   1  \u001b[38;5;71m \u001b[0m\u001b[38;5;71mX-17A-5\u001b[0m\u001b[38;5;71m \u001b[0m  MORRIS GROUP, INC.                   714551   \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-07-01 \u001b[0m\u001b[38;5;39m \u001b[0m  0000714551-24-000001               │\n", "│  \u001b[1m \u001b[0m\u001b[1m2\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71mX-17A-5\u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mOHANAE SECURITIES LLC             \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1785108\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-07-01 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001785108-24-000002\u001b[0m\u001b[1m \u001b[0m              │\n", "│   3  \u001b[38;5;71m \u001b[0m\u001b[38;5;71mX-17A-5\u001b[0m\u001b[38;5;71m \u001b[0m  ROCKFLEET FINANCIAL SERVICES, INC.   1453721  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-07-01 \u001b[0m\u001b[38;5;39m \u001b[0m  0001639541-24-000005               │\n", "│  \u001b[1m \u001b[0m\u001b[1m4\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71mX-17A-5\u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mW CAMPION CAPITAL LLC             \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1605581\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-07-01 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001605581-24-000001\u001b[0m\u001b[1m \u001b[0m              │\n", "│                                                                                                                 │\n", "│ Showing 5 of 5 filings                                                                                          │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["filings.tail(5)"]}, {"cell_type": "markdown", "id": "8d725e33-f735-4528-aa0b-88ae8cc21b04", "metadata": {}, "source": ["## Sampling filings"]}, {"cell_type": "code", "execution_count": 18, "id": "c7a7824f-231d-4720-b00c-9dc58bf4cdf0", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────────────────────────────────────────── Filings ────────────────────────────────────────────────────╮\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1m \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71mform   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mcompany                                 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mcik    \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39mfiling_date\u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1maccession_number    \u001b[0m\u001b[1m \u001b[0m        │\n", "│  ───────────────────────────────────────────────────────────────────────────────────────────────────────        │\n", "│  \u001b[1m \u001b[0m\u001b[1m0\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71mN-PX   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mYieldstreet Alternative Income Fund Inc.\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1762229\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-26 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001104659-24-092656\u001b[0m\u001b[1m \u001b[0m        │\n", "│   1  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m8-K    \u001b[0m\u001b[38;5;71m \u001b[0m  CONSUMER PORTFOLIO SERVICES, INC.          889609   \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  0001683168-24-005410         │\n", "│  \u001b[1m \u001b[0m\u001b[1m2\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m4      \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mNovoCure Ltd                            \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1645113\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-06 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001562180-24-006040\u001b[0m\u001b[1m \u001b[0m        │\n", "│   3  \u001b[38;5;71m \u001b[0m\u001b[38;5;71mABS-15G\u001b[0m\u001b[38;5;71m \u001b[0m  GCP HS Fund                                1850564  \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-07-18 \u001b[0m\u001b[38;5;39m \u001b[0m  0001539497-24-001456         │\n", "│  \u001b[1m \u001b[0m\u001b[1m4\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71mD      \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1m \u001b[0m\u001b[1mBPLESTE A SERIES OF BPSPV MASTER LLC    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2030571\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-07-16 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0002030571-24-000002\u001b[0m\u001b[1m \u001b[0m        │\n", "│                                                                                                                 │\n", "│ Showing 5 of 5 filings                                                                                          │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["filings.sample(5)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}