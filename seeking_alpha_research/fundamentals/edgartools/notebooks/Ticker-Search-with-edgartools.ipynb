{"cells": [{"cell_type": "markdown", "id": "92f62e40-6c62-403b-8a60-e77231aa70c1", "metadata": {}, "source": ["# Ticker Search with <PERSON><PERSON><PERSON>\n", "\n", "This notebook show how to search for tickers using **edgartools**"]}, {"cell_type": "markdown", "id": "d79c0913-f693-4b73-9346-e2af8c3825fa", "metadata": {}, "source": ["\n", "**[Open this notebook in Google Colab](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/Ticker-Search-with-edgartools.ipynb)**"]}, {"cell_type": "code", "execution_count": null, "id": "9b38e67d-08c5-4707-9933-9f4488052d87", "metadata": {}, "outputs": [], "source": ["!pip install -U edgartools"]}, {"cell_type": "code", "execution_count": 12, "id": "e7b47693-7298-499a-bf19-7b32663f7720", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[12:33:55] </span><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> Identity of the Edgar REST client set to <span style=\"font-weight: bold\">[</span><EMAIL><span style=\"font-weight: bold\">]</span>                           <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">core.py:158</span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[12:33:55]\u001b[0m\u001b[2;36m \u001b[0m\u001b[34mINFO    \u001b[0m Identity of the Edgar REST client set to \u001b[1m[\u001b[<EMAIL>\u001b[1m]\u001b[0m                           \u001b[2mcore.py\u001b[0m\u001b[2m:\u001b[0m\u001b[2m158\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from edgar import *\n", "set_identity(\"<EMAIL>\")"]}, {"cell_type": "markdown", "id": "5af0a614-06ba-40e2-a6e9-8138c4f65089", "metadata": {}, "source": ["## Get a company by ticker\n", "The simplest way to get a company is by ticker"]}, {"cell_type": "code", "execution_count": 3, "id": "a5900c3e-3c70-4060-acfd-f1820825d235", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭───────────────────────────────────────────────\u001b[1;38;5;71m Apple Inc. (AAPL) \u001b[0m───────────────────────────────────────────────╮\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mCIK   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCategory               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIndustry            \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIncorporated\u001b[0m\u001b[1m \u001b[0m                                       │\n", "│  ────────────────────────────────────────────────────────────────────────                                       │\n", "│  \u001b[1;35m \u001b[0m\u001b[1;35m320193\u001b[0m\u001b[1;35m \u001b[0m  Large accelerated filer   Electronic Computers   California                                          │\n", "│                                                                                                                 │\n", "│ ╭───────── ✉ Mailing Address ──────────╮                 ╭──────── 🏢 Business Address ─────────╮               │\n", "│ │ ONE APPLE PARK WAY                   │                 │ ONE APPLE PARK WAY                   │               │\n", "│ │ CUPERTINO, CA 95014                  │                 │ CUPERTINO, CA 95014                  │               │\n", "│ ╰──────────────────────────────────────╯                 ╰──────────────────────────────────────╯               │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["company = Company(\"AAPL\")\n", "company"]}, {"cell_type": "markdown", "id": "8edc30ed-4bde-4632-a6b9-222ab0a5a5ef", "metadata": {}, "source": ["### The find_cik function"]}, {"cell_type": "markdown", "id": "968dd26f-e026-4d6d-89c5-100e18116c55", "metadata": {}, "source": ["This effectively calls `find_cik` and then `Company(cik)`"]}, {"cell_type": "code", "execution_count": 4, "id": "14805933-babd-4cea-9b2d-7acd84f00bf8", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭───────────────────────────────────────────────\u001b[1;38;5;71m Apple Inc. (AAPL) \u001b[0m───────────────────────────────────────────────╮\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mCIK   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCategory               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIndustry            \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIncorporated\u001b[0m\u001b[1m \u001b[0m                                       │\n", "│  ────────────────────────────────────────────────────────────────────────                                       │\n", "│  \u001b[1;35m \u001b[0m\u001b[1;35m320193\u001b[0m\u001b[1;35m \u001b[0m  Large accelerated filer   Electronic Computers   California                                          │\n", "│                                                                                                                 │\n", "│ ╭───────── ✉ Mailing Address ──────────╮                 ╭──────── 🏢 Business Address ─────────╮               │\n", "│ │ ONE APPLE PARK WAY                   │                 │ ONE APPLE PARK WAY                   │               │\n", "│ │ CUPERTINO, CA 95014                  │                 │ CUPERTINO, CA 95014                  │               │\n", "│ ╰──────────────────────────────────────╯                 ╰──────────────────────────────────────╯               │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from edgar.reference.tickers import find_cik, get_cik_tickers, get_mutual_fund_tickers\n", "\n", "apple_cik = find_cik(\"AAPL\")\n", "aapl = Company(apple_cik)\n", "aapl"]}, {"cell_type": "markdown", "id": "e004d53c-26c6-4a27-81bd-f321631ddfcf", "metadata": {}, "source": ["## The underlying cik-ticker reference data\n", "If you need the underylinbg data use `get_cik_tickers`"]}, {"cell_type": "code", "execution_count": 5, "id": "3b81f319-3727-42fc-a3ea-b0c42fa21225", "metadata": {}, "outputs": [], "source": ["cik_tickers = get_cik_tickers()"]}, {"cell_type": "markdown", "id": "0e932e58-fd0a-44fd-85b1-351c13c8b472", "metadata": {}, "source": ["##  Get Mutual Fund by ticker\n", "Ticker search also works for mutual funds and ETFs."]}, {"cell_type": "code", "execution_count": 6, "id": "2457e52b-814d-4cef-93bb-315a3995ea67", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cik</th>\n", "      <th>seriesId</th>\n", "      <th>classId</th>\n", "      <th>ticker</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2110</td>\n", "      <td>S000009184</td>\n", "      <td>C000024954</td>\n", "      <td>LACAX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2110</td>\n", "      <td>S000009184</td>\n", "      <td>C000024956</td>\n", "      <td>LIACX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2110</td>\n", "      <td>S000009184</td>\n", "      <td>C000024957</td>\n", "      <td>ACRNX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2110</td>\n", "      <td>S000009184</td>\n", "      <td>C000122735</td>\n", "      <td>CEARX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2110</td>\n", "      <td>S000009184</td>\n", "      <td>C000122736</td>\n", "      <td>CRBRX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28902</th>\n", "      <td>2008359</td>\n", "      <td>S000084724</td>\n", "      <td>C000249177</td>\n", "      <td>CGCV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28903</th>\n", "      <td>2008374</td>\n", "      <td>S000084722</td>\n", "      <td>C000249175</td>\n", "      <td>CGGE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28904</th>\n", "      <td>2008516</td>\n", "      <td>S000084726</td>\n", "      <td>C000249179</td>\n", "      <td>CGIC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28905</th>\n", "      <td>2008517</td>\n", "      <td>S000084728</td>\n", "      <td>C000249181</td>\n", "      <td>CGNG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28906</th>\n", "      <td>2020645</td>\n", "      <td>S000085730</td>\n", "      <td>C000251098</td>\n", "      <td>CBCFX</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>28907 rows × 4 columns</p>\n", "</div>"], "text/plain": ["           cik    seriesId     classId ticker\n", "0         2110  S000009184  C000024954  LACAX\n", "1         2110  S000009184  C000024956  LIACX\n", "2         2110  S000009184  C000024957  ACRNX\n", "3         2110  S000009184  C000122735  CEARX\n", "4         2110  S000009184  C000122736  CRBRX\n", "...        ...         ...         ...    ...\n", "28902  2008359  S000084724  C000249177   CGCV\n", "28903  2008374  S000084722  C000249175   CGGE\n", "28904  2008516  S000084726  C000249179   CGIC\n", "28905  2008517  S000084728  C000249181   CGNG\n", "28906  2020645  S000085730  C000251098  CBCFX\n", "\n", "[28907 rows x 4 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["get_mutual_fund_tickers()"]}, {"cell_type": "markdown", "id": "e93ef6dd-7b3d-4172-80a7-049a86a0a6e7", "metadata": {}, "source": ["## Get a mutual fund company"]}, {"cell_type": "code", "execution_count": 7, "id": "b9b241f6-1aa0-4dc8-a526-abcad376d11d", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭─────────────────────────────────────────────\u001b[1;38;5;71m COLUMBIA ACORN TRUST \u001b[0m──────────────────────────────────────────────╮\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mCIK \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIncorporated \u001b[0m\u001b[1m \u001b[0m                                                                                         │\n", "│  ──────────────────────                                                                                         │\n", "│  \u001b[1;35m \u001b[0m\u001b[1;35m2110\u001b[0m\u001b[1;35m \u001b[0m  Massachusetts                                                                                          │\n", "│                                                                                                                 │\n", "│ ╭───────── ✉ Mailing Address ──────────╮                 ╭──────── 🏢 Business Address ─────────╮               │\n", "│ │ 71 S. WACKER DRIVE, SUITE 2500       │                 │ 71 S. WACKER DRIVE, SUITE 2500       │               │\n", "│ │ CHICAGO, IL 60606                    │                 │ CHICAGO, IL 60606                    │               │\n", "│ ╰──────────────────────────────────────╯                 ╰──────────────────────────────────────╯               │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Company(\"CRBRX\")"]}, {"cell_type": "markdown", "id": "8aa8ea12-96ca-4999-b604-5baa78aeff1b", "metadata": {}, "source": ["## Get an ETF"]}, {"cell_type": "code", "execution_count": 8, "id": "8786071a-4d53-4e0e-812d-7c11b03806d3", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────────────────────────\u001b[1;38;5;71m Capital Group International Core Equity ETF \u001b[0m──────────────────────────────────╮\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mCIK    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIncorporated\u001b[0m\u001b[1m \u001b[0m                                                                                       │\n", "│  ────────────────────────                                                                                       │\n", "│  \u001b[1;35m \u001b[0m\u001b[1;35m2008516\u001b[0m\u001b[1;35m \u001b[0m  Delaware                                                                                            │\n", "│                                                                                                                 │\n", "│ ╭───────── ✉ Mailing Address ──────────╮                 ╭──────── 🏢 Business Address ─────────╮               │\n", "│ │ 333 SOUTH HOPE STREET                │                 │ 6455 IRVINE CENTER DRIVE             │               │\n", "│ │ 55TH FLOOR                           │                 │ IRVINE, CA 92618                     │               │\n", "│ │ LOS ANGELES, CA 90071                │                 ╰──────────────────────────────────────╯               │\n", "│ ╰──────────────────────────────────────╯                                                                        │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["Company(\"CGIC\")"]}, {"cell_type": "markdown", "id": "2976885f-d7da-4b87-913f-74dc27b566dc", "metadata": {}, "source": ["## Get all mutual fund tickers\n", "To get all mutual fund tickers use `get_mutual_fund_tickers`"]}, {"cell_type": "code", "execution_count": 9, "id": "e11d19d0-a94d-415c-aacc-e6f1f13d996e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cik</th>\n", "      <th>seriesId</th>\n", "      <th>classId</th>\n", "      <th>ticker</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2110</td>\n", "      <td>S000009184</td>\n", "      <td>C000024954</td>\n", "      <td>LACAX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2110</td>\n", "      <td>S000009184</td>\n", "      <td>C000024956</td>\n", "      <td>LIACX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2110</td>\n", "      <td>S000009184</td>\n", "      <td>C000024957</td>\n", "      <td>ACRNX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2110</td>\n", "      <td>S000009184</td>\n", "      <td>C000122735</td>\n", "      <td>CEARX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2110</td>\n", "      <td>S000009184</td>\n", "      <td>C000122736</td>\n", "      <td>CRBRX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28902</th>\n", "      <td>2008359</td>\n", "      <td>S000084724</td>\n", "      <td>C000249177</td>\n", "      <td>CGCV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28903</th>\n", "      <td>2008374</td>\n", "      <td>S000084722</td>\n", "      <td>C000249175</td>\n", "      <td>CGGE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28904</th>\n", "      <td>2008516</td>\n", "      <td>S000084726</td>\n", "      <td>C000249179</td>\n", "      <td>CGIC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28905</th>\n", "      <td>2008517</td>\n", "      <td>S000084728</td>\n", "      <td>C000249181</td>\n", "      <td>CGNG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28906</th>\n", "      <td>2020645</td>\n", "      <td>S000085730</td>\n", "      <td>C000251098</td>\n", "      <td>CBCFX</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>28907 rows × 4 columns</p>\n", "</div>"], "text/plain": ["           cik    seriesId     classId ticker\n", "0         2110  S000009184  C000024954  LACAX\n", "1         2110  S000009184  C000024956  LIACX\n", "2         2110  S000009184  C000024957  ACRNX\n", "3         2110  S000009184  C000122735  CEARX\n", "4         2110  S000009184  C000122736  CRBRX\n", "...        ...         ...         ...    ...\n", "28902  2008359  S000084724  C000249177   CGCV\n", "28903  2008374  S000084722  C000249175   CGGE\n", "28904  2008516  S000084726  C000249179   CGIC\n", "28905  2008517  S000084728  C000249181   CGNG\n", "28906  2020645  S000085730  C000251098  CBCFX\n", "\n", "[28907 rows x 4 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from edgar.reference.tickers import get_mutual_fund_tickers\n", "get_mutual_fund_tickers()"]}, {"cell_type": "markdown", "id": "acccf9a7-6553-461e-8c06-7481e5d53a09", "metadata": {}, "source": ["## Using the find function\n", "\n", "**edgartools** has a convenient function `find` that you can use to search for anything, including tickers"]}, {"cell_type": "code", "execution_count": null, "id": "ed75dea7-80ed-4f5e-a1b5-290f7bbe1f73", "metadata": {}, "outputs": [], "source": ["find(\"BRK.A\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}