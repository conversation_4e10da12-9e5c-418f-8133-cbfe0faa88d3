{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["# XBRL with Custom Company Tags\n", "\n", "Some 10-K filings contain XBRL with custom company tags which makes extracting XBRL difficult. This notebook the new XBRL parsing with these filings.\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/XBRL2-CustomTags.ipynb)"], "id": "dfb186c244961025"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "!pip install edgartools", "id": "15dea1c7aa0d45bf"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:49:22.232692Z", "start_time": "2025-04-12T22:49:22.147300Z"}}, "cell_type": "code", "source": ["from edgar import *\n", "from edgar.xbrl import *\n", "\n", "set_identity('<EMAIL>')"], "id": "ee53f89da93d13", "outputs": [], "execution_count": 17}, {"metadata": {}, "cell_type": "markdown", "source": ["## Union Pacific Corp\n", "\n", "Older filings from Union Pacific Corp contain custom company tags."], "id": "48bcdd8281db3dee"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:49:23.655097Z", "start_time": "2025-04-12T22:49:23.647426Z"}}, "cell_type": "code", "source": ["c = Company(\"UNP\")\n", "c"], "id": "9fae1d5a5d5e1247", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m─────────────────────────────────────\u001b[0m\u001b[38;5;244m 🏢 \u001b[0m\u001b[1;32mUNION PACIFIC CORP\u001b[0m\u001b[38;5;244m \u001b[0m\u001b[2;38;5;244m[100885] \u001b[0m\u001b[1;33mUNP\u001b[0m\u001b[38;5;244m \u001b[0m\u001b[38;5;244m──────────────────────────────────────\u001b[0m\u001b[38;5;244m─╮\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m                                                                                                                 \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m                                                                                                                 \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m────────────────────────────────────────────────\u001b[0m\u001b[38;5;244m 📋 Entity \u001b[0m\u001b[38;5;244m────────────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╮\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   CIK   \u001b[1;38;5;32m100885\u001b[0m   Type   \u001b[1;33mOperating\u001b[0m   \u001b[1;33m○\u001b[0m                                                                       \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1mCategor<PERSON>               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIndustry                            \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mFiscal Year End\u001b[0m\u001b[1m \u001b[0m                         \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  ──────────────────────────────────────────────────────────────────────────────────                         \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   Large accelerated filer   4011: Railroads, Line-Haul Operating   Dec 31                                   \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m──────────────────────────────────────────────\u001b[0m\u001b[38;5;244m 📈 Exchanges \u001b[0m\u001b[38;5;244m───────────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╮\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1mExchange\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSymbol\u001b[0m\u001b[1m \u001b[0m                                                                                        \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  ───────────────────                                                                                        \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   NYSE      \u001b[1;33m \u001b[0m\u001b[1;33mUNP   \u001b[0m\u001b[1;33m \u001b[0m                                                                                        \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m─\u001b[0m\u001b[38;5;244m 🏢 Business Address \u001b[0m\u001b[38;5;244m──\u001b[0m\u001b[38;5;244m─╮\u001b[0m         \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m──\u001b[0m\u001b[38;5;244m 📫 Mailing Address \u001b[0m\u001b[38;5;244m──\u001b[0m\u001b[38;5;244m─╮\u001b[0m       \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m 📞 Contact Information \u001b[0m\u001b[38;5;244m─╮\u001b[0m            \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m 1400 DOUGLAS STREET      \u001b[38;5;244m│\u001b[0m         \u001b[38;5;244m│\u001b[0m 1400 DOUGLAS STREET      \u001b[38;5;244m│\u001b[0m       \u001b[38;5;244m│\u001b[0m                          \u001b[38;5;244m│\u001b[0m            \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m<PERSON>\u001b[0m \u001b[38;5;244m│\u001b[0m STOP 0310                \u001b[38;5;244m<PERSON>\u001b[0m         \u001b[38;5;244m│\u001b[0m STOP 0310                \u001b[38;5;244m<PERSON>\u001b[0m       \u001b[38;5;244m│\u001b[0m  \u001b[1;38;5;249m \u001b[0m\u001b[1;38;5;249m<PERSON><PERSON>\u001b[0m\u001b[1;38;5;249m \u001b[0m  402 544 6763   \u001b[38;5;244m│\u001b[0m            \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m OMAHA, NE 68179          \u001b[38;5;244m<PERSON>\u001b[0m         \u001b[38;5;244m│\u001b[0m OMAHA, NE 68179          \u001b[38;5;244m│\u001b[0m       \u001b[38;5;244m│\u001b[0m                          \u001b[38;5;244m│\u001b[0m            \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╰──────────────────────────╯\u001b[0m         \u001b[38;5;244m╰──────────────────────────╯\u001b[0m       \u001b[38;5;244m╰──────────────────────────╯\u001b[0m            \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m╰─\u001b[0m\u001b[38;5;244m───────────────────────────────────────────────\u001b[0m\u001b[38;5;244m SEC Entity Data \u001b[0m\u001b[38;5;244m───────────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╯\u001b[0m"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "execution_count": 18}, {"metadata": {}, "cell_type": "markdown", "source": "## Get the XBRL for the filing", "id": "a28f465042c5a5fd"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:49:25.989906Z", "start_time": "2025-04-12T22:49:25.689218Z"}}, "cell_type": "code", "source": ["filing = Filing(company='UNION PACIFIC CORP', cik=100885, form='10-K', filing_date='2013-02-08', accession_no='0001193125-13-045658')\n", "xb = XBRL.from_filing(filing)\n"], "id": "30262ab3be668b75", "outputs": [], "execution_count": 19}, {"metadata": {}, "cell_type": "markdown", "source": "### Statements", "id": "2619ca4098a59b94"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:49:27.629449Z", "start_time": "2025-04-12T22:49:27.605188Z"}}, "cell_type": "code", "source": "xb.statements\n", "id": "c0b44cfb47fb2622", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1;32mFinancial Statements\u001b[0m                                                                               \n", "                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m#  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mName                                              \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mType               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mParenthetical \u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────── \n", " \u001b[2m \u001b[0m\u001b[1;2;36m2\u001b[0m\u001b[2m  \u001b[0m\u001b[2m \u001b[0m \u001b[32m \u001b[0m\u001b[32mStatementConsolidatedStatementsOfFinancialPosition\u001b[0m\u001b[32m \u001b[0m \u001b[3m \u001b[0m\u001b[3mBalanceSheet       \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m85\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[32m \u001b[0m\u001b[32mConsolidatedStatementsOfComprehensiveIncome       \u001b[0m\u001b[32m \u001b[0m \u001b[3m \u001b[0m\u001b[3mComprehensiveIncome\u001b[0m\u001b[3m \u001b[0m                  \n", "                                                                                                   \n", "\u001b[1;36mDisclosures\u001b[0m                                                                                        \n", "                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m#  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mName                                                      \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mType       \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mParenthetical \u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────── \n", " \u001b[2m \u001b[0m\u001b[1;2;36m20\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureAccountingPronouncements                        \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m14\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureAccountsPayableAndOtherCurrentLiabilities       \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m37\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureAccountsPayableAndOtherCurrentLiabilitiesDetails\u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m36\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureAccountsPayableAndOtherCurrentLiabilitiesTables \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m74\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureAccountsReceivable                              \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m75\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureAccountsReceivableDetails                       \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m17\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureCommitmentsAndContingencies                     \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m42\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureCommitmentsAndContingenciesDetails              \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m41\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureCommitmentsAndContingenciesTables               \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m16\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureDebt                                            \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m61\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureDebtDetails1                                    \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m64\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureDebtDetails1Parentheticals                      \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m  ✓               \n", " \u001b[2m \u001b[0m\u001b[1;2;36m65\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureDebtDetails3                                    \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m87\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureDebtDetails4                                    \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m62\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureDebtDetails5                                    \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m63\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureDebtTables                                      \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m11\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureEarningsPerShare                                \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m31\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureEarningsPerShareDetails                         \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m30\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureEarningsPerShareTables                          \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m15\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureFinancialInstruments                            \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m39\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureFinancialInstrumentsDetails                     \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m10\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureIncomeTaxes                                     \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m29\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureIncomeTaxesDetails1                             \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m77\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureIncomeTaxesDetails2                             \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m78\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureIncomeTaxesDetails3                             \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m79\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureIncomeTaxesDetails4                             \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m76\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureIncomeTaxesTables                               \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m67\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureLeasesTables                                    \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m19\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureNatureOfOperations                              \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m22\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureNatureOfOperationsDetails                       \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m21\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureNatureOfOperationsTables                        \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m9\u001b[0m\u001b[2m  \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureOtherIncome                                     \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m28\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureOtherIncomeDetails                              \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m38\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureOtherIncomeDetailsParentheticals                \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m  ✓               \n", " \u001b[2m \u001b[0m\u001b[1;2;36m27\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureOtherIncomeTables                               \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m13\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureProperties                                      \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m35\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosurePropertiesDetails                               \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m34\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosurePropertiesTables                                \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m8\u001b[0m\u001b[2m  \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlans                                 \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m54\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlansDetails1                         \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m53\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlansDetails2                         \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m25\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlansDetails3                         \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m55\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlansDetails4                         \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m56\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlansDetails5                         \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m57\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlansDetails6                         \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m58\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlansDetails7                         \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m82\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlansDetails8                         \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m59\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlansDetails9                         \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m26\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureRetirementPlansTables                           \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m71\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureScheduleOfValuationAndQualifyingAccounts        \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m73\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureScheduleOfValuationAndQualifyingAccountsDetails \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m72\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureScheduleOfValuationAndQualifyingAccountsTables  \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m68\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureSelectedQuarterlyDataUnaudited                  \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m18\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureShareRepurchaseProgram                          \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m43\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureShareRepurchaseProgramDetails                   \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m45\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureShareRepurchaseProgramTables                    \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m50\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureSignificantAccountingPolicies                   \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m48\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureSignificantAccountingPoliciesPolicies           \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m7\u001b[0m\u001b[2m  \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureStockOptionsAndOtherStockPlans                  \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m83\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureStockOptionsAndOtherStockPlansDetails1          \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m51\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureStockOptionsAndOtherStockPlansDetails2          \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m49\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureStockOptionsAndOtherStockPlansDetails3          \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m24\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureStockOptionsAndOtherStockPlansDetails4          \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m52\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureStockOptionsAndOtherStockPlansDetails5          \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m23\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureStockOptionsAndOtherStockPlansTables            \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m47\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[36m \u001b[0m\u001b[36mDisclosureVariableInterestEntities                        \u001b[0m\u001b[36m \u001b[0m \u001b[3m \u001b[0m\u001b[3mDisclosures\u001b[0m\u001b[3m \u001b[0m                  \n", "                                                                                                   \n", "\u001b[1;33mOther Sections\u001b[0m                                                                                                    \n", "                                                                                                                  \n", " \u001b[1m \u001b[0m\u001b[1m#  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mName                                                                            \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mType\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mParenthetical \u001b[0m\u001b[1m \u001b[0m \n", " ──────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", " \u001b[2m \u001b[0m\u001b[1;2;36m12\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mAccumulatedOtherComprehensiveIncomeLoss                                         \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m33\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mAccumulatedOtherComprehensiveIncomeLossDetails                                  \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m32\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mAccumulatedOtherComprehensiveIncomeLossTables                                   \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m86\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mConsolidatedStatementsOfComprehensiveIncomeParentheticals                       \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m  ✓               \n", " \u001b[2m \u001b[0m\u001b[1;2;36m60\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mDebtDetails2                                                                    \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m84\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mDebtDetails6                                                                    \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m0\u001b[0m\u001b[2m  \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mDocumentAndEntityInformation                                                    \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m44\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mFinancialInstrumentsTables                                                      \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m81\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mIncomeTaxesDetails5                                                             \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m80\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mIncomeTaxesDetails6                                                             \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m66\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mLeases                                                                          \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m40\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mLeasesDetails                                                                   \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m70\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mSelectedQuarterlyDataUnauditedDetails                                           \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m69\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mSelectedQuarterlyDataUnauditedTables                                            \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m4\u001b[0m\u001b[2m  \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mStatementConsolidatedStatementsOfCashFlows                                      \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m5\u001b[0m\u001b[2m  \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mStatementConsolidatedStatementsOfChangesInCommonShareholdersEquity              \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m6\u001b[0m\u001b[2m  \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mStatementConsolidatedStatementsOfChangesInCommonShareholdersEquityParentheticals\u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m  ✓               \n", " \u001b[2m \u001b[0m\u001b[1;2;36m3\u001b[0m\u001b[2m  \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mStatementConsolidatedStatementsOfFinancialPositionParentheticals                \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m  ✓               \n", " \u001b[2m \u001b[0m\u001b[1;2;36m1\u001b[0m\u001b[2m  \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mStatementConsolidatedStatementsOfIncome                                         \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", " \u001b[2m \u001b[0m\u001b[1;2;36m46\u001b[0m\u001b[2m \u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mVariableInterestEntitiesDetails                                                 \u001b[0m\u001b[33m \u001b[0m \u001b[3m \u001b[0m\u001b[3m    \u001b[0m\u001b[3m \u001b[0m                  \n", "                                                                                                                  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "execution_count": 20}, {"metadata": {}, "cell_type": "markdown", "source": "### Income Statement", "id": "1940fde718301a86"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:49:29.771396Z", "start_time": "2025-04-12T22:49:29.749904Z"}}, "cell_type": "code", "source": "xb.statements.income_statement()", "id": "90a34996660e1035", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m           Consolidated Statement of Income (Standardized)            \u001b[0m\n", "\u001b[3m     \u001b[0m\u001b[1;3mYear Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in scaled by 100,000)\u001b[0m\u001b[3m     \u001b[0m\n", "                                                                      \n", " \u001b[1m \u001b[0m\u001b[1m                                    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2012\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2011\u001b[0m\u001b[1m \u001b[0m \n", " ──────────────────────────────────────────────────────────────────── \n", "    Revenue                                                           \n", "      Revenue                                 $19,686        $18,508  \n", "      Revenue                                  $1,240         $1,049  \n", "    Revenue                                   $20,926        $19,557  \n", "      Compensation and benefits                $4,685         $4,681  \n", "      Fuel                                     $3,608         $3,581  \n", "      Purchased services and materials         $2,143         $2,005  \n", "      Depreciation and Amortization            $1,760         $1,617  \n", "      Equipment and other rents                $1,197         $1,167  \n", "      Other                                      $788           $782  \n", "    Operating Expenses                        $14,181        $13,833  \n", "    Operating Income                           $6,745         $5,724  \n", "    Other income (Note 6)                        $108           $112  \n", "    Interest Expense                           $(535)         $(572)  \n", "    Income before income taxes                 $6,318         $5,264  \n", "    Income Tax Expense                         $2,375         $1,972  \n", "    Net Income                                 $3,943         $3,292  \n", "      Earnings Per Share                         0.00           0.00  \n", "      Earnings Per Share (Diluted)               0.00           0.00  \n", "      Shares Outstanding                        4,731          4,857  \n", "      Shares Outstanding (Diluted)              4,765          4,898  \n", "    Dividends declared per share                $0.00          $0.00  \n", "                                                                      "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "execution_count": 21}, {"metadata": {}, "cell_type": "markdown", "source": "## Balance Sheet", "id": "d8a5e6d725e722b"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:49:31.816952Z", "start_time": "2025-04-12T22:49:31.795274Z"}}, "cell_type": "code", "source": "xb.statements.balance_sheet()", "id": "692f21990ecbdd27", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                           Consolidated Balance Sheets (Standardized)                           \u001b[0m\n", "\u001b[3m                     \u001b[0m\u001b[1;3mFiscal Year Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except per share data)\u001b[0m\u001b[3m                     \u001b[0m\n", "                                                                                                \n", " \u001b[1m \u001b[0m\u001b[1m                                                              \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2011\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2012\u001b[0m\u001b[1m \u001b[0m \n", " ────────────────────────────────────────────────────────────────────────────────────────────── \n", "        Cash and Cash Equivalents                                        $1,217         $1,063  \n", "        Accounts Receivable                                              $1,401         $1,331  \n", "        Materials and supplies                                             $614           $660  \n", "        Current portion of deferred taxes                                  $306           $263  \n", "        Other Assets                                                       $189           $297  \n", "        Total Current Assets                                             $3,727         $3,614  \n", "      Investments                                                        $1,175         $1,259  \n", "      Property, Plant and Equipment                                     $39,934        $41,997  \n", "      Other Assets                                                         $260           $283  \n", "      Total Assets                                                      $45,096        $47,153  \n", "        Accounts payable and other current liabilities (Note 12)         $3,108         $2,923  \n", "        Current portion of debt                                            $209           $196  \n", "        Total Current Liabilities                                        $3,317         $3,119  \n", "      Long-Term Debt                                                     $8,697         $8,801  \n", "      Deferred income taxes                                             $12,368        $13,108  \n", "      Other Liabilities                                                  $2,136         $2,248  \n", "      Commitments and contingencies (Notes 16 and 17)                                           \n", "      Total Liabilities                                                 $26,518        $27,276  \n", "        Common Stock                                                     $1,386         $1,386  \n", "        Paid-in-surplus                                                  $4,031         $4,113  \n", "        Retained Earnings                                               $19,508        $22,271  \n", "        Treasury Stock Common Value                                    $(5,293)       $(6,707)  \n", "        Accumulated Other Comprehensive Income/Loss                    $(1,054)       $(1,186)  \n", "        Total Stockholders' Equity                                      $18,578        $19,877  \n", "      Total Liabilities and Stockholders' Equity                        $45,096        $47,153  \n", "                                                                                                "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "execution_count": 22}, {"metadata": {}, "cell_type": "markdown", "source": "## Cashflow Statement", "id": "638bf9d7d6db6655"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:49:33.752819Z", "start_time": "2025-04-12T22:49:33.725127Z"}}, "cell_type": "code", "source": "xb.statements.cashflow_statement()", "id": "79de4fe122a5a3ba", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                  Consolidated Statement of Cash Flows (Standardized)                   \u001b[0m\n", "\u001b[3m                    \u001b[0m\u001b[1;3mYear Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except per share data)\u001b[0m\u001b[3m                     \u001b[0m\n", "                                                                                        \n", " \u001b[1m \u001b[0m\u001b[1m                                                      \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2012\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2011\u001b[0m\u001b[1m \u001b[0m \n", " ────────────────────────────────────────────────────────────────────────────────────── \n", "      Net Income                                                 $3,943         $3,292  \n", "      Net Income                                                                        \n", "        Depreciation and Amortization                            $1,760         $1,617  \n", "        Deferred income tax expense                                $887           $986  \n", "        Other operating activities, net                          $(160)         $(298)  \n", "          Accounts receivable, net                                  $70         $(217)  \n", "          Materials and supplies                                  $(46)          $(80)  \n", "          Other current assets                                   $(108)           $178  \n", "          Accounts payable and other current liabilities         $(185)           $395  \n", "        Net Cash from Operating Activities                       $6,161         $5,873  \n", "      Payments for Property, Plant and Equipment               $(3,738)       $(3,176)  \n", "      Acquisition of equipment pending financing                   $274            $85  \n", "      Proceeds from sale of assets financed                        $274            $85  \n", "      Proceeds from asset sales                                     $80           $108  \n", "      Other investing activities, net                               $25          $(51)  \n", "      Net Cash from Investing Activities                       $(3,633)       $(3,119)  \n", "      Common share repurchases (Note 18)                       $(1,474)       $(1,418)  \n", "      Payments of Dividends                                    $(1,146)         $(837)  \n", "      Debt repaid                                                $(758)         $(690)  \n", "      Proceeds from Issuance of Long-Term Debt                     $695           $486  \n", "      Debt exchange                                                             $(272)  \n", "      Other financing activities, net                                $1           $108  \n", "      Net Cash from Financing Activities                       $(2,682)       $(2,623)  \n", "    Net Change in Cash                                           $(154)           $131  \n", "    Cash and Cash Equivalents                                                           \n", "    Cash and Cash Equivalents                                                           \n", "        Cash dividends declared but not yet paid                                        \n", "        Capital lease financings                                   $290           $154  \n", "        Capital investments accrued but not yet paid               $136           $147  \n", "        Interest, net of amounts capitalized                       $561           $572  \n", "        Income Tax Expense                                     $(1,552)         $(625)  \n", "                                                                                        "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "execution_count": 23}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}