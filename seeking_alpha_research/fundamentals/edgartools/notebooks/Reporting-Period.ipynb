{"cells": [{"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "e0a023b3dea6c227", "metadata": {"ExecuteTime": {"end_time": "2025-01-01T15:58:50.959456Z", "start_time": "2025-01-01T15:58:46.316175Z"}}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a86e03ec0653498f83ad69440e05d1d3", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────────────────────────────\u001b[1m MORGAN STANLEY [895421] 13F-HR/A 📄 \u001b[0m──────────────────────────────────────╮\n", "│ ╭──────────────────────┬────────────╮                                                                           │\n", "│ │\u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m0000895421-24-000436\u001b[0m\u001b[1;38;5;39m \u001b[0m│ 2024-08-16 │                                                                           │\n", "│ ╰──────────────────────┴────────────╯                                                                           │\n", "│ ╭─────────────────────────────────────────────────────────────────────────────────────────────────╮             │\n", "│ │\u001b[1m \u001b[0m\u001b[1mLinks\u001b[0m\u001b[1m: 🏠 Homepage 📄 Primary Document 📜 Full Submission Text                                 \u001b[0m\u001b[1m \u001b[0m│             │\n", "│ ├─────────────────────────────────────────────────────────────────────────────────────────────────┤             │\n", "│ │ 🏠 https://sec.gov/Archives/edgar/data/895421/0000895421-24-000436-index.html                   │             │\n", "│ │ 📄 https://sec.gov/Archives/edgar/data/895421/000089542124000436/xslForm13F_X02/primary_doc.xml │             │\n", "│ │ 📜 https://sec.gov/Archives/edgar/data/895421/000089542124000436/0000895421-24-000436.txt       │             │\n", "│ ╰─────────────────────────────────────────────────────────────────────────────────────────────────╯             │\n", "╰────────────── Form 13F-HR Amendment: Initial quarterly holdings report by institutional managers ───────────────╯"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from edgar import *\n", "\n", "filing = find(\"0000895421-24-000436\")\n", "filing\n"]}, {"cell_type": "code", "execution_count": 2, "id": "21dc39d9146e1409", "metadata": {"ExecuteTime": {"end_time": "2025-01-01T16:10:55.168384Z", "start_time": "2025-01-01T16:10:54.266579Z"}}, "outputs": [], "source": ["c = Company(filing.cik)"]}, {"cell_type": "code", "execution_count": 11, "id": "9c92cc79b04c5cf5", "metadata": {"ExecuteTime": {"end_time": "2025-01-01T20:10:27.295250Z", "start_time": "2025-01-01T20:10:27.281590Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accession_number</th>\n", "      <th>filing_date</th>\n", "      <th>reportDate</th>\n", "      <th>acceptanceDateTime</th>\n", "      <th>act</th>\n", "      <th>form</th>\n", "      <th>fileNumber</th>\n", "      <th>items</th>\n", "      <th>size</th>\n", "      <th>isXBRL</th>\n", "      <th>isInlineXBRL</th>\n", "      <th>primaryDocument</th>\n", "      <th>primaryDocDescription</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>468</th>\n", "      <td>0000950103-24-018085</td>\n", "      <td>2024-12-20</td>\n", "      <td>2024-12-19</td>\n", "      <td>2024-12-20T16:07:59.000Z</td>\n", "      <td>34</td>\n", "      <td>8-K</td>\n", "      <td>001-11758</td>\n", "      <td>5.02,9.01</td>\n", "      <td>326702</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>dp222425_8k.htm</td>\n", "      <td>FORM 8-K</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1098</th>\n", "      <td>0001628280-24-049768</td>\n", "      <td>2024-12-03</td>\n", "      <td>2024-12-01</td>\n", "      <td>2024-12-03T16:26:55.000Z</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>4950</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>xslF345X05/wk-form4_1733261208.xml</td>\n", "      <td>FORM 4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1099</th>\n", "      <td>0001628280-24-049765</td>\n", "      <td>2024-12-03</td>\n", "      <td>2024-12-01</td>\n", "      <td>2024-12-03T16:25:44.000Z</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>4937</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>xslF345X05/wk-form4_1733261137.xml</td>\n", "      <td>FORM 4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1100</th>\n", "      <td>0001628280-24-049763</td>\n", "      <td>2024-12-03</td>\n", "      <td>2024-12-01</td>\n", "      <td>2024-12-03T16:24:46.000Z</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>6210</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>xslF345X05/wk-form4_1733261079.xml</td>\n", "      <td>FORM 4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1101</th>\n", "      <td>0001628280-24-049760</td>\n", "      <td>2024-12-03</td>\n", "      <td>2024-12-01</td>\n", "      <td>2024-12-03T16:23:23.000Z</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>4928</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>xslF345X05/wk-form4_1733260997.xml</td>\n", "      <td>FORM 4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11916</th>\n", "      <td>0001209191-24-001921</td>\n", "      <td>2024-01-19</td>\n", "      <td>2024-01-17</td>\n", "      <td>2024-01-19T16:13:45.000Z</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>6726</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>xslF345X05/doc4.xml</td>\n", "      <td>FORM 4 SUBMISSION</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11917</th>\n", "      <td>0001209191-24-001920</td>\n", "      <td>2024-01-19</td>\n", "      <td>2024-01-17</td>\n", "      <td>2024-01-19T16:12:46.000Z</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>6695</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>xslF345X05/doc4.xml</td>\n", "      <td>FORM 4 SUBMISSION</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11918</th>\n", "      <td>0001209191-24-001916</td>\n", "      <td>2024-01-19</td>\n", "      <td>2024-01-17</td>\n", "      <td>2024-01-19T16:11:28.000Z</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>8442</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>xslF345X05/doc4.xml</td>\n", "      <td>FORM 4 SUBMISSION</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11920</th>\n", "      <td>0000950103-24-000919</td>\n", "      <td>2024-01-19</td>\n", "      <td>2024-01-17</td>\n", "      <td>2024-01-19T16:04:12.000Z</td>\n", "      <td>34</td>\n", "      <td>8-K</td>\n", "      <td>001-11758</td>\n", "      <td>5.02,8.01</td>\n", "      <td>331548</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>dp205475_8k.htm</td>\n", "      <td>FORM 8-K</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12045</th>\n", "      <td>0001157523-24-000060</td>\n", "      <td>2024-01-16</td>\n", "      <td>2024-01-16</td>\n", "      <td>2024-01-16T08:15:31.000Z</td>\n", "      <td>34</td>\n", "      <td>8-K</td>\n", "      <td>001-11758</td>\n", "      <td>2.02,7.01,9.01</td>\n", "      <td>5330104</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>a53881916.htm</td>\n", "      <td>MORGAN STANLEY 8-K</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>78 rows × 13 columns</p>\n", "</div>"], "text/plain": ["           accession_number filing_date  reportDate        acceptanceDateTime  \\\n", "468    0000950103-24-018085  2024-12-20  2024-12-19  2024-12-20T16:07:59.000Z   \n", "1098   0001628280-24-049768  2024-12-03  2024-12-01  2024-12-03T16:26:55.000Z   \n", "1099   0001628280-24-049765  2024-12-03  2024-12-01  2024-12-03T16:25:44.000Z   \n", "1100   0001628280-24-049763  2024-12-03  2024-12-01  2024-12-03T16:24:46.000Z   \n", "1101   0001628280-24-049760  2024-12-03  2024-12-01  2024-12-03T16:23:23.000Z   \n", "...                     ...         ...         ...                       ...   \n", "11916  0001209191-24-001921  2024-01-19  2024-01-17  2024-01-19T16:13:45.000Z   \n", "11917  0001209191-24-001920  2024-01-19  2024-01-17  2024-01-19T16:12:46.000Z   \n", "11918  0001209191-24-001916  2024-01-19  2024-01-17  2024-01-19T16:11:28.000Z   \n", "11920  0000950103-24-000919  2024-01-19  2024-01-17  2024-01-19T16:04:12.000Z   \n", "12045  0001157523-24-000060  2024-01-16  2024-01-16  2024-01-16T08:15:31.000Z   \n", "\n", "      act form fileNumber           items     size  isXBRL  isInlineXBRL  \\\n", "468    34  8-K  001-11758       5.02,9.01   326702       1             1   \n", "1098         4                                4950       0             0   \n", "1099         4                                4937       0             0   \n", "1100         4                                6210       0             0   \n", "1101         4                                4928       0             0   \n", "...    ..  ...        ...             ...      ...     ...           ...   \n", "11916        4                                6726       0             0   \n", "11917        4                                6695       0             0   \n", "11918        4                                8442       0             0   \n", "11920  34  8-K  001-11758       5.02,8.01   331548       1             1   \n", "12045  34  8-K  001-11758  2.02,7.01,9.01  5330104       1             1   \n", "\n", "                          primaryDocument primaryDocDescription  \n", "468                       dp222425_8k.htm              FORM 8-K  \n", "1098   xslF345X05/wk-form4_1733261208.xml                FORM 4  \n", "1099   xslF345X05/wk-form4_1733261137.xml                FORM 4  \n", "1100   xslF345X05/wk-form4_1733261079.xml                FORM 4  \n", "1101   xslF345X05/wk-form4_1733260997.xml                FORM 4  \n", "...                                   ...                   ...  \n", "11916                 xslF345X05/doc4.xml     FORM 4 SUBMISSION  \n", "11917                 xslF345X05/doc4.xml     FORM 4 SUBMISSION  \n", "11918                 xslF345X05/doc4.xml     FORM 4 SUBMISSION  \n", "11920                     dp205475_8k.htm              FORM 8-K  \n", "12045                       a53881916.htm    MORGAN STANLEY 8-K  \n", "\n", "[78 rows x 13 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df = c.filings.to_pandas().query(\"reportDate !=''\")\n", "df\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}