{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "# Fraud Analysis", "id": "ffd26d727ed5d0ec"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:53:06.087055Z", "start_time": "2025-04-12T22:53:06.082494Z"}}, "cell_type": "code", "source": ["from edgar import *\n", "from edgar.xbrl import *\n", "from edgar.xbrl.analysis.ratios import *\n", "from edgar.xbrl.analysis.metrics import *\n"], "id": "7f96a435b6f3f207", "outputs": [], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:53:06.079545Z", "start_time": "2025-04-12T22:53:04.936034Z"}}, "cell_type": "code", "source": ["c = Company(\"CMCSA\")\n", "filing = c.latest(\"10-K\")\n", "xb = XBRL.from_filing(filing)"], "id": "75ef07ed5909bd", "outputs": [], "execution_count": 1}, {"metadata": {}, "cell_type": "markdown", "source": "## Balance Sheet", "id": "9403abb4318bd93b"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:53:07.241277Z", "start_time": "2025-04-12T22:53:07.216510Z"}}, "cell_type": "code", "source": ["balance_sheet = xb.statements.balance_sheet()\n", "balance_sheet"], "id": "4ebde77eb3a78b0b", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                      Consolidated Balance Sheets (Standardized)                       \u001b[0m\n", "\u001b[3m                \u001b[0m\u001b[1;3mFiscal Year Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except per share data)\u001b[0m\u001b[3m                 \u001b[0m\n", "                                                                                       \n", " \u001b[1m \u001b[0m\u001b[1m                                                     \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2024\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────── \n", "        Assets                                                                         \n", "          Current Assets:                                                              \n", "            Cash and Cash Equivalents                           $6,215         $7,322  \n", "            Accounts Receivable                                $13,813        $13,661  \n", "            Other Assets                                        $3,959         $5,817  \n", "            Total Current Assets                               $23,987        $26,801  \n", "          Film and television costs                            $12,920        $12,541  \n", "          Long-Term Investments                                 $9,385         $8,647  \n", "          Property, Plant and Equipment                        $59,700        $62,500  \n", "          Goodwill                                             $59,300        $58,200  \n", "          Franchise rights                                     $59,365        $59,365  \n", "          Other intangible assets, net                         $27,867        $25,599  \n", "          Other Assets                                         $12,333        $12,501  \n", "          Total Assets                                        $264,811       $266,211  \n", "        Liabilities and Equity                                                         \n", "          Current Liabilities:                                                         \n", "            Accounts Payable                                   $12,437        $11,321  \n", "            Deferred Revenue                                    $3,242         $3,507  \n", "            Accrued Liabilities                                $13,284        $10,679  \n", "            Short-Term Debt                                     $2,100         $4,900  \n", "            Advance on sale of investment                       $9,167         $9,167  \n", "            Total Current Liabilities                          $40,198        $39,581  \n", "          Long-Term Debt                                       $95,000        $94,200  \n", "          Deferred income taxes                                $26,003        $25,227  \n", "          Other Liabilities                                    $20,122        $20,942  \n", "          Commitments and contingencies                                                \n", "          Redeemable noncontrolling interests                     $241           $237  \n", "          Equity:                                                                      \n", "            Preferred Stock                                                            \n", "            Common Stock                                           $48            $47  \n", "            Additional paid-in capital                         $38,533        $38,102  \n", "            Retained Earnings                                  $52,892        $56,972  \n", "            Treasury Stock Common Value                       $(7,517)       $(7,517)  \n", "            Accumulated Other Comprehensive Income/Loss       $(1,253)       $(2,043)  \n", "            Total Stockholders' Equity                         $82,703        $85,560  \n", "            Minority Interest                                     $523           $477  \n", "            Total Stockholders' Equity                         $83,226        $86,038  \n", "          Total Liabilities and Stockholders' Equity          $264,811       $266,211  \n", "                                                                                       "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"metadata": {}, "cell_type": "markdown", "source": "## Income Statement", "id": "96ef69d503946f1f"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:53:09.771690Z", "start_time": "2025-04-12T22:53:09.741514Z"}}, "cell_type": "code", "source": ["income_statement = xb.statements.income_statement()\n", "df = income_statement.to_dataframe()\n", "income_statement"], "id": "2911eb1e3365dcc8", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                       Consolidated Statement of Income (Standardized)                        \u001b[0m\n", "\u001b[3m                       \u001b[0m\u001b[1;3mYear Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except per share data)\u001b[0m\u001b[3m                        \u001b[0m\n", "                                                                                              \n", " \u001b[1m \u001b[0m\u001b[1m                                             \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2022\u001b[0m\u001b[1m \u001b[0m \n", " ──────────────────────────────────────────────────────────────────────────────────────────── \n", "    Revenue                                           $123,731       $121,572       $121,427  \n", "    Costs and Expenses:                                                                       \n", "      Programming and production                       $37,026        $36,762        $38,213  \n", "      Marketing and promotion                           $8,073         $7,971         $8,506  \n", "      Other operating and administrative               $40,533        $39,190        $38,263  \n", "      Depreciation and Amortization                     $8,729         $8,854         $8,724  \n", "      Depreciation and Amortization                     $6,072         $5,482         $5,097  \n", "      Goodwill and long-lived asset impairments                                       $8,583  \n", "      Cost of Revenue                               $(100,434)      $(98,258)     $(107,385)  \n", "    Operating Income                                   $23,297        $23,314        $14,041  \n", "    Interest Expense                                  $(4,134)       $(4,087)       $(3,896)  \n", "    Nonoperating Income/Expense                         $(490)         $1,252         $(861)  \n", "    Income Before Tax                                  $18,673        $20,478         $9,284  \n", "    Income Tax Expense                                  $2,796         $5,371         $4,359  \n", "    Net Income                                         $15,877        $15,107         $4,925  \n", "    Net Income                                            $315           $282           $445  \n", "    Net Income                                         $16,192        $15,388         $5,370  \n", "    Earnings Per Share                                    0.00           0.00           0.00  \n", "    Earnings Per Share (Diluted)                          0.00           0.00           0.00  \n", "                                                                                              "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:53:11.259970Z", "start_time": "2025-04-12T22:53:11.235174Z"}}, "cell_type": "code", "source": ["(xb.query()\n", " .from_statement(\"IncomeStatement\")\n", " .by_label(\"Total costs\")\n", " .to_dataframe()\n", " )\n"], "id": "22d53bdb2c0812df", "outputs": [{"data": {"text/plain": ["                    concept                     label          value  \\\n", "0  us-gaap:CostsAndExpenses  Total costs and expenses  -100434000000   \n", "1  us-gaap:CostsAndExpenses  Total costs and expenses   -98258000000   \n", "2  us-gaap:CostsAndExpenses  Total costs and expenses  -107385000000   \n", "\n", "   numeric_value period_start  period_end unit_ref decimals   statement_type  \\\n", "0  -1.004340e+11   2024-01-01  2024-12-31      usd       -6  IncomeStatement   \n", "1  -9.825800e+10   2023-01-01  2023-12-31      usd       -6  IncomeStatement   \n", "2  -1.073850e+11   2022-01-01  2022-12-31      usd       -6  IncomeStatement   \n", "\n", "                                      statement_role  \n", "0  http://www.comcast.com/role/ConsolidatedStatem...  \n", "1  http://www.comcast.com/role/ConsolidatedStatem...  \n", "2  http://www.comcast.com/role/ConsolidatedStatem...  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>value</th>\n", "      <th>numeric_value</th>\n", "      <th>period_start</th>\n", "      <th>period_end</th>\n", "      <th>unit_ref</th>\n", "      <th>decimals</th>\n", "      <th>statement_type</th>\n", "      <th>statement_role</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap:CostsAndExpenses</td>\n", "      <td>Total costs and expenses</td>\n", "      <td>-100434000000</td>\n", "      <td>-1.004340e+11</td>\n", "      <td>2024-01-01</td>\n", "      <td>2024-12-31</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.comcast.com/role/ConsolidatedStatem...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap:CostsAndExpenses</td>\n", "      <td>Total costs and expenses</td>\n", "      <td>-98258000000</td>\n", "      <td>-9.825800e+10</td>\n", "      <td>2023-01-01</td>\n", "      <td>2023-12-31</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.comcast.com/role/ConsolidatedStatem...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap:CostsAndExpenses</td>\n", "      <td>Total costs and expenses</td>\n", "      <td>-107385000000</td>\n", "      <td>-1.073850e+11</td>\n", "      <td>2022-01-01</td>\n", "      <td>2022-12-31</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.comcast.com/role/ConsolidatedStatem...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:53:13.913933Z", "start_time": "2025-04-12T22:53:13.901321Z"}}, "cell_type": "code", "source": ["(xb.facts.to_dataframe()\n", " .query(\"concept.str.match('us-gaap:Cost.*')\")\n", " .filter(['concept', 'label', 'value'])\n", " )"], "id": "6b944be9518be8ac", "outputs": [{"data": {"text/plain": ["                      concept                     label          value\n", "131  us-gaap:CostsAndExpenses  Total costs and expenses  -100434000000\n", "132  us-gaap:CostsAndExpenses  Total costs and expenses   -98258000000\n", "133  us-gaap:CostsAndExpenses  Total costs and expenses  -107385000000"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>131</th>\n", "      <td>us-gaap:CostsAndExpenses</td>\n", "      <td>Total costs and expenses</td>\n", "      <td>-100434000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>132</th>\n", "      <td>us-gaap:CostsAndExpenses</td>\n", "      <td>Total costs and expenses</td>\n", "      <td>-98258000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133</th>\n", "      <td>us-gaap:CostsAndExpenses</td>\n", "      <td>Total costs and expenses</td>\n", "      <td>-107385000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-29T16:55:22.171522Z", "start_time": "2025-03-29T16:55:22.099192Z"}}, "cell_type": "code", "source": "fr = FinancialRatios(xb)", "id": "9f6b45926d77f0b3", "outputs": [], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-29T16:56:31.635642Z", "start_time": "2025-03-29T16:56:31.631453Z"}}, "cell_type": "code", "source": "fr.get_ratio_data('operating_margin')", "id": "9924df45eed8c76e", "outputs": [{"data": {"text/plain": ["                                 2022-12-31 2023-12-31 2024-12-31\n", "StandardConcept.OPERATING_INCOME        NaN        NaN        NaN\n", "StandardConcept.REVENUE                 NaN        NaN        NaN"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>2022-12-31</th>\n", "      <th>2023-12-31</th>\n", "      <th>2024-12-31</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>StandardConcept.OPERATING_INCOME</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>StandardConcept.REVENUE</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "execution_count": 7}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "6d9f53d079532dfd"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}