{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["# Stitching Statements\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/XBRL2-StitchingStatements.ipynb)"], "id": "f489bd28c4cb21fe"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "!pip install edgartools", "id": "42562996c4a195d0"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-27T15:24:17.404410Z", "start_time": "2025-03-27T15:24:17.380765Z"}}, "cell_type": "code", "source": ["from edgar import *\n", "from edgar.xbrl import *\n", "from edgar.reference.tickers import popular_us_stocks\n", "\n", "set_identity(\"<EMAIL>\")\n", "\n", "stocks = popular_us_stocks()\n", "tickers = stocks.Ticker.to_list()\n", "\n", "ticker = tickers[0]\n", "ticker\n"], "id": "f0fc4ed5b0e56d35", "outputs": [{"data": {"text/plain": ["'AAPL'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-27T15:24:22.374487Z", "start_time": "2025-03-27T15:24:19.420931Z"}}, "cell_type": "code", "source": ["c = Company(ticker)\n", "filings = c.get_filings(form=\"10-K\").head(5)\n", "xbrls = XBRLS.from_filings(filings)\n", "c"], "id": "a48ff3c6b072b667", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m─────────────────────────────────────────\u001b[0m\u001b[38;5;244m 🏢 \u001b[0m\u001b[1;32mApple Inc.\u001b[0m\u001b[38;5;244m \u001b[0m\u001b[2;38;5;244m[320193] \u001b[0m\u001b[1;33mAAPL\u001b[0m\u001b[38;5;244m \u001b[0m\u001b[38;5;244m─────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╮\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m                                                                                                                 \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m                                                                                                                 \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m────────────────────────────────────────────────\u001b[0m\u001b[38;5;244m 📋 Entity \u001b[0m\u001b[38;5;244m────────────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╮\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   CIK   \u001b[1;38;5;32m320193\u001b[0m   Type   \u001b[1;33mOperating\u001b[0m   \u001b[1;33m○\u001b[0m                                                                       \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1mCategor<PERSON>               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIndustry                  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mFiscal Year End\u001b[0m\u001b[1m \u001b[0m                                   \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  ────────────────────────────────────────────────────────────────────────                                   \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   Large accelerated filer   3571: Electronic Computers   Sep 27                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m──────────────────────────────────────────────\u001b[0m\u001b[38;5;244m 📈 Exchanges \u001b[0m\u001b[38;5;244m───────────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╮\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1mExchange\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSymbol\u001b[0m\u001b[1m \u001b[0m                                                                                        \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  ───────────────────                                                                                        \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   Nasdaq    \u001b[1;33m \u001b[0m\u001b[1;33mAAPL  \u001b[0m\u001b[1;33m \u001b[0m                                                                                        \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m──\u001b[0m\u001b[38;5;244m 🏢 Business Address \u001b[0m\u001b[38;5;244m───\u001b[0m\u001b[38;5;244m─╮\u001b[0m      \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m───\u001b[0m\u001b[38;5;244m 📫 Mailing Address \u001b[0m\u001b[38;5;244m───\u001b[0m\u001b[38;5;244m─╮\u001b[0m     \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m─\u001b[0m\u001b[38;5;244m 📞 Contact Information \u001b[0m\u001b[38;5;244m─\u001b[0m\u001b[38;5;244m─╮\u001b[0m           \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m ONE APPLE PARK WAY         \u001b[38;5;244m│\u001b[0m      \u001b[38;5;244m│\u001b[0m ONE APPLE PARK WAY         \u001b[38;5;244m│\u001b[0m     \u001b[38;5;244m│\u001b[0m                            \u001b[38;5;244m│\u001b[0m           \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m CUPERTINO, CA 95014        \u001b[38;5;244m│\u001b[0m      \u001b[38;5;244m│\u001b[0m CUPERTINO, CA 95014        \u001b[38;5;244m│\u001b[0m     \u001b[38;5;244m│\u001b[0m  \u001b[1;38;5;249m \u001b[0m\u001b[1;38;5;249m<PERSON>hone\u001b[0m\u001b[1;38;5;249m \u001b[0m  (408) 996-1010   \u001b[38;5;244m│\u001b[0m           \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╰────────────────────────────╯\u001b[0m      \u001b[38;5;244m╰────────────────────────────╯\u001b[0m     \u001b[38;5;244m│\u001b[0m                            \u001b[38;5;244m│\u001b[0m           \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m                                                                        \u001b[38;5;244m╰────────────────────────────╯\u001b[0m           \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m─────────────────────────────────────────────\u001b[0m\u001b[38;5;244m 📜 Former Names \u001b[0m\u001b[38;5;244m─────────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╮\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   \u001b[3mAPPLE INC             \u001b[0m   January 2007 to August 2019                                                      \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   \u001b[3mAPPLE COMPUTER INC    \u001b[0m   January 1994 to January 2007                                                     \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   \u001b[3mAPPLE COMPUTER INC/ FA\u001b[0m   July 1997 to July 1997                                                           \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m╰─\u001b[0m\u001b[38;5;244m───────────────────────────────────────────────\u001b[0m\u001b[38;5;244m SEC Entity Data \u001b[0m\u001b[38;5;244m───────────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╯\u001b[0m"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-27T15:24:25.984228Z", "start_time": "2025-03-27T15:24:25.911756Z"}}, "cell_type": "code", "source": ["balance_sheet = xbrls.statements.balance_sheet()\n", "balance_sheet"], "id": "74d3c8ce26ee4be4", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                             CONSOLIDATED BALANCE SHEET (5-Period View) (Standardized)                             \u001b[0m\n", "\u001b[3m                            \u001b[0m\u001b[1;3mFiscal Year Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m                            \u001b[0m\n", "                                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m                                    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 30, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 24, 2022\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 25, 2021\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 26, 2020\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "      Commitments and contingencies                                                                                \n", "      Common Stock Shares Issued           15,116,786     15,550,061                                               \n", "      Common Stock Shares Outstanding      15,116,786     15,550,061                                               \n", "      Total Assets                           $364,980       $352,583       $352,755       $351,002       $323,888  \n", "      Total Liabilities                      $308,030       $290,437       $302,083       $287,912       $258,549  \n", "      Total Liabilities and                  $364,980       $352,583       $352,755       $351,002       $323,888  \n", "  Stockholders' Equity                                                                                             \n", "        Accounts Payable                      $68,960        $62,611        $64,115        $54,763        $42,296  \n", "        Accounts Receivable                   $33,410        $29,508        $28,184        $26,278        $16,120  \n", "        Accumulated Other                    $(7,172)      $(11,452)      $(11,109)           $163         $(406)  \n", "  Comprehensive Income/Loss                                                                                        \n", "        Cash and Cash Equivalents             $29,943        $29,965        $23,646        $34,940        $38,016  \n", "        Commercial paper                       $9,967         $5,985         $9,982         $6,000         $5,000  \n", "        Common Stock                          $83,276        $73,812        $64,849        $57,365        $50,779  \n", "        Deferred Revenue                       $8,249         $8,061         $7,912         $7,612         $6,643  \n", "        Inventory                              $7,286         $6,331         $4,946         $6,580         $4,061  \n", "        Long-Term Debt                        $85,750        $95,281        $98,959       $109,106        $98,667  \n", "        Marketable Securities                 $35,228        $31,590        $24,658        $27,699        $52,927  \n", "        Marketable securities                 $91,479       $100,544       $120,805       $127,877       $100,887  \n", "        Other Assets                          $74,834        $64,758        $54,428        $48,849        $42,522  \n", "        Other Liabilities                     $45,888        $49,848        $49,142        $53,325        $54,490  \n", "        Property, Plant and Equipment         $45,680        $43,715        $42,117        $39,440        $36,766  \n", "        Retained Earnings                   $(19,154)         $(214)       $(3,068)         $5,562        $14,966  \n", "        Short-Term Debt                       $10,912         $9,822        $11,128         $9,613         $8,773  \n", "        Total Current Assets                 $211,993       $209,017       $217,350       $216,166       $180,175  \n", "        Total Current Liabilities            $131,638       $145,129       $148,101       $162,431       $153,157  \n", "        Total Stockholders' Equity            $56,950        $62,146        $50,672        $63,090        $65,339  \n", "        Vendor non-trade receivables          $32,833        $31,477        $32,748        $25,228        $21,325  \n", "                                                                                                                   "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"metadata": {}, "cell_type": "markdown", "source": "## Stitching older Apple Statements", "id": "7308b4827ffaf093"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-22T17:22:36.843304Z", "start_time": "2025-03-22T17:22:35.920316Z"}}, "cell_type": "code", "source": ["c = Company(\"AAPL\")\n", "filings = c.get_filings(form=\"10-K\", filing_date=\"2019-01-01:2020-11-05\").head(2)\n", "xbrls = XBRLS.from_filings(filings)\n", "xbrls.statements.income_statement()"], "id": "d818887eeef5703", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m            CONSOLIDATED INCOME STATEMENT (2-Period View) (Standardized)             \u001b[0m\n", "\u001b[3m                \u001b[0m\u001b[1;3mYear Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m                 \u001b[0m\n", "                                                                                     \n", " \u001b[1m \u001b[0m\u001b[1mLine Item                                          \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 26, 2020\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2019\u001b[0m\u001b[1m \u001b[0m \n", " ─────────────────────────────────────────────────────────────────────────────────── \n", "        Cost of Revenue                                   $(169,559)     $(161,782)  \n", "        Gross Profit                                        $104,956        $98,392  \n", "        Income Before Tax                                    $67,091        $65,737  \n", "        Income Tax Expense                                    $9,680        $10,481  \n", "        Net Income                                           $57,411        $55,256  \n", "        Nonoperating Income/Expense                             $803         $1,807  \n", "        Operating Expenses                                 $(38,668)      $(34,462)  \n", "        Operating Income                                     $66,288        $63,930  \n", "        Revenue                                             $274,515       $260,174  \n", "          Earnings Per Share                                    0.00           0.00  \n", "          Earnings Per Share (Diluted)                          0.00           0.00  \n", "          Research and Development Expense                   $18,752        $16,217  \n", "          Selling, General and Administrative Expense        $19,916        $18,245  \n", "          Shares Outstanding                              17,352,119      4,617,834  \n", "          Shares Outstanding (Diluted)                    17,528,214      4,648,913  \n", "                                                                                     "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {}, "cell_type": "markdown", "source": "## Quarterly Stitching", "id": "3721ac1f74676d49"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-22T17:22:42.110781Z", "start_time": "2025-03-22T17:22:39.622898Z"}}, "cell_type": "code", "source": ["filings = c.get_filings(form=\"10-Q\").head(5)\n", "xbrls = XBRLS.from_filings(filings)\n", "xbrls.statements.income_statement()"], "id": "5138f51030f6fe44", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                           CONSOLIDATED INCOME STATEMENT (5-Period View) (Standardized)                            \u001b[0m\n", "\u001b[3m                           \u001b[0m\u001b[1;3mThree Months Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m                            \u001b[0m\n", "                                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1mLine Item                            \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 28, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mJun 29, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mMar 30, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 30, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mJul 1, 2023\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "        Cost of Revenue                      $(66,025)      $(46,099)      $(48,482)      $(64,720)     $(45,384)  \n", "        Gross Profit                           $58,275        $39,678        $42,271        $54,855       $36,413  \n", "        Income Before Tax                      $42,584        $25,494        $28,058        $40,323       $22,733  \n", "        Income Tax Expense                    $(6,254)       $(4,046)       $(4,422)       $(6,407)      $(2,852)  \n", "        Net Income                             $36,330        $21,448        $23,636        $33,916       $19,881  \n", "        Nonoperating Income/Expense             $(248)           $142           $158          $(50)        $(265)  \n", "        Operating Expenses                   $(15,443)      $(14,326)      $(14,371)      $(14,482)     $(13,415)  \n", "        Operating Income                       $42,832        $25,352        $27,900        $40,373       $22,998  \n", "        Revenue                               $124,300        $85,777        $90,753       $119,575       $81,797  \n", "          Earnings Per Share                      0.00           0.00           0.00           0.00          0.00  \n", "          Earnings Per Share (Diluted)            0.00           0.00           0.00           0.00          0.00  \n", "          Research and Development              $8,268         $8,006         $7,903         $7,696        $7,442  \n", "  Expense                                                                                                          \n", "          Selling, General and                  $7,175         $6,320         $6,468         $6,786        $5,973  \n", "  Administrative Expense                                                                                           \n", "          Shares Outstanding                15,081,724     15,287,521     15,405,856     15,509,763    15,697,614  \n", "          Shares Outstanding (Diluted)      15,150,865     15,348,175     15,464,709     15,576,641    15,775,021  \n", "                                                                                                                   "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "execution_count": 5}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}