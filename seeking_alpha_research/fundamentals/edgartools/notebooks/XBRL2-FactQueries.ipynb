{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# XBRL2 - Querying XBRL Facts with Enhanced API\n", "\n", "This notebook demonstrates how to use the enhanced Facts module in the XBRL2 API to query and analyze XBRL facts in various ways. \n", "\n", "The Facts module lets you directly access and filter individual XBRL facts using a flexible query interface. Key features include:\n", "\n", "1. **Filter by statement type** - Get facts from specific financial statements\n", "2. **Filter by period views** - Use predefined period selections for easier time-based analysis\n", "3. **Smart text search** - Search across concept names, labels, and elements \n", "4. **Safe numeric filtering** - Properly handle None values in numeric comparisons\n", "5. **Period and dimension filtering** - Analyze facts across time periods and dimensions\n", "6. **Pandas integration** - Easy conversion to DataFrames for further analysis\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/XBRL2-FactQueries.ipynb)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "!pip install edgartools"}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:49:39.596026Z", "start_time": "2025-04-12T22:49:39.593789Z"}}, "source": ["from edgar import *\n", "from edgar.xbrl import XBRL, FactQuery, FactsView\n", "import pandas as pd\n", "\n", "set_identity(\"<EMAIL>\")"], "outputs": [], "execution_count": 2}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load a Filing with XBRL Data"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:49:42.213595Z", "start_time": "2025-04-12T22:49:41.534518Z"}}, "source": ["company = Company('AAPL')\n", "filing = company.latest(\"10-K\")  # Get the latest 10-K filing"], "outputs": [], "execution_count": 3}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:49:43.465315Z", "start_time": "2025-04-12T22:49:43.271445Z"}}, "source": ["# Parse the XBRL data\n", "xbrl = XBRL.from_filing(filing)"], "outputs": [], "execution_count": 4}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic Facts Access\n", "\n", "The `facts_view` property provides access to all facts in the XBRL document."]}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:50:10.558370Z", "start_time": "2025-04-12T22:50:10.543673Z"}}, "cell_type": "code", "source": ["# Get the facts view\n", "facts = xbrl.facts\n", "\n", "# Get a summary of the facts\n", "summary = facts.summarize()\n", "print(f\"Total facts: {summary['total_facts']}\")\n", "print(f\"\\nFacts by statement type:\")\n", "for stmt, count in summary['by_statement'].items():\n", "    print(f\"  {stmt}: {count}\")\n", "\n", "print(f\"\\nFacts by period type:\")\n", "for period_type, count in summary['by_period_type'].items():\n", "    print(f\"  {period_type}: {count}\")\n", "\n", "print(f\"\\nUnique dimensions: {len(summary['dimensions'])}\")\n", "if summary['dimensions']:\n", "    for dim in summary['dimensions'][:5]:  # Show first 5\n", "        print(f\"  {dim}\")\n", "    if len(summary['dimensions']) > 5:\n", "        print(f\"  ...and {len(summary['dimensions']) - 5} more\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total facts: 1042\n", "\n", "Facts by statement type:\n", "  CoverPage: 59\n", "  unknown: 543\n", "  IncomeStatement: 117\n", "  ComprehensiveIncome: 21\n", "  BalanceSheet: 158\n", "  BalanceSheetParenthetical: 4\n", "  StatementOfEquity: 26\n", "  CashFlowStatement: 85\n", "  AccountingPolicies: 14\n", "  SegmentDisclosure: 15\n", "\n", "Facts by period type:\n", "  duration: 550\n", "  instant: 492\n", "\n", "Unique dimensions: 25\n", "  ecd_IndividualAxis\n", "  srt_ConsolidationItemsAxis\n", "  srt_MajorCustomersAxis\n", "  srt_ProductOrServiceAxis\n", "  srt_RangeAxis\n", "  ...and 20 more\n"]}], "execution_count": 7}, {"cell_type": "markdown", "metadata": {}, "source": ["## Querying Facts\n", "\n", "The `FactQuery` provides a fluent interface for filtering facts by various criteria."]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:50:14.062955Z", "start_time": "2025-04-12T22:50:14.054172Z"}}, "source": ["# Find revenue-related facts\n", "revenue_df = facts.query().by_concept(\"Revenue\").to_dataframe()\n", "revenue_df[['concept',  'numeric_value', 'period_end']].head(10)"], "outputs": [{"data": {"text/plain": ["                                             concept  numeric_value  \\\n", "0  us-gaap:RevenueRemainingPerformanceObligationE...            NaN   \n", "1  us-gaap:RevenueRemainingPerformanceObligationE...            NaN   \n", "2  us-gaap:RevenueRemainingPerformanceObligationE...            NaN   \n", "3  us-gaap:RevenueRemainingPerformanceObligationE...            NaN   \n", "4  us-gaap:RevenueFromContractWithCustomerExcludi...   2.948660e+11   \n", "5  us-gaap:RevenueFromContractWithCustomerExcludi...   2.980850e+11   \n", "6  us-gaap:RevenueFromContractWithCustomerExcludi...   3.161990e+11   \n", "7  us-gaap:RevenueFromContractWithCustomerExcludi...   9.616900e+10   \n", "8  us-gaap:RevenueFromContractWithCustomerExcludi...   8.520000e+10   \n", "9  us-gaap:RevenueFromContractWithCustomerExcludi...   7.812900e+10   \n", "\n", "   period_end  \n", "0         NaN  \n", "1         NaN  \n", "2         NaN  \n", "3         NaN  \n", "4  2024-09-28  \n", "5  2023-09-30  \n", "6  2022-09-24  \n", "7  2024-09-28  \n", "8  2023-09-30  \n", "9  2022-09-24  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>numeric_value</th>\n", "      <th>period_end</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>2.948660e+11</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>2.980850e+11</td>\n", "      <td>2023-09-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>3.161990e+11</td>\n", "      <td>2022-09-24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>9.616900e+10</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>8.520000e+10</td>\n", "      <td>2023-09-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>7.812900e+10</td>\n", "      <td>2022-09-24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "execution_count": 8}, {"metadata": {}, "cell_type": "markdown", "source": "## Query By Label"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:50:16.052423Z", "start_time": "2025-04-12T22:50:16.044091Z"}}, "cell_type": "code", "source": "# Improved text search function\n# This feature allows searching across all text fields (concept, label, element_name, etc.)\n# and handles NULL values appropriately\n\n# Search for \"Revenue\" in any text field\nrevenue_results = facts.search_facts(\"Revenue\")\nprint(f\"Found {len(revenue_results)} facts containing 'Revenue' in any text field\")\n\n# Display the first few results\nrevenue_results[['concept', 'label', 'numeric_value', 'period_end']].head(5)", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 66 facts containing 'Revenue' in any text field\n"]}, {"data": {"text/plain": ["                                             concept  \\\n", "0  us-gaap:RevenueRemainingPerformanceObligationE...   \n", "1  us-gaap:RevenueRemainingPerformanceObligationE...   \n", "2  us-gaap:RevenueRemainingPerformanceObligationE...   \n", "3  us-gaap:RevenueRemainingPerformanceObligationE...   \n", "4  us-gaap:RevenueFromContractWithCustomerExcludi...   \n", "\n", "                                               label  numeric_value  \\\n", "0  Deferred revenue, expected timing of realizati...            NaN   \n", "1  Deferred revenue, expected timing of realizati...            NaN   \n", "2  Deferred revenue, expected timing of realizati...            NaN   \n", "3  Deferred revenue, expected timing of realizati...            NaN   \n", "4                                          Net sales   2.948660e+11   \n", "\n", "   period_end  \n", "0         NaN  \n", "1         NaN  \n", "2         NaN  \n", "3         NaN  \n", "4  2024-09-28  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>numeric_value</th>\n", "      <th>period_end</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>Deferred revenue, expected timing of realizati...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>Deferred revenue, expected timing of realizati...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>Deferred revenue, expected timing of realizati...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>Deferred revenue, expected timing of realizati...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>2.948660e+11</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "execution_count": 9}, {"metadata": {}, "cell_type": "markdown", "source": "## Query by text"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:50:18.039228Z", "start_time": "2025-04-12T22:50:18.028339Z"}}, "cell_type": "code", "source": ["(facts.query().by_text(\"Tax\")\n", " .to_dataframe().head(10))"], "outputs": [{"data": {"text/plain": ["                                             concept  \\\n", "0                  dei:EntityTaxIdentificationNumber   \n", "1  us-gaap:RevenueFromContractWithCustomerExcludi...   \n", "2  us-gaap:RevenueFromContractWithCustomerExcludi...   \n", "3  us-gaap:RevenueFromContractWithCustomerExcludi...   \n", "4  us-gaap:RevenueFromContractWithCustomerExcludi...   \n", "5  us-gaap:RevenueFromContractWithCustomerExcludi...   \n", "6  us-gaap:RevenueFromContractWithCustomerExcludi...   \n", "7  us-gaap:RevenueFromContractWithCustomerExcludi...   \n", "8  us-gaap:RevenueFromContractWithCustomerExcludi...   \n", "9  us-gaap:RevenueFromContractWithCustomerExcludi...   \n", "\n", "                              label         value  numeric_value period_start  \\\n", "0  Entity Tax Identification Number    94-2404110            NaN   2023-10-01   \n", "1                         Net sales  294866000000   2.948660e+11   2023-10-01   \n", "2                         Net sales  298085000000   2.980850e+11   2022-09-25   \n", "3                         Net sales  316199000000   3.161990e+11   2021-09-26   \n", "4                         Net sales   96169000000   9.616900e+10   2023-10-01   \n", "5                         Net sales   85200000000   8.520000e+10   2022-09-25   \n", "6                         Net sales   78129000000   7.812900e+10   2021-09-26   \n", "7                         Net sales  391035000000   3.910350e+11   2023-10-01   \n", "8                         Net sales  383285000000   3.832850e+11   2022-09-25   \n", "9                         Net sales  394328000000   3.943280e+11   2021-09-26   \n", "\n", "   period_end context_ref unit_ref decimals period_type  ...  \\\n", "0  2024-09-28         c-1     None     None    duration  ...   \n", "1  2024-09-28        c-13      usd       -6    duration  ...   \n", "2  2023-09-30        c-14      usd       -6    duration  ...   \n", "3  2022-09-24        c-15      usd       -6    duration  ...   \n", "4  2024-09-28        c-16      usd       -6    duration  ...   \n", "5  2023-09-30        c-17      usd       -6    duration  ...   \n", "6  2022-09-24        c-18      usd       -6    duration  ...   \n", "7  2024-09-28         c-1      usd       -6    duration  ...   \n", "8  2023-09-30        c-19      usd       -6    duration  ...   \n", "9  2022-09-24        c-20      usd       -6    duration  ...   \n", "\n", "  dim_srt_ProductOrServiceAxis period_instant  \\\n", "0                          NaN            NaN   \n", "1        us-gaap:ProductMember            NaN   \n", "2        us-gaap:ProductMember            NaN   \n", "3        us-gaap:ProductMember            NaN   \n", "4        us-gaap:ServiceMember            NaN   \n", "5        us-gaap:ServiceMember            NaN   \n", "6        us-gaap:ServiceMember            NaN   \n", "7                          NaN            NaN   \n", "8                          NaN            NaN   \n", "9                          NaN            NaN   \n", "\n", "  dim_us-gaap_StatementEquityComponentsAxis  \\\n", "0                                       NaN   \n", "1                                       NaN   \n", "2                                       NaN   \n", "3                                       NaN   \n", "4                                       NaN   \n", "5                                       NaN   \n", "6                                       NaN   \n", "7                                       NaN   \n", "8                                       NaN   \n", "9                                       NaN   \n", "\n", "  dim_us-gaap_FairValueByFairValueHierarchyLevelAxis  \\\n", "0                                                NaN   \n", "1                                                NaN   \n", "2                                                NaN   \n", "3                                                NaN   \n", "4                                                NaN   \n", "5                                                NaN   \n", "6                                                NaN   \n", "7                                                NaN   \n", "8                                                NaN   \n", "9                                                NaN   \n", "\n", "  dim_us-gaap_FinancialInstrumentAxis  \\\n", "0                                 NaN   \n", "1                                 NaN   \n", "2                                 NaN   \n", "3                                 NaN   \n", "4                                 NaN   \n", "5                                 NaN   \n", "6                                 NaN   \n", "7                                 NaN   \n", "8                                 NaN   \n", "9                                 NaN   \n", "\n", "  dim_us-gaap_LossContingenciesByNatureOfContingencyAxis dim_srt_RangeAxis  \\\n", "0                                                NaN                   NaN   \n", "1                                                NaN                   NaN   \n", "2                                                NaN                   NaN   \n", "3                                                NaN                   NaN   \n", "4                                                NaN                   NaN   \n", "5                                                NaN                   NaN   \n", "6                                                NaN                   NaN   \n", "7                                                NaN                   NaN   \n", "8                                                NaN                   NaN   \n", "9                                                NaN                   NaN   \n", "\n", "  dim_us-gaap_AwardTypeAxis dim_us-gaap_StatementBusinessSegmentsAxis  \\\n", "0                       NaN                                       NaN   \n", "1                       NaN                                       NaN   \n", "2                       NaN                                       NaN   \n", "3                       NaN                                       NaN   \n", "4                       NaN                                       NaN   \n", "5                       NaN                                       NaN   \n", "6                       NaN                                       NaN   \n", "7                       NaN                                       NaN   \n", "8                       NaN                                       NaN   \n", "9                       NaN                                       NaN   \n", "\n", "  dim_srt_StatementGeographicalAxis  \n", "0                               NaN  \n", "1                               NaN  \n", "2                               NaN  \n", "3                               NaN  \n", "4                               NaN  \n", "5                               NaN  \n", "6                               NaN  \n", "7                               NaN  \n", "8                               NaN  \n", "9                               NaN  \n", "\n", "[10 rows x 27 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>value</th>\n", "      <th>numeric_value</th>\n", "      <th>period_start</th>\n", "      <th>period_end</th>\n", "      <th>context_ref</th>\n", "      <th>unit_ref</th>\n", "      <th>decimals</th>\n", "      <th>period_type</th>\n", "      <th>...</th>\n", "      <th>dim_srt_ProductOrServiceAxis</th>\n", "      <th>period_instant</th>\n", "      <th>dim_us-gaap_StatementEquityComponentsAxis</th>\n", "      <th>dim_us-gaap_FairValueByFairValueHierarchyLevelAxis</th>\n", "      <th>dim_us-gaap_FinancialInstrumentAxis</th>\n", "      <th>dim_us-gaap_LossContingenciesByNatureOfContingencyAxis</th>\n", "      <th>dim_srt_RangeAxis</th>\n", "      <th>dim_us-gaap_AwardTypeAxis</th>\n", "      <th>dim_us-gaap_StatementBusinessSegmentsAxis</th>\n", "      <th>dim_srt_StatementGeographicalAxis</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>dei:EntityTaxIdentificationNumber</td>\n", "      <td>Entity Tax Identification Number</td>\n", "      <td>94-2404110</td>\n", "      <td>NaN</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>294866000000</td>\n", "      <td>2.948660e+11</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-13</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>us-gaap:ProductMember</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>298085000000</td>\n", "      <td>2.980850e+11</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>c-14</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>us-gaap:ProductMember</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>316199000000</td>\n", "      <td>3.161990e+11</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>c-15</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>us-gaap:ProductMember</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>96169000000</td>\n", "      <td>9.616900e+10</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-16</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>us-gaap:ServiceMember</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>85200000000</td>\n", "      <td>8.520000e+10</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>c-17</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>us-gaap:ServiceMember</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>78129000000</td>\n", "      <td>7.812900e+10</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>c-18</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>us-gaap:ServiceMember</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>391035000000</td>\n", "      <td>3.910350e+11</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-1</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>383285000000</td>\n", "      <td>3.832850e+11</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>c-19</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>394328000000</td>\n", "      <td>3.943280e+11</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>c-20</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 27 columns</p>\n", "</div>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "execution_count": 10}, {"metadata": {}, "cell_type": "markdown", "source": "## Query by instant"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:50:20.155548Z", "start_time": "2025-04-12T22:50:20.141161Z"}}, "cell_type": "code", "source": "facts.query().by_period_type(\"instant\").to_dataframe().head(10)", "outputs": [{"data": {"text/plain": ["                                             concept  \\\n", "0  us-gaap:RevenueRemainingPerformanceObligationE...   \n", "1  us-gaap:RevenueRemainingPerformanceObligationE...   \n", "2  us-gaap:RevenueRemainingPerformanceObligationE...   \n", "3  us-gaap:RevenueRemainingPerformanceObligationE...   \n", "4  us-gaap:HedgedAssetStatementOfFinancialPositio...   \n", "5  us-gaap:HedgedAssetStatementOfFinancialPositio...   \n", "6  us-gaap:HedgedLiabilityStatementOfFinancialPos...   \n", "7  us-gaap:HedgedLiabilityStatementOfFinancialPos...   \n", "8  us-gaap:OperatingLeaseRightOfUseAssetStatement...   \n", "9  us-gaap:OperatingLeaseRightOfUseAssetStatement...   \n", "\n", "                                               label  \\\n", "0  Deferred revenue, expected timing of realizati...   \n", "1  Deferred revenue, expected timing of realizati...   \n", "2  Deferred revenue, expected timing of realizati...   \n", "3  Deferred revenue, expected timing of realizati...   \n", "4  Hedged asset, statement of financial position ...   \n", "5  Hedged asset, statement of financial position ...   \n", "6  Hedged liability, statement of financial posit...   \n", "7  Hedged liability, statement of financial posit...   \n", "8  Operating lease, right-of-use asset, statement...   \n", "9  Operating lease, right-of-use asset, statement...   \n", "\n", "                                               value  numeric_value  \\\n", "0                                                P1Y            NaN   \n", "1                                                P1Y            NaN   \n", "2                                                P1Y            NaN   \n", "3                                                P1Y            NaN   \n", "4  http://fasb.org/us-gaap/2024#MarketableSecurit...            NaN   \n", "5  http://fasb.org/us-gaap/2024#MarketableSecurit...            NaN   \n", "6  http://fasb.org/us-gaap/2024#LongTermDebtCurre...            NaN   \n", "7  http://fasb.org/us-gaap/2024#LongTermDebtCurre...            NaN   \n", "8  http://fasb.org/us-gaap/2024#OtherAssetsNoncur...            NaN   \n", "9  http://fasb.org/us-gaap/2024#OtherAssetsNoncur...            NaN   \n", "\n", "  context_ref unit_ref decimals period_type period_instant entity_identifier  \\\n", "0        c-58     None     None     instant     2024-09-28        0000320193   \n", "1        c-59     None     None     instant     2024-09-28        0000320193   \n", "2        c-60     None     None     instant     2024-09-28        0000320193   \n", "3        c-61     None     None     instant     2024-09-28        0000320193   \n", "4        c-21     None     None     instant     2024-09-28        0000320193   \n", "5        c-22     None     None     instant     2023-09-30        0000320193   \n", "6        c-21     None     None     instant     2024-09-28        0000320193   \n", "7        c-22     None     None     instant     2023-09-30        0000320193   \n", "8        c-21     None     None     instant     2024-09-28        0000320193   \n", "9        c-22     None     None     instant     2023-09-30        0000320193   \n", "\n", "   ... dim_us-gaap_PropertyPlantAndEquipmentByTypeAxis dim_srt_RangeAxis  \\\n", "0  ...                                             NaN               NaN   \n", "1  ...                                             NaN               NaN   \n", "2  ...                                             NaN               NaN   \n", "3  ...                                             NaN               NaN   \n", "4  ...                                             NaN               NaN   \n", "5  ...                                             NaN               NaN   \n", "6  ...                                             Na<PERSON>   \n", "7  ...                                             NaN               NaN   \n", "8  ...                                             NaN               NaN   \n", "9  ...                                             NaN               NaN   \n", "\n", "  dim_us-gaap_UnrecordedUnconditionalPurchaseObligationByCategoryOfItemPurchasedAxis  \\\n", "0                                                NaN                                   \n", "1                                                NaN                                   \n", "2                                                NaN                                   \n", "3                                                NaN                                   \n", "4                                                NaN                                   \n", "5                                                NaN                                   \n", "6                                                NaN                                   \n", "7                                                NaN                                   \n", "8                                                NaN                                   \n", "9                                                NaN                                   \n", "\n", "  dim_us-gaap_ShortTermDebtTypeAxis dim_us-gaap_DebtInstrumentAxis  \\\n", "0                               NaN                            NaN   \n", "1                               NaN                            NaN   \n", "2                               NaN                            NaN   \n", "3                               NaN                            NaN   \n", "4                               NaN                            NaN   \n", "5                               NaN                            NaN   \n", "6                               NaN                            NaN   \n", "7                               NaN                            NaN   \n", "8                               NaN                            NaN   \n", "9                               NaN                            NaN   \n", "\n", "  dim_us-gaap_LongtermDebtTypeAxis dim_us-gaap_PlanNameAxis  \\\n", "0                              NaN                      NaN   \n", "1                              NaN                      NaN   \n", "2                              NaN                      NaN   \n", "3                              NaN                      NaN   \n", "4                              NaN                      NaN   \n", "5                              NaN                      NaN   \n", "6                              NaN                      NaN   \n", "7                              NaN                      NaN   \n", "8                              NaN                      NaN   \n", "9                              NaN                      NaN   \n", "\n", "  dim_us-gaap_AwardTypeAxis dim_srt_StatementGeographicalAxis  \\\n", "0                       NaN                               NaN   \n", "1                       NaN                               NaN   \n", "2                       NaN                               NaN   \n", "3                       NaN                               NaN   \n", "4                       NaN                               NaN   \n", "5                       NaN                               NaN   \n", "6                       NaN                               NaN   \n", "7                       NaN                               NaN   \n", "8                       NaN                               NaN   \n", "9                       NaN                               NaN   \n", "\n", "  dim_ecd_IndividualAxis  \n", "0                    NaN  \n", "1                    NaN  \n", "2                    NaN  \n", "3                    NaN  \n", "4                    NaN  \n", "5                    NaN  \n", "6                    NaN  \n", "7                    NaN  \n", "8                    NaN  \n", "9                    NaN  \n", "\n", "[10 rows x 35 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>value</th>\n", "      <th>numeric_value</th>\n", "      <th>context_ref</th>\n", "      <th>unit_ref</th>\n", "      <th>decimals</th>\n", "      <th>period_type</th>\n", "      <th>period_instant</th>\n", "      <th>entity_identifier</th>\n", "      <th>...</th>\n", "      <th>dim_us-gaap_PropertyPlantAndEquipmentByTypeAxis</th>\n", "      <th>dim_srt_RangeAxis</th>\n", "      <th>dim_us-gaap_UnrecordedUnconditionalPurchaseObligationByCategoryOfItemPurchasedAxis</th>\n", "      <th>dim_us-gaap_ShortTermDebtTypeAxis</th>\n", "      <th>dim_us-gaap_DebtInstrumentAxis</th>\n", "      <th>dim_us-gaap_LongtermDebtTypeAxis</th>\n", "      <th>dim_us-gaap_PlanNameAxis</th>\n", "      <th>dim_us-gaap_AwardTypeAxis</th>\n", "      <th>dim_srt_StatementGeographicalAxis</th>\n", "      <th>dim_ecd_IndividualAxis</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>Deferred revenue, expected timing of realizati...</td>\n", "      <td>P1Y</td>\n", "      <td>NaN</td>\n", "      <td>c-58</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>instant</td>\n", "      <td>2024-09-28</td>\n", "      <td>0000320193</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>Deferred revenue, expected timing of realizati...</td>\n", "      <td>P1Y</td>\n", "      <td>NaN</td>\n", "      <td>c-59</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>instant</td>\n", "      <td>2024-09-28</td>\n", "      <td>0000320193</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>Deferred revenue, expected timing of realizati...</td>\n", "      <td>P1Y</td>\n", "      <td>NaN</td>\n", "      <td>c-60</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>instant</td>\n", "      <td>2024-09-28</td>\n", "      <td>0000320193</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap:RevenueRemainingPerformanceObligationE...</td>\n", "      <td>Deferred revenue, expected timing of realizati...</td>\n", "      <td>P1Y</td>\n", "      <td>NaN</td>\n", "      <td>c-61</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>instant</td>\n", "      <td>2024-09-28</td>\n", "      <td>0000320193</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap:HedgedAssetStatementOfFinancialPositio...</td>\n", "      <td>Hedged asset, statement of financial position ...</td>\n", "      <td>http://fasb.org/us-gaap/2024#MarketableSecurit...</td>\n", "      <td>NaN</td>\n", "      <td>c-21</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>instant</td>\n", "      <td>2024-09-28</td>\n", "      <td>0000320193</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap:HedgedAssetStatementOfFinancialPositio...</td>\n", "      <td>Hedged asset, statement of financial position ...</td>\n", "      <td>http://fasb.org/us-gaap/2024#MarketableSecurit...</td>\n", "      <td>NaN</td>\n", "      <td>c-22</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>instant</td>\n", "      <td>2023-09-30</td>\n", "      <td>0000320193</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap:HedgedLiabilityStatementOfFinancialPos...</td>\n", "      <td>Hedged liability, statement of financial posit...</td>\n", "      <td>http://fasb.org/us-gaap/2024#LongTermDebtCurre...</td>\n", "      <td>NaN</td>\n", "      <td>c-21</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>instant</td>\n", "      <td>2024-09-28</td>\n", "      <td>0000320193</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap:HedgedLiabilityStatementOfFinancialPos...</td>\n", "      <td>Hedged liability, statement of financial posit...</td>\n", "      <td>http://fasb.org/us-gaap/2024#LongTermDebtCurre...</td>\n", "      <td>NaN</td>\n", "      <td>c-22</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>instant</td>\n", "      <td>2023-09-30</td>\n", "      <td>0000320193</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap:OperatingLeaseRightOfUseAssetStatement...</td>\n", "      <td>Operating lease, right-of-use asset, statement...</td>\n", "      <td>http://fasb.org/us-gaap/2024#OtherAssetsNoncur...</td>\n", "      <td>NaN</td>\n", "      <td>c-21</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>instant</td>\n", "      <td>2024-09-28</td>\n", "      <td>0000320193</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap:OperatingLeaseRightOfUseAssetStatement...</td>\n", "      <td>Operating lease, right-of-use asset, statement...</td>\n", "      <td>http://fasb.org/us-gaap/2024#OtherAssetsNoncur...</td>\n", "      <td>NaN</td>\n", "      <td>c-22</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>instant</td>\n", "      <td>2023-09-30</td>\n", "      <td>0000320193</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 35 columns</p>\n", "</div>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "execution_count": 11}, {"metadata": {}, "cell_type": "markdown", "source": "## Query by duration"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:50:22.518951Z", "start_time": "2025-04-12T22:50:22.509093Z"}}, "cell_type": "code", "source": "facts.query().by_period_type(\"duration\").to_dataframe().head(10)", "outputs": [{"data": {"text/plain": ["                         concept                         label       value  \\\n", "0              dei:AmendmentFlag                Amendment Flag       false   \n", "1    dei:DocumentFiscalYearFocus    Document Fiscal Year Focus        2024   \n", "2  dei:DocumentFiscalPeriodFocus  Document Fiscal Period Focus          FY   \n", "3      dei:EntityCentralIndexKey      Entity Central Index Key  0000320193   \n", "4                 TrdArrDuration                           NaN       P856D   \n", "5                 TrdArrDuration                           NaN       P473D   \n", "6               dei:DocumentType                 Document Type        10-K   \n", "7       dei:DocumentAnnualReport        Document Annual Report        true   \n", "8      dei:DocumentPeriodEndDate      Document Period End Date  2024-09-28   \n", "9   dei:CurrentFiscalYearEndDate  Current Fiscal Year End Date     --09-28   \n", "\n", "   numeric_value period_start  period_end context_ref unit_ref decimals  \\\n", "0            NaN   2023-10-01  2024-09-28         c-1     None     None   \n", "1         2024.0   2023-10-01  2024-09-28         c-1     None     None   \n", "2            NaN   2023-10-01  2024-09-28         c-1     None     None   \n", "3       320193.0   2023-10-01  2024-09-28         c-1     None     None   \n", "4            NaN   2024-06-30  2024-09-28       c-189     None     None   \n", "5            NaN   2024-06-30  2024-09-28       c-192     None     None   \n", "6            NaN   2023-10-01  2024-09-28         c-1     None     None   \n", "7            NaN   2023-10-01  2024-09-28         c-1     None     None   \n", "8            NaN   2023-10-01  2024-09-28         c-1     None     None   \n", "9            NaN   2023-10-01  2024-09-28         c-1     None     None   \n", "\n", "  period_type  ... dim_us-gaap_ConcentrationRiskByTypeAxis  \\\n", "0    duration  ...                                     NaN   \n", "1    duration  ...                                     NaN   \n", "2    duration  ...                                     NaN   \n", "3    duration  ...                                     NaN   \n", "4    duration  ...                                     NaN   \n", "5    duration  ...                                     NaN   \n", "6    duration  ...                                     NaN   \n", "7    duration  ...                                     NaN   \n", "8    duration  ...                                     NaN   \n", "9    duration  ...                                     NaN   \n", "\n", "  dim_us-gaap_LossContingenciesByNatureOfContingencyAxis  \\\n", "0                                                NaN       \n", "1                                                NaN       \n", "2                                                NaN       \n", "3                                                NaN       \n", "4                                                NaN       \n", "5                                                NaN       \n", "6                                                NaN       \n", "7                                                NaN       \n", "8                                                NaN       \n", "9                                                NaN       \n", "\n", "  dim_us-gaap_ShortTermDebtTypeAxis dim_us-gaap_DebtInstrumentAxis  \\\n", "0                               NaN                            NaN   \n", "1                               NaN                            NaN   \n", "2                               NaN                            NaN   \n", "3                               NaN                            NaN   \n", "4                               NaN                            NaN   \n", "5                               NaN                            NaN   \n", "6                               NaN                            NaN   \n", "7                               NaN                            NaN   \n", "8                               NaN                            NaN   \n", "9                               NaN                            NaN   \n", "\n", "  dim_us-gaap_LongtermDebtTypeAxis dim_us-gaap_AwardTypeAxis  \\\n", "0                              NaN                       NaN   \n", "1                              NaN                       NaN   \n", "2                              NaN                       NaN   \n", "3                              NaN                       NaN   \n", "4                              NaN                       NaN   \n", "5                              NaN                       NaN   \n", "6                              NaN                       NaN   \n", "7                              NaN                       NaN   \n", "8                              NaN                       NaN   \n", "9                              NaN                       NaN   \n", "\n", "  dim_us-gaap_PlanNameAxis dim_us-gaap_StatementBusinessSegmentsAxis  \\\n", "0                      NaN                                       NaN   \n", "1                      NaN                                       NaN   \n", "2                      NaN                                       NaN   \n", "3                      NaN                                       NaN   \n", "4                      NaN                                       NaN   \n", "5                      NaN                                       NaN   \n", "6                      NaN                                       NaN   \n", "7                      NaN                                       NaN   \n", "8                      NaN                                       NaN   \n", "9                      NaN                                       NaN   \n", "\n", "  dim_srt_ConsolidationItemsAxis dim_srt_StatementGeographicalAxis  \n", "0                            NaN                               NaN  \n", "1                            NaN                               NaN  \n", "2                            NaN                               NaN  \n", "3                            NaN                               NaN  \n", "4                            NaN                               NaN  \n", "5                            NaN                               NaN  \n", "6                            NaN                               NaN  \n", "7                            NaN                               NaN  \n", "8                            NaN                               NaN  \n", "9                            NaN                               NaN  \n", "\n", "[10 rows x 35 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>value</th>\n", "      <th>numeric_value</th>\n", "      <th>period_start</th>\n", "      <th>period_end</th>\n", "      <th>context_ref</th>\n", "      <th>unit_ref</th>\n", "      <th>decimals</th>\n", "      <th>period_type</th>\n", "      <th>...</th>\n", "      <th>dim_us-gaap_ConcentrationRiskByTypeAxis</th>\n", "      <th>dim_us-gaap_LossContingenciesByNatureOfContingencyAxis</th>\n", "      <th>dim_us-gaap_ShortTermDebtTypeAxis</th>\n", "      <th>dim_us-gaap_DebtInstrumentAxis</th>\n", "      <th>dim_us-gaap_LongtermDebtTypeAxis</th>\n", "      <th>dim_us-gaap_AwardTypeAxis</th>\n", "      <th>dim_us-gaap_PlanNameAxis</th>\n", "      <th>dim_us-gaap_StatementBusinessSegmentsAxis</th>\n", "      <th>dim_srt_ConsolidationItemsAxis</th>\n", "      <th>dim_srt_StatementGeographicalAxis</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>dei:AmendmentFlag</td>\n", "      <td>Amendment Flag</td>\n", "      <td>false</td>\n", "      <td>NaN</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>dei:DocumentFiscalYearFocus</td>\n", "      <td>Document Fiscal Year Focus</td>\n", "      <td>2024</td>\n", "      <td>2024.0</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>dei:DocumentFiscalPeriodFocus</td>\n", "      <td>Document Fiscal Period Focus</td>\n", "      <td>FY</td>\n", "      <td>NaN</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>dei:EntityCentralIndexKey</td>\n", "      <td>Entity Central Index Key</td>\n", "      <td>0000320193</td>\n", "      <td>320193.0</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TrdArrDuration</td>\n", "      <td>NaN</td>\n", "      <td>P856D</td>\n", "      <td>NaN</td>\n", "      <td>2024-06-30</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-189</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>TrdArrDuration</td>\n", "      <td>NaN</td>\n", "      <td>P473D</td>\n", "      <td>NaN</td>\n", "      <td>2024-06-30</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-192</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>dei:DocumentType</td>\n", "      <td>Document Type</td>\n", "      <td>10-K</td>\n", "      <td>NaN</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>dei:DocumentAnnualReport</td>\n", "      <td>Document Annual Report</td>\n", "      <td>true</td>\n", "      <td>NaN</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>dei:DocumentPeriodEndDate</td>\n", "      <td>Document Period End Date</td>\n", "      <td>2024-09-28</td>\n", "      <td>NaN</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>dei:CurrentFiscalYearEndDate</td>\n", "      <td>Current Fiscal Year End Date</td>\n", "      <td>--09-28</td>\n", "      <td>NaN</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "      <td>c-1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>duration</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 35 columns</p>\n", "</div>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "execution_count": 12}, {"metadata": {}, "cell_type": "markdown", "source": "## Query by statement type"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:52:11.296131Z", "start_time": "2025-04-12T22:52:11.288428Z"}}, "cell_type": "code", "source": ["# Safe numeric value filtering with improved by_value method\n", "# This function now safely handles None values and properly compares numeric values\n", "\n", "# Find values over 10 billion (safe handling of None values)\n", "large_values = facts.query() \\\n", "    .by_statement_type('IncomeStatement') \\\n", "    .by_value(lambda v: v > 10_000_000_000) \\\n", "    .sort_by('numeric_value', ascending=False) \\\n", "    .to_dataframe()\n", "\n", "print(f\"Found {len(large_values)} values over 10 billion\")\n", "large_values[['concept', 'label', 'numeric_value', 'period_start']].head(5)\n", "#mid_values[['concept', 'label', 'numeric_value', 'period_start']].head(5)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 96 values over 10 billion\n"]}, {"data": {"text/plain": ["                                             concept      label  \\\n", "0  us-gaap:RevenueFromContractWithCustomerExcludi...  Net sales   \n", "1  us-gaap:RevenueFromContractWithCustomerExcludi...  Net sales   \n", "2  us-gaap:RevenueFromContractWithCustomerExcludi...  Net sales   \n", "3  us-gaap:RevenueFromContractWithCustomerExcludi...  Net sales   \n", "4  us-gaap:RevenueFromContractWithCustomerExcludi...  Net sales   \n", "\n", "   numeric_value period_start  \n", "0   3.943280e+11   2021-09-26  \n", "1   3.910350e+11   2023-10-01  \n", "2   3.832850e+11   2022-09-25  \n", "3   3.161990e+11   2021-09-26  \n", "4   2.980850e+11   2022-09-25  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>numeric_value</th>\n", "      <th>period_start</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>3.943280e+11</td>\n", "      <td>2021-09-26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>3.910350e+11</td>\n", "      <td>2023-10-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>3.832850e+11</td>\n", "      <td>2022-09-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>3.161990e+11</td>\n", "      <td>2021-09-26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>2.980850e+11</td>\n", "      <td>2022-09-25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "execution_count": 19}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:52:13.547718Z", "start_time": "2025-04-12T22:52:13.541880Z"}}, "cell_type": "code", "source": ["# Find facts from the income statement with values greater than 10 billion\n", "large_values = (facts.query()\n", "    .by_statement_type('IncomeStatement')\n", "    .by_value(lambda v: v > 10_000_000_000)\n", "    .sort_by('numeric_value', ascending=False)\n", "    .to_dataframe()\n", ")\n", "\n", "large_values[['concept', 'value', 'period_start', 'period_end']].head(10)"], "outputs": [{"data": {"text/plain": ["                                             concept         value  \\\n", "0  us-gaap:RevenueFromContractWithCustomerExcludi...  394328000000   \n", "1  us-gaap:RevenueFromContractWithCustomerExcludi...  391035000000   \n", "2  us-gaap:RevenueFromContractWithCustomerExcludi...  383285000000   \n", "3  us-gaap:RevenueFromContractWithCustomerExcludi...  316199000000   \n", "4  us-gaap:RevenueFromContractWithCustomerExcludi...  298085000000   \n", "5  us-gaap:RevenueFromContractWithCustomerExcludi...  294866000000   \n", "6  us-gaap:RevenueFromContractWithCustomerExcludi...  205489000000   \n", "7  us-gaap:RevenueFromContractWithCustomerExcludi...  201183000000   \n", "8  us-gaap:RevenueFromContractWithCustomerExcludi...  200583000000   \n", "9  us-gaap:RevenueFromContractWithCustomerExcludi...  181887000000   \n", "\n", "  period_start  period_end  \n", "0   2021-09-26  2022-09-24  \n", "1   2023-10-01  2024-09-28  \n", "2   2022-09-25  2023-09-30  \n", "3   2021-09-26  2022-09-24  \n", "4   2022-09-25  2023-09-30  \n", "5   2023-10-01  2024-09-28  \n", "6   2021-09-26  2022-09-24  \n", "7   2023-10-01  2024-09-28  \n", "8   2022-09-25  2023-09-30  \n", "9   2023-10-01  2024-09-28  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>value</th>\n", "      <th>period_start</th>\n", "      <th>period_end</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>394328000000</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>391035000000</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>383285000000</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>316199000000</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>298085000000</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>294866000000</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>205489000000</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>201183000000</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>200583000000</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>181887000000</td>\n", "      <td>2023-10-01</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "execution_count": 20}, {"metadata": {}, "cell_type": "markdown", "source": "## Query by period views"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:52:16.463807Z", "start_time": "2025-04-12T22:52:16.461175Z"}}, "cell_type": "code", "source": ["facts.get_facts_by_period_view(\"IncomeStatement\",\n", "                               \"Three Recent Quarters\").head(10)"], "outputs": [{"data": {"text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: []"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "execution_count": 21}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:52:31.596499Z", "start_time": "2025-04-12T22:52:31.568336Z"}}, "cell_type": "code", "source": "# Improved period filtering with by_period_key and by_period_keys methods\n\n# Let's get the available reporting periods\nall_periods = xbrl.reporting_periods\nprint(f\"Filing has {len(all_periods)} reporting periods\")\n\n# Get the most recent instant period (for balance sheet)\nlatest_period = next((p for p in all_periods if p['type'] == 'instant'), \n                     None)\nif latest_period:\n    print(f\"Latest instant period: {latest_period['date']}\")\n    \n    # Get assets from the balance sheet for this period\n    assets = facts.query() \\\n        .by_statement_type('BalanceSheet') \\\n        .by_period_key(latest_period['key']) \\\n        .by_label(\"Asset\", exact=False) \\\n        .to_dataframe()\n    \n    print(f\"\\nFound {len(assets)} asset-related facts for period {latest_period['key']}\")\n    display(assets[['concept', 'label', 'numeric_value']].head(5))\n    \n# Let's get the last 2 periods for the income statement\nduration_periods = [p for p in all_periods if p['type'] == 'duration']\nif len(duration_periods) >= 2:\n    period_keys = [p['key'] for p in duration_periods[:2]]\n    print(f\"\\nComparing periods: {period_keys}\")\n    \n    # Get income data for these two periods\n    income_comparison = facts.query() \\\n        .by_statement_type('IncomeStatement') \\\n        .by_period_keys(period_keys) \\\n        .by_label(\"Revenue\", exact=False) \\\n        .to_dataframe()\n    \n    print(f\"Found {len(income_comparison)} revenue facts across both periods\")\n    \n    # Create a pivot table to show the comparison\n    pivot = income_comparison.pivot_table(\n        values='numeric_value',\n        index=['concept', 'label'],\n        columns='period_key',\n        aggfunc='first'\n    )\n    \n    display(pivot.head(5))", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filing has 13 reporting periods\n", "Latest instant period: 2024-10-18\n", "\n", "Found 0 asset-related facts for period instant_2024-10-18\n"]}, {"ename": "KeyError", "evalue": "\"None of [Index(['concept', 'label', 'numeric_value'], dtype='object')] are in the [columns]\"", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[23], line 21\u001b[0m\n\u001b[1;32m     14\u001b[0m     assets \u001b[38;5;241m=\u001b[39m facts\u001b[38;5;241m.\u001b[39mquery() \\\n\u001b[1;32m     15\u001b[0m         \u001b[38;5;241m.\u001b[39mby_statement_type(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mBalanceSheet\u001b[39m\u001b[38;5;124m'\u001b[39m) \\\n\u001b[1;32m     16\u001b[0m         \u001b[38;5;241m.\u001b[39mby_period_key(latest_period[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mkey\u001b[39m\u001b[38;5;124m'\u001b[39m]) \\\n\u001b[1;32m     17\u001b[0m         \u001b[38;5;241m.\u001b[39mby_label(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAsset\u001b[39m\u001b[38;5;124m\"\u001b[39m, exact\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m) \\\n\u001b[1;32m     18\u001b[0m         \u001b[38;5;241m.\u001b[39mto_dataframe()\n\u001b[1;32m     20\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mFound \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(assets)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m asset-related facts for period \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mlatest_period[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mkey\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 21\u001b[0m     display(\u001b[43massets\u001b[49m\u001b[43m[\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mconcept\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mlabel\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mnumeric_value\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mhead(\u001b[38;5;241m5\u001b[39m))\n\u001b[1;32m     23\u001b[0m \u001b[38;5;66;03m# Let's get the last 2 periods for the income statement\u001b[39;00m\n\u001b[1;32m     24\u001b[0m duration_periods \u001b[38;5;241m=\u001b[39m [p \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m all_periods \u001b[38;5;28;01mif\u001b[39;00m p[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtype\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mduration\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "File \u001b[0;32m~/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages/pandas/core/frame.py:4108\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   4106\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_iterator(key):\n\u001b[1;32m   4107\u001b[0m         key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(key)\n\u001b[0;32m-> 4108\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_indexer_strict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcolumns\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m[\u001b[38;5;241m1\u001b[39m]\n\u001b[1;32m   4110\u001b[0m \u001b[38;5;66;03m# take() does not accept boolean indexers\u001b[39;00m\n\u001b[1;32m   4111\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(indexer, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdtype\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mbool\u001b[39m:\n", "File \u001b[0;32m~/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages/pandas/core/indexes/base.py:6200\u001b[0m, in \u001b[0;36mIndex._get_indexer_strict\u001b[0;34m(self, key, axis_name)\u001b[0m\n\u001b[1;32m   6197\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   6198\u001b[0m     keyarr, indexer, new_indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reindex_non_unique(keyarr)\n\u001b[0;32m-> 6200\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_if_missing\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkeyarr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   6202\u001b[0m keyarr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtake(indexer)\n\u001b[1;32m   6203\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, Index):\n\u001b[1;32m   6204\u001b[0m     \u001b[38;5;66;03m# GH 42790 - Preserve name from an Index\u001b[39;00m\n", "File \u001b[0;32m~/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages/pandas/core/indexes/base.py:6249\u001b[0m, in \u001b[0;36mIndex._raise_if_missing\u001b[0;34m(self, key, indexer, axis_name)\u001b[0m\n\u001b[1;32m   6247\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m nmissing:\n\u001b[1;32m   6248\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m nmissing \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mlen\u001b[39m(indexer):\n\u001b[0;32m-> 6249\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNone of [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m] are in the [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00maxis_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   6251\u001b[0m     not_found \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(ensure_index(key)[missing_mask\u001b[38;5;241m.\u001b[39mnonzero()[\u001b[38;5;241m0\u001b[39m]]\u001b[38;5;241m.\u001b[39munique())\n\u001b[1;32m   6252\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnot_found\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m not in index\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31m<PERSON><PERSON>Error\u001b[0m: \"None of [Index(['concept', 'label', 'numeric_value'], dtype='object')] are in the [columns]\""]}], "execution_count": 23}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:52:39.585605Z", "start_time": "2025-04-12T22:52:39.582781Z"}}, "cell_type": "code", "source": "xbrl.get_period_views(\"IncomeStatement\")", "outputs": [{"data": {"text/plain": ["[{'name': 'Three-Year Comparison',\n", "  'description': 'Compares three fiscal years',\n", "  'period_keys': ['duration_2023-10-01_2024-09-28',\n", "   'duration_2022-09-25_2023-09-30',\n", "   'duration_2021-09-26_2022-09-24']},\n", " {'name': 'Annual Comparison',\n", "  'description': 'Compares recent fiscal years',\n", "  'period_keys': ['duration_2023-10-01_2024-09-28',\n", "   'duration_2022-09-25_2023-09-30']}]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "execution_count": 24}, {"cell_type": "markdown", "metadata": {}, "source": ["## Working with Specific Facts"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:52:41.414846Z", "start_time": "2025-04-12T22:52:41.407455Z"}}, "source": ["# Get all facts from the balance sheet\n", "balance_sheet_facts = facts.get_statement_facts('BalanceSheet')\n", "print(f\"Balance sheet has {len(balance_sheet_facts)} facts\")\n", "\n", "# Show some key balance sheet facts\n", "balance_sheet_facts[balance_sheet_facts['label'].str.contains('Total Assets|Total Liabilities|Stockholders')][['concept', 'label', 'numeric_value']].head(10)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Balance sheet has 158 facts\n"]}, {"data": {"text/plain": ["Empty DataFrame\n", "Columns: [concept, label, numeric_value]\n", "Index: []"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>numeric_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "execution_count": 25}, {"cell_type": "markdown", "metadata": {}, "source": ["## Time Series Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": "## Period Views\n\nXBRL data typically contains multiple reporting periods. The Facts module allows you to query facts using predefined period views."}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:52:43.981981Z", "start_time": "2025-04-12T22:52:43.978549Z"}}, "source": "# Get available period views for the income statement\nincome_views = facts.get_available_period_views('IncomeStatement')\nprint(\"Available period views for Income Statement:\")\nfor view in income_views:\n    print(f\"- {view['name']}: {view['description']}\")\n    print(f\"  Periods: {view['period_keys']}\")\n    print(\"\")\n\n# Also check balance sheet views\nbalance_sheet_views = facts.get_available_period_views('BalanceSheet')\nprint(\"\\nAvailable period views for Balance Sheet:\")\nfor view in balance_sheet_views:\n    print(f\"- {view['name']}: {view['description']}\")\n    print(f\"  Periods: {view['period_keys']}\")\n    print(\"\")", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available period views for Income Statement:\n", "- Three-Year Comparison: Compares three fiscal years\n", "  Periods: ['duration_2023-10-01_2024-09-28', 'duration_2022-09-25_2023-09-30', 'duration_2021-09-26_2022-09-24']\n", "\n", "- Annual Comparison: Compares recent fiscal years\n", "  Periods: ['duration_2023-10-01_2024-09-28', 'duration_2022-09-25_2023-09-30']\n", "\n", "\n", "Available period views for Balance Sheet:\n", "- Three Recent Periods: Shows three most recent reporting periods\n", "  Periods: ['instant_2024-10-18', 'instant_2024-09-28', 'instant_2024-03-29']\n", "\n", "- Three-Year Annual Comparison: Shows three fiscal years for comparison\n", "  Periods: ['instant_2024-10-18', 'instant_2024-09-28', 'instant_2023-09-30']\n", "\n", "- Annual Comparison: Shows two fiscal years for comparison\n", "  Periods: ['instant_2024-10-18', 'instant_2024-09-28']\n", "\n"]}], "execution_count": 26}, {"cell_type": "markdown", "metadata": {}, "source": "# Query facts using a specific period view\nif income_views:  # Make sure there are available views\n    # Let's use Annual Comparison if available, otherwise use the first view\n    annual_view = next((view for view in income_views if 'Annual' in view['name']), income_views[0])\n    view_name = annual_view['name']\n    print(f\"Getting facts for period view: {view_name}\")\n    \n    # Get facts filtered by this period view\n    view_facts = facts.get_facts_by_period_view('IncomeStatement', view_name)\n    \n    # Get key metrics like revenue and net income\n    key_metrics = view_facts[view_facts['label'].str.contains('Revenue|Net Income|Operating Income', \n                                                             case=False, na=False)]\n    \n    # Show the results\n    print(f\"Found {len(key_metrics)} key metrics across {len(annual_view['period_keys'])} periods\")\n    display(key_metrics[['concept', 'label', 'numeric_value', 'period_key']].head(10))\n    \n    # Create a pivot table to better visualize the data across periods\n    pivot = key_metrics.pivot_table(\n        values='numeric_value',\n        index=['concept', 'label'],\n        columns='period_key',\n        aggfunc='first'\n    )\n    \n    # Display the pivoted data\n    print(\"\\nPivot table of key metrics across periods:\")\n    display(pivot)"}, {"cell_type": "markdown", "metadata": {}, "source": "## Time Series Analysis"}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:52:48.443655Z", "start_time": "2025-04-12T22:52:48.433525Z"}}, "source": ["# If we have a useful dimension, create a pivoted view\n", "if dimensions:\n", "    # Use the first dimension as an example\n", "    dim_name = list(dimensions.keys())[0]\n", "    pivot_df = facts.pivot_by_dimension(dim_name)\n", "    if not pivot_df.empty:\n", "        display(pivot_df.head())"], "outputs": [{"ename": "NameError", "evalue": "name 'dimensions' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[27], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# If we have a useful dimension, create a pivoted view\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mdimensions\u001b[49m:\n\u001b[1;32m      3\u001b[0m     \u001b[38;5;66;03m# Use the first dimension as an example\u001b[39;00m\n\u001b[1;32m      4\u001b[0m     dim_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(dimensions\u001b[38;5;241m.\u001b[39mkeys())[\u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m      5\u001b[0m     pivot_df \u001b[38;5;241m=\u001b[39m facts\u001b[38;5;241m.\u001b[39mpivot_by_dimension(dim_name)\n", "\u001b[0;31mNameError\u001b[0m: name 'dimensions' is not defined"]}], "execution_count": 27}, {"cell_type": "markdown", "metadata": {}, "source": "# Compare quarterly periods if available\nquarterly_view = next((view for view in income_views if 'Quarter' in view['name']), None)\nif quarterly_view:\n    print(f\"Analyzing quarterly comparison: {quarterly_view['name']}\")\n    \n    # Get facts for quarterly comparison\n    quarterly_facts = facts.get_facts_by_period_view('IncomeStatement', quarterly_view['name'])\n    \n    # Focus on revenue\n    revenue_facts = quarterly_facts[quarterly_facts['label'].str.contains('Revenue', case=False, na=False)]\n    \n    # Show quarterly revenue\n    display(revenue_facts[['concept', 'label', 'numeric_value', 'period_key']].head(10))\n    \n    # Pivot by quarter\n    revenue_pivot = revenue_facts.pivot_table(\n        values='numeric_value',\n        index=['concept', 'label'],\n        columns='period_key',\n        aggfunc='first'\n    )\n    \n    # Display the quarterly comparison\n    print(\"\\nQuarterly Revenue Comparison:\")\n    display(revenue_pivot)\n    \n    # Plot if we have at least 2 periods\n    if len(quarterly_view['period_keys']) >= 2 and not revenue_pivot.empty:\n        # Convert the pivot table to be more plot-friendly\n        plot_df = revenue_pivot.reset_index()\n        \n        # Only plot the revenue row (not sub-components)\n        main_revenue = plot_df[plot_df['label'].str.contains('^Revenue$|^Total Revenue$', case=False, regex=True)]\n        \n        if not main_revenue.empty:\n            # Melt the dataframe to get it in the right format for plotting\n            period_columns = [col for col in main_revenue.columns if col not in ['concept', 'label']]\n            plot_ready = main_revenue.melt(\n                id_vars=['concept', 'label'],\n                value_vars=period_columns,\n                var_name='Period',\n                value_name='Revenue'\n            )\n            \n            # Plot\n            ax = plot_ready.plot(\n                x='Period', \n                y='Revenue', \n                kind='bar', \n                figsize=(12, 6), \n                title=f\"Revenue by Quarter\"\n            )\n            ax.set_ylabel('Revenue ($)')\n            ax.set_xlabel('Period')\n            ax.grid(axis='y')"}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get income statement items across periods\n", "income_pivot = facts.pivot_by_period(statement_type='IncomeStatement')\n", "if not income_pivot.empty:\n", "    # Filter to just a few key metrics\n", "    key_metrics = income_pivot[income_pivot['label'].str.contains('Revenue|Income|Earnings', case=False, na=False)]\n", "    display(key_metrics.head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complex Queries\n", "\n", "Combining multiple filters allows for powerful and specific queries."]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-18T14:21:57.992137Z", "start_time": "2025-03-18T14:21:57.968681Z"}}, "source": ["# Find facts that are:\n", "# 1. Related to the balance sheet\n", "# 2. Have \"cash\" in their label\n", "# 3. Are for the most recent period\n", "# 4. Have a value greater than 1 billion\n", "\n", "# First, find the most recent period in the balance sheet\n", "bs_periods = [p for p in summary['periods'] if 'instant' in p]\n", "if bs_periods:\n", "    latest_period = sorted(bs_periods)[-1]  # Get the last period when sorted\n", "    \n", "    complex_query = facts.query()\\\n", "        .by_statement_type('BalanceSheet')\\\n", "        .by_label('cash', exact=False)\\\n", "        .by_custom(lambda f: 'period_key' in f and f['period_key'] == latest_period)\\\n", "        .by_value(lambda v: v > 1_000_000_000)\\\n", "        .sort_by('numeric_value', ascending=False)\n", "    \n", "    result = complex_query.to_dataframe()\n", "    display(result[['concept', 'label', 'numeric_value', 'period_key']].head())"], "outputs": [{"ename": "KeyError", "evalue": "\"None of [Index(['concept', 'label', 'numeric_value', 'period_key'], dtype='object')] are in the [columns]\"", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[38], line 20\u001b[0m\n\u001b[1;32m     12\u001b[0m complex_query \u001b[38;5;241m=\u001b[39m facts\u001b[38;5;241m.\u001b[39mquery()\\\n\u001b[1;32m     13\u001b[0m     \u001b[38;5;241m.\u001b[39mby_statement_type(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mBalanceSheet\u001b[39m\u001b[38;5;124m'\u001b[39m)\\\n\u001b[1;32m     14\u001b[0m     \u001b[38;5;241m.\u001b[39mby_label(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcash\u001b[39m\u001b[38;5;124m'\u001b[39m, exact\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\\\n\u001b[1;32m     15\u001b[0m     \u001b[38;5;241m.\u001b[39mby_custom(\u001b[38;5;28;01mlambda\u001b[39;00m f: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mperiod_key\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01min\u001b[39;00m f \u001b[38;5;129;01mand\u001b[39;00m f[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mperiod_key\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m latest_period)\\\n\u001b[1;32m     16\u001b[0m     \u001b[38;5;241m.\u001b[39mby_value(\u001b[38;5;28;01mlambda\u001b[39;00m v: v \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1_000_000_000\u001b[39m)\\\n\u001b[1;32m     17\u001b[0m     \u001b[38;5;241m.\u001b[39msort_by(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mnumeric_value\u001b[39m\u001b[38;5;124m'\u001b[39m, ascending\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[1;32m     19\u001b[0m result \u001b[38;5;241m=\u001b[39m complex_query\u001b[38;5;241m.\u001b[39mto_dataframe()\n\u001b[0;32m---> 20\u001b[0m display(\u001b[43mresult\u001b[49m\u001b[43m[\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mconcept\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mlabel\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mnumeric_value\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mperiod_key\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mhead())\n", "File \u001b[0;32m~/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages/pandas/core/frame.py:4108\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   4106\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_iterator(key):\n\u001b[1;32m   4107\u001b[0m         key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(key)\n\u001b[0;32m-> 4108\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_indexer_strict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcolumns\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m[\u001b[38;5;241m1\u001b[39m]\n\u001b[1;32m   4110\u001b[0m \u001b[38;5;66;03m# take() does not accept boolean indexers\u001b[39;00m\n\u001b[1;32m   4111\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(indexer, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdtype\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mbool\u001b[39m:\n", "File \u001b[0;32m~/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages/pandas/core/indexes/base.py:6200\u001b[0m, in \u001b[0;36mIndex._get_indexer_strict\u001b[0;34m(self, key, axis_name)\u001b[0m\n\u001b[1;32m   6197\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   6198\u001b[0m     keyarr, indexer, new_indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reindex_non_unique(keyarr)\n\u001b[0;32m-> 6200\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_if_missing\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkeyarr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   6202\u001b[0m keyarr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtake(indexer)\n\u001b[1;32m   6203\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, Index):\n\u001b[1;32m   6204\u001b[0m     \u001b[38;5;66;03m# GH 42790 - Preserve name from an Index\u001b[39;00m\n", "File \u001b[0;32m~/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages/pandas/core/indexes/base.py:6249\u001b[0m, in \u001b[0;36mIndex._raise_if_missing\u001b[0;34m(self, key, indexer, axis_name)\u001b[0m\n\u001b[1;32m   6247\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m nmissing:\n\u001b[1;32m   6248\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m nmissing \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mlen\u001b[39m(indexer):\n\u001b[0;32m-> 6249\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNone of [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m] are in the [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00maxis_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   6251\u001b[0m     not_found \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(ensure_index(key)[missing_mask\u001b[38;5;241m.\u001b[39mnonzero()[\u001b[38;5;241m0\u001b[39m]]\u001b[38;5;241m.\u001b[39munique())\n\u001b[1;32m   6252\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnot_found\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m not in index\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: \"None of [Index(['concept', 'label', 'numeric_value', 'period_key'], dtype='object')] are in the [columns]\""]}], "execution_count": 38}, {"cell_type": "markdown", "metadata": {}, "source": ["## Combining with Traditional Statement Access\n", "\n", "The facts module complements the existing statements functionality, allowing you to analyze the same data in different ways."]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-18T14:22:05.471343Z", "start_time": "2025-03-18T14:22:05.433790Z"}}, "source": ["# Get the balance sheet using the statements API\n", "balance_sheet = xbrl.statements.balance_sheet()\n", "print(balance_sheet)"], "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[3m                           CONSOLIDATEDBALANCESHEETS (Standardized)                            \u001b[0m\n", "\u001b[3m                  \u001b[0m\u001b[1;3mFiscal Year Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m                  \u001b[0m\n", "                                                                                               \n", " \u001b[1m \u001b[0m\u001b[1mLine Item                                     \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 30, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 24, 2022\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────── \n", "    ASSETS:                                                                                    \n", "      Current assets:                                                                          \n", "        Cash and Cash Equivalents                       $29,943        $29,965                 \n", "        Marketable securities                           $35,228        $31,590                 \n", "        Accounts Receivable                             $33,410        $29,508                 \n", "        Vendor non-trade receivables                    $32,833        $31,477                 \n", "        Inventory                                        $7,286         $6,331                 \n", "        Other Assets                                    $14,287        $14,695                 \n", "        Total Current Assets                           $152,987       $143,566                 \n", "      Non-current assets:                                                                      \n", "        Marketable securities                           $91,479       $100,544                 \n", "        Property, Plant and Equipment                   $45,680        $43,715                 \n", "        Other Assets                                    $74,834        $64,758                 \n", "        Total Current Assets                           $211,993       $209,017                 \n", "      Total Assets                                     $364,980       $352,583                 \n", "    LIABILITIES AND SHAREH<PERSON>DERS’ EQUITY:                                                      \n", "      Current liabilities:                                                                     \n", "        Accounts Payable                                $68,960        $62,611                 \n", "        Other Liabilities                               $78,304        $58,829                 \n", "        Deferred Revenue                                 $8,249         $8,061                 \n", "        Commercial paper                                 $9,967         $5,985                 \n", "        Short-Term Debt                                 $10,912         $9,822                 \n", "        Total Current Liabilities                      $176,392       $145,308                 \n", "      Non-current liabilities:                                                                 \n", "        Long-Term Debt                                  $85,750        $95,281                 \n", "        Other Liabilities                               $45,888        $49,848                 \n", "        Total Current Liabilities                      $131,638       $145,129                 \n", "      Total Liabilities                                $308,030       $290,437                 \n", "      Commitments and contingencies                                                            \n", "      Common Stock Shares Outstanding                15,116,786     15,550,061     15,943,425  \n", "      Common Stock Shares Issued                     15,116,786     15,550,061                 \n", "      Shareholders’ equity:                                                                    \n", "        Common Stock                                    $83,276        $73,812                 \n", "        Retained Earnings                             $(19,154)         $(214)                 \n", "        Accumulated other comprehensive loss           $(7,172)      $(11,452)                 \n", "        Total Stockholders' Equity                      $56,950        $62,146        $50,672  \n", "      Total Liabilities and Stockholders' Equity       $364,980       $352,583                 \n", "                                                                                               \n", "\n"]}], "execution_count": 39}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-18T14:22:07.910894Z", "start_time": "2025-03-18T14:22:07.904424Z"}}, "source": ["# Compare with facts-based approach for the same data\n", "cash_assets = facts.query()\\\n", "    .by_statement_type('BalanceSheet')\\\n", "    .by_label(r'[Cc]ash|[Ee]quivalent')\\\n", "    .to_dataframe()\n", "\n", "display(cash_assets[['concept', 'label', 'numeric_value', 'period_instant']].head())"], "outputs": [{"data": {"text/plain": ["                                         concept  \\\n", "0  us-gaap:CashAndCashEquivalentsAtCarryingValue   \n", "1  us-gaap:CashAndCashEquivalentsAtCarryingValue   \n", "2  us-gaap:CashAndCashEquivalentsAtCarryingValue   \n", "3  us-gaap:CashAndCashEquivalentsAtCarryingValue   \n", "4  us-gaap:CashAndCashEquivalentsAtCarryingValue   \n", "\n", "                                          label  numeric_value period_instant  \n", "0  Cash and Cash Equivalents, at Carrying Value   2.994300e+10     2024-09-28  \n", "1  Cash and Cash Equivalents, at Carrying Value   2.996500e+10     2023-09-30  \n", "2  Cash and Cash Equivalents, at Carrying Value   2.719900e+10     2024-09-28  \n", "3  Cash and Cash Equivalents, at Carrying Value   7.780000e+08     2024-09-28  \n", "4  Cash and Cash Equivalents, at Carrying Value   0.000000e+00     2024-09-28  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>numeric_value</th>\n", "      <th>period_instant</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap:CashAndCashEquivalentsAtCarryingValue</td>\n", "      <td>Cash and Cash Equivalents, at Carrying Value</td>\n", "      <td>2.994300e+10</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap:CashAndCashEquivalentsAtCarryingValue</td>\n", "      <td>Cash and Cash Equivalents, at Carrying Value</td>\n", "      <td>2.996500e+10</td>\n", "      <td>2023-09-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap:CashAndCashEquivalentsAtCarryingValue</td>\n", "      <td>Cash and Cash Equivalents, at Carrying Value</td>\n", "      <td>2.719900e+10</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap:CashAndCashEquivalentsAtCarryingValue</td>\n", "      <td>Cash and Cash Equivalents, at Carrying Value</td>\n", "      <td>7.780000e+08</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap:CashAndCashEquivalentsAtCarryingValue</td>\n", "      <td>Cash and Cash Equivalents, at Carrying Value</td>\n", "      <td>0.000000e+00</td>\n", "      <td>2024-09-28</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 40}, {"cell_type": "markdown", "metadata": {}, "source": "## Conclusion\n\nThe enhanced Facts module provides a flexible and powerful way to query and analyze XBRL data, complementing the statement-oriented approach of the core XBRL2 API. You can use it to:\n\n1. Search for specific concepts, labels, or values with robust handling of null values\n2. Analyze facts across multiple dimensions and time periods\n3. Use smart text search across multiple fields with a single query\n4. Filter by predefined period views or custom period selections\n5. Combine multiple filters for precise data selection\n6. Generate pandas DataFrames for further analysis and visualization\n\nRecent enhancements include:\n- Improved text search across multiple fields with `search_facts()` method\n- Safer numeric value filtering with proper null-value handling\n- New period filtering methods including `by_period_key()` and `by_period_keys()`\n- Enhanced period views with facts count statistics\n- Better handling of namespaced elements with colon/underscore conversion\n\nThis query-oriented approach is particularly useful for ad-hoc analysis, data exploration, and extracting specific metrics for financial modeling."}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}