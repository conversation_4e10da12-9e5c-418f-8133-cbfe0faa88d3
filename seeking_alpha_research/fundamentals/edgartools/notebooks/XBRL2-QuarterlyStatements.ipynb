{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "# Quarterly Statements\n", "id": "8b36a7043a472e37"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "#!pip install edgartools", "id": "422e271d482f73c9"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-02T11:23:51.702927Z", "start_time": "2025-04-02T11:23:51.699201Z"}}, "cell_type": "code", "source": ["from edgar import *\n", "from rich import print\n", "from edgar.xbrl import *\n", "from edgar.reference.tickers import popular_us_stocks"], "id": "c7dc9cfbb25925e", "outputs": [], "execution_count": 8}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-02T11:23:52.745533Z", "start_time": "2025-04-02T11:23:52.742440Z"}}, "cell_type": "code", "source": "%load_ext rich", "id": "b624d9b5dfb782c1", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The rich extension is already loaded. To reload it, use:\n", "  %reload_ext rich\n"]}], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-02T11:23:54.545669Z", "start_time": "2025-04-02T11:23:54.539699Z"}}, "cell_type": "code", "source": ["stocks = popular_us_stocks()\n", "tickers =stocks.Ticker.to_list()\n", "tickers[10]"], "id": "27378d3b7eed9ec9", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32m'V'\u001b[0m"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "execution_count": 10}, {"metadata": {}, "cell_type": "markdown", "source": "## Quarterly Statements\n", "id": "5d7af195f7ed7cef"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-02T11:23:57.298736Z", "start_time": "2025-04-02T11:23:56.880566Z"}}, "cell_type": "code", "source": ["def quarterly_statements(ticker):\n", "    print(ticker)\n", "    c = Company(ticker)\n", "    filings = c.latest(\"10-Q\", 20)\n", "    xbrl0 = XBRL.from_filing(filings[0])\n", "    balance_sheet = xbrl0.statements.balance_sheet()\n", "    print(balance_sheet)\n", "\n", "    income_statement = xbrl0.statements.income_statement()\n", "    print(income_statement)\n", "\n", "    #xbrl_last = XBRL.from_filing(filings[-1])\n", "    #balance_sheet = xbrl_last.statements.balance_sheet()\n", "    #print(balance_sheet)\n", "\n", "quarterly_statements(tickers[1])\n"], "id": "50a023f524d9f2ae", "outputs": [{"data": {"text/plain": ["MSFT\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">MSFT\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                    Consolidated Balance Sheets (Standardized)                     \u001b[0m\n", "\u001b[3m             \u001b[0m\u001b[1;3mSecond Quarter Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except per share data)\u001b[0m\u001b[3m             \u001b[0m\n", "                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m                                                 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 30, 2024\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────── \n", "    Assets                                                                         \n", "      Current assets:                                                              \n", "        Cash and Cash Equivalents                          $17,482                 \n", "        Short-term investments                             $54,073                 \n", "        Cash and Cash Equivalents                          $71,555                 \n", "        Accounts Receivable                                $48,188                 \n", "        Inventory                                             $909                 \n", "        Other Assets                                       $26,428                 \n", "        Total Current Assets                              $147,080                 \n", "      Property, Plant and Equipment                       $166,902                 \n", "      Operating lease right-of-use assets                  $22,816                 \n", "      Long-Term Investments                                $15,581                 \n", "      Goodwill                                            $119,191                 \n", "      Intangible Assets                                    $25,385                 \n", "      Other Assets                                         $36,943                 \n", "      Total Assets                                        $533,898                 \n", "    Liabilities and stockholders’ equity                                           \n", "      Current liabilities:                                                         \n", "        Accounts Payable                                   $22,608                 \n", "        Short-<PERSON><PERSON>                                           \u0014                 \n", "        Short-Term Debt                                     $5,248                 \n", "        Accrued Liabilities                                 $9,176                 \n", "        Short-term income taxes                             $6,056                 \n", "        Short-term unearned revenue                        $45,508                 \n", "        Other Liabilities                                  $20,286                 \n", "        Total Current Liabilities                         $108,882                 \n", "      Long-Term Debt                                       $39,722                 \n", "      Long-term income taxes                               $24,389                 \n", "      Long-term unearned revenue                            $2,537                 \n", "      Deferred income taxes                                 $2,513                 \n", "      Operating lease liabilities                          $17,254                 \n", "      Other Liabilities                                    $35,906                 \n", "      Total Liabilities                                   $231,203                 \n", "      Commitments and contingencies                                                \n", "      Stockholders’ equity:                                                        \n", "        Common Stock                                      $104,829                 \n", "        Retained Earnings                                 $203,482                 \n", "        Accumulated Other Comprehensive Income/Loss       $(5,616)                 \n", "        Total Stockholders' Equity                        $302,695       $102,976  \n", "      Total Liabilities and Stockholders' Equity          $533,898                 \n", "                                                                                   \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                    Consolidated Balance Sheets (Standardized)                     </span>\n", "<span style=\"font-style: italic\">             </span><span style=\"font-weight: bold; font-style: italic\">Second Quarter Ended</span><span style=\"font-style: italic\"> (In millions, except per share data)             </span>\n", "                                                                                   \n", " <span style=\"font-weight: bold\">                                                   </span> <span style=\"font-weight: bold\"> Dec 31, 2024 </span> <span style=\"font-weight: bold\"> Sep 30, 2024 </span> \n", " ───────────────────────────────────────────────────────────────────────────────── \n", "    Assets                                                                         \n", "      Current assets:                                                              \n", "        Cash and Cash Equivalents                          $17,482                 \n", "        Short-term investments                             $54,073                 \n", "        Cash and Cash Equivalents                          $71,555                 \n", "        Accounts Receivable                                $48,188                 \n", "        Inventory                                             $909                 \n", "        Other Assets                                       $26,428                 \n", "        Total Current Assets                              $147,080                 \n", "      Property, Plant and Equipment                       $166,902                 \n", "      Operating lease right-of-use assets                  $22,816                 \n", "      Long-Term Investments                                $15,581                 \n", "      Goodwill                                            $119,191                 \n", "      Intangible Assets                                    $25,385                 \n", "      Other Assets                                         $36,943                 \n", "      Total Assets                                        $533,898                 \n", "    Liabilities and stockholders’ equity                                           \n", "      Current liabilities:                                                         \n", "        Accounts Payable                                   $22,608                 \n", "        Short-<PERSON><PERSON>                                           \u0014                 \n", "        Short-Term Debt                                     $5,248                 \n", "        Accrued Liabilities                                 $9,176                 \n", "        Short-term income taxes                             $6,056                 \n", "        Short-term unearned revenue                        $45,508                 \n", "        Other Liabilities                                  $20,286                 \n", "        Total Current Liabilities                         $108,882                 \n", "      Long-Term Debt                                       $39,722                 \n", "      Long-term income taxes                               $24,389                 \n", "      Long-term unearned revenue                            $2,537                 \n", "      Deferred income taxes                                 $2,513                 \n", "      Operating lease liabilities                          $17,254                 \n", "      Other Liabilities                                    $35,906                 \n", "      Total Liabilities                                   $231,203                 \n", "      Commitments and contingencies                                                \n", "      Stockholders’ equity:                                                        \n", "        Common Stock                                      $104,829                 \n", "        Retained Earnings                                 $203,482                 \n", "        Accumulated Other Comprehensive Income/Loss       $(5,616)                 \n", "        Total Stockholders' Equity                        $302,695       $102,976  \n", "      Total Liabilities and Stockholders' Equity          $533,898                 \n", "                                                                                   \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                    Consolidated Statement of Income (Standardized)                     \u001b[0m\n", "\u001b[3m                \u001b[0m\u001b[1;3mThree Months Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except per share data)\u001b[0m\u001b[3m                 \u001b[0m\n", "                                                                                        \n", " \u001b[1m \u001b[0m\u001b[1m                                                 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2024 (Q1)\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 31, 2024\u001b[0m\u001b[1m \u001b[0m \n", " ────────────────────────────────────────────────────────────────────────────────────── \n", "      Product and Service                                                               \n", "        Product and Service                                                             \n", "        Revenue                                                  69,632        135,217  \n", "        Cost of Revenue                                         -21,799        -41,898  \n", "        Gross Profit                                             47,833         93,319  \n", "        Research and Development Expense                         -7,917        -15,461  \n", "        Selling, General and Administrative Expense              -6,440        -12,157  \n", "        Selling, General and Administrative Expense              -1,823         -3,496  \n", "        Operating Income                                         31,653         62,205  \n", "        Nonoperating Income/Expense                              -2,288         -2,571  \n", "        Income Before Tax                                        29,365         59,634  \n", "        Income Tax Expense                                       -5,257        -10,859  \n", "        Net Income                                               24,108         48,775  \n", "        Earnings per share:                                                             \n", "          Earnings Per Share                                          0              0  \n", "          Earnings Per Share (Diluted)                                0              0  \n", "        Weighted average shares outstanding:                                            \n", "          Shares Outstanding                                      7,435          7,434  \n", "          Shares Outstanding (Diluted)                            7,468          7,469  \n", "                                                                                        \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                    Consolidated Statement of Income (Standardized)                     </span>\n", "<span style=\"font-style: italic\">                </span><span style=\"font-weight: bold; font-style: italic\">Three Months Ended</span><span style=\"font-style: italic\"> (In millions, except per share data)                 </span>\n", "                                                                                        \n", " <span style=\"font-weight: bold\">                                                   </span> <span style=\"font-weight: bold\"> Dec 31, 2024 (Q1) </span> <span style=\"font-weight: bold\"> Dec 31, 2024 </span> \n", " ────────────────────────────────────────────────────────────────────────────────────── \n", "      Product and Service                                                               \n", "        Product and Service                                                             \n", "        Revenue                                                  69,632        135,217  \n", "        Cost of Revenue                                         -21,799        -41,898  \n", "        Gross Profit                                             47,833         93,319  \n", "        Research and Development Expense                         -7,917        -15,461  \n", "        Selling, General and Administrative Expense              -6,440        -12,157  \n", "        Selling, General and Administrative Expense              -1,823         -3,496  \n", "        Operating Income                                         31,653         62,205  \n", "        Nonoperating Income/Expense                              -2,288         -2,571  \n", "        Income Before Tax                                        29,365         59,634  \n", "        Income Tax Expense                                       -5,257        -10,859  \n", "        Net Income                                               24,108         48,775  \n", "        Earnings per share:                                                             \n", "          Earnings Per Share                                          0              0  \n", "          Earnings Per Share (Diluted)                                0              0  \n", "        Weighted average shares outstanding:                                            \n", "          Shares Outstanding                                      7,435          7,434  \n", "          Shares Outstanding (Diluted)                            7,468          7,469  \n", "                                                                                        \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 11}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-02T11:24:02.031628Z", "start_time": "2025-04-02T11:24:01.911316Z"}}, "cell_type": "code", "source": ["c = Company(\"AAPL\")\n", "f = c.latest(\"10-Q\")\n", "xbrl = XBRL.from_filing(f)\n", "balance_sheet = xbrl.statements.balance_sheet()\n", "balance_sheet"], "id": "63f1980c0e6ef9a2", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\n", "\u001b[3m                    Consolidated Balance Sheets (Standardized)                     \u001b[0m\n", "\u001b[3m           \u001b[0m\u001b[1;3mFirst Quarter Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m           \u001b[0m\n", "                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m                                                 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 28, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────── \n", "    ASSETS:                                                                        \n", "      Current assets:                                                              \n", "        Cash and Cash Equivalents                          $30,299        $29,943  \n", "        Marketable Securities                              $23,476        $35,228  \n", "        Accounts Receivable                                $29,639        $33,410  \n", "        Vendor non-trade receivables                       $29,667        $32,833  \n", "        Inventory                                           $6,911         $7,286  \n", "        Other Assets                                       $13,248        $14,287  \n", "        Total Current Assets                              $133,240       $152,987  \n", "      Non-current assets:                                                          \n", "        Marketable securities                              $87,593        $91,479  \n", "        Property, Plant and Equipment                      $46,069        $45,680  \n", "        Other Assets                                       $77,183        $74,834  \n", "        Total Current Assets                              $210,845       $211,993  \n", "      Total Assets                                        $344,085       $364,980  \n", "    LIABILITIES AND SHAREH<PERSON>DERS’ EQUITY:                                          \n", "      Current liabilities:                                                         \n", "        Accounts Payable                                   $61,910        $68,960  \n", "        Other Liabilities                                  $61,151        $78,304  \n", "        Deferred Revenue                                    $8,461         $8,249  \n", "        Commercial paper                                    $1,995         $9,967  \n", "        Short-Term Debt                                    $10,848        $10,912  \n", "        Total Current Liabilities                         $144,365       $176,392  \n", "      Non-current liabilities:                                                     \n", "        Long-Term Debt                                     $83,956        $85,750  \n", "        Other Liabilities                                  $49,006        $45,888  \n", "        Total Current Liabilities                         $132,962       $131,638  \n", "      Total Liabilities                                   $277,327       $308,030  \n", "      Commitments and contingencies                                                \n", "      Common Stock Shares Outstanding                      $15,041        $15,117  \n", "      Common Stock Shares Issued                           $15,041        $15,117  \n", "      Shareholders’ equity:                                                        \n", "        Common Stock                                       $84,768        $83,276  \n", "        Retained Earnings                                $(11,221)      $(19,154)  \n", "        Accumulated Other Comprehensive Income/Loss       $(6,789)       $(7,172)  \n", "        Total Stockholders' Equity                         $66,758        $56,950  \n", "      Total Liabilities and Stockholders' Equity          $344,085       $364,980  \n", "                                                                                   "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "execution_count": 12}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-02T00:33:18.322584Z", "start_time": "2025-04-02T00:33:18.306559Z"}}, "cell_type": "code", "source": "xbrl.statements.income_statement()", "id": "782047b9ad5a10e2", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\n", "\u001b[3m                        Consolidated Statement of Income (Standardized)                        \u001b[0m\n", "\u001b[3m                 \u001b[0m\u001b[1;3mThree Months Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m                  \u001b[0m\n", "                                                                                               \n", " \u001b[1m \u001b[0m\u001b[1m                                                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 28, 2024 (Q1)\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDec 30, 2023 (Q1)\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────── \n", "        Revenue                                               124,300,000         119,575,000  \n", "        Cost of Revenue                                       -66,025,000         -64,720,000  \n", "        Gross Profit                                           58,275,000          54,855,000  \n", "        Operating Expenses                                                                     \n", "          Research and Development Expense                      8,268,000           7,696,000  \n", "          Selling, General and Administrative Expense           7,175,000           6,786,000  \n", "          Operating Expenses                                  -15,443,000         -14,482,000  \n", "        Operating Income                                       42,832,000          40,373,000  \n", "        Nonoperating Income/Expense                              -248,000             -50,000  \n", "        Income Before Tax                                      42,584,000          40,323,000  \n", "        Income Tax Expense                                     -6,254,000          -6,407,000  \n", "        Net Income                                             36,330,000          33,916,000  \n", "        Earnings per share:                                                                    \n", "          Earnings Per Share                                            0                   0  \n", "          Earnings Per Share (Diluted)                                  0                   0  \n", "        Shares used in computing earnings per share:                                           \n", "          Shares Outstanding                                   15,081,724          15,509,763  \n", "          Shares Outstanding (Diluted)                         15,150,865          15,576,641  \n", "                                                                                               "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-02T00:33:21.214469Z", "start_time": "2025-04-02T00:33:21.177490Z"}}, "cell_type": "code", "source": ["(xbrl.query()\n", "    .by_text(\"Revenue\")\n", "    .by_statement_type(\"IncomeStatement\")\n", "    .to_dataframe()\n", " )"], "id": "1a4a3b2836545cfe", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\n", "                                              concept      label  \\\n", "\u001b[1;36m0\u001b[0m   us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m1\u001b[0m   us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m2\u001b[0m   us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m3\u001b[0m   us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m4\u001b[0m   us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m5\u001b[0m   us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m6\u001b[0m   us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m7\u001b[0m   us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m8\u001b[0m   us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m9\u001b[0m   us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m10\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m11\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m12\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m13\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m14\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m15\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m16\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m17\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m18\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m19\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m20\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m21\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m22\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\u001b[1;36m23\u001b[0m  us-gaap:RevenueFromContractWithCustomerExcludi\u001b[33m...\u001b[0m  Net sales   \n", "\n", "           value  numeric_value period_start  period_end unit_ref decimals  \\\n", "\u001b[1;36m0\u001b[0m    \u001b[1;36m***********\u001b[0m   \u001b[1;36m9.796000e+10\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m1\u001b[0m    \u001b[1;36m***********\u001b[0m   \u001b[1;36m9.645800e+10\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m2\u001b[0m    \u001b[1;36m26340000000\u001b[0m   \u001b[1;36m2.634000e+10\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m3\u001b[0m    \u001b[1;36m23117000000\u001b[0m   \u001b[1;36m2.311700e+10\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m4\u001b[0m   \u001b[1;36m124300000000\u001b[0m   \u001b[1;36m1.243000e+11\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m5\u001b[0m   \u001b[1;36m119575000000\u001b[0m   \u001b[1;36m1.195750e+11\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m6\u001b[0m    \u001b[1;36m69138000000\u001b[0m   \u001b[1;36m6.913800e+10\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m7\u001b[0m    \u001b[1;36m69702000000\u001b[0m   \u001b[1;36m6.970200e+10\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m8\u001b[0m     \u001b[1;36m8987000000\u001b[0m   \u001b[1;36m8.987000e+09\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m9\u001b[0m     \u001b[1;36m7780000000\u001b[0m   \u001b[1;36m7.780000e+09\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m10\u001b[0m    \u001b[1;36m8088000000\u001b[0m   \u001b[1;36m8.088000e+09\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m11\u001b[0m    \u001b[1;36m7023000000\u001b[0m   \u001b[1;36m7.023000e+09\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m12\u001b[0m   \u001b[1;36m11747000000\u001b[0m   \u001b[1;36m1.174700e+10\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m13\u001b[0m   \u001b[1;36m11953000000\u001b[0m   \u001b[1;36m1.195300e+10\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m14\u001b[0m   \u001b[1;36m52648000000\u001b[0m   \u001b[1;36m5.264800e+10\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m15\u001b[0m   \u001b[1;36m50430000000\u001b[0m   \u001b[1;36m5.043000e+10\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m16\u001b[0m   \u001b[1;36m33861000000\u001b[0m   \u001b[1;36m3.386100e+10\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m17\u001b[0m   \u001b[1;36m30397000000\u001b[0m   \u001b[1;36m3.039700e+10\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m18\u001b[0m   \u001b[1;36m18513000000\u001b[0m   \u001b[1;36m1.851300e+10\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m19\u001b[0m   \u001b[1;36m20819000000\u001b[0m   \u001b[1;36m2.081900e+10\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m20\u001b[0m    \u001b[1;36m8987000000\u001b[0m   \u001b[1;36m8.987000e+09\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m21\u001b[0m    \u001b[1;36m7767000000\u001b[0m   \u001b[1;36m7.767000e+09\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m22\u001b[0m   \u001b[1;36m10291000000\u001b[0m   \u001b[1;36m1.029100e+10\u001b[0m   \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m29\u001b[0m  \u001b[1;36m2024\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m28\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\u001b[1;36m23\u001b[0m   \u001b[1;36m10162000000\u001b[0m   \u001b[1;36m1.016200e+10\u001b[0m   \u001b[1;36m2023\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m01\u001b[0m  \u001b[1;36m2023\u001b[0m-\u001b[1;36m12\u001b[0m-\u001b[1;36m30\u001b[0m      usd       \u001b[1;36m-6\u001b[0m   \n", "\n", "   footnotes            dim_srt_ProductOrServiceAxis   statement_type  \\\n", "\u001b[1;36m0\u001b[0m         \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                   us-gaap:ProductMember  IncomeStatement   \n", "\u001b[1;36m1\u001b[0m         \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                   us-gaap:ProductMember  IncomeStatement   \n", "\u001b[1;36m2\u001b[0m         \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                   us-gaap:ServiceMember  IncomeStatement   \n", "\u001b[1;36m3\u001b[0m         \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                   us-gaap:ServiceMember  IncomeStatement   \n", "\u001b[1;36m4\u001b[0m         \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m5\u001b[0m         \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m6\u001b[0m         \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                       aapl:IPhoneMember  IncomeStatement   \n", "\u001b[1;36m7\u001b[0m         \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                       aapl:IPhoneMember  IncomeStatement   \n", "\u001b[1;36m8\u001b[0m         \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                          aapl:MacMember  IncomeStatement   \n", "\u001b[1;36m9\u001b[0m         \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                          aapl:MacMember  IncomeStatement   \n", "\u001b[1;36m10\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                         aapl:IPadMember  IncomeStatement   \n", "\u001b[1;36m11\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                         aapl:IPadMember  IncomeStatement   \n", "\u001b[1;36m12\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m  aapl:WearablesHomeandAccessoriesMember  IncomeStatement   \n", "\u001b[1;36m13\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m  aapl:WearablesHomeandAccessoriesMember  IncomeStatement   \n", "\u001b[1;36m14\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m15\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m16\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m17\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m18\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m19\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m20\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m21\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m22\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\u001b[1;36m23\u001b[0m        \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m                                     NaN  IncomeStatement   \n", "\n", "                                       statement_role  \\\n", "\u001b[1;36m0\u001b[0m   \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m1\u001b[0m   \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m2\u001b[0m   \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m3\u001b[0m   \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m4\u001b[0m   \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m5\u001b[0m   \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m6\u001b[0m   \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m7\u001b[0m   \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m8\u001b[0m   \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m9\u001b[0m   \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m10\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m11\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m12\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m13\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m14\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m15\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m16\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m17\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m18\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m19\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m20\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m21\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m22\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\u001b[1;36m23\u001b[0m  \u001b[4;94mhttp://www.apple.com/role/CONDENSEDCONSOLIDATE...\u001b[0m   \n", "\n", "   dim_us-gaap_StatementBusinessSegmentsAxis  \n", "\u001b[1;36m0\u001b[0m                                        NaN  \n", "\u001b[1;36m1\u001b[0m                                        NaN  \n", "\u001b[1;36m2\u001b[0m                                        NaN  \n", "\u001b[1;36m3\u001b[0m                                        NaN  \n", "\u001b[1;36m4\u001b[0m                                        NaN  \n", "\u001b[1;36m5\u001b[0m                                        NaN  \n", "\u001b[1;36m6\u001b[0m                                        NaN  \n", "\u001b[1;36m7\u001b[0m                                        NaN  \n", "\u001b[1;36m8\u001b[0m                                        NaN  \n", "\u001b[1;36m9\u001b[0m                                        NaN  \n", "\u001b[1;36m10\u001b[0m                                       NaN  \n", "\u001b[1;36m11\u001b[0m                                       NaN  \n", "\u001b[1;36m12\u001b[0m                                       NaN  \n", "\u001b[1;36m13\u001b[0m                                       NaN  \n", "\u001b[1;36m14\u001b[0m                aapl:AmericasSegmentMember  \n", "\u001b[1;36m15\u001b[0m                aapl:AmericasSegmentMember  \n", "\u001b[1;36m16\u001b[0m                  aapl:EuropeSegmentMember  \n", "\u001b[1;36m17\u001b[0m                  aapl:EuropeSegmentMember  \n", "\u001b[1;36m18\u001b[0m            aapl:GreaterChinaSegmentMember  \n", "\u001b[1;36m19\u001b[0m            aapl:GreaterChinaSegmentMember  \n", "\u001b[1;36m20\u001b[0m                   aapl:JapanSegmentMember  \n", "\u001b[1;36m21\u001b[0m                   aapl:JapanSegmentMember  \n", "\u001b[1;36m22\u001b[0m       aapl:RestOfAsiaPacificSegmentMember  \n", "\u001b[1;36m23\u001b[0m       aapl:RestOfAsiaPacificSegmentMember  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>value</th>\n", "      <th>numeric_value</th>\n", "      <th>period_start</th>\n", "      <th>period_end</th>\n", "      <th>unit_ref</th>\n", "      <th>decimals</th>\n", "      <th>footnotes</th>\n", "      <th>dim_srt_ProductOrServiceAxis</th>\n", "      <th>statement_type</th>\n", "      <th>statement_role</th>\n", "      <th>dim_us-gaap_StatementBusinessSegmentsAxis</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>***********</td>\n", "      <td>9.796000e+10</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>us-gaap:ProductMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>***********</td>\n", "      <td>9.645800e+10</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>us-gaap:ProductMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>26340000000</td>\n", "      <td>2.634000e+10</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>us-gaap:ServiceMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>23117000000</td>\n", "      <td>2.311700e+10</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>us-gaap:ServiceMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>124300000000</td>\n", "      <td>1.243000e+11</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>119575000000</td>\n", "      <td>1.195750e+11</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>69138000000</td>\n", "      <td>6.913800e+10</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>aapl:IPhoneMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>69702000000</td>\n", "      <td>6.970200e+10</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>aapl:IPhoneMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>8987000000</td>\n", "      <td>8.987000e+09</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>aapl:MacMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>7780000000</td>\n", "      <td>7.780000e+09</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>aapl:MacMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>8088000000</td>\n", "      <td>8.088000e+09</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>aapl:IPadMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>7023000000</td>\n", "      <td>7.023000e+09</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>aapl:IPadMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>11747000000</td>\n", "      <td>1.174700e+10</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>aapl:WearablesHomeandAccessoriesMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>11953000000</td>\n", "      <td>1.195300e+10</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>aapl:WearablesHomeandAccessoriesMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>52648000000</td>\n", "      <td>5.264800e+10</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>aapl:AmericasSegmentMember</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>50430000000</td>\n", "      <td>5.043000e+10</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>aapl:AmericasSegmentMember</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>33861000000</td>\n", "      <td>3.386100e+10</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>aapl:EuropeSegmentMember</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>30397000000</td>\n", "      <td>3.039700e+10</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>aapl:EuropeSegmentMember</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>18513000000</td>\n", "      <td>1.851300e+10</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>aapl:GreaterChinaSegmentMember</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>20819000000</td>\n", "      <td>2.081900e+10</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>aapl:GreaterChinaSegmentMember</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>8987000000</td>\n", "      <td>8.987000e+09</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>aapl:JapanSegmentMember</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>7767000000</td>\n", "      <td>7.767000e+09</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>aapl:JapanSegmentMember</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>10291000000</td>\n", "      <td>1.029100e+10</td>\n", "      <td>2024-09-29</td>\n", "      <td>2024-12-28</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>aapl:RestOfAsiaPacificSegmentMember</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Net sales</td>\n", "      <td>10162000000</td>\n", "      <td>1.016200e+10</td>\n", "      <td>2023-10-01</td>\n", "      <td>2023-12-30</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.apple.com/role/CONDENSEDCONSOLIDATE...</td>\n", "      <td>aapl:RestOfAsiaPacificSegmentMember</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "execution_count": 7}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-21T00:00:15.169627Z", "start_time": "2025-03-21T00:00:15.168395Z"}}, "cell_type": "code", "source": "", "id": "faca4bf8ffc6f3e8", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "7b7c01ad13137d00"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}