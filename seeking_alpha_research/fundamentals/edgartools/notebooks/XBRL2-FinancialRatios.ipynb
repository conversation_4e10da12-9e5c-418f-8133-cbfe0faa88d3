{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "# Financial Ratios", "id": "e51377b1e8b1a058"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:52:54.309401Z", "start_time": "2025-04-12T22:52:54.306057Z"}}, "cell_type": "code", "source": ["from edgar import *\n", "from edgar.xbrl import *"], "id": "8c4fcd8128e3a324", "outputs": [], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:52:55.961061Z", "start_time": "2025-04-12T22:52:54.599524Z"}}, "cell_type": "code", "source": ["c = Company(\"AAPL\")\n", "filing = c.latest(\"10-K\")\n", "xbrl = XBRL.from_filing(filing)"], "id": "b3d768389ece1763", "outputs": [], "execution_count": 2}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["cash_flow = xbrl.statements.cashflow_statement()\n", "cash_flow"], "id": "c348fb8cfe44dce5"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["rendered = cash_flow.render()\n", "rendered.to_dataframe()"], "id": "e5786db82adeeed2"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:53:00.486619Z", "start_time": "2025-04-12T22:53:00.457699Z"}}, "cell_type": "code", "source": "cash_flow.to_dataframe()", "id": "6eab554004e798e", "outputs": [{"data": {"text/plain": ["                                              concept  \\\n", "0   us-gaap_CashCashEquivalentsRestrictedCashAndRe...   \n", "1   us-gaap_NetCashProvidedByUsedInOperatingActivi...   \n", "2                               us-gaap_NetIncomeLoss   \n", "3   us-gaap_AdjustmentsToReconcileNetIncomeLossToC...   \n", "4        us-gaap_DepreciationDepletionAndAmortization   \n", "5                      us-gaap_ShareBasedCompensation   \n", "6                   us-gaap_OtherNoncashIncomeExpense   \n", "7   us-gaap_IncreaseDecreaseInOperatingCapitalAbst...   \n", "8        us-gaap_IncreaseDecreaseInAccountsReceivable   \n", "9          us-gaap_IncreaseDecreaseInOtherReceivables   \n", "10              us-gaap_IncreaseDecreaseInInventories   \n", "11     us-gaap_IncreaseDecreaseInOtherOperatingAssets   \n", "12          us-gaap_IncreaseDecreaseInAccountsPayable   \n", "13  us-gaap_IncreaseDecreaseInOtherOperatingLiabil...   \n", "14  us-gaap_NetCashProvidedByUsedInOperatingActivi...   \n", "15  us-gaap_NetCashProvidedByUsedInInvestingActivi...   \n", "16  us-gaap_PaymentsToAcquireAvailableForSaleSecur...   \n", "17  us-gaap_ProceedsFromMaturitiesPrepaymentsAndCa...   \n", "18  us-gaap_ProceedsFromSaleOfAvailableForSaleSecu...   \n", "19  us-gaap_PaymentsToAcquirePropertyPlantAndEquip...   \n", "20  us-gaap_PaymentsForProceedsFromOtherInvestingA...   \n", "21  us-gaap_NetCashProvidedByUsedInInvestingActivi...   \n", "22  us-gaap_NetCashProvidedByUsedInFinancingActivi...   \n", "23  us-gaap_PaymentsRelatedToTaxWithholdingForShar...   \n", "24                        us-gaap_PaymentsOfDividends   \n", "25         us-gaap_PaymentsForRepurchaseOfCommonStock   \n", "26         us-gaap_ProceedsFromIssuanceOfLongTermDebt   \n", "27                   us-gaap_RepaymentsOfLongTermDebt   \n", "28    us-gaap_ProceedsFromRepaymentsOfCommercialPaper   \n", "29  us-gaap_ProceedsFromPaymentsForOtherFinancingA...   \n", "30  us-gaap_NetCashProvidedByUsedInFinancingActivi...   \n", "31  us-gaap_CashCashEquivalentsRestrictedCashAndRe...   \n", "32  us-gaap_CashCashEquivalentsRestrictedCashAndRe...   \n", "33    us-gaap_SupplementalCashFlowInformationAbstract   \n", "34                         us-gaap_IncomeTaxesPaidNet   \n", "\n", "                                                label      2024-09-28  \\\n", "0   Cash, cash equivalents, and restricted cash an...                   \n", "1                               Operating activities:                   \n", "2                                          Net Income   ***********.0   \n", "3                                          Net Income                   \n", "4                       Depreciation and amortization   ***********.0   \n", "5                    Share-based compensation expense   1**********.0   \n", "6                                               Other   -**********.0   \n", "7        Changes in operating assets and liabilities:                   \n", "8                            Accounts receivable, net   -**********.0   \n", "9                        Vendor non-trade receivables   -**********.0   \n", "10                                        Inventories   -**********.0   \n", "11               Other current and non-current assets  -***********.0   \n", "12                                   Accounts Payable    **********.0   \n", "13          Other current and non-current liabilities   ***********.0   \n", "14                 Net Cash from Operating Activities  ************.0   \n", "15                              Investing activities:                   \n", "16                 Purchases of marketable securities  -***********.0   \n", "17  Proceeds from Maturities, Prepayments and Call...   ***********.0   \n", "18            Proceeds from Sale of Debt Securities,    ***********.0   \n", "19         Payments for Property, Plant and Equipment   -**********.0   \n", "20                                              Other   -**********.0   \n", "21                 Net Cash from Investing Activities    **********.0   \n", "22                              Financing activities:                   \n", "23       Tax Withholding for Share-Based Compensation   -**********.0   \n", "24                              Payments of Dividends  -***********.0   \n", "25                        Repurchases of common stock  -94949000000.0   \n", "26           Proceeds from Issuance of Long-Term Debt             0.0   \n", "27                            Repayments of term debt   -9958000000.0   \n", "28     Proceeds from (Repayments of) Commercial Paper    3960000000.0   \n", "29                                              Other    -*********.0   \n", "30                 Net Cash from Financing Activities -121983000000.0   \n", "31                                 Net Change in Cash    -*********.0   \n", "32  Cash, cash equivalents, and restricted cash an...                   \n", "33                 Supplemental cash flow disclosure:                   \n", "34                                 Income Tax Expense   26102000000.0   \n", "\n", "        2023-09-30      2022-09-24  level  abstract  dimension  \n", "0                                       1     False      False  \n", "1                                       1     False      False  \n", "2    96995000000.0   99803000000.0      2     False      False  \n", "3                                       2     False      False  \n", "4    11519000000.0   11104000000.0      3     False      False  \n", "5    10833000000.0    9038000000.0      3     False      False  \n", "6    -2227000000.0    **********.0      3     False      False  \n", "7                                       3     False      False  \n", "8    -**********.0   -**********.0      4     False      False  \n", "9     **********.0   -**********.0      4     False      False  \n", "10   -**********.0    **********.0      4     False      False  \n", "11   -**********.0   -**********.0      4     False      False  \n", "12   -**********.0    **********.0      4     False      False  \n", "13    **********.0    **********.0      4     False      False  \n", "14  ************.0  ************.0      2     False      False  \n", "15                                      1     False      False  \n", "16  -29513000000.0  -76923000000.0      2     False      False  \n", "17   39686000000.0   29917000000.0      2     False      False  \n", "18    5828000000.0   37446000000.0      2     False      False  \n", "19  -10959000000.0  -10708000000.0      2     False      False  \n", "20   -1337000000.0   -2086000000.0      2     False      False  \n", "21    3705000000.0  -22354000000.0      2     False      False  \n", "22                                      1     False      False  \n", "23   -5431000000.0   -6223000000.0      2     False      False  \n", "24  -15025000000.0  -14841000000.0      2     False      False  \n", "25  -77550000000.0  -89402000000.0      2     False      False  \n", "26    5228000000.0    5465000000.0      2     False      False  \n", "27  -11151000000.0   -9543000000.0      2     False      False  \n", "28   -3978000000.0    3955000000.0      2     False      False  \n", "29    -581000000.0    -160000000.0      2     False      False  \n", "30 -108488000000.0 -110749000000.0      2     False      False  \n", "31    5760000000.0  -10952000000.0      1     False      False  \n", "32                                      1     False      False  \n", "33                                      1     False      False  \n", "34   18679000000.0   19573000000.0      2     False      False  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>2024-09-28</th>\n", "      <th>2023-09-30</th>\n", "      <th>2022-09-24</th>\n", "      <th>level</th>\n", "      <th>abstract</th>\n", "      <th>dimension</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap_CashCashEquivalentsRestrictedCashAndRe...</td>\n", "      <td>Cash, cash equivalents, and restricted cash an...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInOperatingActivi...</td>\n", "      <td>Operating activities:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap_NetIncomeLoss</td>\n", "      <td>Net Income</td>\n", "      <td>***********.0</td>\n", "      <td>96995000000.0</td>\n", "      <td>99803000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap_AdjustmentsToReconcileNetIncomeLossToC...</td>\n", "      <td>Net Income</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap_DepreciationDepletionAndAmortization</td>\n", "      <td>Depreciation and amortization</td>\n", "      <td>***********.0</td>\n", "      <td>11519000000.0</td>\n", "      <td>11104000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap_ShareBasedCompensation</td>\n", "      <td>Share-based compensation expense</td>\n", "      <td>1**********.0</td>\n", "      <td>10833000000.0</td>\n", "      <td>9038000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap_OtherNoncashIncomeExpense</td>\n", "      <td>Other</td>\n", "      <td>-**********.0</td>\n", "      <td>-2227000000.0</td>\n", "      <td>**********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap_IncreaseDecreaseInOperatingCapitalAbst...</td>\n", "      <td>Changes in operating assets and liabilities:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap_IncreaseDecreaseInAccountsReceivable</td>\n", "      <td>Accounts receivable, net</td>\n", "      <td>-**********.0</td>\n", "      <td>-**********.0</td>\n", "      <td>-**********.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap_IncreaseDecreaseInOtherReceivables</td>\n", "      <td>Vendor non-trade receivables</td>\n", "      <td>-**********.0</td>\n", "      <td>**********.0</td>\n", "      <td>-**********.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>us-gaap_IncreaseDecreaseInInventories</td>\n", "      <td>Inventories</td>\n", "      <td>-**********.0</td>\n", "      <td>-**********.0</td>\n", "      <td>**********.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>us-gaap_IncreaseDecreaseInOtherOperatingAssets</td>\n", "      <td>Other current and non-current assets</td>\n", "      <td>-***********.0</td>\n", "      <td>-**********.0</td>\n", "      <td>-**********.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>us-gaap_IncreaseDecreaseInAccountsPayable</td>\n", "      <td>Accounts Payable</td>\n", "      <td>**********.0</td>\n", "      <td>-**********.0</td>\n", "      <td>**********.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>us-gaap_IncreaseDecreaseInOtherOperatingLiabil...</td>\n", "      <td>Other current and non-current liabilities</td>\n", "      <td>***********.0</td>\n", "      <td>**********.0</td>\n", "      <td>**********.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInOperatingActivi...</td>\n", "      <td>Net Cash from Operating Activities</td>\n", "      <td>************.0</td>\n", "      <td>************.0</td>\n", "      <td>************.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInInvestingActivi...</td>\n", "      <td>Investing activities:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>us-gaap_PaymentsToAcquireAvailableForSaleSecur...</td>\n", "      <td>Purchases of marketable securities</td>\n", "      <td>-***********.0</td>\n", "      <td>-29513000000.0</td>\n", "      <td>-76923000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>us-gaap_ProceedsFromMaturitiesPrepaymentsAndCa...</td>\n", "      <td>Proceeds from Maturities, Prepayments and Call...</td>\n", "      <td>***********.0</td>\n", "      <td>39686000000.0</td>\n", "      <td>29917000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>us-gaap_ProceedsFromSaleOfAvailableForSaleSecu...</td>\n", "      <td>Proceeds from Sale of Debt Securities,</td>\n", "      <td>***********.0</td>\n", "      <td>5828000000.0</td>\n", "      <td>37446000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>us-gaap_PaymentsToAcquirePropertyPlantAndEquip...</td>\n", "      <td>Payments for Property, Plant and Equipment</td>\n", "      <td>-**********.0</td>\n", "      <td>-10959000000.0</td>\n", "      <td>-10708000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>us-gaap_PaymentsForProceedsFromOtherInvestingA...</td>\n", "      <td>Other</td>\n", "      <td>-**********.0</td>\n", "      <td>-1337000000.0</td>\n", "      <td>-2086000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInInvestingActivi...</td>\n", "      <td>Net Cash from Investing Activities</td>\n", "      <td>**********.0</td>\n", "      <td>3705000000.0</td>\n", "      <td>-22354000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInFinancingActivi...</td>\n", "      <td>Financing activities:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>us-gaap_PaymentsRelatedToTaxWithholdingForShar...</td>\n", "      <td>Tax Withholding for Share-Based Compensation</td>\n", "      <td>-**********.0</td>\n", "      <td>-5431000000.0</td>\n", "      <td>-6223000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>us-gaap_PaymentsOfDividends</td>\n", "      <td>Payments of Dividends</td>\n", "      <td>-***********.0</td>\n", "      <td>-15025000000.0</td>\n", "      <td>-14841000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>us-gaap_PaymentsForRepurchaseOfCommonStock</td>\n", "      <td>Repurchases of common stock</td>\n", "      <td>-94949000000.0</td>\n", "      <td>-77550000000.0</td>\n", "      <td>-89402000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>us-gaap_ProceedsFromIssuanceOfLongTermDebt</td>\n", "      <td>Proceeds from Issuance of Long-Term Debt</td>\n", "      <td>0.0</td>\n", "      <td>5228000000.0</td>\n", "      <td>5465000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>us-gaap_RepaymentsOfLongTermDebt</td>\n", "      <td>Repayments of term debt</td>\n", "      <td>-9958000000.0</td>\n", "      <td>-11151000000.0</td>\n", "      <td>-9543000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>us-gaap_ProceedsFromRepaymentsOfCommercialPaper</td>\n", "      <td>Proceeds from (Repayments of) Commercial Paper</td>\n", "      <td>3960000000.0</td>\n", "      <td>-3978000000.0</td>\n", "      <td>3955000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>us-gaap_ProceedsFromPaymentsForOtherFinancingA...</td>\n", "      <td>Other</td>\n", "      <td>-*********.0</td>\n", "      <td>-581000000.0</td>\n", "      <td>-160000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInFinancingActivi...</td>\n", "      <td>Net Cash from Financing Activities</td>\n", "      <td>-121983000000.0</td>\n", "      <td>-108488000000.0</td>\n", "      <td>-110749000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>us-gaap_CashCashEquivalentsRestrictedCashAndRe...</td>\n", "      <td>Net Change in Cash</td>\n", "      <td>-*********.0</td>\n", "      <td>5760000000.0</td>\n", "      <td>-10952000000.0</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>us-gaap_CashCashEquivalentsRestrictedCashAndRe...</td>\n", "      <td>Cash, cash equivalents, and restricted cash an...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>us-gaap_SupplementalCashFlowInformationAbstract</td>\n", "      <td>Supplemental cash flow disclosure:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>us-gaap_IncomeTaxesPaidNet</td>\n", "      <td>Income Tax Expense</td>\n", "      <td>26102000000.0</td>\n", "      <td>18679000000.0</td>\n", "      <td>19573000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "execution_count": 5}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}