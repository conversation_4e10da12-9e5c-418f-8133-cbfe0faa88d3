{"cells": [{"cell_type": "markdown", "id": "cf7ecb7d-7ebf-46a1-af0b-cab7f5769a1b", "metadata": {}, "source": ["# Extract Earnings Releases\n", "\n", "This notebook shows how to get the earnings release from an **8-K** filing"]}, {"cell_type": "markdown", "id": "7c908011-f45a-4cd0-8629-ca0d32a18369", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/Extract-Earnings-Releases.ipynb)"]}, {"cell_type": "code", "execution_count": null, "id": "c0fc526c-4245-48f9-9290-f0a456ec2ef5", "metadata": {}, "outputs": [], "source": ["!pip install edgartools"]}, {"cell_type": "code", "execution_count": 1, "id": "fe0d57f7-5868-4d13-aee3-a95fcf4b95d9", "metadata": {}, "outputs": [], "source": ["from edgar import *\n", "from edgar.files.html import HtmlDocument\n", "\n", "set_identity(\"<EMAIL>\")"]}, {"cell_type": "markdown", "id": "373ab2dc-0c4e-4fb0-8ec0-e898dcc24ec2", "metadata": {}, "source": ["## Find a Filing"]}, {"cell_type": "code", "execution_count": 2, "id": "2b9b32f4-b025-4eeb-b8eb-0442c38df1d2", "metadata": {}, "outputs": [], "source": ["filing = Filing(company='Apple Inc.', cik=320193, form='8-K', filing_date='2024-08-01', accession_no='0000320193-24-000080')"]}, {"cell_type": "markdown", "id": "b0becf04-c791-419c-9f3f-90e561c1fbae", "metadata": {}, "source": ["## Attachments\n", "\n", "Get the filing attachments"]}, {"cell_type": "code", "execution_count": 3, "id": "738f6541-072b-4d03-8386-342577f6c148", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3m                                        Documents                                         \u001b[0m                      │\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mSeq\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDocument                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDescription                  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mType   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSize    \u001b[0m\u001b[1m \u001b[0m                       │\n", "│  ────────────────────────────────────────────────────────────────────────────────────────                       │\n", "│   1     aapl-20240801.htm             8-K                             8-K       40.2 KB                         │\n", "│  \u001b[1m \u001b[0m\u001b[1m2  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1ma8-kex991q3202406292024.htm\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mEX-99.1                      \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mEX-99.1\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m164.5 KB\u001b[0m\u001b[1m \u001b[0m                       │\n", "│   7     aapl-20240801_g1.jpg                                          GRAPHIC   1.2 KB                          │\n", "│  \u001b[1m \u001b[0m\u001b[1m   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0000320193-24-000080.txt   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mComplete submission text file\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m       \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m427.5 KB\u001b[0m\u001b[1m \u001b[0m                       │\n", "│                                                                                                                 │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3m                                                  Data Files                                                   \u001b[0m │\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mSeq\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDocument             \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDescription                                           \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mType      \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSize   \u001b[0m\u001b[1m \u001b[0m  │\n", "│  ─────────────────────────────────────────────────────────────────────────────────────────────────────────────  │\n", "│   3     aapl-20240801.xsd       XBRL TAXONOMY EXTENSION SCHEMA DOCUMENT                  EX-101.SCH   4.1 KB    │\n", "│  \u001b[1m \u001b[0m\u001b[1m4  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1maapl-20240801_def.xml\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mXBRL TAXONOMY EXTENSION DEFINITION LINKBASE DOCUMENT  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mEX-101.DEF\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m18.6 KB\u001b[0m\u001b[1m \u001b[0m  │\n", "│   5     aapl-20240801_lab.xml   XBRL TAXONOMY EXTENSION LABEL LINKBASE DOCUMENT          EX-101.LAB   35.5 KB   │\n", "│  \u001b[1m \u001b[0m\u001b[1m6  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1maapl-20240801_pre.xml\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mXBRL TAXONOMY EXTENSION PRESENTATION LINKBASE DOCUMENT\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mEX-101.PRE\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m19.7 KB\u001b[0m\u001b[1m \u001b[0m  │\n", "│   18    aapl-20240801_htm.xml   EXTRACTED XBRL INSTANCE DOCUMENT                         XML          9.0 KB    │\n", "│                                                                                                                 │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["attachments = filing.attachments\n", "attachments"]}, {"cell_type": "markdown", "id": "45493d9e-bc8f-40d9-958f-03cabaa92ae6", "metadata": {}, "source": ["### Get the attachments that are exhibits\n", "Some filings are actually exhibits. Call `exhibits` to get just the exhibits."]}, {"cell_type": "code", "execution_count": 4, "id": "6ce75415-f8ba-4259-85c1-ebdda82361e8", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3m                               Documents                                \u001b[0m                                        │\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mSeq\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDocument                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDescription\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mType   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSize    \u001b[0m\u001b[1m \u001b[0m                                         │\n", "│  ──────────────────────────────────────────────────────────────────────                                         │\n", "│   1     aapl-20240801.htm             8-K           8-K       40.2 KB                                           │\n", "│  \u001b[1m \u001b[0m\u001b[1m2  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1ma8-kex991q3202406292024.htm\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mEX-99.1    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mEX-99.1\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m164.5 KB\u001b[0m\u001b[1m \u001b[0m                                         │\n", "│                                                                                                                 │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["exhibits = attachments.exhibits\n", "exhibits"]}, {"cell_type": "markdown", "id": "943c2aaf-994e-426e-87e8-8edcf70e2141", "metadata": {}, "source": ["## Select an exhibit by index"]}, {"cell_type": "code", "execution_count": 10, "id": "f70fa521-b80d-44a2-9913-8f179679fc7d", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭─────────────────────────────┬─────────────┬─────────┬──────────╮\n", "│\u001b[1m \u001b[0m\u001b[1mDocument                   \u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mDescription\u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mType   \u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mSize    \u001b[0m\u001b[1m \u001b[0m│\n", "├─────────────────────────────┼─────────────┼─────────┼──────────┤\n", "│ a8-kex991q3202406292024.htm │ EX-99.1     │ EX-99.1 │ 164.5 KB │\n", "╰─────────────────────────────┴─────────────┴─────────┴──────────╯"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["exhibits[1]"]}, {"cell_type": "markdown", "id": "2bca1b22-c010-4b6b-bad1-c7f427dc48de", "metadata": {}, "source": ["## Query for just exhibit EX-99.1\n", "Alternatively, you can use the query function to make sure you are selecting only EX-99.1. Not all filings have this exhibit\n", "\n", "```python\n", "results = exhibits.query(\"document_type in ['EX-99.1', 'EX-99', 'EX-99.01']\")\n", "if len(results) > 0:\n", "    print(results[0])\n", "```"]}, {"cell_type": "markdown", "id": "8f6a8f4b-e23b-4b64-b6d6-d1bef32ebe39", "metadata": {}, "source": ["## Download the html content of the exhibit"]}, {"cell_type": "code", "execution_count": 6, "id": "eae61758-1e12-4fa6-94f9-64bdc7d08065", "metadata": {}, "outputs": [], "source": ["html = exhibits[1].download()"]}, {"cell_type": "markdown", "id": "cb00a7b9-be21-46d9-b7c4-516184400277", "metadata": {}, "source": ["## Extract the text of the exhibit"]}, {"cell_type": "code", "execution_count": 7, "id": "3ed46b96-64a3-4d97-a09c-685d51b8c3bd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document\n", "\n", "Exhibit 99.1\n", "Apple reports third quarter results\n", "June quarter records for Revenue and EPS\n", "Services revenue reaches new all-time high\n", "CUPERTINO, CALIFORNIA — Apple® today announced financial results for its fiscal 2024 third quarter ended June 29, 2024. The Company posted quarterly revenue of $85.8 billion, up 5 percent year over year, and quarterly earnings per diluted share of $1.40, up 11 percent year over year.\n", "“Today Apple is reporting a new June quarter revenue record of $85.8 billion, up 5 percent from a year ago,” said <PERSON>, Apple’s CEO. “During the quarter, we were excited to announce incredible updates to our software platforms at our Worldwide Developers Conference, including Apple Intelligence, a breakthrough personal intelligence system that puts powerful, private generative AI models at the core of iPhone, iPad, and Mac. We very much look forward to sharing these tools with our users, and we continue to invest significantly in the innovations that will enrich our customers’ lives, while leading with the values that drive our work.”\n", "“During the quarter, our record business performance generated EPS growth of 11 percent and nearly $29 billion in operating cash flow, allowing us to return over $32 billion to shareholders,” said <PERSON>, Apple’s CFO. “We are also very pleased that our installed base of active devices reached a new all-time high in all geographic segments, thanks to very high levels of customer satisfaction and loyalty.”\n", "Apple’s board of directors has declared a cash dividend of $0.25 per share of the Company’s common stock. The dividend is payable on August 15, 2024 to shareholders of record as of the close of business on August 12, 2024.\n", "Apple will provide live streaming of its Q3 2024 financial results conference call beginning at 2:00 p.m. PT on August 1, 2024 at apple.com/investor/earnings-call. The webcast will be available for replay for approximately two weeks thereafter.\n", "Apple periodically provides information for investors on its corporate website, apple.com, and its investor relations website, investor.apple.com. This includes press releases and other information about financial performance, reports filed or furnished with the SEC, information on corporate governance, and details related to its annual meeting of shareholders.\n", "This press release contains forward-looking statements, within the meaning of the Private Securities Litigation Reform Act of 1995. These forward-looking statements include without limitation those about payment of the Company’s quarterly dividend and future business plans. These statements involve risks and uncertainties, and actual results may differ materially from any future results expressed or implied by the forward-looking statements. Risks and uncertainties include without limitation: effects of global and regional economic conditions, including as a result of government policies, war, terrorism, natural disasters, and public health issues; risks relating to the design, manufacture, introduction, and transition of products and services in highly competitive and rapidly changing markets, including from reliance on third parties for components, technology, manufacturing, applications, and content; risks relating to information technology system failures, network disruptions, and failure to protect, loss of, or unauthorized access to, or release of, data; and effects of unfavorable legal proceedings, government investigations, and complex and changing laws and regulations. More information on these risks and other potential factors that could affect the Company’s business, reputation, results of operations, financial condition, and stock price is included in the Company’s filings with the SEC, including in the “Risk Factors” and “Management’s Discussion and Analysis of Financial Condition and Results of Operations” sections of the Company’s most recently filed periodic reports on Form 10-K and Form 10-Q and subsequent filings. The Company assumes no obligation to update any forward-looking statements, which speak only as of the date they are made.\n", "Apple revolutionized personal technology with the introduction of the Macintosh in 1984. Today, Apple leads the world in innovation with iPhone, iPad, Mac, AirPods, Apple Watch, and Apple Vision Pro. Apple’s six software platforms — iOS, iPadOS, macOS, watchOS, visionOS, and tvOS — provide seamless experiences across all Apple devices and empower people with breakthrough services including the App Store, Apple Music, Apple Pay, iCloud, and Apple TV+. Apple’s more than 150,000 employees are dedicated to making the best products on earth and to leaving the world better than we found it.\n", "Press Contact:\n", "<PERSON>\n", "Apple\n", "<EMAIL>\n", "(*************\n", "Investor Relations Contact:\n", "<PERSON><PERSON><PERSON>\n", "Apple\n", "<PERSON><PERSON><PERSON>@apple.com\n", "(*************\n", "NOTE TO EDITORS: For additional information visit Apple Newsroom (www.apple.com/newsroom), or email Apple’s Media <NAME_EMAIL>.\n", "© 2024 Apple Inc. All rights reserved. Apple and the Apple logo are trademarks of Apple. Other company and product names may be trademarks of their respective owners.\n", "Apple Inc.\n", "CONDENSED CONSOLIDATED STATEMENTS OF OPERATIONS (Unaudited)\n", "(In millions, except number of shares, which are reflected in thousands, and per-share amounts)\n", "\n", " | Three Months Ended | | Nine Months Ended\n", " | June 29,2024 | | July 1,2023 | | June 29,2024 | | July 1,2023\n", "Net sales: | | | | | | | \n", "Products | $ | 61,564 | | | $ | 60,584 | | $ | 224,908 | $ | 230,901\n", "Services | 24,213 | | | 21,213 | | | 71,197 | | 62,886 \n", "Total net sales (1) | 85,777 | | | 81,797 | | | 296,105 | | 293,787 \n", "Cost of sales: | | | | | | | \n", "Products | 39,803 | | | 39,136 | | | 140,667 | | 146,696 \n", "Services | 6,296 | | | 6,248 | | | 18,634 | | 18,370 \n", "Total cost of sales | 46,099 | | | 45,384 | | | 159,301 | | 165,066 \n", "Gross margin | 39,678 | | | 36,413 | | | 136,804 | | 128,721 \n", "Operating expenses: | | | | | | | \n", "Research and development | 8,006 | | | 7,442 | | | 23,605 | | 22,608 \n", "Selling, general and administrative | 6,320 | | | 5,973 | | | 19,574 | | 18,781 \n", "Total operating expenses | 14,326 | | | 13,415 | | | 43,179 | | 41,389 \n", "Operating income | 25,352 | | | 22,998 | | | 93,625 | | 87,332 \n", "Other income/(expense), net | 142 | | | (265) | | | 250 | | (594) \n", "Income before provision for income taxes | 25,494 | | | 22,733 | | | 93,875 | | 86,738 \n", "Provision for income taxes | 4,046 | | | 2,852 | | | 14,875 | | 12,699 \n", "Net income | $ | 21,448 | | | $ | 19,881 | | $ | 79,000 | $ | 74,039 \n", "Earnings per share: | | | | | | | \n", "Basic | $ | 1.40 | | | $ | 1.27 | | $ | 5.13 | $ | 4.69 \n", "Diluted | $ | 1.40 | | | $ | 1.26 | | $ | 5.11 | $ | 4.67 \n", "Shares used in computing earnings per share: | | | | | | | \n", "Basic | 15,287,521 | | | 15,697,614 | | | 15,401,047 | | 15,792,497\n", "Diluted | 15,348,175 | | | 15,775,021 | | | 15,463,175 | | 15,859,263\n", "(1) Net sales by reportable segment: | | | | | | | \n", "Americas | $ | 37,678 | | | $ | 35,383 | | $ | 125,381 | $ | 122,445\n", "Europe | 21,884 | | | 20,205 | | | 76,404 | | 71,831 \n", "Greater China | 14,728 | | | 15,758 | | | 51,919 | | 57,475 \n", "Japan | 5,097 | | | 4,821 | | | 19,126 | | 18,752 \n", "Rest of Asia Pacific | 6,390 | | | 5,630 | | | 23,275 | | 23,284 \n", "Total net sales | $ | 85,777 | | | $ | 81,797 | | $ | 296,105 | $ | 293,787\n", "(1) Net sales by category: | | | | | | | \n", "iPhone | $ | 39,296 | | | $ | 39,669 | | $ | 154,961 | $ | 156,778\n", "Mac | 7,009 | | | 6,840 | | | 22,240 | | 21,743 \n", "iPad | 7,162 | | | 5,791 | | | 19,744 | | 21,857 \n", "Wearables, Home and Accessories | 8,097 | | | 8,284 | | | 27,963 | | 30,523 \n", "Services | 24,213 | | | 21,213 | | | 71,197 | | 62,886 \n", "Total net sales | $ | 85,777 | | | $ | 81,797 | | $ | 296,105 | $ | 293,787\n", "\n", "Apple Inc.\n", "CONDENSED CONSOLIDATED BALANCE SHEETS (Unaudited)\n", "(In millions, except number of shares, which are reflected in thousands, and par value)\n", "\n", " | June 29,2024 | | September 30,2023\n", "ASSETS: \n", "Current assets: | | | \n", "Cash and cash equivalents | $ | 25,565 | | | $ | 29,965 \n", "Marketable securities | 36,236 | | | 31,590 | \n", "Accounts receivable, net | 22,795 | | | 29,508 | \n", "Vendor non-trade receivables | 20,377 | | | 31,477 | \n", "Inventories | 6,165 | | | 6,331 | \n", "Other current assets | 14,297 | | | 14,695 | \n", "Total current assets | 125,435 | | | 143,566 | \n", "Non-current assets: | | | \n", "Marketable securities | 91,240 | | | 100,544 | \n", "Property, plant and equipment, net | 44,502 | | | 43,715 | \n", "Other non-current assets | 70,435 | | | 64,758 | \n", "Total non-current assets | 206,177 | | | 209,017 | \n", "Total assets | $ | 331,612 | | | $ | 352,583\n", "LIABILITIES AND SHAREH<PERSON>DERS’ EQUITY: \n", "Current liabilities: | | | \n", "Accounts payable | $ | 47,574 | | | $ | 62,611 \n", "Other current liabilities | 60,889 | | | 58,829 | \n", "Deferred revenue | 8,053 | | | 8,061 | \n", "Commercial paper | 2,994 | | | 5,985 | \n", "Term debt | 12,114 | | | 9,822 | \n", "Total current liabilities | 131,624 | | | 145,308 | \n", "Non-current liabilities: | | | \n", "Term debt | 86,196 | | | 95,281 | \n", "Other non-current liabilities | 47,084 | | | 49,848 | \n", "Total non-current liabilities | 133,280 | | | 145,129 | \n", "Total liabilities | 264,904 | | | 290,437 | \n", "Commitments and contingencies | | | \n", "Shareholders’ equity: | | | \n", "Common stock and additional paid-in capital, $0.00001 par value: 50,400,000 shares authorized; 15,222,259 and 15,550,061 shares issued and outstanding, respectively | 79,850 | | | 73,812 | \n", "Accumulated deficit | (4,726) | | | (214) | \n", "Accumulated other comprehensive loss | (8,416) | | | (11,452) | \n", "Total shareholders’ equity | 66,708 | | | 62,146 | \n", "Total liabilities and shareholders’ equity | $ | 331,612 | | | $ | 352,583\n", "\n", "Apple Inc.\n", "CONDENSED CONSOLIDATED STATEMENTS OF CASH FLOWS (Unaudited)\n", "(In millions)\n", "\n", " | Nine Months Ended\n", " | June 29,2024 | | July 1,2023\n", "Cash, cash equivalents and restricted cash, beginning balances | $ | 30,737 | | | $ | 24,977\n", "Operating activities: | | | \n", "Net income | 79,000 | | | 74,039 | \n", "Adjustments to reconcile net income to cash generated by operating activities: | | | \n", "Depreciation and amortization | 8,534 | | | 8,866 | \n", "Share-based compensation expense | 8,830 | | | 8,208 | \n", "Other | (1,964) | | | (1,651) | \n", "Changes in operating assets and liabilities: | | | \n", "Accounts receivable, net | 6,697 | | | 7,609 | \n", "Vendor non-trade receivables | 11,100 | | | 13,111 | \n", "Inventories | 41 | | | (2,570) | \n", "Other current and non-current assets | (5,626) | | | (4,863) | \n", "Accounts payable | (15,171) | | | (16,790) | \n", "Other current and non-current liabilities | 2 | | | 2,986 | \n", "Cash generated by operating activities | 91,443 | | | 88,945 | \n", "Investing activities: | | | \n", "Purchases of marketable securities | (38,074) | | | (20,956) | \n", "Proceeds from maturities of marketable securities | 39,838 | | | 27,857 | \n", "Proceeds from sales of marketable securities | 7,382 | | | 3,959 | \n", "Payments for acquisition of property, plant and equipment | (6,539) | | | (8,796) | \n", "Other | (1,117) | | | (753) | \n", "Cash generated by investing activities | 1,490 | | | 1,311 | \n", "Financing activities: | | | \n", "Payments for taxes related to net share settlement of equity awards | (5,163) | | | (5,119) | \n", "Payments for dividends and dividend equivalents | (11,430) | | | (11,267) | \n", "Repurchases of common stock | (69,866) | | | (56,547) | \n", "Proceeds from issuance of term debt, net | — | | | 5,228 | \n", "Repayments of term debt | (7,400) | | | (11,151) | \n", "Repayments of commercial paper, net | (2,985) | | | (5,971) | \n", "Other | (191) | | | (508) | \n", "Cash used in financing activities | (97,035) | | | (85,335) | \n", "Increase/(Decrease) in cash, cash equivalents and restricted cash | (4,102) | | | 4,921 | \n", "Cash, cash equivalents and restricted cash, ending balances | $ | 26,635 | | | $ | 29,898\n", "Supplemental cash flow disclosure: | | | \n", "Cash paid for income taxes, net | $ | 19,230 | | | $ | 7,020 \n", "\n", "\n"]}], "source": ["exhibit_text = HtmlDocument.from_html(exhibits[1].download()).text\n", "print(exhibit_text)"]}, {"cell_type": "code", "execution_count": 9, "id": "7e066bd2-8904-44cb-b3f7-db91a95b2e51", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3m                               Documents                                \u001b[0m                                        │\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1mSeq\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDocument                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDescription\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mType   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSize    \u001b[0m\u001b[1m \u001b[0m                                         │\n", "│  ──────────────────────────────────────────────────────────────────────                                         │\n", "│   2     a8-kex991q3202406292024.htm   EX-99.1       EX-99.1   164.5 KB                                          │\n", "│                                                                                                                 │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["exhibits.query(\"document_type in ['EX-99.1', 'EX-99', 'EX-99.01']\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}