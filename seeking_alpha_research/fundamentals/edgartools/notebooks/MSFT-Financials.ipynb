{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "# Microsoft Financials", "id": "c9832c2ee003a8be"}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-10T17:19:06.766722Z", "start_time": "2025-05-10T17:19:06.764324Z"}}, "cell_type": "code", "source": ["from edgar import *\n", "from edgar.xbrl import *\n", "from workbooks.WideTables import income_statement"], "id": "46c02f7d8c9ba1e9", "outputs": [], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-10T14:21:58.905398Z", "start_time": "2025-05-10T14:21:58.901367Z"}}, "cell_type": "code", "source": ["filing = find(\"000119312515272806\")\n", "filing"], "id": "3ef2ea1667c21345", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭─────────────── \u001b[1mForm 10-K \u001b[0m\u001b[1;32mMICROSOFT CORP\u001b[0m \u001b[2m[789019] \u001b[0m\u001b[1;33mMSFT\u001b[0m ────────────────╮\n", "│                                                                       │\n", "│  \u001b[2m \u001b[0m\u001b[2mAccession Number    \u001b[0m\u001b[2m \u001b[0m \u001b[2m \u001b[0m\u001b[2mFiling Date\u001b[0m\u001b[2m \u001b[0m \u001b[2m \u001b[0m\u001b[2mPeriod of Report\u001b[0m\u001b[2m \u001b[0m \u001b[2m \u001b[0m\u001b[2mDocuments\u001b[0m\u001b[2m \u001b[0m  │\n", "│  ───────────────────────────────────────────────────────────────────  │\n", "│   \u001b[1;38;5;39m0001193125-15-272806\u001b[0m   \u001b[1m2015-07-31 \u001b[0m   \u001b[1m2015-06-30      \u001b[0m   154         │\n", "│                                                                       │\n", "│                                                                       │\n", "│                                                                       │\n", "│                                                                       │\n", "╰─────────────────\u001b[2m Annual report for public companies \u001b[0m──────────────────╯"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-10T17:19:44.885254Z", "start_time": "2025-05-10T17:19:44.651712Z"}}, "cell_type": "code", "source": ["xb = filing.xbrl()\n", "income_statement = xb.statements.income_statement()\n", "income_statement"], "id": "d4ee8872c59ca375", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                  Consolidated Statement of Income (Standardized)                  \u001b[0m\n", "\u001b[3m                  \u001b[0m\u001b[1;3mYear Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except per share data)\u001b[0m\u001b[3m                  \u001b[0m\n", "                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m                                                 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mJun 30, 2015\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mJun 30, 2014\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────── \n", "        Revenue                                            $93,580        $86,833  \n", "        Cost of Revenue                                  $(33,038)      $(27,078)  \n", "        Gross Profit                                       $60,542        $59,755  \n", "        Research and Development Expense                 $(12,046)      $(11,381)  \n", "        Selling, General and Administrative Expense      $(15,713)      $(15,811)  \n", "        Selling, General and Administrative Expense       $(4,611)       $(4,677)  \n", "        Operating Income                                   $18,161        $27,759  \n", "        Nonoperating Income/Expense                           $346            $61  \n", "        Income before income taxes                         $18,507        $27,820  \n", "        Income Tax Expense                                  $6,314         $5,746  \n", "        Net Income                                         $12,193        $22,074  \n", "        Earnings per share:                                                        \n", "          Earnings Per Share                                  0.00           0.00  \n", "          Earnings Per Share (Diluted)                        0.00           0.00  \n", "        Weighted average shares outstanding:                                       \n", "          Shares Outstanding                                 8,177          8,299  \n", "          Shares Outstanding (Diluted)                       8,254          8,399  \n", "        Cash dividends declared per common share                $0             $0  \n", "                                                                                   "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "execution_count": 7}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}