{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["# Standardized Statements\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/XBRL2-StandardizedStatements.ipynb)"], "id": "156a80c90defa518"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-25T18:25:47.529407Z", "start_time": "2025-03-25T18:25:45.608216Z"}}, "cell_type": "code", "source": "!pip install -U edgartools", "id": "66dd433a376b568e", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: edgartools in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (3.13.9)\r\n", "Collecting edgartools\r\n", "  Downloading edgartools-3.13.10-py3-none-any.whl.metadata (17 kB)\r\n", "Requirement already satisfied: beautifulsoup4>=4.10.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (4.12.3)\r\n", "Requirement already satisfied: fastcore>=1.5.29 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (1.5.38)\r\n", "Requirement already satisfied: httpx>=0.25.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (0.27.0)\r\n", "Requirement already satisfied: humanize>=4.0.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (4.9.0)\r\n", "Requirement already satisfied: lxml>=4.4 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (5.2.2)\r\n", "Requirement already satisfied: nest-asyncio>=1.5.1 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (1.6.0)\r\n", "Requirement already satisfied: orjson>=3.6.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (3.10.3)\r\n", "Requirement already satisfied: pandas>=2.0.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (2.2.2)\r\n", "Requirement already satisfied: pyarrow>=14.0.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (19.0.0)\r\n", "Requirement already satisfied: pydantic>=2.0.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (2.7.1)\r\n", "Requirement already satisfied: rank-bm25>=0.2.1 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (0.2.1)\r\n", "Requirement already satisfied: rapidfuzz>=3.5.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (3.9.4)\r\n", "Requirement already satisfied: rich>=13.0.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (13.7.1)\r\n", "Requirement already satisfied: stamina>=24.2.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (24.3.0)\r\n", "Requirement already satisfied: tabulate>=0.9.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (0.9.0)\r\n", "Requirement already satisfied: textdistance>=4.5.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (4.6.2)\r\n", "Requirement already satisfied: tqdm>=4.62.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (4.62.0)\r\n", "Requirement already satisfied: unidecode>=1.2.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (1.3.8)\r\n", "Requirement already satisfied: soupsieve>1.2 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from beautifulsoup4>=4.10.0->edgartools) (2.5)\r\n", "Requirement already satisfied: packaging in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from fastcore>=1.5.29->edgartools) (24.2)\r\n", "Requirement already satisfied: anyio in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpx>=0.25.0->edgartools) (4.4.0)\r\n", "Requirement already satisfied: certifi in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpx>=0.25.0->edgartools) (2024.2.2)\r\n", "Requirement already satisfied: httpcore==1.* in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpx>=0.25.0->edgartools) (1.0.5)\r\n", "Requirement already satisfied: idna in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpx>=0.25.0->edgartools) (3.7)\r\n", "Requirement already satisfied: sniffio in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpx>=0.25.0->edgartools) (1.3.1)\r\n", "Requirement already satisfied: h11<0.15,>=0.13 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpcore==1.*->httpx>=0.25.0->edgartools) (0.14.0)\r\n", "Requirement already satisfied: numpy>=1.23.2 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pandas>=2.0.0->edgartools) (1.26.4)\r\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pandas>=2.0.0->edgartools) (2.9.0.post0)\r\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pandas>=2.0.0->edgartools) (2024.1)\r\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pandas>=2.0.0->edgartools) (2024.1)\r\n", "Requirement already satisfied: annotated-types>=0.4.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pydantic>=2.0.0->edgartools) (0.7.0)\r\n", "Requirement already satisfied: pydantic-core==2.18.2 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pydantic>=2.0.0->edgartools) (2.18.2)\r\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pydantic>=2.0.0->edgartools) (4.12.0)\r\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from rich>=13.0.0->edgartools) (3.0.0)\r\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from rich>=13.0.0->edgartools) (2.18.0)\r\n", "Requirement already satisfied: tenacity in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from stamina>=24.2.0->edgartools) (9.0.0)\r\n", "Requirement already satisfied: mdurl~=0.1 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich>=13.0.0->edgartools) (0.1.2)\r\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from python-dateutil>=2.8.2->pandas>=2.0.0->edgartools) (1.16.0)\r\n", "Downloading edgartools-3.13.10-py3-none-any.whl (1.2 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m23.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hInstalling collected packages: edgartools\r\n", "  Attempting uninstall: ed<PERSON><PERSON>s\r\n", "    Found existing installation: edgartools 3.13.9\r\n", "    Uninstalling edgartools-3.13.9:\r\n", "      Successfully uninstalled edgartools-3.13.9\r\n", "Successfully installed edgartools-3.13.10\r\n"]}], "execution_count": 1}, {"metadata": {}, "cell_type": "markdown", "source": "## <PERSON>up <PERSON>", "id": "a7d9a1d07cfbe00a"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-25T18:25:47.535543Z", "start_time": "2025-03-25T18:25:47.533556Z"}}, "cell_type": "code", "source": ["from edgar import *\n", "\n", "set_identity('<EMAIL>')"], "id": "2716f1dd7ef7672", "outputs": [], "execution_count": 2}, {"metadata": {}, "cell_type": "markdown", "source": "## Import XBRL2", "id": "939342696295ca19"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-25T18:25:47.601555Z", "start_time": "2025-03-25T18:25:47.585858Z"}}, "cell_type": "code", "source": "from edgar.xbrl import *", "id": "b39fb3794f355b38", "outputs": [], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-25T18:25:50.464831Z", "start_time": "2025-03-25T18:25:48.885746Z"}}, "cell_type": "code", "source": ["c = Company(\"AAPL\")\n", "f = c.latest(\"10-K\")\n", "xbrl = XBRL.from_filing(f)"], "id": "c1ea6b235cb4d103", "outputs": [], "execution_count": 4}, {"metadata": {}, "cell_type": "markdown", "source": "## Income Statement", "id": "7d9e027c955e6ee8"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-25T18:25:50.485581Z", "start_time": "2025-03-25T18:25:50.469158Z"}}, "cell_type": "code", "source": ["income_statement = xbrl.statements.income_statement()\n", "income_statement"], "id": "accdbf852c069af1", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                                  Consolidated Statement of Income (Standardized)                                  \u001b[0m\n", "\u001b[3m                               \u001b[0m\u001b[1;3mYear Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m                                \u001b[0m\n", "                                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m                                                \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSeptember 28, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSeptember 30, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSeptember 24, 2022\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "        Revenue                                                $391,035             $383,285             $394,328  \n", "        Cost of Revenue                                      $(210,352)           $(214,137)           $(223,546)  \n", "        Gross Profit                                           $180,683             $169,148             $170,782  \n", "        Operating Expenses                                                                                         \n", "          Research and Development Expense                      $31,370              $29,915              $26,251  \n", "          Selling, General and Administrative                   $26,097              $24,932              $25,094  \n", "  Expense                                                                                                          \n", "          Operating Expenses                                  $(57,467)            $(54,847)            $(51,345)  \n", "        Operating Income                                       $123,216             $114,301             $119,437  \n", "        Nonoperating Income/Expense                                $269               $(565)               $(334)  \n", "        Income Before Tax                                      $123,485             $113,736             $119,103  \n", "        Income Tax Expense                                      $29,749              $16,741              $19,300  \n", "        Net Income                                              $93,736              $96,995              $99,803  \n", "        Earnings per share:                                                                                        \n", "          Earnings Per Share                                       0.00                 0.00                 0.00  \n", "          Earnings Per Share (Diluted)                             0.00                 0.00                 0.00  \n", "        Shares used in computing earnings per                                                                      \n", "  share:                                                                                                           \n", "          Shares Outstanding                                 15,343,783           15,744,231           16,215,963  \n", "          Shares Outstanding (Diluted)                       15,408,095           15,812,547           16,325,819  \n", "                                                                                                                   "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-25T18:25:53.060212Z", "start_time": "2025-03-25T18:25:53.025694Z"}}, "cell_type": "code", "source": "income_statement.to_dataframe()", "id": "97581c798a1403e1", "outputs": [{"data": {"text/plain": ["                                              concept  \\\n", "0                     us-gaap_IncomeStatementAbstract   \n", "1                              us-gaap_StatementTable   \n", "2                            srt_ProductOrServiceAxis   \n", "3                       srt_ProductsAndServicesDomain   \n", "4                               us-gaap_ProductMember   \n", "5                               us-gaap_ServiceMember   \n", "6                          us-gaap_StatementLineItems   \n", "7   us-gaap_RevenueFromContractWithCustomerExcludi...   \n", "8                  us-gaap_CostOfGoodsAndServicesSold   \n", "9                                 us-gaap_GrossProfit   \n", "10                  us-gaap_OperatingExpensesAbstract   \n", "11              us-gaap_ResearchAndDevelopmentExpense   \n", "12     us-gaap_SellingGeneralAndAdministrativeExpense   \n", "13                          us-gaap_OperatingExpenses   \n", "14                        us-gaap_OperatingIncomeLoss   \n", "15                  us-gaap_NonoperatingIncomeExpense   \n", "16  us-gaap_IncomeLossFromContinuingOperationsBefo...   \n", "17                    us-gaap_IncomeTaxExpenseBenefit   \n", "18                              us-gaap_NetIncomeLoss   \n", "19                   us-gaap_EarningsPerShareAbstract   \n", "20                      us-gaap_EarningsPerShareBasic   \n", "21                    us-gaap_EarningsPerShareDiluted   \n", "22  us-gaap_WeightedAverageNumberOfSharesOutstandi...   \n", "23  us-gaap_WeightedAverageNumberOfSharesOutstandi...   \n", "24  us-gaap_WeightedAverageNumberOfDilutedSharesOu...   \n", "\n", "                                           label  level  is_abstract  \\\n", "0                    Income Statement [Abstract]      0        False   \n", "1                              Statement [Table]      1        False   \n", "2                     Product and Service [Axis]      2        False   \n", "3                   Product and Service [Domain]      3        False   \n", "4                                       Products      4        False   \n", "5                                       Services      4        False   \n", "6                         Statement [Line Items]      2        False   \n", "7                                        Revenue      3        False   \n", "8                                Cost of Revenue      3        False   \n", "9                                   Gross Profit      3        False   \n", "10                            Operating Expenses      3        False   \n", "11              Research and Development Expense      4        False   \n", "12   Selling, General and Administrative Expense      4        False   \n", "13                            Operating Expenses      4        False   \n", "14                              Operating Income      3        False   \n", "15                   Nonoperating Income/Expense      3        False   \n", "16                             Income Before Tax      3        False   \n", "17                            Income Tax Expense      3        False   \n", "18                                    Net Income      3        False   \n", "19                           Earnings per share:      3        False   \n", "20                            Earnings Per Share      4        False   \n", "21                  Earnings Per Share (Diluted)      4        False   \n", "22  Shares used in computing earnings per share:      3        False   \n", "23                            Shares Outstanding      4        False   \n", "24                  Shares Outstanding (Diluted)      4        False   \n", "\n", "    has_values  2021-09-26_2022-09-24  2022-09-25_2023-09-30  \\\n", "0        False                    NaN                    NaN   \n", "1        False                    NaN                    NaN   \n", "2        False                    NaN                    NaN   \n", "3        False                    NaN                    NaN   \n", "4        False                    NaN                    NaN   \n", "5        False                    NaN                    NaN   \n", "6        False                    NaN                    NaN   \n", "7         True           3.943280e+11           3.832850e+11   \n", "8         True          -2.235460e+11          -2.141370e+11   \n", "9         True           1.707820e+11           1.691480e+11   \n", "10       False                    NaN                    NaN   \n", "11        True           2.625100e+10           2.991500e+10   \n", "12        True           2.509400e+10           2.493200e+10   \n", "13        True          -5.134500e+10          -5.484700e+10   \n", "14        True           1.194370e+11           1.143010e+11   \n", "15        True          -3.340000e+08          -5.650000e+08   \n", "16        True           1.191030e+11           1.137360e+11   \n", "17        True           1.930000e+10           1.674100e+10   \n", "18        True           9.980300e+10           9.699500e+10   \n", "19       False                    NaN                    NaN   \n", "20        True           6.150000e+00           6.160000e+00   \n", "21        True           6.110000e+00           6.130000e+00   \n", "22       False                    NaN                    NaN   \n", "23        True           1.621596e+10           1.574423e+10   \n", "24        True           1.632582e+10           1.581255e+10   \n", "\n", "    2023-10-01_2024-09-28                            original_label  \n", "0                     NaN                                       NaN  \n", "1                     NaN                                       NaN  \n", "2                     NaN                                       NaN  \n", "3                     NaN                                       NaN  \n", "4                     NaN                                       NaN  \n", "5                     NaN                                       NaN  \n", "6                     NaN                                       NaN  \n", "7            3.910350e+11                                 Net sales  \n", "8           -2.103520e+11                             Cost of sales  \n", "9            1.806830e+11                              Gross margin  \n", "10                    NaN                       Operating expenses:  \n", "11           3.137000e+10                  Research and development  \n", "12           2.609700e+10       Selling, general and administrative  \n", "13          -5.746700e+10                  Total operating expenses  \n", "14           1.232160e+11                          Operating income  \n", "15           2.690000e+08               Other income/(expense), net  \n", "16           1.234850e+11  Income before provision for income taxes  \n", "17           2.974900e+10                Provision for income taxes  \n", "18           9.373600e+10                                Net income  \n", "19                    NaN                                       NaN  \n", "20           6.110000e+00              Basic (in dollars per share)  \n", "21           6.080000e+00            Diluted (in dollars per share)  \n", "22                    NaN                                       NaN  \n", "23           1.534378e+10                         Basic (in shares)  \n", "24           1.540810e+10                       Diluted (in shares)  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>level</th>\n", "      <th>is_abstract</th>\n", "      <th>has_values</th>\n", "      <th>2021-09-26_2022-09-24</th>\n", "      <th>2022-09-25_2023-09-30</th>\n", "      <th>2023-10-01_2024-09-28</th>\n", "      <th>original_label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap_IncomeStatementAbstract</td>\n", "      <td>Income Statement [Abstract]</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap_StatementTable</td>\n", "      <td>Statement [Table]</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>srt_ProductOrServiceAxis</td>\n", "      <td>Product and Service [Axis]</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>srt_ProductsAndServicesDomain</td>\n", "      <td>Product and Service [Domain]</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap_ProductMember</td>\n", "      <td>Products</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap_ServiceMember</td>\n", "      <td>Services</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap_StatementLineItems</td>\n", "      <td>Statement [Line Items]</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap_RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Revenue</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>3.943280e+11</td>\n", "      <td>3.832850e+11</td>\n", "      <td>3.910350e+11</td>\n", "      <td>Net sales</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap_CostOfGoodsAndServicesSold</td>\n", "      <td>Cost of Revenue</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>-2.235460e+11</td>\n", "      <td>-2.141370e+11</td>\n", "      <td>-2.103520e+11</td>\n", "      <td>Cost of sales</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap_GrossProfit</td>\n", "      <td>Gross Profit</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.707820e+11</td>\n", "      <td>1.691480e+11</td>\n", "      <td>1.806830e+11</td>\n", "      <td>Gross margin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>us-gaap_OperatingExpensesAbstract</td>\n", "      <td>Operating Expenses</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Operating expenses:</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>us-gaap_ResearchAndDevelopmentExpense</td>\n", "      <td>Research and Development Expense</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2.625100e+10</td>\n", "      <td>2.991500e+10</td>\n", "      <td>3.137000e+10</td>\n", "      <td>Research and development</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>us-gaap_SellingGeneralAndAdministrativeExpense</td>\n", "      <td>Selling, General and Administrative Expense</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2.509400e+10</td>\n", "      <td>2.493200e+10</td>\n", "      <td>2.609700e+10</td>\n", "      <td>Selling, general and administrative</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>us-gaap_OperatingExpenses</td>\n", "      <td>Operating Expenses</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>-5.134500e+10</td>\n", "      <td>-5.484700e+10</td>\n", "      <td>-5.746700e+10</td>\n", "      <td>Total operating expenses</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>us-gaap_OperatingIncomeLoss</td>\n", "      <td>Operating Income</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.194370e+11</td>\n", "      <td>1.143010e+11</td>\n", "      <td>1.232160e+11</td>\n", "      <td>Operating income</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>us-gaap_NonoperatingIncomeExpense</td>\n", "      <td>Nonoperating Income/Expense</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>-3.340000e+08</td>\n", "      <td>-5.650000e+08</td>\n", "      <td>2.690000e+08</td>\n", "      <td>Other income/(expense), net</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>us-gaap_IncomeLossFromContinuingOperationsBefo...</td>\n", "      <td>Income Before Tax</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.191030e+11</td>\n", "      <td>1.137360e+11</td>\n", "      <td>1.234850e+11</td>\n", "      <td>Income before provision for income taxes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>us-gaap_IncomeTaxExpenseBenefit</td>\n", "      <td>Income Tax Expense</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.930000e+10</td>\n", "      <td>1.674100e+10</td>\n", "      <td>2.974900e+10</td>\n", "      <td>Provision for income taxes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>us-gaap_NetIncomeLoss</td>\n", "      <td>Net Income</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>9.980300e+10</td>\n", "      <td>9.699500e+10</td>\n", "      <td>9.373600e+10</td>\n", "      <td>Net income</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>us-gaap_EarningsPerShareAbstract</td>\n", "      <td>Earnings per share:</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>us-gaap_EarningsPerShareBasic</td>\n", "      <td>Earnings Per Share</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>6.150000e+00</td>\n", "      <td>6.160000e+00</td>\n", "      <td>6.110000e+00</td>\n", "      <td>Basic (in dollars per share)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>us-gaap_EarningsPerShareDiluted</td>\n", "      <td>Earnings Per Share (Diluted)</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>6.110000e+00</td>\n", "      <td>6.130000e+00</td>\n", "      <td>6.080000e+00</td>\n", "      <td>Diluted (in dollars per share)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>us-gaap_WeightedAverageNumberOfSharesOutstandi...</td>\n", "      <td>Shares used in computing earnings per share:</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>us-gaap_WeightedAverageNumberOfSharesOutstandi...</td>\n", "      <td>Shares Outstanding</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.621596e+10</td>\n", "      <td>1.574423e+10</td>\n", "      <td>1.534378e+10</td>\n", "      <td>Basic (in shares)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>us-gaap_WeightedAverageNumberOfDilutedSharesOu...</td>\n", "      <td>Shares Outstanding (Diluted)</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.632582e+10</td>\n", "      <td>1.581255e+10</td>\n", "      <td>1.540810e+10</td>\n", "      <td>Diluted (in shares)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"metadata": {}, "cell_type": "markdown", "source": "## Balance Sheet", "id": "2f2db6a8cb887e26"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-25T18:25:54.919074Z", "start_time": "2025-03-25T18:25:54.893431Z"}}, "cell_type": "code", "source": ["balance_sheet = xbrl.statements.balance_sheet()\n", "balance_sheet"], "id": "a0784b948fedbaf9", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m             Consolidated Balance Sheets (Standardized)             \u001b[0m\n", "\u001b[3m    \u001b[0m\u001b[1;3mFiscal Year Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m     \u001b[0m\n", "                                                                    \n", " \u001b[1m \u001b[0m\u001b[1m                                                 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024\u001b[0m\u001b[1m \u001b[0m \n", " ────────────────────────────────────────────────────────────────── \n", "    ASSETS:                                                         \n", "      Current assets:                                               \n", "        Cash and Cash Equivalents                          $29,943  \n", "        Marketable Securities                              $35,228  \n", "        Accounts Receivable                                $33,410  \n", "        Vendor non-trade receivables                       $32,833  \n", "        Inventory                                           $7,286  \n", "        Other Assets                                       $14,287  \n", "        Total Current Assets                              $152,987  \n", "      Non-current assets:                                           \n", "        Marketable securities                              $91,479  \n", "        Property, Plant and Equipment                      $45,680  \n", "        Other Assets                                       $74,834  \n", "        Total Current Assets                              $211,993  \n", "      Total Assets                                        $364,980  \n", "    LIABILITIES AND SHAREH<PERSON>DERS’ EQUITY:                           \n", "      Current liabilities:                                          \n", "        Accounts Payable                                   $68,960  \n", "        Other Liabilities                                  $78,304  \n", "        Deferred Revenue                                    $8,249  \n", "        Commercial paper                                    $9,967  \n", "        Short-Term Debt                                    $10,912  \n", "        Total Current Liabilities                         $176,392  \n", "      Non-current liabilities:                                      \n", "        Long-Term Debt                                     $85,750  \n", "        Other Liabilities                                  $45,888  \n", "        Total Current Liabilities                         $131,638  \n", "      Total Liabilities                                   $308,030  \n", "      Commitments and contingencies                                 \n", "      Common Stock Shares Outstanding                   15,116,786  \n", "      Common Stock Shares Issued                        15,116,786  \n", "      Shareholders’ equity:                                         \n", "        Common Stock                                       $83,276  \n", "        Retained Earnings                                $(19,154)  \n", "        Accumulated Other Comprehensive Income/Loss       $(7,172)  \n", "        Total Stockholders' Equity                         $56,950  \n", "      Total Liabilities and Stockholders' Equity          $364,980  \n", "                                                                    "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "execution_count": 7}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-25T18:25:56.865731Z", "start_time": "2025-03-25T18:25:56.769628Z"}}, "cell_type": "code", "source": "f.statements.balance_sheet.view()", "id": "b688e277448078a7", "outputs": [{"data": {"text/plain": ["\u001b[34m                                                                                \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[1m \u001b[0m\u001b[1mCONSOLIDATED BALANCE SHEETS - USD ($)shares in\u001b[0m\u001b[1m \u001b[0m\u001b[34m \u001b[0m\u001b[1m              \u001b[0m\u001b[34m \u001b[0m\u001b[1m              \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[1m \u001b[0m\u001b[1mThousands, $ in Millions                      \u001b[0m\u001b[1m \u001b[0m\u001b[34m \u001b[0m\u001b[1mSep. 28, 2024\u001b[0m\u001b[1m \u001b[0m\u001b[34m \u001b[0m\u001b[1mSep. 30, 2023\u001b[0m\u001b[1m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m ────────────────────────────────────────────────────────────────────────────── \u001b[0m\n", "\u001b[34m \u001b[0m Current assets:                                \u001b[34m \u001b[0m              \u001b[34m \u001b[0m              \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mCash and cash equivalents                     \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m     $ 29,943\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m     $ 29,965\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Marketable securities                          \u001b[34m \u001b[0m       35,228 \u001b[34m \u001b[0m       31,590 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mAccounts receivable, net                      \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       33,410\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       29,508\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Vendor non-trade receivables                   \u001b[34m \u001b[0m       32,833 \u001b[34m \u001b[0m       31,477 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mInventories                                   \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m        7,286\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m        6,331\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Other current assets                           \u001b[34m \u001b[0m       14,287 \u001b[34m \u001b[0m       14,695 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mTotal current assets                          \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m      152,987\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m      143,566\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Non-current assets:                            \u001b[34m \u001b[0m              \u001b[34m \u001b[0m              \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mMarketable securities                         \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       91,479\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m      100,544\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Property, plant and equipment, net             \u001b[34m \u001b[0m       45,680 \u001b[34m \u001b[0m       43,715 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mOther non-current assets                      \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       74,834\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       64,758\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Total non-current assets                       \u001b[34m \u001b[0m      211,993 \u001b[34m \u001b[0m      209,017 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mTotal assets                                  \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m      364,980\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m      352,583\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Current liabilities:                           \u001b[34m \u001b[0m              \u001b[34m \u001b[0m              \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mAccounts payable                              \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       68,960\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       62,611\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Other current liabilities                      \u001b[34m \u001b[0m       78,304 \u001b[34m \u001b[0m       58,829 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mDeferred revenue                              \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m        8,249\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m        8,061\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Commercial paper                               \u001b[34m \u001b[0m        9,967 \u001b[34m \u001b[0m        5,985 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mTerm debt                                     \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       10,912\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m        9,822\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Total current liabilities                      \u001b[34m \u001b[0m      176,392 \u001b[34m \u001b[0m      145,308 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mNon-current liabilities:                      \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m             \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m             \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Term debt                                      \u001b[34m \u001b[0m       85,750 \u001b[34m \u001b[0m       95,281 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mOther non-current liabilities                 \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       45,888\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       49,848\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Total non-current liabilities                  \u001b[34m \u001b[0m      131,638 \u001b[34m \u001b[0m      145,129 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mTotal liabilities                             \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m      308,030\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m      290,437\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Commitments and contingencies                  \u001b[34m \u001b[0m              \u001b[34m \u001b[0m              \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mCommon stock, shares outstanding (in shares)  \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m   15,116,786\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m   15,550,061\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Common stock, shares issued (in shares)        \u001b[34m \u001b[0m   15,116,786 \u001b[34m \u001b[0m   15,550,061 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mShareholders’ equity:                         \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m             \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m             \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Common stock and additional paid-in capital,   \u001b[34m \u001b[0m              \u001b[34m \u001b[0m              \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m $0.00001 par value:                            \u001b[34m \u001b[0m              \u001b[34m \u001b[0m              \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m 50,400,000 shares authorized; 15,116,786 and   \u001b[34m \u001b[0m     $ 83,276 \u001b[34m \u001b[0m     $ 73,812 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m 15,550,061 shares issued and outstanding,      \u001b[34m \u001b[0m              \u001b[34m \u001b[0m              \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m respectively                                   \u001b[34m \u001b[0m              \u001b[34m \u001b[0m              \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mAccumulated deficit                           \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m      -19,154\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m         -214\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Accumulated other comprehensive loss           \u001b[34m \u001b[0m       -7,172 \u001b[34m \u001b[0m      -11,452 \u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[38;5;245mTotal shareholders’ equity                    \u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       56,950\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\u001b[38;5;245m       62,146\u001b[0m\u001b[38;5;245m \u001b[0m\u001b[34m \u001b[0m\n", "\u001b[34m \u001b[0m Total liabilities and shareholders’ equity     \u001b[34m \u001b[0m    $ 364,980 \u001b[34m \u001b[0m    $ 352,583 \u001b[34m \u001b[0m\n", "\u001b[34m                                                                                \u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">                                                                                </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"font-weight: bold\"> CONSOLIDATED BALANCE SHEETS - USD ($)shares in </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"font-weight: bold\">              </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"font-weight: bold\">              </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"font-weight: bold\"> Thousands, $ in Millions                       </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"font-weight: bold\">Sep. 28, 2024 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"font-weight: bold\">Sep. 30, 2023 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> ────────────────────────────────────────────────────────────────────────────── </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Current assets:                                <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Cash and cash equivalents                      </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">     $ 29,943 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">     $ 29,965 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Marketable securities                          <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       35,228 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       31,590 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Accounts receivable, net                       </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       33,410 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       29,508 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Vendor non-trade receivables                   <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       32,833 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       31,477 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Inventories                                    </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">        7,286 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">        6,331 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Other current assets                           <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       14,287 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       14,695 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Total current assets                           </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">      152,987 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">      143,566 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Non-current assets:                            <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Marketable securities                          </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       91,479 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">      100,544 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Property, plant and equipment, net             <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       45,680 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       43,715 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Other non-current assets                       </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       74,834 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       64,758 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Total non-current assets                       <span style=\"color: #000080; text-decoration-color: #000080\"> </span>      211,993 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>      209,017 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Total assets                                   </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">      364,980 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">      352,583 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Current liabilities:                           <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Accounts payable                               </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       68,960 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       62,611 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Other current liabilities                      <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       78,304 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       58,829 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Deferred revenue                               </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">        8,249 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">        8,061 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Commercial paper                               <span style=\"color: #000080; text-decoration-color: #000080\"> </span>        9,967 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>        5,985 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Term debt                                      </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       10,912 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">        9,822 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Total current liabilities                      <span style=\"color: #000080; text-decoration-color: #000080\"> </span>      176,392 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>      145,308 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Non-current liabilities:                       </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">              </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">              </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Term debt                                      <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       85,750 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       95,281 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Other non-current liabilities                  </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       45,888 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       49,848 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Total non-current liabilities                  <span style=\"color: #000080; text-decoration-color: #000080\"> </span>      131,638 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>      145,129 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Total liabilities                              </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">      308,030 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">      290,437 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Commitments and contingencies                  <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Common stock, shares outstanding (in shares)   </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">   15,116,786 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">   15,550,061 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Common stock, shares issued (in shares)        <span style=\"color: #000080; text-decoration-color: #000080\"> </span>   15,116,786 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>   15,550,061 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Shareholders’ equity:                          </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">              </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">              </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Common stock and additional paid-in capital,   <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> $0.00001 par value:                            <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> 50,400,000 shares authorized; 15,116,786 and   <span style=\"color: #000080; text-decoration-color: #000080\"> </span>     $ 83,276 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>     $ 73,812 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> 15,550,061 shares issued and outstanding,      <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> respectively                                   <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>              <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Accumulated deficit                            </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">      -19,154 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">         -214 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Accumulated other comprehensive loss           <span style=\"color: #000080; text-decoration-color: #000080\"> </span>       -7,172 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>      -11,452 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\"> Total shareholders’ equity                     </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       56,950 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span><span style=\"color: #8a8a8a; text-decoration-color: #8a8a8a\">       62,146 </span><span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\"> </span> Total liabilities and shareholders’ equity     <span style=\"color: #000080; text-decoration-color: #000080\"> </span>    $ 364,980 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>    $ 352,583 <span style=\"color: #000080; text-decoration-color: #000080\"> </span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">                                                                                </span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 8}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-15T14:23:35.394328Z", "start_time": "2025-03-15T14:23:35.332106Z"}}, "cell_type": "code", "source": "balance_sheet.to_dataframe()", "id": "243cb51a3689319f", "outputs": [{"data": {"text/plain": ["                                              concept  \\\n", "0        us-gaap_StatementOfFinancialPositionAbstract   \n", "1                              us-gaap_AssetsAbstract   \n", "2                       us-gaap_AssetsCurrentAbstract   \n", "3       us-gaap_CashAndCashEquivalentsAtCarryingValue   \n", "4                 us-gaap_MarketableSecuritiesCurrent   \n", "5                us-gaap_AccountsReceivableNetCurrent   \n", "6                  us-gaap_NontradeReceivablesCurrent   \n", "7                                us-gaap_InventoryNet   \n", "8                          us-gaap_OtherAssetsCurrent   \n", "9                               us-gaap_AssetsCurrent   \n", "10                   us-gaap_AssetsNoncurrentAbstract   \n", "11             us-gaap_MarketableSecuritiesNoncurrent   \n", "12               us-gaap_PropertyPlantAndEquipmentNet   \n", "13                      us-gaap_OtherAssetsNoncurrent   \n", "14                           us-gaap_AssetsNoncurrent   \n", "15                                     us-gaap_Assets   \n", "16   us-gaap_LiabilitiesAndStockholdersEquityAbstract   \n", "17                 us-gaap_LiabilitiesCurrentAbstract   \n", "18                     us-gaap_AccountsPayableCurrent   \n", "19                    us-gaap_OtherLiabilitiesCurrent   \n", "20       us-gaap_ContractWithCustomerLiabilityCurrent   \n", "21                            us-gaap_CommercialPaper   \n", "22                        us-gaap_LongTermDebtCurrent   \n", "23                         us-gaap_LiabilitiesCurrent   \n", "24              us-gaap_LiabilitiesNoncurrentAbstract   \n", "25                     us-gaap_LongTermDebtNoncurrent   \n", "26                 us-gaap_OtherLiabilitiesNoncurrent   \n", "27                      us-gaap_LiabilitiesNoncurrent   \n", "28                                us-gaap_Liabilities   \n", "29                us-gaap_CommitmentsAndContingencies   \n", "30               us-gaap_CommonStockSharesOutstanding   \n", "31                    us-gaap_CommonStockSharesIssued   \n", "32                 us-gaap_StockholdersEquityAbstract   \n", "33  us-gaap_CommonStocksIncludingAdditionalPaidInC...   \n", "34         us-gaap_RetainedEarningsAccumulatedDeficit   \n", "35  us-gaap_AccumulatedOtherComprehensiveIncomeLos...   \n", "36                         us-gaap_StockholdersEquity   \n", "37           us-gaap_LiabilitiesAndStockholdersEquity   \n", "\n", "                                         label  level  is_abstract  \\\n", "0   Statement of Financial Position [Abstract]      0        False   \n", "1                                      ASSETS:      1        False   \n", "2                              Current assets:      2        False   \n", "3                    Cash and Cash Equivalents      3        False   \n", "4                        Marketable securities      3        False   \n", "5                          Accounts Receivable      3        False   \n", "6                 Vendor non-trade receivables      3        False   \n", "7                                    Inventory      3        False   \n", "8                                 Other Assets      3        False   \n", "9                         Total Current Assets      3        False   \n", "10                         Non-current assets:      2        False   \n", "11                       Marketable securities      3        False   \n", "12               Property, Plant and Equipment      3        False   \n", "13                                Other Assets      3        False   \n", "14                        Total Current Assets      3        False   \n", "15                                Total Assets      2        False   \n", "16       LIABI<PERSON><PERSON>IE<PERSON> AND SHAREH<PERSON>DERS’ EQUITY:      1        False   \n", "17                        Current liabilities:      2        False   \n", "18                            Accounts Payable      3        False   \n", "19                           Other Liabilities      3        False   \n", "20                            Deferred Revenue      3        False   \n", "21                            Commercial paper      3        False   \n", "22                             Short-Term Debt      3        False   \n", "23                   Total Current Liabilities      3        False   \n", "24                    Non-current liabilities:      2        False   \n", "25                              Long-Term Debt      3        False   \n", "26                           Other Liabilities      3        False   \n", "27                   Total Current Liabilities      3        False   \n", "28                           Total Liabilities      2        False   \n", "29               Commitments and contingencies      2        False   \n", "30             Common Stock Shares Outstanding      2        False   \n", "31                  Common Stock Shares Issued      2        False   \n", "32                       Shareholders’ equity:      2        False   \n", "33                                Common Stock      3        False   \n", "34                           Retained <PERSON><PERSON><PERSON><PERSON>      3        False   \n", "35        Accumulated other comprehensive loss      3        False   \n", "36                  Total Stockholders' Equity      3        False   \n", "37  Total Liabilities and Stockholders' Equity      2        False   \n", "\n", "    has_values    2021-09-25    2022-09-24      2023-09-30      2024-09-28  \\\n", "0        False           NaN           NaN            None            None   \n", "1        False           NaN           NaN            None            None   \n", "2        False           NaN           NaN            None            None   \n", "3         True           NaN           NaN   29965000000.0   ***********.0   \n", "4         True           NaN           NaN   ***********.0   ***********.0   \n", "5         True           NaN           NaN   ***********.0   ***********.0   \n", "6         True           NaN           NaN   ***********.0   ***********.0   \n", "7         True           NaN           NaN    **********.0    **********.0   \n", "8         True           NaN           NaN   14695000000.0   14287000000.0   \n", "9         True           NaN           NaN  143566000000.0  152987000000.0   \n", "10       False           NaN           NaN            None            None   \n", "11        True           NaN           NaN  100544000000.0   91479000000.0   \n", "12        True           NaN           NaN   43715000000.0   45680000000.0   \n", "13        True           NaN           NaN   64758000000.0   74834000000.0   \n", "14        True           NaN           NaN  209017000000.0  211993000000.0   \n", "15        True           NaN           NaN  ************.0  ************.0   \n", "16       False           NaN           NaN            None            None   \n", "17       False           NaN           NaN            None            None   \n", "18        True           NaN           NaN   ***********.0   ***********.0   \n", "19        True           NaN           NaN   ***********.0   ***********.0   \n", "20        True           NaN           NaN    **********.0    **********.0   \n", "21        True           NaN           NaN    5985000000.0    9967000000.0   \n", "22        True           NaN           NaN    9822000000.0   10912000000.0   \n", "23        True           NaN           NaN  145308000000.0  176392000000.0   \n", "24       False           NaN           NaN            None            None   \n", "25        True           NaN           NaN   95281000000.0   85750000000.0   \n", "26        True           NaN           NaN   49848000000.0   45888000000.0   \n", "27        True           NaN           NaN  145129000000.0  131638000000.0   \n", "28        True           NaN           NaN  290437000000.0  308030000000.0   \n", "29        True           NaN           NaN                                   \n", "30        True  1.642679e+10  1.594342e+10   15550061000.0   15116786000.0   \n", "31        True           NaN           NaN   15550061000.0   15116786000.0   \n", "32       False           NaN           NaN            None            None   \n", "33        True           NaN           NaN   73812000000.0   83276000000.0   \n", "34        True           NaN           NaN    -214000000.0  -19154000000.0   \n", "35        True           NaN           NaN  -11452000000.0   -7172000000.0   \n", "36        True  6.309000e+10  5.067200e+10   62146000000.0   ***********.0   \n", "37        True           NaN           NaN  ************.0  ************.0   \n", "\n", "                                       original_label  \n", "0                                                 NaN  \n", "1                                                 NaN  \n", "2                                                 NaN  \n", "3                           Cash and cash equivalents  \n", "4                                                 NaN  \n", "5                            Accounts receivable, net  \n", "6                                                 NaN  \n", "7                                         Inventories  \n", "8                                Other current assets  \n", "9                                Total current assets  \n", "10                                                NaN  \n", "11                                                NaN  \n", "12                 Property, plant and equipment, net  \n", "13                           Other non-current assets  \n", "14                           Total non-current assets  \n", "15                                       Total assets  \n", "16                                                NaN  \n", "17                                                NaN  \n", "18                                   Accounts payable  \n", "19                          Other current liabilities  \n", "20                                   Deferred revenue  \n", "21                                                NaN  \n", "22                                          Term debt  \n", "23                          Total current liabilities  \n", "24                                                NaN  \n", "25                                          Term debt  \n", "26                      Other non-current liabilities  \n", "27                      Total non-current liabilities  \n", "28                                  Total liabilities  \n", "29                                                NaN  \n", "30       Common stock, shares outstanding (in shares)  \n", "31            Common stock, shares issued (in shares)  \n", "32                                                NaN  \n", "33  Common stock and additional paid-in capital, $...  \n", "34                                Accumulated deficit  \n", "35                                                NaN  \n", "36                         Total shareholders’ equity  \n", "37         Total liabilities and shareholders’ equity  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>level</th>\n", "      <th>is_abstract</th>\n", "      <th>has_values</th>\n", "      <th>2021-09-25</th>\n", "      <th>2022-09-24</th>\n", "      <th>2023-09-30</th>\n", "      <th>2024-09-28</th>\n", "      <th>original_label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap_StatementOfFinancialPositionAbstract</td>\n", "      <td>Statement of Financial Position [Abstract]</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap_AssetsAbstract</td>\n", "      <td>ASSETS:</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap_AssetsCurrentAbstract</td>\n", "      <td>Current assets:</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap_CashAndCashEquivalentsAtCarryingValue</td>\n", "      <td>Cash and Cash Equivalents</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>29965000000.0</td>\n", "      <td>***********.0</td>\n", "      <td>Cash and cash equivalents</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap_MarketableSecuritiesCurrent</td>\n", "      <td>Marketable securities</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap_AccountsReceivableNetCurrent</td>\n", "      <td>Accounts Receivable</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>Accounts receivable, net</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap_NontradeReceivablesCurrent</td>\n", "      <td>Vendor non-trade receivables</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap_InventoryNet</td>\n", "      <td>Inventory</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>**********.0</td>\n", "      <td>**********.0</td>\n", "      <td>Inventories</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap_OtherAssetsCurrent</td>\n", "      <td>Other Assets</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>14695000000.0</td>\n", "      <td>14287000000.0</td>\n", "      <td>Other current assets</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap_AssetsCurrent</td>\n", "      <td>Total Current Assets</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>143566000000.0</td>\n", "      <td>152987000000.0</td>\n", "      <td>Total current assets</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>us-gaap_AssetsNoncurrentAbstract</td>\n", "      <td>Non-current assets:</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>us-gaap_MarketableSecuritiesNoncurrent</td>\n", "      <td>Marketable securities</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>100544000000.0</td>\n", "      <td>91479000000.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>us-gaap_PropertyPlantAndEquipmentNet</td>\n", "      <td>Property, Plant and Equipment</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>43715000000.0</td>\n", "      <td>45680000000.0</td>\n", "      <td>Property, plant and equipment, net</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>us-gaap_OtherAssetsNoncurrent</td>\n", "      <td>Other Assets</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>64758000000.0</td>\n", "      <td>74834000000.0</td>\n", "      <td>Other non-current assets</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>us-gaap_AssetsNoncurrent</td>\n", "      <td>Total Current Assets</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>209017000000.0</td>\n", "      <td>211993000000.0</td>\n", "      <td>Total non-current assets</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>us-gaap_Assets</td>\n", "      <td>Total Assets</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>************.0</td>\n", "      <td>************.0</td>\n", "      <td>Total assets</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>us-gaap_LiabilitiesAndStockholdersEquityAbstract</td>\n", "      <td>LIABILITIES AND SHAREHOLDERS’ EQUITY:</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>us-gaap_LiabilitiesCurrentAbstract</td>\n", "      <td>Current liabilities:</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>us-gaap_AccountsPayableCurrent</td>\n", "      <td>Accounts Payable</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>Accounts payable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>us-gaap_OtherLiabilitiesCurrent</td>\n", "      <td>Other Liabilities</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>Other current liabilities</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>us-gaap_ContractWithCustomerLiabilityCurrent</td>\n", "      <td>Deferred Revenue</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>**********.0</td>\n", "      <td>**********.0</td>\n", "      <td>Deferred revenue</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>us-gaap_CommercialPaper</td>\n", "      <td>Commercial paper</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5985000000.0</td>\n", "      <td>9967000000.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>us-gaap_LongTermDebtCurrent</td>\n", "      <td>Short-Term Debt</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>9822000000.0</td>\n", "      <td>10912000000.0</td>\n", "      <td>Term debt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>us-gaap_LiabilitiesCurrent</td>\n", "      <td>Total Current Liabilities</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>145308000000.0</td>\n", "      <td>176392000000.0</td>\n", "      <td>Total current liabilities</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>us-gaap_LiabilitiesNoncurrentAbstract</td>\n", "      <td>Non-current liabilities:</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>us-gaap_LongTermDebtNoncurrent</td>\n", "      <td>Long-Term Debt</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>95281000000.0</td>\n", "      <td>85750000000.0</td>\n", "      <td>Term debt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>us-gaap_OtherLiabilitiesNoncurrent</td>\n", "      <td>Other Liabilities</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>49848000000.0</td>\n", "      <td>45888000000.0</td>\n", "      <td>Other non-current liabilities</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>us-gaap_LiabilitiesNoncurrent</td>\n", "      <td>Total Current Liabilities</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>145129000000.0</td>\n", "      <td>131638000000.0</td>\n", "      <td>Total non-current liabilities</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>us-gaap_Liabilities</td>\n", "      <td>Total Liabilities</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>290437000000.0</td>\n", "      <td>308030000000.0</td>\n", "      <td>Total liabilities</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>us-gaap_CommitmentsAndContingencies</td>\n", "      <td>Commitments and contingencies</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>us-gaap_CommonStockSharesOutstanding</td>\n", "      <td>Common Stock Shares Outstanding</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.642679e+10</td>\n", "      <td>1.594342e+10</td>\n", "      <td>15550061000.0</td>\n", "      <td>15116786000.0</td>\n", "      <td>Common stock, shares outstanding (in shares)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>us-gaap_CommonStockSharesIssued</td>\n", "      <td>Common Stock Shares Issued</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>15550061000.0</td>\n", "      <td>15116786000.0</td>\n", "      <td>Common stock, shares issued (in shares)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>us-gaap_StockholdersEquityAbstract</td>\n", "      <td>Shareholders’ equity:</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>us-gaap_CommonStocksIncludingAdditionalPaidInC...</td>\n", "      <td>Common Stock</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>73812000000.0</td>\n", "      <td>83276000000.0</td>\n", "      <td>Common stock and additional paid-in capital, $...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>us-gaap_RetainedEarningsAccumulatedDeficit</td>\n", "      <td>Retained Earnings</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-214000000.0</td>\n", "      <td>-19154000000.0</td>\n", "      <td>Accumulated deficit</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>us-gaap_AccumulatedOtherComprehensiveIncomeLos...</td>\n", "      <td>Accumulated other comprehensive loss</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-11452000000.0</td>\n", "      <td>-7172000000.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>us-gaap_StockholdersEquity</td>\n", "      <td>Total Stockholders' Equity</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>6.309000e+10</td>\n", "      <td>5.067200e+10</td>\n", "      <td>62146000000.0</td>\n", "      <td>***********.0</td>\n", "      <td>Total shareholders’ equity</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>us-gaap_LiabilitiesAndStockholdersEquity</td>\n", "      <td>Total Liabilities and Stockholders' Equity</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>************.0</td>\n", "      <td>************.0</td>\n", "      <td>Total liabilities and shareholders’ equity</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "execution_count": 8}, {"metadata": {}, "cell_type": "markdown", "source": "## Cash Flow Statement", "id": "edfd0d31b88f3c59"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-15T14:23:37.481943Z", "start_time": "2025-03-15T14:23:37.430622Z"}}, "cell_type": "code", "source": ["cashflow_statement = xbrl.statements.cashflow_statement()\n", "cashflow_statement"], "id": "251003675e08c9a9", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                                 CONSOLIDATEDSTATEMENTSOFCASHFLOWS (Standardized)                                  \u001b[0m\n", "\u001b[3m                                  \u001b[0m\u001b[1;3mYear Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except per share data)\u001b[0m\u001b[3m                                  \u001b[0m\n", "                                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1mLine Item                                                         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 30, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 24, 2022\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "    Cash, cash equivalents, and restricted cash and cash                                                           \n", "  equivalents, ending balances                                                                                     \n", "    Operating activities:                                                                                          \n", "      Net Income                                                            $93,736        $96,995        $99,803  \n", "      Adjustments to reconcile net income to cash generated by                                                     \n", "  operating activities:                                                                                            \n", "        Depreciation and amortization                                       $11,445        $11,519        $11,104  \n", "        Share-based compensation expense                                    $11,688        $10,833         $9,038  \n", "        Other                                                                $2,266         $2,227       $(1,006)  \n", "        Changes in operating assets and liabilities:                                                               \n", "          Accounts receivable, net                                           $3,788         $1,688         $1,823  \n", "          Vendor non-trade receivables                                       $1,356       $(1,271)         $7,520  \n", "          Inventories                                                        $1,046         $1,618       $(1,484)  \n", "          Other current and non-current assets                              $11,731         $5,684         $6,499  \n", "          Accounts Payable                                                   $6,020       $(1,889)         $9,448  \n", "          Other current and non-current liabilities                         $15,552         $3,031         $6,110  \n", "      Net Cash from Operating Activities                                   $118,254       $110,543       $122,151  \n", "    Investing activities:                                                                                          \n", "      Purchases of marketable securities                                    $48,656        $29,513        $76,923  \n", "      Proceeds from Maturities, Prepayments and Calls of Securities         $51,211        $39,686        $29,917  \n", "      Proceeds from Sale of Debt Securities,                                $11,135         $5,828        $37,446  \n", "      Payments for Property, Plant and Equipment                             $9,447        $10,959        $10,708  \n", "      Other                                                                  $1,308         $1,337         $2,086  \n", "      Net Cash from Investing Activities                                     $2,935         $3,705      $(22,354)  \n", "    Financing activities:                                                                                          \n", "      Tax Withholding for Share-Based Compensation                           $5,441         $5,431         $6,223  \n", "      Payments of Dividends                                                 $15,234        $15,025        $14,841  \n", "      Repurchases of common stock                                           $94,949        $77,550        $89,402  \n", "      Proceeds from Issuance of Long-Term Debt                                     \u0014         $5,228         $5,465  \n", "      Repayments of term debt                                                $9,958        $11,151         $9,543  \n", "      Proceeds from (Repayments of) Commercial Paper                         $3,960       $(3,978)         $3,955  \n", "      Other                                                                  $(361)         $(581)         $(160)  \n", "      Net Cash from Financing Activities                                 $(121,983)     $(108,488)     $(110,749)  \n", "    Net Change in Cash                                                       $(794)         $5,760      $(10,952)  \n", "    Cash, cash equivalents, and restricted cash and cash                                                           \n", "  equivalents, ending balances                                                                                     \n", "    Supplemental cash flow disclosure:                                                                             \n", "      Income Tax Expense                                                    $26,102        $18,679        $19,573  \n", "                                                                                                                   "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-15T14:02:56.353333Z", "start_time": "2025-03-15T14:02:56.266587Z"}}, "cell_type": "code", "source": "cashflow_statement.to_dataframe()", "id": "3f4e5c7b74bfcb01", "outputs": [{"data": {"text/plain": ["                                              concept  \\\n", "0                us-gaap_StatementOfCashFlowsAbstract   \n", "1   us-gaap_CashCashEquivalentsRestrictedCashAndRe...   \n", "2   us-gaap_NetCashProvidedByUsedInOperatingActivi...   \n", "3                               us-gaap_NetIncomeLoss   \n", "4   us-gaap_AdjustmentsToReconcileNetIncomeLossToC...   \n", "5        us-gaap_DepreciationDepletionAndAmortization   \n", "6                      us-gaap_ShareBasedCompensation   \n", "7                   us-gaap_OtherNoncashIncomeExpense   \n", "8   us-gaap_IncreaseDecreaseInOperatingCapitalAbst...   \n", "9        us-gaap_IncreaseDecreaseInAccountsReceivable   \n", "10         us-gaap_IncreaseDecreaseInOtherReceivables   \n", "11              us-gaap_IncreaseDecreaseInInventories   \n", "12     us-gaap_IncreaseDecreaseInOtherOperatingAssets   \n", "13          us-gaap_IncreaseDecreaseInAccountsPayable   \n", "14  us-gaap_IncreaseDecreaseInOtherOperatingLiabil...   \n", "15  us-gaap_NetCashProvidedByUsedInOperatingActivi...   \n", "16  us-gaap_NetCashProvidedByUsedInInvestingActivi...   \n", "17  us-gaap_PaymentsToAcquireAvailableForSaleSecur...   \n", "18  us-gaap_ProceedsFromMaturitiesPrepaymentsAndCa...   \n", "19  us-gaap_ProceedsFromSaleOfAvailableForSaleSecu...   \n", "20  us-gaap_PaymentsToAcquirePropertyPlantAndEquip...   \n", "21  us-gaap_PaymentsForProceedsFromOtherInvestingA...   \n", "22  us-gaap_NetCashProvidedByUsedInInvestingActivi...   \n", "23  us-gaap_NetCashProvidedByUsedInFinancingActivi...   \n", "24  us-gaap_PaymentsRelatedToTaxWithholdingForShar...   \n", "25                        us-gaap_PaymentsOfDividends   \n", "26         us-gaap_PaymentsForRepurchaseOfCommonStock   \n", "27         us-gaap_ProceedsFromIssuanceOfLongTermDebt   \n", "28                   us-gaap_RepaymentsOfLongTermDebt   \n", "29    us-gaap_ProceedsFromRepaymentsOfCommercialPaper   \n", "30  us-gaap_ProceedsFromPaymentsForOtherFinancingA...   \n", "31  us-gaap_NetCashProvidedByUsedInFinancingActivi...   \n", "32  us-gaap_CashCashEquivalentsRestrictedCashAndRe...   \n", "33  us-gaap_CashCashEquivalentsRestrictedCashAndRe...   \n", "34    us-gaap_SupplementalCashFlowInformationAbstract   \n", "35                         us-gaap_IncomeTaxesPaidNet   \n", "\n", "                                                label  level  is_abstract  \\\n", "0                  Statement of Cash Flows [Abstract]      0        False   \n", "1   Cash, cash equivalents, and restricted cash an...      1        False   \n", "2                               Operating activities:      1        False   \n", "3                                          Net Income      2        False   \n", "4   Adjustments to reconcile net income to cash ge...      2        False   \n", "5                       Depreciation and amortization      3        False   \n", "6                    Share-based compensation expense      3        False   \n", "7                                               Other      3        False   \n", "8        Changes in operating assets and liabilities:      3        False   \n", "9                            Accounts receivable, net      4        False   \n", "10                       Vendor non-trade receivables      4        False   \n", "11                                        Inventories      4        False   \n", "12               Other current and non-current assets      4        False   \n", "13                                   Accounts Payable      4        False   \n", "14          Other current and non-current liabilities      4        False   \n", "15             Cash generated by operating activities      2        False   \n", "16                              Investing activities:      1        False   \n", "17                 Purchases of marketable securities      2        False   \n", "18  Proceeds from maturities of marketable securities      2        False   \n", "19       Proceeds from sales of marketable securities      2        False   \n", "20  Payments for acquisition of property, plant an...      2        False   \n", "21                                              Other      2        False   \n", "22   Cash generated by/(used in) investing activities      2        False   \n", "23                              Financing activities:      1        False   \n", "24  Payments for taxes related to net share settle...      2        False   \n", "25    Payments for dividends and dividend equivalents      2        False   \n", "26                        Repurchases of common stock      2        False   \n", "27           Proceeds from issuance of term debt, net      2        False   \n", "28                            Repayments of term debt      2        False   \n", "29  Proceeds from/(Repayments of) commercial paper...      2        False   \n", "30                                              Other      2        False   \n", "31                  Cash used in financing activities      2        False   \n", "32  Increase/(Decrease) in cash, cash equivalents,...      1        False   \n", "33  Cash, cash equivalents, and restricted cash an...      1        False   \n", "34                 Supplemental cash flow disclosure:      1        False   \n", "35                    Cash paid for income taxes, net      2        False   \n", "\n", "    has_values  2021-09-26_2022-09-24  2022-09-25_2023-09-30  \\\n", "0        False                    NaN                    NaN   \n", "1         True                    NaN                    NaN   \n", "2        False                    NaN                    NaN   \n", "3         True           9.980300e+10           9.699500e+10   \n", "4        False                    NaN                    NaN   \n", "5         True           1.110400e+10           1.151900e+10   \n", "6         True           9.038000e+09           1.083300e+10   \n", "7         True          -1.006000e+09           2.227000e+09   \n", "8        False                    NaN                    NaN   \n", "9         True           1.823000e+09           1.688000e+09   \n", "10        True           7.520000e+09          -1.271000e+09   \n", "11        True          -1.484000e+09           1.618000e+09   \n", "12        True           6.499000e+09           5.684000e+09   \n", "13        True           9.448000e+09          -1.889000e+09   \n", "14        True           6.110000e+09           3.031000e+09   \n", "15        True           1.221510e+11           1.105430e+11   \n", "16       False                    NaN                    NaN   \n", "17        True           7.692300e+10           2.951300e+10   \n", "18        True           2.991700e+10           3.968600e+10   \n", "19        True           3.744600e+10           5.828000e+09   \n", "20        True           1.070800e+10           1.095900e+10   \n", "21        True           2.086000e+09           1.337000e+09   \n", "22        True          -2.235400e+10           3.705000e+09   \n", "23       False                    NaN                    NaN   \n", "24        True           6.223000e+09           5.431000e+09   \n", "25        True           1.484100e+10           1.502500e+10   \n", "26        True           8.940200e+10           7.755000e+10   \n", "27        True           5.465000e+09           5.228000e+09   \n", "28        True           9.543000e+09           1.115100e+10   \n", "29        True           3.955000e+09          -3.978000e+09   \n", "30        True          -1.600000e+08          -5.810000e+08   \n", "31        True          -1.107490e+11          -1.084880e+11   \n", "32        True          -1.095200e+10           5.760000e+09   \n", "33        True                    NaN                    NaN   \n", "34       False                    NaN                    NaN   \n", "35        True           1.957300e+10           1.867900e+10   \n", "\n", "    2023-10-01_2024-09-28    2021-09-25    2022-09-24    2023-09-30  \\\n", "0                     NaN           NaN           NaN           NaN   \n", "1                     NaN  3.592900e+10  2.497700e+10  3.073700e+10   \n", "2                     NaN           NaN           NaN           NaN   \n", "3            9.373600e+10           NaN           NaN           NaN   \n", "4                     NaN           NaN           NaN           NaN   \n", "5            1.144500e+10           NaN           NaN           NaN   \n", "6            1.168800e+10           NaN           NaN           NaN   \n", "7            2.266000e+09           NaN           NaN           NaN   \n", "8                     NaN           NaN           NaN           NaN   \n", "9            3.788000e+09           NaN           NaN           NaN   \n", "10           1.356000e+09           NaN           NaN           NaN   \n", "11           1.046000e+09           NaN           NaN           NaN   \n", "12           1.173100e+10           NaN           NaN           NaN   \n", "13           6.020000e+09           NaN           NaN           NaN   \n", "14           1.555200e+10           NaN           NaN           NaN   \n", "15           1.182540e+11           NaN           NaN           NaN   \n", "16                    NaN           NaN           NaN           NaN   \n", "17           4.865600e+10           NaN           NaN           NaN   \n", "18           5.121100e+10           NaN           NaN           NaN   \n", "19           1.113500e+10           NaN           NaN           NaN   \n", "20           9.447000e+09           NaN           NaN           NaN   \n", "21           1.308000e+09           NaN           NaN           NaN   \n", "22           2.935000e+09           NaN           NaN           NaN   \n", "23                    NaN           NaN           NaN           NaN   \n", "24           5.441000e+09           NaN           NaN           NaN   \n", "25           1.523400e+10           NaN           NaN           NaN   \n", "26           9.494900e+10           NaN           NaN           NaN   \n", "27           0.000000e+00           NaN           NaN           NaN   \n", "28           9.958000e+09           NaN           NaN           NaN   \n", "29           3.960000e+09           NaN           NaN           NaN   \n", "30          -3.610000e+08           NaN           NaN           NaN   \n", "31          -1.219830e+11           NaN           NaN           NaN   \n", "32          -7.940000e+08           NaN           NaN           NaN   \n", "33                    NaN  3.592900e+10  2.497700e+10  3.073700e+10   \n", "34                    NaN           NaN           NaN           NaN   \n", "35           2.610200e+10           NaN           NaN           NaN   \n", "\n", "      2024-09-28    original_label  \n", "0            NaN               NaN  \n", "1   2.994300e+10               NaN  \n", "2            NaN               NaN  \n", "3            NaN        Net income  \n", "4            NaN               NaN  \n", "5            NaN               NaN  \n", "6            NaN               NaN  \n", "7            NaN               NaN  \n", "8            NaN               NaN  \n", "9            NaN               NaN  \n", "10           NaN               NaN  \n", "11           NaN               NaN  \n", "12           NaN               NaN  \n", "13           NaN  Accounts payable  \n", "14           NaN               NaN  \n", "15           NaN               NaN  \n", "16           NaN               NaN  \n", "17           NaN               NaN  \n", "18           NaN               NaN  \n", "19           NaN               NaN  \n", "20           NaN               NaN  \n", "21           NaN               NaN  \n", "22           NaN               NaN  \n", "23           NaN               NaN  \n", "24           NaN               NaN  \n", "25           NaN               NaN  \n", "26           NaN               NaN  \n", "27           NaN               NaN  \n", "28           NaN               NaN  \n", "29           NaN               NaN  \n", "30           NaN               NaN  \n", "31           NaN               NaN  \n", "32           NaN               NaN  \n", "33  2.994300e+10               NaN  \n", "34           NaN               NaN  \n", "35           NaN               NaN  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>level</th>\n", "      <th>is_abstract</th>\n", "      <th>has_values</th>\n", "      <th>2021-09-26_2022-09-24</th>\n", "      <th>2022-09-25_2023-09-30</th>\n", "      <th>2023-10-01_2024-09-28</th>\n", "      <th>2021-09-25</th>\n", "      <th>2022-09-24</th>\n", "      <th>2023-09-30</th>\n", "      <th>2024-09-28</th>\n", "      <th>original_label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap_StatementOfCashFlowsAbstract</td>\n", "      <td>Statement of Cash Flows [Abstract]</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap_CashCashEquivalentsRestrictedCashAndRe...</td>\n", "      <td>Cash, cash equivalents, and restricted cash an...</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.592900e+10</td>\n", "      <td>2.497700e+10</td>\n", "      <td>3.073700e+10</td>\n", "      <td>2.994300e+10</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInOperatingActivi...</td>\n", "      <td>Operating activities:</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap_NetIncomeLoss</td>\n", "      <td>Net Income</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>9.980300e+10</td>\n", "      <td>9.699500e+10</td>\n", "      <td>9.373600e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Net income</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap_AdjustmentsToReconcileNetIncomeLossToC...</td>\n", "      <td>Adjustments to reconcile net income to cash ge...</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap_DepreciationDepletionAndAmortization</td>\n", "      <td>Depreciation and amortization</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.110400e+10</td>\n", "      <td>1.151900e+10</td>\n", "      <td>1.144500e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap_ShareBasedCompensation</td>\n", "      <td>Share-based compensation expense</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>9.038000e+09</td>\n", "      <td>1.083300e+10</td>\n", "      <td>1.168800e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap_OtherNoncashIncomeExpense</td>\n", "      <td>Other</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>-1.006000e+09</td>\n", "      <td>2.227000e+09</td>\n", "      <td>2.266000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap_IncreaseDecreaseInOperatingCapitalAbst...</td>\n", "      <td>Changes in operating assets and liabilities:</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap_IncreaseDecreaseInAccountsReceivable</td>\n", "      <td>Accounts receivable, net</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.823000e+09</td>\n", "      <td>1.688000e+09</td>\n", "      <td>3.788000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>us-gaap_IncreaseDecreaseInOtherReceivables</td>\n", "      <td>Vendor non-trade receivables</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>7.520000e+09</td>\n", "      <td>-1.271000e+09</td>\n", "      <td>1.356000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>us-gaap_IncreaseDecreaseInInventories</td>\n", "      <td>Inventories</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>-1.484000e+09</td>\n", "      <td>1.618000e+09</td>\n", "      <td>1.046000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>us-gaap_IncreaseDecreaseInOtherOperatingAssets</td>\n", "      <td>Other current and non-current assets</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>6.499000e+09</td>\n", "      <td>5.684000e+09</td>\n", "      <td>1.173100e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>us-gaap_IncreaseDecreaseInAccountsPayable</td>\n", "      <td>Accounts Payable</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>9.448000e+09</td>\n", "      <td>-1.889000e+09</td>\n", "      <td>6.020000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Accounts payable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>us-gaap_IncreaseDecreaseInOtherOperatingLiabil...</td>\n", "      <td>Other current and non-current liabilities</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>6.110000e+09</td>\n", "      <td>3.031000e+09</td>\n", "      <td>1.555200e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInOperatingActivi...</td>\n", "      <td>Cash generated by operating activities</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.221510e+11</td>\n", "      <td>1.105430e+11</td>\n", "      <td>1.182540e+11</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInInvestingActivi...</td>\n", "      <td>Investing activities:</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>us-gaap_PaymentsToAcquireAvailableForSaleSecur...</td>\n", "      <td>Purchases of marketable securities</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>7.692300e+10</td>\n", "      <td>2.951300e+10</td>\n", "      <td>4.865600e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>us-gaap_ProceedsFromMaturitiesPrepaymentsAndCa...</td>\n", "      <td>Proceeds from maturities of marketable securities</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2.991700e+10</td>\n", "      <td>3.968600e+10</td>\n", "      <td>5.121100e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>us-gaap_ProceedsFromSaleOfAvailableForSaleSecu...</td>\n", "      <td>Proceeds from sales of marketable securities</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>3.744600e+10</td>\n", "      <td>5.828000e+09</td>\n", "      <td>1.113500e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>us-gaap_PaymentsToAcquirePropertyPlantAndEquip...</td>\n", "      <td>Payments for acquisition of property, plant an...</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.070800e+10</td>\n", "      <td>1.095900e+10</td>\n", "      <td>9.447000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>us-gaap_PaymentsForProceedsFromOtherInvestingA...</td>\n", "      <td>Other</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2.086000e+09</td>\n", "      <td>1.337000e+09</td>\n", "      <td>1.308000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInInvestingActivi...</td>\n", "      <td>Cash generated by/(used in) investing activities</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>-2.235400e+10</td>\n", "      <td>3.705000e+09</td>\n", "      <td>2.935000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInFinancingActivi...</td>\n", "      <td>Financing activities:</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>us-gaap_PaymentsRelatedToTaxWithholdingForShar...</td>\n", "      <td>Payments for taxes related to net share settle...</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>6.223000e+09</td>\n", "      <td>5.431000e+09</td>\n", "      <td>5.441000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>us-gaap_PaymentsOfDividends</td>\n", "      <td>Payments for dividends and dividend equivalents</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.484100e+10</td>\n", "      <td>1.502500e+10</td>\n", "      <td>1.523400e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>us-gaap_PaymentsForRepurchaseOfCommonStock</td>\n", "      <td>Repurchases of common stock</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>8.940200e+10</td>\n", "      <td>7.755000e+10</td>\n", "      <td>9.494900e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>us-gaap_ProceedsFromIssuanceOfLongTermDebt</td>\n", "      <td>Proceeds from issuance of term debt, net</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>5.465000e+09</td>\n", "      <td>5.228000e+09</td>\n", "      <td>0.000000e+00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>us-gaap_RepaymentsOfLongTermDebt</td>\n", "      <td>Repayments of term debt</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>9.543000e+09</td>\n", "      <td>1.115100e+10</td>\n", "      <td>9.958000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>us-gaap_ProceedsFromRepaymentsOfCommercialPaper</td>\n", "      <td>Proceeds from/(Repayments of) commercial paper...</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>3.955000e+09</td>\n", "      <td>-3.978000e+09</td>\n", "      <td>3.960000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>us-gaap_ProceedsFromPaymentsForOtherFinancingA...</td>\n", "      <td>Other</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>-1.600000e+08</td>\n", "      <td>-5.810000e+08</td>\n", "      <td>-3.610000e+08</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>us-gaap_NetCashProvidedByUsedInFinancingActivi...</td>\n", "      <td>Cash used in financing activities</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>-1.107490e+11</td>\n", "      <td>-1.084880e+11</td>\n", "      <td>-1.219830e+11</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>us-gaap_CashCashEquivalentsRestrictedCashAndRe...</td>\n", "      <td>Increase/(Decrease) in cash, cash equivalents,...</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>-1.095200e+10</td>\n", "      <td>5.760000e+09</td>\n", "      <td>-7.940000e+08</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>us-gaap_CashCashEquivalentsRestrictedCashAndRe...</td>\n", "      <td>Cash, cash equivalents, and restricted cash an...</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.592900e+10</td>\n", "      <td>2.497700e+10</td>\n", "      <td>3.073700e+10</td>\n", "      <td>2.994300e+10</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>us-gaap_SupplementalCashFlowInformationAbstract</td>\n", "      <td>Supplemental cash flow disclosure:</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>us-gaap_IncomeTaxesPaidNet</td>\n", "      <td>Cash paid for income taxes, net</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1.957300e+10</td>\n", "      <td>1.867900e+10</td>\n", "      <td>2.610200e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "execution_count": 10}, {"metadata": {}, "cell_type": "markdown", "source": "## Older income statement", "id": "395c464ae554e560"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-15T16:13:59.369283Z", "start_time": "2025-03-15T16:13:58.581951Z"}}, "cell_type": "code", "source": ["filing = Filing(form='10-K', filing_date='2015-10-28', company='APPLE INC', cik=320193, accession_no='0001193125-15-356351')\n", "xbrl = XBRL.from_filing(filing)"], "id": "5dc905246e374448", "outputs": [], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-15T16:14:00.311596Z", "start_time": "2025-03-15T16:14:00.276788Z"}}, "cell_type": "code", "source": "xbrl.statements.income_statement()", "id": "94ff44f367927962", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                          StatementOfIncome (Standardized)                           \u001b[0m\n", "\u001b[3m                \u001b[0m\u001b[1;3mYear Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m                 \u001b[0m\n", "                                                                                     \n", " \u001b[1m \u001b[0m\u001b[1mLine Item                                          \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 26, 2015\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 27, 2014\u001b[0m\u001b[1m \u001b[0m \n", " ─────────────────────────────────────────────────────────────────────────────────── \n", "        Revenue                                             $233,715       $182,795  \n", "        Cost of Revenue                                     $140,089       $112,258  \n", "        Gross Profit                                         $93,626        $70,537  \n", "        Operating Expenses                                                           \n", "          Research and Development Expense                    $8,067         $6,041  \n", "          Selling, General and Administrative Expense        $14,329        $11,993  \n", "          Operating Expenses                                 $22,396        $18,034  \n", "        Operating Income                                     $71,230        $52,503  \n", "        Nonoperating Income/Expense                           $1,285           $980  \n", "        Income Before Tax                                    $72,515        $53,483  \n", "        Income Tax Expense                                   $19,121        $13,973  \n", "        Net Income                                           $53,394        $39,510  \n", "        Earnings per share:                                                          \n", "          Earnings Per Share                                    0.00           0.00  \n", "          Earnings Per Share (Diluted)                          0.00           0.00  \n", "        Shares used in computing earnings per share:                                 \n", "          Shares Outstanding                               5,753,421      6,085,572  \n", "          Shares Outstanding (Diluted)                     5,793,069      6,122,663  \n", "        Cash dividends declared per share                      $0.00          $0.00  \n", "                                                                                     "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "execution_count": 7}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-15T16:19:38.721251Z", "start_time": "2025-03-15T16:19:38.648957Z"}}, "cell_type": "code", "source": ["df = xbrl.statements.income_statement().to_dataframe()\n", "df[['concept', 'label', '2015-03-29_2015-06-27']]"], "id": "2af75d3ce1664899", "outputs": [{"data": {"text/plain": ["                                              concept  \\\n", "0                     us-gaap_IncomeStatementAbstract   \n", "1                              us-gaap_StatementTable   \n", "2                                 dei_LegalEntityAxis   \n", "3                                    dei_EntityDomain   \n", "4                          us-gaap_StatementLineItems   \n", "5                             us-gaap_SalesRevenueNet   \n", "6                  us-gaap_CostOfGoodsAndServicesSold   \n", "7                                 us-gaap_GrossProfit   \n", "8                   us-gaap_OperatingExpensesAbstract   \n", "9               us-gaap_ResearchAndDevelopmentExpense   \n", "10     us-gaap_SellingGeneralAndAdministrativeExpense   \n", "11                          us-gaap_OperatingExpenses   \n", "12                        us-gaap_OperatingIncomeLoss   \n", "13                  us-gaap_NonoperatingIncomeExpense   \n", "14  us-gaap_IncomeLossFromContinuingOperationsBefo...   \n", "15                    us-gaap_IncomeTaxExpenseBenefit   \n", "16                              us-gaap_NetIncomeLoss   \n", "17                   us-gaap_EarningsPerShareAbstract   \n", "18                      us-gaap_EarningsPerShareBasic   \n", "19                    us-gaap_EarningsPerShareDiluted   \n", "20  us-gaap_WeightedAverageNumberOfSharesOutstandi...   \n", "21  us-gaap_WeightedAverageNumberOfSharesOutstandi...   \n", "22  us-gaap_WeightedAverageNumberOfDilutedSharesOu...   \n", "23       us-gaap_CommonStockDividendsPerShareDeclared   \n", "\n", "                                           label  2015-03-29_2015-06-27  \n", "0                    Income Statement [Abstract]                    NaN  \n", "1                              Statement [Table]                    NaN  \n", "2                            Legal Entity [Axis]                    NaN  \n", "3                                Entity [Domain]                    NaN  \n", "4                         Statement [Line Items]                    NaN  \n", "5                                        Revenue           4.960500e+10  \n", "6                                Cost of Revenue                    NaN  \n", "7                                   Gross Profit           1.968100e+10  \n", "8                             Operating Expenses                    NaN  \n", "9               Research and Development Expense                    NaN  \n", "10   Selling, General and Administrative Expense                    NaN  \n", "11                            Operating Expenses                    NaN  \n", "12                              Operating Income                    NaN  \n", "13                   Nonoperating Income/Expense                    NaN  \n", "14                             Income Before Tax                    NaN  \n", "15                            Income Tax Expense                    NaN  \n", "16                                    Net Income           1.067700e+10  \n", "17                           Earnings per share:                    NaN  \n", "18                            Earnings Per Share           1.860000e+00  \n", "19                  Earnings Per Share (Diluted)           1.850000e+00  \n", "20  Shares used in computing earnings per share:                    NaN  \n", "21                            Shares Outstanding                    NaN  \n", "22                  Shares Outstanding (Diluted)                    NaN  \n", "23             Cash dividends declared per share           5.200000e-01  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>2015-03-29_2015-06-27</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap_IncomeStatementAbstract</td>\n", "      <td>Income Statement [Abstract]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap_StatementTable</td>\n", "      <td>Statement [Table]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>dei_LegalEntityAxis</td>\n", "      <td>Legal Entity [Axis]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>dei_EntityDomain</td>\n", "      <td>Entity [Domain]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap_StatementLineItems</td>\n", "      <td>Statement [Line Items]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap_SalesRevenueNet</td>\n", "      <td>Revenue</td>\n", "      <td>4.960500e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap_CostOfGoodsAndServicesSold</td>\n", "      <td>Cost of Revenue</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap_GrossProfit</td>\n", "      <td>Gross Profit</td>\n", "      <td>1.968100e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap_OperatingExpensesAbstract</td>\n", "      <td>Operating Expenses</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap_ResearchAndDevelopmentExpense</td>\n", "      <td>Research and Development Expense</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>us-gaap_SellingGeneralAndAdministrativeExpense</td>\n", "      <td>Selling, General and Administrative Expense</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>us-gaap_OperatingExpenses</td>\n", "      <td>Operating Expenses</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>us-gaap_OperatingIncomeLoss</td>\n", "      <td>Operating Income</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>us-gaap_NonoperatingIncomeExpense</td>\n", "      <td>Nonoperating Income/Expense</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>us-gaap_IncomeLossFromContinuingOperationsBefo...</td>\n", "      <td>Income Before Tax</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>us-gaap_IncomeTaxExpenseBenefit</td>\n", "      <td>Income Tax Expense</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>us-gaap_NetIncomeLoss</td>\n", "      <td>Net Income</td>\n", "      <td>1.067700e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>us-gaap_EarningsPerShareAbstract</td>\n", "      <td>Earnings per share:</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>us-gaap_EarningsPerShareBasic</td>\n", "      <td>Earnings Per Share</td>\n", "      <td>1.860000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>us-gaap_EarningsPerShareDiluted</td>\n", "      <td>Earnings Per Share (Diluted)</td>\n", "      <td>1.850000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>us-gaap_WeightedAverageNumberOfSharesOutstandi...</td>\n", "      <td>Shares used in computing earnings per share:</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>us-gaap_WeightedAverageNumberOfSharesOutstandi...</td>\n", "      <td>Shares Outstanding</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>us-gaap_WeightedAverageNumberOfDilutedSharesOu...</td>\n", "      <td>Shares Outstanding (Diluted)</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>us-gaap_CommonStockDividendsPerShareDeclared</td>\n", "      <td>Cash dividends declared per share</td>\n", "      <td>5.200000e-01</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "execution_count": 15}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}