{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "# XBRL Concepts", "id": "ef77936064c67f2"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": 1, "source": "from edgar import *", "id": "initial_id"}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-22T20:33:34.837901Z", "start_time": "2025-05-22T20:33:34.176538Z"}}, "cell_type": "code", "source": ["c = Company(\"RTX\")\n", "tenk = c.latest(\"10-K\")\n", "xb = XBRL.from_filing(tenk)\n", "df = xb.query().by_text(\"NetIncome\").to_dataframe()\n", "df\n"], "id": "8d79785bbc8e6424", "outputs": [{"data": {"text/plain": ["                                              concept  \\\n", "0                               us-gaap:NetIncomeLoss   \n", "1                               us-gaap:NetIncomeLoss   \n", "2                               us-gaap:NetIncomeLoss   \n", "3   us-gaap:NetIncomeLossAttributableToNoncontroll...   \n", "4   us-gaap:NetIncomeLossAttributableToNoncontroll...   \n", "5   us-gaap:NetIncomeLossAttributableToNoncontroll...   \n", "6   us-gaap:NetIncomeLossAttributableToRedeemableN...   \n", "7   us-gaap:NetIncomeLossAttributableToRedeemableN...   \n", "8   us-gaap:NetIncomeLossAttributableToRedeemableN...   \n", "9                               us-gaap:NetIncomeLoss   \n", "10                              us-gaap:NetIncomeLoss   \n", "11                              us-gaap:NetIncomeLoss   \n", "12                              us-gaap:NetIncomeLoss   \n", "13                              us-gaap:NetIncomeLoss   \n", "14                              us-gaap:NetIncomeLoss   \n", "15  rtx:EstimatedNetIncomeChargeFromUnfavorableTax...   \n", "16  rtx:EstimatedNetIncomeChargeFromUnfavorableTax...   \n", "\n", "                                                label       value  \\\n", "0       Net income attributable to common shareowners  4774000000   \n", "1       Net income attributable to common shareowners  3195000000   \n", "2       Net income attributable to common shareowners  5197000000   \n", "3                                          Net income   *********   \n", "4                                          Net income   *********   \n", "5                                          Net income   *********   \n", "6   Redeemable Noncontrolling Interest in subsidia...     8000000   \n", "7   Redeemable Noncontrolling Interest in subsidia...     8000000   \n", "8   Redeemable Noncontrolling Interest in subsidia...     8000000   \n", "9       Net income attributable to common shareowners  4774000000   \n", "10      Net income attributable to common shareowners  4774000000   \n", "11      Net income attributable to common shareowners  3195000000   \n", "12      Net income attributable to common shareowners  3195000000   \n", "13      Net income attributable to common shareowners  5197000000   \n", "14      Net income attributable to common shareowners  5197000000   \n", "15  Estimated Net Income Charge from Unfavorable T...    30000000   \n", "16  Estimated Net Income Charge from Unfavorable T...    50000000   \n", "\n", "    numeric_value period_start  period_end  period_end  period_end  \\\n", "0    4.774000e+09   2024-01-01  2024-12-31         NaN  2024-12-31   \n", "1    3.195000e+09   2023-01-01  2023-12-31         NaN  2023-12-31   \n", "2    5.197000e+09   2022-01-01  2022-12-31         NaN  2022-12-31   \n", "3    2.390000e+08   2024-01-01  2024-12-31         NaN  2024-12-31   \n", "4    1.850000e+08   2023-01-01  2023-12-31         NaN  2023-12-31   \n", "5    1.110000e+08   2022-01-01  2022-12-31         NaN  2022-12-31   \n", "6    8.000000e+06   2024-01-01  2024-12-31         NaN  2024-12-31   \n", "7    8.000000e+06   2023-01-01  2023-12-31         NaN  2023-12-31   \n", "8    8.000000e+06   2022-01-01  2022-12-31         NaN  2022-12-31   \n", "9    4.774000e+09   2024-01-01  2024-12-31         NaN  2024-12-31   \n", "10   4.774000e+09   2024-01-01  2024-12-31         NaN  2024-12-31   \n", "11   3.195000e+09   2023-01-01  2023-12-31         NaN  2023-12-31   \n", "12   3.195000e+09   2023-01-01  2023-12-31         NaN  2023-12-31   \n", "13   5.197000e+09   2022-01-01  2022-12-31         NaN  2022-12-31   \n", "14   5.197000e+09   2022-01-01  2022-12-31         NaN  2022-12-31   \n", "15   3.000000e+07          NaN         NaN  2025-01-07         NaN   \n", "16   5.000000e+07          NaN         NaN  2025-01-07         NaN   \n", "\n", "    period_end  period_end  ...  period_end  period_end  period_end decimals  \\\n", "0          NaN  2024-12-31  ...         NaN  2024-12-31         NaN       -6   \n", "1          NaN  2023-12-31  ...         NaN  2023-12-31         NaN       -6   \n", "2          NaN  2022-12-31  ...         NaN  2022-12-31         NaN       -6   \n", "3          NaN  2024-12-31  ...         NaN  2024-12-31         NaN       -6   \n", "4          NaN  2023-12-31  ...         NaN  2023-12-31         NaN       -6   \n", "5          NaN  2022-12-31  ...         NaN  2022-12-31         NaN       -6   \n", "6          NaN  2024-12-31  ...         NaN  2024-12-31         NaN       -6   \n", "7          NaN  2023-12-31  ...         NaN  2023-12-31         NaN       -6   \n", "8          NaN  2022-12-31  ...         NaN  2022-12-31         NaN       -6   \n", "9          NaN  2024-12-31  ...         NaN  2024-12-31         NaN       -6   \n", "10         NaN  2024-12-31  ...         NaN  2024-12-31         NaN       -6   \n", "11         NaN  2023-12-31  ...         NaN  2023-12-31         NaN       -6   \n", "12         NaN  2023-12-31  ...         NaN  2023-12-31         NaN       -6   \n", "13         NaN  2022-12-31  ...         NaN  2022-12-31         NaN       -6   \n", "14         NaN  2022-12-31  ...         NaN  2022-12-31         NaN       -6   \n", "15  2025-01-07         NaN  ...  2025-01-07         NaN  2025-01-07       -6   \n", "16  2025-01-07         NaN  ...  2025-01-07         NaN  2025-01-07       -6   \n", "\n", "   unit_ref dim_us-gaap_StatementEquityComponentsAxis     statement_type  \\\n", "0       usd            us-gaap:RetainedEarningsMember    IncomeStatement   \n", "1       usd            us-gaap:RetainedEarningsMember    IncomeStatement   \n", "2       usd            us-gaap:RetainedEarningsMember    IncomeStatement   \n", "3       usd      us-gaap:NoncontrollingInterestMember  StatementOfEquity   \n", "4       usd      us-gaap:NoncontrollingInterestMember  StatementOfEquity   \n", "5       usd      us-gaap:NoncontrollingInterestMember  StatementOfEquity   \n", "6       usd      us-gaap:NoncontrollingInterestMember  StatementOfEquity   \n", "7       usd      us-gaap:NoncontrollingInterestMember  StatementOfEquity   \n", "8       usd      us-gaap:NoncontrollingInterestMember  StatementOfEquity   \n", "9       usd                                       NaN    IncomeStatement   \n", "10      usd                                       NaN    IncomeStatement   \n", "11      usd                                       NaN    IncomeStatement   \n", "12      usd                                       NaN    IncomeStatement   \n", "13      usd                                       NaN    IncomeStatement   \n", "14      usd                                       NaN    IncomeStatement   \n", "15      usd                                       NaN                NaN   \n", "16      usd                                       NaN                NaN   \n", "\n", "                                       statement_role  dim_srt_RangeAxis  \\\n", "0   http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "1   http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "2   http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "3   http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "4   http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "5   http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "6   http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "7   http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "8   http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "9   http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "10  http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "11  http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "12  http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "13  http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "14  http://www.rtx.com/role/ConsolidatedStatemento...                NaN   \n", "15                                                NaN  srt:MinimumMember   \n", "16                                                NaN  srt:MaximumMember   \n", "\n", "   dim_srt_StatementScenarioAxis  \n", "0                            NaN  \n", "1                            NaN  \n", "2                            NaN  \n", "3                            NaN  \n", "4                            NaN  \n", "5                            NaN  \n", "6                            NaN  \n", "7                            NaN  \n", "8                            NaN  \n", "9                            NaN  \n", "10                           NaN  \n", "11                           NaN  \n", "12                           NaN  \n", "13                           NaN  \n", "14                           NaN  \n", "15    srt:ScenarioForecastMember  \n", "16    srt:ScenarioForecastMember  \n", "\n", "[17 rows x 28 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>value</th>\n", "      <th>numeric_value</th>\n", "      <th>period_start</th>\n", "      <th>period_end</th>\n", "      <th>period_end</th>\n", "      <th>period_end</th>\n", "      <th>period_end</th>\n", "      <th>period_end</th>\n", "      <th>...</th>\n", "      <th>period_end</th>\n", "      <th>period_end</th>\n", "      <th>period_end</th>\n", "      <th>decimals</th>\n", "      <th>unit_ref</th>\n", "      <th>dim_us-gaap_StatementEquityComponentsAxis</th>\n", "      <th>statement_type</th>\n", "      <th>statement_role</th>\n", "      <th>dim_srt_RangeAxis</th>\n", "      <th>dim_srt_StatementScenarioAxis</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap:NetIncomeLoss</td>\n", "      <td>Net income attributable to common shareowners</td>\n", "      <td>4774000000</td>\n", "      <td>4.774000e+09</td>\n", "      <td>2024-01-01</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>us-gaap:RetainedEarningsMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap:NetIncomeLoss</td>\n", "      <td>Net income attributable to common shareowners</td>\n", "      <td>3195000000</td>\n", "      <td>3.195000e+09</td>\n", "      <td>2023-01-01</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>us-gaap:RetainedEarningsMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap:NetIncomeLoss</td>\n", "      <td>Net income attributable to common shareowners</td>\n", "      <td>5197000000</td>\n", "      <td>5.197000e+09</td>\n", "      <td>2022-01-01</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>us-gaap:RetainedEarningsMember</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap:NetIncomeLossAttributableToNoncontroll...</td>\n", "      <td>Net income</td>\n", "      <td>*********</td>\n", "      <td>2.390000e+08</td>\n", "      <td>2024-01-01</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>us-gaap:NoncontrollingInterestMember</td>\n", "      <td>StatementOfEquity</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap:NetIncomeLossAttributableToNoncontroll...</td>\n", "      <td>Net income</td>\n", "      <td>*********</td>\n", "      <td>1.850000e+08</td>\n", "      <td>2023-01-01</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>us-gaap:NoncontrollingInterestMember</td>\n", "      <td>StatementOfEquity</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap:NetIncomeLossAttributableToNoncontroll...</td>\n", "      <td>Net income</td>\n", "      <td>*********</td>\n", "      <td>1.110000e+08</td>\n", "      <td>2022-01-01</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>us-gaap:NoncontrollingInterestMember</td>\n", "      <td>StatementOfEquity</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap:NetIncomeLossAttributableToRedeemableN...</td>\n", "      <td>Redeemable Noncontrolling Interest in subsidia...</td>\n", "      <td>8000000</td>\n", "      <td>8.000000e+06</td>\n", "      <td>2024-01-01</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>us-gaap:NoncontrollingInterestMember</td>\n", "      <td>StatementOfEquity</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap:NetIncomeLossAttributableToRedeemableN...</td>\n", "      <td>Redeemable Noncontrolling Interest in subsidia...</td>\n", "      <td>8000000</td>\n", "      <td>8.000000e+06</td>\n", "      <td>2023-01-01</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>us-gaap:NoncontrollingInterestMember</td>\n", "      <td>StatementOfEquity</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap:NetIncomeLossAttributableToRedeemableN...</td>\n", "      <td>Redeemable Noncontrolling Interest in subsidia...</td>\n", "      <td>8000000</td>\n", "      <td>8.000000e+06</td>\n", "      <td>2022-01-01</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>us-gaap:NoncontrollingInterestMember</td>\n", "      <td>StatementOfEquity</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap:NetIncomeLoss</td>\n", "      <td>Net income attributable to common shareowners</td>\n", "      <td>4774000000</td>\n", "      <td>4.774000e+09</td>\n", "      <td>2024-01-01</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>us-gaap:NetIncomeLoss</td>\n", "      <td>Net income attributable to common shareowners</td>\n", "      <td>4774000000</td>\n", "      <td>4.774000e+09</td>\n", "      <td>2024-01-01</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2024-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>us-gaap:NetIncomeLoss</td>\n", "      <td>Net income attributable to common shareowners</td>\n", "      <td>3195000000</td>\n", "      <td>3.195000e+09</td>\n", "      <td>2023-01-01</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>us-gaap:NetIncomeLoss</td>\n", "      <td>Net income attributable to common shareowners</td>\n", "      <td>3195000000</td>\n", "      <td>3.195000e+09</td>\n", "      <td>2023-01-01</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2023-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>us-gaap:NetIncomeLoss</td>\n", "      <td>Net income attributable to common shareowners</td>\n", "      <td>5197000000</td>\n", "      <td>5.197000e+09</td>\n", "      <td>2022-01-01</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>us-gaap:NetIncomeLoss</td>\n", "      <td>Net income attributable to common shareowners</td>\n", "      <td>5197000000</td>\n", "      <td>5.197000e+09</td>\n", "      <td>2022-01-01</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2022-12-31</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>IncomeStatement</td>\n", "      <td>http://www.rtx.com/role/ConsolidatedStatemento...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>rtx:EstimatedNetIncomeChargeFromUnfavorableTax...</td>\n", "      <td>Estimated Net Income Charge from Unfavorable T...</td>\n", "      <td>30000000</td>\n", "      <td>3.000000e+07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-07</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-07</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>2025-01-07</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-07</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>srt:MinimumMember</td>\n", "      <td>srt:ScenarioForecastMember</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>rtx:EstimatedNetIncomeChargeFromUnfavorableTax...</td>\n", "      <td>Estimated Net Income Charge from Unfavorable T...</td>\n", "      <td>50000000</td>\n", "      <td>5.000000e+07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-07</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-07</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>2025-01-07</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-07</td>\n", "      <td>-6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>srt:MaximumMember</td>\n", "      <td>srt:ScenarioForecastMember</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>17 rows × 28 columns</p>\n", "</div>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "execution_count": 8}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}