{"cells": [{"cell_type": "markdown", "id": "e85bee59-e4ef-4f79-a3f1-68d508a28c41", "metadata": {}, "source": ["# Initial Insider Transactions\n", "\n", "This notebook shows how to use edgartools to get access to initial insider transaction data\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/Initial-Insider-Transactions.ipynb)"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "!pip install edgartools seaborn", "id": "78bed481a90d9731"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": 1, "source": ["from edgar import *\n", "from tqdm.auto import tqdm\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "pd.options.display.max_columns = None\n", "pd.options.display.max_rows = 200\n", "\n", "set_identity('Mike<PERSON>@winery.com')"], "id": "8e71cbd3-d4c6-47a4-bc54-29fdcebfb9f0"}, {"cell_type": "markdown", "id": "0699c274-6157-455c-b255-21261bd5ede6", "metadata": {}, "source": ["## Getting Insider Filings\n", "\n", "To list insider filings on <PERSON> use `get_filings` and filter by form.\n", "\n", "The forms are \n", "\n", "- **Form 3** - Initial ownership\n", "- **Form 4** - Changes in ownership\n", "- **Form 5** - Annual changes in ownership"]}, {"cell_type": "code", "execution_count": 2, "id": "d4667b8d-158f-411a-8340-5be3041dca55", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1;38;5;245m╭─\u001b[0m\u001b[1;38;5;245m─────────────────────────────────────────────────\u001b[0m\u001b[1;38;5;245m SEC Filings \u001b[0m\u001b[1;38;5;245m─────────────────────────────────────────────────\u001b[0m\u001b[1;38;5;245m─╮\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m                                                                                                                 \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m   \u001b[1m \u001b[0m\u001b[1mForm   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m       CIK\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mTicker\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCompany                               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mFiling Date\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mAccession Number   \u001b[0m\u001b[1m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  ─────────────────────────────────────────────────────────────────────────────────────────────────────────────  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m    4        \u001b[2m \u001b[0m\u001b[2m   1232980\u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33m      \u001b[0m\u001b[33m \u001b[0m \u001b[1;32m \u001b[0m\u001b[1;32mABRAMSON STEVEN V                     \u001b[0m\u001b[1;32m \u001b[0m  2025-03-06   \u001b[2m \u001b[0m\u001b[2m0000950170-25-0345…\u001b[0m\u001b[2m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m   \u001b[1m \u001b[0m\u001b[1m4      \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m   1070494\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33mACAD  \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;32m \u001b[0m\u001b[1;32mACADIA PHARMACEUTICALS INC            \u001b[0m\u001b[1;32m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2025-03-06 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001415889-25-0072…\u001b[0m\u001b[1;2m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m    4        \u001b[2m \u001b[0m\u001b[2m    935036\u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mACIW  \u001b[0m\u001b[33m \u001b[0m \u001b[1;32m \u001b[0m\u001b[1;32mACI WORLDWIDE, INC.                   \u001b[0m\u001b[1;32m \u001b[0m  2025-03-06   \u001b[2m \u001b[0m\u001b[2m0001952333-25-0000…\u001b[0m\u001b[2m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m   \u001b[1m \u001b[0m\u001b[1m4      \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m    935036\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33mACIW  \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;32m \u001b[0m\u001b[1;32mACI WORLDWIDE, INC.                   \u001b[0m\u001b[1;32m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2025-03-06 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001952333-25-0000…\u001b[0m\u001b[1;2m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m    4        \u001b[2m \u001b[0m\u001b[2m    935036\u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mACIW  \u001b[0m\u001b[33m \u001b[0m \u001b[1;32m \u001b[0m\u001b[1;32mACI WORLDWIDE, INC.                   \u001b[0m\u001b[1;32m \u001b[0m  2025-03-06   \u001b[2m \u001b[0m\u001b[2m0001952333-25-0000…\u001b[0m\u001b[2m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m   \u001b[1m \u001b[0m\u001b[1m4      \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m    935036\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33mACIW  \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;32m \u001b[0m\u001b[1;32mACI WORLDWIDE, INC.                   \u001b[0m\u001b[1;32m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2025-03-06 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001952333-25-0000…\u001b[0m\u001b[1;2m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m    4        \u001b[2m \u001b[0m\u001b[2m   1768369\u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33m      \u001b[0m\u001b[33m \u001b[0m \u001b[1;32m \u001b[0m\u001b[1;32mACOFF VIOLA L                         \u001b[0m\u001b[1;32m \u001b[0m  2025-03-06   \u001b[2m \u001b[0m\u001b[2m0001062993-25-0047…\u001b[0m\u001b[2m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m   \u001b[1m \u001b[0m\u001b[1m4      \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m   1637873\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33mACVA  \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;32m \u001b[0m\u001b[1;32mACV Auctions Inc.                     \u001b[0m\u001b[1;32m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2025-03-06 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001628280-25-0109…\u001b[0m\u001b[1;2m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m    4        \u001b[2m \u001b[0m\u001b[2m   1637873\u001b[0m\u001b[2m \u001b[0m \u001b[33m \u001b[0m\u001b[33mACVA  \u001b[0m\u001b[33m \u001b[0m \u001b[1;32m \u001b[0m\u001b[1;32mACV Auctions Inc.                     \u001b[0m\u001b[1;32m \u001b[0m  2025-03-06   \u001b[2m \u001b[0m\u001b[2m0001628280-25-0109…\u001b[0m\u001b[2m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m   \u001b[1m \u001b[0m\u001b[1m4      \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m   1637873\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33mACVA  \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;32m \u001b[0m\u001b[1;32mACV Auctions Inc.                     \u001b[0m\u001b[1;32m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2025-03-06 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001628280-25-0109…\u001b[0m\u001b[1;2m \u001b[0m  \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m                                                                                                                 \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m╰─\u001b[0m\u001b[1;38;5;245m────────────────────────────────\u001b[0m\u001b[1;38;5;245m SEC Filings between 2025-03-06 and 2025-03-06 \u001b[0m\u001b[1;38;5;245m────────────────────────────────\u001b[0m\u001b[1;38;5;245m─╯\u001b[0m"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["filings = get_filings(form=4).head(10)\n", "filings"]}, {"cell_type": "markdown", "id": "0015370a-8cf1-4da7-ba23-f9644f4b14da", "metadata": {}, "source": ["## Insider Filings for a Company\n", "\n", "If you are interested in insider transactions for a specific company first find the company using ticker or cik, and from there you can find the insider transactions.\n", "\n", "Let's look at insider information for **Vertex Pharmaceuticals**"]}, {"cell_type": "code", "execution_count": 3, "id": "a27575cc-805e-4cfe-86c7-3ee960333463", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m──────────────────────────────\u001b[0m\u001b[38;5;244m 🏢 \u001b[0m\u001b[1;32mVERTEX PHARMACEUTICALS INC / MA\u001b[0m\u001b[38;5;244m \u001b[0m\u001b[2;38;5;244m[875320] \u001b[0m\u001b[1;33mVRTX\u001b[0m\u001b[38;5;244m \u001b[0m\u001b[38;5;244m───────────────────────────────\u001b[0m\u001b[38;5;244m─╮\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m                                                                                                                 \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m                                                                                                                 \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m────────────────────────────────────────────────\u001b[0m\u001b[38;5;244m 📋 Entity \u001b[0m\u001b[38;5;244m────────────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╮\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   CIK   \u001b[1;38;5;32m875320\u001b[0m   Type   \u001b[1;33mOperating\u001b[0m   \u001b[1;33m○\u001b[0m                                                                       \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1mCategor<PERSON>               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIndustry                         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mFiscal Year End\u001b[0m\u001b[1m \u001b[0m                            \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  ───────────────────────────────────────────────────────────────────────────────                            \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   Large accelerated filer   2834: Pharmaceutical Preparations   Dec 31                                      \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m──────────────────────────────────────────────\u001b[0m\u001b[38;5;244m 📈 Exchanges \u001b[0m\u001b[38;5;244m───────────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╮\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1mExchange\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSymbol\u001b[0m\u001b[1m \u001b[0m                                                                                        \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m  ───────────────────                                                                                        \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m   Nasdaq    \u001b[1;33m \u001b[0m\u001b[1;33mVRTX  \u001b[0m\u001b[1;33m \u001b[0m                                                                                        \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m                                                                                                             \u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m─\u001b[0m\u001b[38;5;244m 🏢 Business Address \u001b[0m\u001b[38;5;244m──\u001b[0m\u001b[38;5;244m─╮\u001b[0m         \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m──\u001b[0m\u001b[38;5;244m 📫 Mailing Address \u001b[0m\u001b[38;5;244m──\u001b[0m\u001b[38;5;244m─╮\u001b[0m       \u001b[38;5;244m╭─\u001b[0m\u001b[38;5;244m 📞 Contact Information \u001b[0m\u001b[38;5;244m─╮\u001b[0m            \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m 50 NORTHERN AVENUE       \u001b[38;5;244m│\u001b[0m         \u001b[38;5;244m│\u001b[0m 50 NORTHERN AVENUE       \u001b[38;5;244m│\u001b[0m       \u001b[38;5;244m│\u001b[0m                          \u001b[38;5;244m│\u001b[0m            \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m│\u001b[0m B<PERSON><PERSON><PERSON>, MA 02210         \u001b[38;5;244m│\u001b[0m         \u001b[38;5;244m│\u001b[0m BOSTON, MA 02210         \u001b[38;5;244m│\u001b[0m       \u001b[38;5;244m│\u001b[0m  \u001b[1;38;5;249m \u001b[0m\u001b[1;38;5;249m<PERSON><PERSON>\u001b[0m\u001b[1;38;5;249m \u001b[0m  6173416393     \u001b[38;5;244m│\u001b[0m            \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m \u001b[38;5;244m╰──────────────────────────╯\u001b[0m         \u001b[38;5;244m╰──────────────────────────╯\u001b[0m       \u001b[38;5;244m│\u001b[0m                          \u001b[38;5;244m│\u001b[0m            \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m│\u001b[0m                                                                         \u001b[38;5;244m╰──────────────────────────╯\u001b[0m            \u001b[38;5;244m│\u001b[0m\n", "\u001b[38;5;244m╰─\u001b[0m\u001b[38;5;244m───────────────────────────────────────────────\u001b[0m\u001b[38;5;244m SEC Entity Data \u001b[0m\u001b[38;5;244m───────────────────────────────────────────────\u001b[0m\u001b[38;5;244m─╯\u001b[0m"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["company = Company(\"VRTX\")\n", "company"]}, {"cell_type": "markdown", "id": "dcc84395-d8ed-430e-a524-6148584c8fbf", "metadata": {}, "source": ["## Initial Insiders\n", "\n", "The form 3 filings contain information about the initial positions for insiders in a company. This also means we can see roughly when persons became insiders from their initial position filings.\n", "\n", "Let's see all the company's initial filings"]}, {"cell_type": "code", "execution_count": 4, "id": "59cbaffc-03f1-417f-86e6-855012fb895b", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1;38;5;245m╭─\u001b[0m\u001b[1;38;5;245m────────────────────────────\u001b[0m\u001b[1;38;5;245m \u001b[0m\u001b[1;38;5;245mFilings for \u001b[0m\u001b[1;32mVERTEX PHARMACEUTICALS INC / MA\u001b[0m\u001b[1;2;38;5;245m [\u001b[0m\u001b[1;33m875320\u001b[0m\u001b[1;2;38;5;245m]\u001b[0m\u001b[1;38;5;245m \u001b[0m\u001b[1;38;5;245m─────────────────────────────\u001b[0m\u001b[1;38;5;245m─╮\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m                                                                                                                 \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1m #\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mForm    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDescription                                       \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mFiling Date\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mAccession Number    \u001b[0m\u001b[1m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  ─────────────────────────────────────────────────────────────────────────────────────────────────────────      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m 0\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2024-05-17   \u001b[2m \u001b[0m\u001b[2m0000875320-24-000146\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m 1\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2023-12-06 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-23-000050\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m 2\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2023-10-10   \u001b[2m \u001b[0m\u001b[2m0001209191-23-052532\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m 3\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2023-09-05 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-23-048157\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m 4\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2022-09-21   \u001b[2m \u001b[0m\u001b[2m0000899243-22-031640\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m 5\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2022-05-18 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000899243-22-018592\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m 6\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2021-05-03   \u001b[2m \u001b[0m\u001b[2m0001209191-21-029509\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m 7\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2021-03-08 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-21-018514\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m 8\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2021-02-05   \u001b[2m \u001b[0m\u001b[2m0001209191-21-008084\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m 9\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2020-06-05 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-20-034743\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m10\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2019-10-03   \u001b[2m \u001b[0m\u001b[2m0001209191-19-052174\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m11\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2019-10-03 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-19-052161\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m12\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2019-04-12   \u001b[2m \u001b[0m\u001b[2m0001209191-19-024464\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m13\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2019-02-22 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-19-012522\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m14\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2018-12-21   \u001b[2m \u001b[0m\u001b[2m0001209191-18-063964\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m15\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2018-04-02 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-18-022523\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m16\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2017-09-15   \u001b[2m \u001b[0m\u001b[2m0001209191-17-052900\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m17\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2017-06-09 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-17-038853\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m18\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2016-01-06   \u001b[2m \u001b[0m\u001b[2m0001209191-16-089937\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m19\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2015-06-08 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-15-050987\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m20\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2014-07-17   \u001b[2m \u001b[0m\u001b[2m0001209191-14-047886\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m21\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2014-05-09 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-14-032606\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m22\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2013-08-22   \u001b[2m \u001b[0m\u001b[2m0001209191-13-041734\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m23\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2012-09-18 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-12-045954\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m24\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2012-09-06   \u001b[2m \u001b[0m\u001b[2m0001209191-12-044469\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m25\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2012-07-26 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-12-039171\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m26\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2012-06-13   \u001b[2m \u001b[0m\u001b[2m0001209191-12-033674\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m27\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2012-05-29 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-12-031021\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m28\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2011-09-07   \u001b[2m \u001b[0m\u001b[2m0001209191-11-046864\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m29\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2011-05-18 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0001209191-11-029315\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m30\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2011-05-13   \u001b[2m \u001b[0m\u001b[2m0001209191-11-028288\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m31\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2010-10-14 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-10-000051\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m32\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2010-07-13   \u001b[2m \u001b[0m\u001b[2m0000875320-10-000038\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m33\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2009-12-11 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-09-000107\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m34\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2009-07-07   \u001b[2m \u001b[0m\u001b[2m0000875320-09-000055\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m35\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2009-07-07 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-09-000054\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m36\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2009-07-07   \u001b[2m \u001b[0m\u001b[2m0000875320-09-000053\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m37\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2008-09-30 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-08-000075\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m38\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2008-06-19   \u001b[2m \u001b[0m\u001b[2m0000875320-08-000044\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m39\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2007-07-24 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-07-000083\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m40\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2007-07-18   \u001b[2m \u001b[0m\u001b[2m0000875320-07-000081\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m41\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2007-07-16 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-07-000072\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m42\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2006-01-25   \u001b[2m \u001b[0m\u001b[2m0000875320-06-000005\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m43\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2005-12-15 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-05-000072\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m44\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2005-05-13   \u001b[2m \u001b[0m\u001b[2m0000875320-05-000020\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m45\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2005-02-22 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-05-000016\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m46\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2004-07-26   \u001b[2m \u001b[0m\u001b[2m0000875320-04-000085\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m47\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2004-05-10 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-04-000044\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[2m \u001b[0m\u001b[2m48\u001b[0m\u001b[2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m  2004-05-10   \u001b[2m \u001b[0m\u001b[2m0000875320-04-000043\u001b[0m\u001b[2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m  \u001b[1;2m \u001b[0m\u001b[1;2m49\u001b[0m\u001b[1;2m \u001b[0m \u001b[1;33m \u001b[0m\u001b[1;33m3       \u001b[0m\u001b[1;33m \u001b[0m \u001b[1;34m \u001b[0m\u001b[1;34mInitial statement of beneficial ownership         \u001b[0m\u001b[1;34m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2004-05-10 \u001b[0m\u001b[1m \u001b[0m \u001b[1;2m \u001b[0m\u001b[1;2m0000875320-04-000042\u001b[0m\u001b[1;2m \u001b[0m      \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m                                                                                                                 \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m                                                                                                                 \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m                                                                                                                 \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m│\u001b[0m \u001b[2mShowing \u001b[0m\u001b[1;31m1\u001b[0m\u001b[2m to \u001b[0m\u001b[1;31m50\u001b[0m\u001b[2m of \u001b[0m\u001b[1m52\u001b[0m\u001b[2m filings.\u001b[0m\u001b[2m Page using \u001b[0m\u001b[1;38;5;245m← prev()\u001b[0m\u001b[2m and \u001b[0m\u001b[1;38;5;245mnext() →\u001b[0m                                                 \u001b[1;38;5;245m│\u001b[0m\n", "\u001b[1;38;5;245m╰─\u001b[0m\u001b[1;38;5;245m──────────────────────────────\u001b[0m\u001b[1;38;5;245m Company filings between 2003-05-23 and 2024-05-17 \u001b[0m\u001b[1;38;5;245m──────────────────────────────\u001b[0m\u001b[1;38;5;245m─╯\u001b[0m"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["initial_filings = (company.get_filings(form=3)\n", "                     .filter(filing_date=':2025-03-04')) # Filter up to this date to keep the notebook data stable\n", "initial_filings"]}, {"cell_type": "markdown", "id": "8156824e-2031-45c0-94cc-1c24b20d5cd9", "metadata": {}, "source": ["There are just over 50 initial insider filings since **2004** for **VRTX**. This could be related to the SEC's **2003** rule change mandating electronic filings with previous filings being paper based. "]}, {"cell_type": "code", "execution_count": 5, "id": "9ecab1d5", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────── \u001b[1mForm 3 \u001b[0m\u001b[1;32mVERTEX PHARMACEUTICALS INC / MA\u001b[0m \u001b[2m[875320] \u001b[0m\u001b[1;33mVRTX\u001b[0m ─────────╮\n", "│                                                                       │\n", "│  \u001b[2m \u001b[0m\u001b[2mAccession Number    \u001b[0m\u001b[2m \u001b[0m \u001b[2m \u001b[0m\u001b[2mFiling Date\u001b[0m\u001b[2m \u001b[0m \u001b[2m \u001b[0m\u001b[2mPeriod of Report\u001b[0m\u001b[2m \u001b[0m \u001b[2m \u001b[0m\u001b[2mDocuments\u001b[0m\u001b[2m \u001b[0m  │\n", "│  ───────────────────────────────────────────────────────────────────  │\n", "│   \u001b[1;38;5;39m0000875320-24-000146\u001b[0m   \u001b[1m2024-05-17 \u001b[0m   \u001b[1m2024-05-15      \u001b[0m   3           │\n", "│                                                                       │\n", "│                                                                       │\n", "│                                                                       │\n", "│                                                                       │\n", "╰──────────────\u001b[2m Initial statement of beneficial ownership \u001b[0m──────────────╯"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["initial_filings[0]"]}, {"cell_type": "code", "execution_count": 8, "id": "868609f1", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────── \u001b[1mInitial Beneficial Ownership\u001b[0m ────────────────╮\n", "│ \u001b[1;34mInsider: \u001b[0m\u001b[1;34m \u001b[0m<PERSON><PERSON><PERSON><PERSON>                                 │\n", "│ \u001b[1;34mPosition:\u001b[0m\u001b[1;34m \u001b[0mDirector                                           │\n", "│ \u001b[1;34mCompany: \u001b[0m\u001b[1;34m \u001b[0mVERTEX PHARMACEUTICALS INC / MA (VRTX)             │\n", "│ \u001b[1;34mDate:    \u001b[0m\u001b[1;34m \u001b[0m2024-05-15                                         │\n", "│ \u001b[1;34mForm:    \u001b[0m\u001b[1;34m \u001b[0mForm 3 (Initial Statement of Beneficial Ownership) │\n", "│ \u001b[3mNo Securities Beneficially Owned\u001b[0m                             │\n", "│ \u001b[3mRemarks: Exhibit 24 - Power of Attorney\u001b[0m                      │\n", "╰──────────────────────────────────────────────────────────────╯"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["form3 = initial_filings[0].obj()\n", "form3"]}, {"cell_type": "code", "execution_count": 9, "id": "9e197574", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Form</th>\n", "      <th>Issuer</th>\n", "      <th>Ticker</th>\n", "      <th>Insider</th>\n", "      <th>Position</th>\n", "      <th>Remarks</th>\n", "      <th>Total Shares</th>\n", "      <th>Has Derivatives</th>\n", "      <th>Holdings</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-05-15</td>\n", "      <td>Form 3</td>\n", "      <td>VERTEX PHARMACEUTICALS INC / MA</td>\n", "      <td>VRTX</td>\n", "      <td><PERSON></td>\n", "      <td>Director</td>\n", "      <td>Exhibit 24 - Power of Attorney</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Date    Form                           Issuer Ticker  \\\n", "0 2024-05-15  Form 3  VERTEX PHARMACEUTICALS INC / MA   VRTX   \n", "\n", "              Insider  Position                         Remarks  Total Shares  \\\n", "0  <PERSON>  Director  Exhibit 24 - Power of Attorney             0   \n", "\n", "   Has Derivatives  Holdings  \n", "0            False         0  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["form3.to_dataframe()"]}, {"cell_type": "markdown", "id": "b5d27235-47dd-40a1-9dbc-c42908cb4d00", "metadata": {}, "source": ["## Extracting the initial insider information\n", "\n", "To get access to the data inside the insider filings we need to loop through each filing, convert to data objects, then dataframes. Then we concatenate into a final dataframe."]}, {"cell_type": "code", "execution_count": 7, "id": "5fddd863-f73f-45bb-be0a-8bb7acc6edcf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 52/52 [00:13<00:00,  3.90it/s]\n"]}], "source": ["def get_company_insiders(ticker:str,\n", "                         summary:bool=True):\n", "    \"\"\"Transform raw Form 3 filings into analyzable data\n", "    \n", "    Parameters:\n", "        ticker (str): Stock symbol of target company\n", "        summary (str): If True produce one summary row. Otherwise break out in individual holdings\n", "    Returns:\n", "        Depends on whether we are doing the summary or not \n", "        if summary== True\n", "            pd.DataFrame: Structured ownership data with columns:\n", "                - Date: The date of the transaction\n", "                - Insider: Name of executive/director\n", "                - Position: Company role\n", "                - TotalShares: The total shares held\n", "                - Holdings: How many different holding positions e.g 3 derivatives, 1 common stock\n", "                - Common Stock Holdings: How many different Common Stock Holdings\n", "                - Derivative Holdings: How many different Derivative Holdings \n", "        else:\n", "            May contain additional columns about individual positions like\n", "            - Expiration Date\n", "            - OWnership Nature\n", "            - Underlying Security\n", "    \"\"\"\n", "    c = Company(ticker)\n", "    initial_filings = c.get_filings(form=3)\n", "    insider_data = []\n", "    for filing in tqdm(initial_filings):\n", "        # Convert to data object\n", "        form3 = filing.obj()\n", "        # Get the data in a dataframe. Set detailed = False to get 1 summary row\n", "        df = form3.to_dataframe(detailed = not summary)\n", "\n", "        insider_data.append(df)\n", "\n", "    insider_data = (pd.concat(insider_data, ignore_index=True)\n", "                    .reset_index(drop=True)\n", "                    \n", "    )\n", "    insider_data = insider_data.drop(\n", "        columns=[c \n", "        for c in ['Form', 'Issuer', 'Ticker', 'Remarks', 'Has Derivatives']\n", "        if c in insider_data.columns\n", "        ]\n", "    )\n", "    return insider_data\n", "\n", "\n", "# Fix the variable name in your existing code\n", "vrtx_summary = get_company_insiders(\"VRTX\")\n", "\n"]}, {"cell_type": "markdown", "id": "6af57641", "metadata": {}, "source": ["## 1. Analyze Ownership Patterns\n", "\n", "Vertex demonstrates a clear two-tier ownership philosophy:\n", "\n", "- **Executive Leadership**: Technical operations, legal, and therapeutic division leaders show substantial direct ownership (13,879-21,175 shares)\n", "- **Board Directors**: Most recent director appointments show zero initial ownership, suggesting a performance-contingent compensation model"]}, {"cell_type": "code", "execution_count": 10, "id": "22c7158c", "metadata": {}, "outputs": [], "source": ["# Function to analyze ownership patterns in Form 3 filings\n", "def analyze_ownership_patterns(df):\n", "    \"\"\"Analyze ownership patterns from Form 3 summary data\n", "    \n", "    Parameters:\n", "        df (pd.DataFrame): DataFrame containing Form 3 summary data\n", "        \n", "    Returns:\n", "        dict: Dictionary of insights and metrics\n", "    \"\"\"\n", "    # Add role classification\n", "    role_patterns = df.assign(\n", "        is_director=df['Position'].str.contains('Director'),\n", "        is_executive=~df['Position'].str.contains('Director'),\n", "        has_skin_in_game=df['Total Shares'] > 0\n", "    )\n", "    \n", "    # Calculate key metrics\n", "    insights = {\n", "        'director_ownership_rate': role_patterns[role_patterns['is_director']]['has_skin_in_game'].mean(),\n", "        'executive_ownership_rate': role_patterns[role_patterns['is_executive']]['has_skin_in_game'].mean(),\n", "        'derivative_users': (role_patterns['Derivative Holdings'] > 0).sum(),\n", "        'avg_exec_shares': role_patterns[role_patterns['is_executive']]['Total Shares'].mean(),\n", "        'recent_trend': role_patterns.sort_values('Date', ascending=False).head(5)['Total Shares'].mean(),\n", "        'historical_trend': role_patterns.sort_values('Date').head(5)['Total Shares'].mean(),\n", "        'role_patterns': role_patterns  # Return the enhanced DataFrame for further analysis\n", "    }\n", "    \n", "    return insights"]}, {"cell_type": "code", "execution_count": 11, "id": "920d70f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Director ownership rate: 9.1%\n", "Executive ownership rate: 36.7%\n", "Recent trend (last 5 filings): 2776 shares\n", "Historical trend (first 5 filings): 6416 shares\n"]}], "source": ["# 1. Analyze ownership patterns\n", "vrtx_insights = analyze_ownership_patterns(vrtx_summary)\n", "print(f\"Director ownership rate: {vrtx_insights['director_ownership_rate']:.1%}\")\n", "print(f\"Executive ownership rate: {vrtx_insights['executive_ownership_rate']:.1%}\")\n", "print(f\"Recent trend (last 5 filings): {vrtx_insights['recent_trend']:.0f} shares\")\n", "print(f\"Historical trend (first 5 filings): {vrtx_insights['historical_trend']:.0f} shares\")"]}, {"cell_type": "markdown", "id": "d47f30b7", "metadata": {}, "source": ["## 2. Trends over time\n", "\n", "The data reveals a distinct shift in Vertex's compensation philosophy:\n", "\n", "- **2020-2021**: Both executives and directors received meaningful initial equity positions\n", "- **2022-2024**: Zero-ownership appointments became standard for directors and some executives\n", "- This transition coincides with Vertex's expansion beyond cystic fibrosis into broader therapeutic areas"]}, {"cell_type": "code", "execution_count": 12, "id": "f081ae53", "metadata": {}, "outputs": [], "source": ["# Function to visualize ownership trends over time\n", "def visualize_ownership_trends(df):\n", "    \"\"\"Create visualizations of ownership trends from Form 3 data\n", "    \n", "    Parameters:\n", "        df (pd.DataFrame): DataFrame containing Form 3 summary data\n", "        \n", "    Returns:\n", "        matplotlib.figure.Figure: Figure object containing the plot\n", "    \"\"\"\n", "    # Convert date to datetime if it's not already\n", "    if not pd.api.types.is_datetime64_any_dtype(df['Date']):\n", "        df['Date'] = pd.to_datetime(df['Date'])\n", "    \n", "    # Create a figure with multiple plots\n", "    fig, axes = plt.subplots(2, 1, figsize=(12, 10))\n", "    \n", "    # Plot 1: <PERSON><PERSON><PERSON> plot of initial ownership by role over time\n", "    ax = axes[0]\n", "    scatter = ax.scatter(\n", "        df['Date'], \n", "        df['Total Shares'],\n", "        s=100,\n", "        c=df['Position'].str.contains('Director').map({True: 'blue', False: 'red'}),\n", "        alpha=0.7\n", "    )\n", "    \n", "    # Add a legend\n", "    from matplotlib.lines import Line2D\n", "    legend_elements = [\n", "        Line2D([0], [0], marker='o', color='w', markerfacecolor='red', markersize=10, label='Executive'),\n", "        Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=10, label='Director')\n", "    ]\n", "    ax.legend(handles=legend_elements, loc='upper right')\n", "    \n", "    # Add labels and title\n", "    ax.set_xlabel('Filing Date')\n", "    ax.set_ylabel('Total Shares')\n", "    ax.set_title('Initial Ownership Trends Over Time')\n", "    \n", "    # Add a trend line (rolling average)\n", "    if len(df) > 5:\n", "        # Sort by date\n", "        df_sorted = df.sort_values('Date')\n", "        # Calculate rolling average\n", "        df_sorted['rolling_avg'] = df_sorted['Total Shares'].rolling(window=3, min_periods=1).mean()\n", "        # Plot rolling average\n", "        ax.plot(df_sorted['Date'], df_sorted['rolling_avg'], 'k--', alpha=0.5)\n", "    \n", "    # Plot 2: Bar chart of average ownership by role\n", "    ax = axes[1]\n", "    role_data = df.assign(Role=df['Position'].apply(lambda x: 'Director' if 'Director' in x else 'Executive'))\n", "    role_avg = role_data.groupby('Role')['Total Shares'].mean().reset_index()\n", "    \n", "    # Create bar chart\n", "    bars = ax.bar(\n", "        role_avg['Role'],\n", "        role_avg['Total Shares'],\n", "        color=['blue', 'red']\n", "    )\n", "    \n", "    # Add labels and title\n", "    ax.set_xlabel('Role')\n", "    ax.set_ylabel('Average Initial Shares')\n", "    ax.set_title('Average Initial Ownership by Role')\n", "    \n", "    # Add value labels on bars\n", "    for bar in bars:\n", "        height = bar.get_height()\n", "        ax.text(\n", "            bar.get_x() + bar.get_width()/2.,\n", "            height,\n", "            f'{int(height):,}',\n", "            ha='center',\n", "            va='bottom'\n", "        )\n", "    \n", "    # Adjust layout and return figure\n", "    plt.tight_layout()\n", "    return fig\n"]}, {"cell_type": "code", "execution_count": 13, "id": "85b7f7af", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 2. Visualize ownership trends\n", "fig = visualize_ownership_trends(vrtx_summary)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "15be9811", "metadata": {}, "source": ["## 3. Derivative vs Direct Ownership\n", "\n", "Only one insider (<PERSON>, General Counsel) shows significant derivative holdings (3) alongside common stock. This outlier pattern suggests:\n", "\n", "- Specialized retention strategy for legal expertise\n", "- Potential negotiated compensation structure\n", "- Different risk profile compared to other executives"]}, {"cell_type": "code", "execution_count": 14, "id": "36af0654", "metadata": {}, "outputs": [], "source": ["# Function to analyze derivative positions\n", "def analyze_derivative_positions(df):\n", "    \"\"\"Analyze derivative positions from Form 3 summary data\n", "    \n", "    Parameters:\n", "        df (pd.DataFrame): DataFrame containing Form 3 summary data\n", "        \n", "    Returns:\n", "        dict: Dictionary of derivative insights\n", "    \"\"\"\n", "    # Identify insiders with derivative positions\n", "    derivative_holders = df[df['Derivative Holdings'] > 0].copy()\n", "    \n", "    # Calculate metrics\n", "    insights = {\n", "        'derivative_holder_count': len(derivative_holders),\n", "        'derivative_holder_pct': len(derivative_holders) / len(df) if len(df) > 0 else 0,\n", "        'avg_derivative_holdings': derivative_holders['Derivative Holdings'].mean() if len(derivative_holders) > 0 else 0,\n", "        'derivative_holders': derivative_holders,\n", "        'derivative_to_common_ratio': derivative_holders['Derivative Holdings'].sum() / \n", "                                     derivative_holders['Common Stock Holdings'].sum() \n", "                                     if derivative_holders['Common Stock Holdings'].sum() > 0 else float('inf')\n", "    }\n", "    \n", "    return insights"]}, {"cell_type": "code", "execution_count": 15, "id": "b7f834af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Derivative holders: 19 (36.5%)\n", "Average derivative holdings per holder: 2.9\n", "Derivative to common stock ratio: 1.81\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Insider</th>\n", "      <th>Position</th>\n", "      <th>Total Shares</th>\n", "      <th>Holdings</th>\n", "      <th>Common Stock Holdings</th>\n", "      <th>Derivative Holdings</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2021-03-02</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON>, General Counsel</td>\n", "      <td>13394</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2019-10-01</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>SVP, CRO</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2019-10-01</td>\n", "      <td><PERSON></td>\n", "      <td>EVP, GMDA</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2018-04-01</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>EVP and CMO</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2014-07-15</td>\n", "      <td><PERSON></td>\n", "      <td>SVP GMDA, CMO</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2013-08-13</td>\n", "      <td><PERSON></td>\n", "      <td>SVP, Human Resources</td>\n", "      <td>150</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2012-07-25</td>\n", "      <td><PERSON></td>\n", "      <td>SVP, Corp. Communications</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2011-08-29</td>\n", "      <td><PERSON></td>\n", "      <td>SVP</td>\n", "      <td>264</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2010-10-06</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>SVP, Corp Strategy &amp; Bus Devlp</td>\n", "      <td>320</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2008-09-29</td>\n", "      <td><PERSON></td>\n", "      <td>V.P. and Corporate Controller</td>\n", "      <td>300</td>\n", "      <td>7</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>2007-07-12</td>\n", "      <td><PERSON></td>\n", "      <td>SVP, Human Resources</td>\n", "      <td>360</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>2006-01-25</td>\n", "      <td>VERTEX PHARMACEUTICALS INC / MA</td>\n", "      <td>10% Owner</td>\n", "      <td>450000</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>2005-12-12</td>\n", "      <td><PERSON></td>\n", "      <td>SVP, Organizational Developmen</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>2005-05-11</td>\n", "      <td><PERSON></td>\n", "      <td>Director</td>\n", "      <td>18000</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>2005-02-15</td>\n", "      <td><PERSON></td>\n", "      <td>EVP, Strategic &amp; Corp. Devlp.</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>2004-07-23</td>\n", "      <td><PERSON></td>\n", "      <td>Director</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>2004-05-06</td>\n", "      <td><PERSON></td>\n", "      <td>VP, Research</td>\n", "      <td>32082</td>\n", "      <td>27</td>\n", "      <td>5</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>2004-05-06</td>\n", "      <td><PERSON></td>\n", "      <td>Director</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>2004-05-06</td>\n", "      <td><PERSON></td>\n", "      <td>Director</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Date                          Insider  \\\n", "7  2021-03-02                          <PERSON>   \n", "10 2019-10-01                   Our<PERSON> Tatsis   \n", "11 2019-10-01                     <PERSON>   \n", "15 2018-04-01               <PERSON><PERSON><PERSON>   \n", "20 2014-07-15              <PERSON>   \n", "22 2013-08-13                  <PERSON>   \n", "25 2012-07-25                     <PERSON>   \n", "28 2011-08-29                   <PERSON>   \n", "31 2010-10-06             <PERSON><PERSON>   \n", "37 2008-09-29                     <PERSON>   \n", "41 2007-07-12                       <PERSON>   \n", "42 2006-01-25  VERTEX PHARMACEUTICALS INC / MA   \n", "43 2005-12-12               <PERSON>   \n", "44 2005-05-11                    <PERSON>   \n", "45 2005-02-15                <PERSON>   \n", "46 2004-07-23                   <PERSON>   \n", "47 2004-05-06                 <PERSON>   \n", "48 2004-05-06             <PERSON>   \n", "49 2004-05-06                   <PERSON>   \n", "\n", "                          Position  Total Shares  Holdings  \\\n", "7             <PERSON><PERSON>, General Counsel         13394         4   \n", "10                        SVP, CRO             0         6   \n", "11                       EVP, GMDA             0         2   \n", "15                     EVP and CMO             0         2   \n", "20                   SVP GMDA, CMO             0         2   \n", "22            SVP, Human Resources           150         4   \n", "25       SVP, Corp. Communications             0         5   \n", "28                             SVP           264         7   \n", "31  SVP, Corp Strategy & Bus Devlp           320         4   \n", "37   V.P. and Corporate Controller           300         7   \n", "41            SVP, Human Resources           360         4   \n", "42                       10% Owner        450000         4   \n", "43  SVP, Organizational Developmen             0         2   \n", "44                        Director         18000         2   \n", "45   EVP, Strategic & Corp. Devlp.             0         2   \n", "46                        Director             0         1   \n", "47                    VP, Research         32082        27   \n", "48                        Director             0         1   \n", "49                        Director             0         1   \n", "\n", "    Common Stock Holdings  Derivative Holdings  \n", "7                       1                    3  \n", "10                      4                    2  \n", "11                      1                    1  \n", "15                      1                    1  \n", "20                      1                    1  \n", "22                      2                    2  \n", "25                      1                    4  \n", "28                      2                    5  \n", "31                      2                    2  \n", "37                      4                    3  \n", "41                      2                    2  \n", "42                      2                    2  \n", "43                      1                    1  \n", "44                      1                    1  \n", "45                      1                    1  \n", "46                      0                    1  \n", "47                      5                   22  \n", "48                      0                    1  \n", "49                      0                    1  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 3. Analyze derivative positions\n", "derivative_insights = analyze_derivative_positions(vrtx_summary)\n", "print(f\"Derivative holders: {derivative_insights['derivative_holder_count']} ({derivative_insights['derivative_holder_pct']:.1%})\")\n", "print(f\"Average derivative holdings per holder: {derivative_insights['avg_derivative_holdings']:.1f}\")\n", "print(f\"Derivative to common stock ratio: {derivative_insights['derivative_to_common_ratio']:.2f}\")\n", "\n", "# Show the derivative holders\n", "derivative_insights['derivative_holders']"]}, {"cell_type": "markdown", "id": "37744b9e", "metadata": {}, "source": ["## 4. Strategic Insights\n", "\n", "These patterns provide actionable intelligence for investors:\n", "\n", "- **Board Independence**: Zero-ownership directors may exercise more independent judgment\n", "- **Executive Alignment**: Operational leaders maintain significant \"skin in the game\"\n", "- **Succession Planning**: Track subsequent Form 4 filings from zero-ownership appointees to identify which executives are being groomed for larger roles\n", "- **Compensation Evolution**: The shift toward zero initial ownership suggests increased emphasis on performance-based equity awards"]}, {"cell_type": "code", "execution_count": 16, "id": "1e9096de", "metadata": {}, "outputs": [], "source": ["# Function to calculate strategic implications\n", "def calculate_strategic_implications(df):\n", "    \"\"\"Calculate strategic implications from Form 3 data\n", "    \n", "    Parameters:\n", "        df (pd.DataFrame): DataFrame containing Form 3 summary data\n", "        \n", "    Returns:\n", "        dict: Dictionary of strategic insights\n", "    \"\"\"\n", "    # Calculate ownership concentration\n", "    top_holders = df.nlargest(3, 'Total Shares')\n", "    ownership_concentration = top_holders['Total Shares'].sum() / df['Total Shares'].sum() if df['Total Shares'].sum() > 0 else 0\n", "    \n", "    # Calculate temporal shifts\n", "    df['year'] = pd.to_datetime(df['Date']).dt.year\n", "    yearly_avg = df.groupby('year')['Total Shares'].mean().reset_index()\n", "    \n", "    # Calculate zero-ownership appointments\n", "    zero_ownership = df[df['Total Shares'] == 0]\n", "    zero_ownership_rate = len(zero_ownership) / len(df) if len(df) > 0 else 0\n", "    \n", "    # Calculate executive vs director metrics\n", "    is_director = df['Position'].str.contains('Director')\n", "    director_df = df[is_director]\n", "    executive_df = df[~is_director]\n", "    \n", "    director_zero_rate = (director_df['Total Shares'] == 0).mean() if len(director_df) > 0 else 0\n", "    executive_zero_rate = (executive_df['Total Shares'] == 0).mean() if len(executive_df) > 0 else 0\n", "    \n", "    # Compile insights\n", "    insights = {\n", "        'ownership_concentration': ownership_concentration,\n", "        'yearly_avg': yearly_avg,\n", "        'zero_ownership_rate': zero_ownership_rate,\n", "        'director_zero_rate': director_zero_rate,\n", "        'executive_zero_rate': executive_zero_rate,\n", "        'compensation_evolution': yearly_avg,\n", "        'top_holders': top_holders\n", "    }\n", "    \n", "    return insights"]}, {"cell_type": "code", "execution_count": 17, "id": "26b0513b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top 3 insiders control 91.2% of reported insider shares\n", "Zero-ownership appointment rate: 75.0%\n", "Director zero-ownership rate: 90.9%\n", "Executive zero-ownership rate: 63.3%\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>year</th>\n", "      <th>Total Shares</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2003</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2004</td>\n", "      <td>8020.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2005</td>\n", "      <td>6000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2006</td>\n", "      <td>450000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2007</td>\n", "      <td>120.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2008</td>\n", "      <td>150.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2009</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2010</td>\n", "      <td>160.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2011</td>\n", "      <td>88.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2012</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2013</td>\n", "      <td>150.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2014</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2015</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2016</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2017</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2018</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2019</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2020</td>\n", "      <td>207.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2021</td>\n", "      <td>11987.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2022</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2023</td>\n", "      <td>4626.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    year   Total Shares\n", "0   2003       0.000000\n", "1   2004    8020.500000\n", "2   2005    6000.000000\n", "3   2006  450000.000000\n", "4   2007     120.000000\n", "5   2008     150.000000\n", "6   2009       0.000000\n", "7   2010     160.000000\n", "8   2011      88.000000\n", "9   2012       0.000000\n", "10  2013     150.000000\n", "11  2014       0.000000\n", "12  2015       0.000000\n", "13  2016       0.000000\n", "14  2017       0.000000\n", "15  2018       0.000000\n", "16  2019       0.000000\n", "17  2020     207.000000\n", "18  2021   11987.666667\n", "19  2022       0.000000\n", "20  2023    4626.333333\n", "21  2024       0.000000"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4. Calculate strategic implications\n", "strategic_insights = calculate_strategic_implications(vrtx_summary)\n", "print(f\"Top 3 insiders control {strategic_insights['ownership_concentration']:.1%} of reported insider shares\")\n", "print(f\"Zero-ownership appointment rate: {strategic_insights['zero_ownership_rate']:.1%}\")\n", "print(f\"Director zero-ownership rate: {strategic_insights['director_zero_rate']:.1%}\")\n", "print(f\"Executive zero-ownership rate: {strategic_insights['executive_zero_rate']:.1%}\")\n", "\n", "# Show yearly average ownership trends\n", "strategic_insights['yearly_avg']"]}, {"cell_type": "markdown", "id": "b6ac502a", "metadata": {}, "source": ["## 5. Comparison with peers\n", "\n", "Compared to biotech industry norms, Vertex's pattern of substantial executive ownership (particularly in therapeutic leadership) signals:\n", "\n", "- Strong confidence in pipeline prospects\n", "- Alignment with long-term shareholder interests\n", "- Potential retention strategy for key scientific talent\n", "\n", "These insights demonstrate how even summary-level Form 3 data can reveal significant strategic patterns when analyzed systematically."]}, {"cell_type": "code", "execution_count": 18, "id": "c8083a01", "metadata": {}, "outputs": [], "source": ["# Function to compare with industry peers\n", "def compare_with_peers(ticker, peer_tickers):\n", "    \"\"\"Compare Form 3 patterns with industry peers\n", "    \n", "    Parameters:\n", "        ticker (str): Main company ticker\n", "        peer_tickers (list): List of peer company tickers\n", "        \n", "    Returns:\n", "        pd.DataFrame: Comparison metrics across peers with nicely formatted values\n", "    \"\"\"\n", "    # Function to get summary metrics for a company\n", "    def get_company_metrics(ticker):\n", "        try:\n", "            # Get insider data\n", "            c = Company(ticker)\n", "            initial_filings = c.get_filings(form=3)\n", "            \n", "            # Process filings\n", "            insider_data = []\n", "            for filing in tqdm(initial_filings, desc=f\"Processing {ticker}\"):\n", "                form3 = filing.obj()\n", "                df = form3.to_dataframe(detailed=False)\n", "                insider_data.append(df)\n", "            \n", "            if not insider_data:\n", "                return {\n", "                    'ticker': ticker,\n", "                    'avg_shares': 0,\n", "                    'zero_rate': 1.0,\n", "                    'derivative_rate': 0,\n", "                    'exec_ownership': 0\n", "                }\n", "            \n", "            # Combine data\n", "            insider_df = pd.concat(insider_data, ignore_index=True).reset_index(drop=True)\n", "            \n", "            # Calculate metrics\n", "            is_director = insider_df['Position'].str.contains('Director')\n", "            director_df = insider_df[is_director]\n", "            executive_df = insider_df[~is_director]\n", "            \n", "            return {\n", "                'ticker': ticker,\n", "                'avg_shares': insider_df['Total Shares'].mean(),\n", "                'zero_rate': (insider_df['Total Shares'] == 0).mean(),\n", "                'derivative_rate': (insider_df['Derivative Holdings'] > 0).mean(),\n", "                'exec_ownership': executive_df['Total Shares'].mean() if len(executive_df) > 0 else 0\n", "            }\n", "        except Exception as e:\n", "            print(f\"Error processing {ticker}: {e}\")\n", "            return {\n", "                'ticker': ticker,\n", "                'avg_shares': None,\n", "                'zero_rate': None,\n", "                'derivative_rate': None,\n", "                'exec_ownership': None\n", "            }\n", "    \n", "    # Get metrics for main company and peers\n", "    all_tickers = [ticker] + peer_tickers\n", "    metrics = [get_company_metrics(t) for t in all_tickers]\n", "    \n", "    # Convert to DataFrame\n", "    comparison_df = pd.DataFrame(metrics)\n", "    \n", "    # Format the DataFrame for better readability\n", "    formatted_df = comparison_df.copy()\n", "    \n", "    # Rename columns for clarity\n", "    formatted_df.columns = [\n", "        'Ticker', \n", "        'Avg Initial Shares', \n", "        'Zero-Ownership Rate', \n", "        'Derivative Usage Rate',\n", "        'Avg Executive Shares'\n", "    ]\n", "    \n", "    # Format numbers nicely\n", "    # Integer formatting for share counts\n", "    formatted_df['Avg Initial Shares'] = formatted_df['Avg Initial Shares'].apply(\n", "        lambda x: f\"{int(x):,}\" if pd.notnull(x) else 'N/A'\n", "    )\n", "    formatted_df['Avg Executive Shares'] = formatted_df['Avg Executive Shares'].apply(\n", "        lambda x: f\"{int(x):,}\" if pd.notnull(x) else 'N/A'\n", "    )\n", "    \n", "    # Percentage formatting\n", "    formatted_df['Zero-Ownership Rate'] = formatted_df['Zero-Ownership Rate'].apply(\n", "        lambda x: f\"{x:.1%}\" if pd.notnull(x) else 'N/A'\n", "    )\n", "    formatted_df['Derivative Usage Rate'] = formatted_df['Derivative Usage Rate'].apply(\n", "        lambda x: f\"{x:.1%}\" if pd.notnull(x) else 'N/A'\n", "    )\n", "    \n", "    # Set ticker as index for better display\n", "    formatted_df = formatted_df.set_index('Ticker')\n", "    \n", "    return formatted_df"]}, {"cell_type": "code", "execution_count": 19, "id": "e14a4c99", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing VRTX: 100%|██████████| 52/52 [00:05<00:00,  9.79it/s]\n", "Processing REGN: 100%|██████████| 25/25 [00:07<00:00,  3.20it/s]\n", "Processing BIIB: 100%|██████████| 79/79 [00:27<00:00,  2.90it/s]\n", "Processing GILD: 100%|██████████| 52/52 [00:14<00:00,  3.62it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Avg Initial Shares</th>\n", "      <th>Zero-Ownership Rate</th>\n", "      <th>Derivative Usage Rate</th>\n", "      <th>Avg Executive Shares</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>VRTX</th>\n", "      <td>10,606</td>\n", "      <td>75.0%</td>\n", "      <td>36.5%</td>\n", "      <td>17,777</td>\n", "    </tr>\n", "    <tr>\n", "      <th>REGN</th>\n", "      <td>610</td>\n", "      <td>88.0%</td>\n", "      <td>28.0%</td>\n", "      <td>954</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BIIB</th>\n", "      <td>558,303</td>\n", "      <td>74.7%</td>\n", "      <td>31.6%</td>\n", "      <td>773,780</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GILD</th>\n", "      <td>1,426,516</td>\n", "      <td>51.9%</td>\n", "      <td>38.5%</td>\n", "      <td>2,004,341</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Avg Initial Shares Zero-Ownership Rate Derivative Usage Rate  \\\n", "Ticker                                                                \n", "VRTX               10,606               75.0%                 36.5%   \n", "REGN                  610               88.0%                 28.0%   \n", "BIIB              558,303               74.7%                 31.6%   \n", "GILD            1,426,516               51.9%                 38.5%   \n", "\n", "       Avg Executive Shares  \n", "Ticker                       \n", "VRTX                 17,777  \n", "REGN                    954  \n", "BIIB                773,780  \n", "GILD              2,004,341  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# 5. <PERSON><PERSON><PERSON> with industry peers (this may take a few minutes to run)\n", "peer_comparison = compare_with_peers(\"VRTX\", [\"REGN\", \"BIIB\", \"GILD\"])\n", "peer_comparison"]}, {"cell_type": "code", "execution_count": 41, "id": "abfefaac-c52c-42a3-931a-cb7e4268451d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'director_ownership_rate': np.float64(0.09090909090909091),\n", " 'executive_ownership_rate': np.float64(0.36666666666666664),\n", " 'derivative_users': np.int64(19),\n", " 'avg_exec_shares': np.float64(17777.266666666666),\n", " 'recent_trend': np.float64(2775.8),\n", " 'historical_trend': np.float64(6416.4)}"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Extract strategic insights\n", "def analyze_vrtx_patterns(df):\n", "    # Role-based ownership analysis\n", "    role_patterns = df.assign(\n", "        is_director = df['Position'].str.contains('Director'),\n", "        is_executive = ~df['Position'].str.contains('Director'),\n", "        has_skin_in_game = df['Total Shares'] > 0\n", "    )\n", "    \n", "    # Calculate key metrics\n", "    insights = {\n", "        'director_ownership_rate': role_patterns[role_patterns['is_director']]['has_skin_in_game'].mean(),\n", "        'executive_ownership_rate': role_patterns[role_patterns['is_executive']]['has_skin_in_game'].mean(),\n", "        'derivative_users': (role_patterns['Derivative Holdings'] > 0).sum(),\n", "        'avg_exec_shares': role_patterns[role_patterns['is_executive']]['Total Shares'].mean(),\n", "        'recent_trend': role_patterns.sort_values('Date', ascending=False).head(5)['Total Shares'].mean(),\n", "        'historical_trend': role_patterns.sort_values('Date').head(5)['Total Shares'].mean()\n", "    }\n", "    \n", "    return insights\n", "\n", "vrtx_insights = analyze_vrtx_patterns(wrtx_insiders)\n", "vrtx_insights"]}, {"cell_type": "markdown", "id": "69cea686-ad45-4cbe-b44f-fa154a7c0518", "metadata": {}, "source": ["## Viewing specific insider filings"]}, {"cell_type": "markdown", "id": "9e437c7e-43f7-4a6a-8aa2-105fabe3ffde", "metadata": {}, "source": ["#### <PERSON> (Director)\n", "<PERSON>'s initial filing indicated no holdings"]}, {"cell_type": "code", "execution_count": 20, "id": "3338fd10-1c5e-4465-afb8-3cc7dacd1334", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────── \u001b[1mInitial Beneficial Ownership\u001b[0m ────────────────╮\n", "│ \u001b[1;34mInsider: \u001b[0m\u001b[1;34m \u001b[0m<PERSON><PERSON><PERSON><PERSON>                                 │\n", "│ \u001b[1;34mPosition:\u001b[0m\u001b[1;34m \u001b[0mDirector                                           │\n", "│ \u001b[1;34mCompany: \u001b[0m\u001b[1;34m \u001b[0mVERTEX PHARMACEUTICALS INC / MA (VRTX)             │\n", "│ \u001b[1;34mDate:    \u001b[0m\u001b[1;34m \u001b[0m2024-05-15                                         │\n", "│ \u001b[1;34mForm:    \u001b[0m\u001b[1;34m \u001b[0mForm 3 (Initial Statement of Beneficial Ownership) │\n", "│ \u001b[3mNo Securities Beneficially Owned\u001b[0m                             │\n", "│ \u001b[3mRemarks: Exhibit 24 - Power of Attorney\u001b[0m                      │\n", "╰──────────────────────────────────────────────────────────────╯"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["initial_filings[0].obj()"]}, {"cell_type": "markdown", "id": "b1583fb0-d8de-4c20-81bd-9e691026774c", "metadata": {}, "source": ["#### <PERSON> (EVP)\n", "\n", "Here we see **<PERSON> III** initial position in common stock"]}, {"cell_type": "code", "execution_count": 21, "id": "15443079-e081-4d03-a72b-a32d88b83ffa", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────── \u001b[1mInitial Beneficial Ownership\u001b[0m ────────────────╮\n", "│ \u001b[1;34mInsider: \u001b[0m\u001b[1;34m \u001b[0mEdward <PERSON> III                         │\n", "│ \u001b[1;34mPosition:\u001b[0m\u001b[1;34m \u001b[0mEVP, Chief Technical Ops. Off.                     │\n", "│ \u001b[1;34mCompany: \u001b[0m\u001b[1;34m \u001b[0mVERTEX PHARMACEUTICALS INC / MA (VRTX)             │\n", "│ \u001b[1;34mDate:    \u001b[0m\u001b[1;34m \u001b[0m2023-08-29                                         │\n", "│ \u001b[1;34mForm:    \u001b[0m\u001b[1;34m \u001b[0mForm 3 (Initial Statement of Beneficial Ownership) │\n", "│ \u001b[1m        Common Stock Holdings        \u001b[0m                        │\n", "│                                                              │\n", "│  \u001b[1m \u001b[0m\u001b[1mSecurity    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mShares\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mOwnership\u001b[0m\u001b[1m \u001b[0m                         │\n", "│  ───────────────────────────────────                         │\n", "│  \u001b[1m \u001b[0m\u001b[1mCommon <PERSON>\u001b[0m\u001b[1m \u001b[0m  13,879   Direct                             │\n", "│                                                              │\n", "│ \u001b[3mRemarks: Exhibit 24 - Power of Attorney\u001b[0m                      │\n", "╰──────────────────────────────────────────────────────────────╯"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["initial_filings[3].obj()"]}, {"cell_type": "markdown", "id": "495a7c95-5459-4d20-98c1-6cbe2e8d35b9", "metadata": {}, "source": ["#### <PERSON><PERSON>\n", "This filing by Ourania Tatsis has both Common Stock and Derivative Holdings"]}, {"cell_type": "code", "execution_count": 22, "id": "3352afe6-5914-4afb-a477-f23170feebcc", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭────────────────────────────────── \u001b[1mInitial Beneficial Ownership\u001b[0m ───────────────────────────────────╮\n", "│ \u001b[1;34mInsider: \u001b[0m\u001b[1;34m \u001b[0m<PERSON><PERSON><PERSON>                                                                          │\n", "│ \u001b[1;34mPosition:\u001b[0m\u001b[1;34m \u001b[0mSVP, CRO                                                                                │\n", "│ \u001b[1;34mCompany: \u001b[0m\u001b[1;34m \u001b[0mVERTEX PHARMACEUTICALS INC / MA (VRTX)                                                  │\n", "│ \u001b[1;34mDate:    \u001b[0m\u001b[1;34m \u001b[0m2019-10-01                                                                              │\n", "│ \u001b[1;34mForm:    \u001b[0m\u001b[1;34m \u001b[0mForm 3 (Initial Statement of Beneficial Ownership)                                      │\n", "│ \u001b[1m         Common Stock Holdings          \u001b[0m                                                          │\n", "│                                                                                                   │\n", "│  \u001b[1m \u001b[0m\u001b[1mSecurity    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m   Shares\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mOwnership\u001b[0m\u001b[1m \u001b[0m                                                           │\n", "│  ──────────────────────────────────────                                                           │\n", "│  \u001b[1m \u001b[0m\u001b[1mCommon Stock\u001b[0m\u001b[1m \u001b[0m  3500 [F1]   Direct                                                               │\n", "│  \u001b[1m \u001b[0m\u001b[1mCommon Stock\u001b[0m\u001b[1m \u001b[0m  1876 [F2]   Direct                                                               │\n", "│  \u001b[1m \u001b[0m\u001b[1mCommon Stock\u001b[0m\u001b[1m \u001b[0m   632 [F3]   Direct                                                               │\n", "│  \u001b[1m \u001b[0m\u001b[1mCommon Stock\u001b[0m\u001b[1m \u001b[0m  1876 [F4]   Direct                                                               │\n", "│                                                                                                   │\n", "│ \u001b[1m                                      Derivative Securities                                      \u001b[0m │\n", "│                                                                                                   │\n", "│  \u001b[1m \u001b[0m\u001b[1mSecurity                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mUnderlying  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mShares\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mExercise Price\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mExpiration\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mOwnership\u001b[0m\u001b[1m \u001b[0m  │\n", "│  ───────────────────────────────────────────────────────────────────────────────────────────────  │\n", "│  \u001b[1m \u001b[0m\u001b[1mStock Option (Right to Buy)\u001b[0m\u001b[1m \u001b[0m \u001b[3m \u001b[0m\u001b[3mCommon Stock\u001b[0m\u001b[3m \u001b[0m   8,938  \u001b[32m \u001b[0m\u001b[32m       $187.53\u001b[0m\u001b[32m \u001b[0m \u001b[2m \u001b[0m\u001b[2m2029-02-05\u001b[0m\u001b[2m \u001b[0m  Direct      │\n", "│  \u001b[1m \u001b[0m\u001b[1mStock Option (Right to Buy)\u001b[0m\u001b[1m \u001b[0m \u001b[3m \u001b[0m\u001b[3mCommon Stock\u001b[0m\u001b[3m \u001b[0m   6,522  \u001b[32m \u001b[0m\u001b[32m       $155.57\u001b[0m\u001b[32m \u001b[0m \u001b[2m \u001b[0m\u001b[2m2028-02-05\u001b[0m\u001b[2m \u001b[0m  Direct      │\n", "│                                                                                                   │\n", "╰───────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["initial_filings[10].obj()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}