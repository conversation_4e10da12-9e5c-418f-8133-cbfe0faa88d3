{"cells": [{"cell_type": "markdown", "id": "0995a262-7141-4a35-bf45-e7280f21885f", "metadata": {}, "source": ["# Reading Data from XBRL Files\n", "\n", "Some SEC filings, including **10-K's** and **10-Qs**, have XBRL files as attachments. This notebook shows how to access the data within these XBRL files."]}, {"cell_type": "markdown", "id": "8fde2744-72c3-4cc4-b6cc-cdeb7d803455", "metadata": {}, "source": ["**[Open this notebook in Google Colab](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/Reading-Data-From-XBRL.ipynb)**"]}, {"cell_type": "markdown", "id": "e1d009c9-220c-4242-97af-f57cfb1054ea", "metadata": {}, "source": ["## Install edgartools"]}, {"cell_type": "code", "execution_count": null, "id": "6edccaa6-5b20-4320-a59f-50b982098788", "metadata": {}, "outputs": [], "source": ["!pip install edgartools"]}, {"cell_type": "markdown", "id": "54fb7dce-cd50-4169-815a-54799fbae1d2", "metadata": {}, "source": ["## Import edgar"]}, {"cell_type": "code", "execution_count": 4, "id": "7ff5a292-66ef-4b57-ae7d-6929d212c332", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[16:43:54] </span><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> Identity of the Edgar REST client set to <span style=\"font-weight: bold\">[</span><EMAIL><span style=\"font-weight: bold\">]</span>                           <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">core.py:160</span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[16:43:54]\u001b[0m\u001b[2;36m \u001b[0m\u001b[34mINFO    \u001b[0m Identity of the Edgar REST client set to \u001b[1m[\u001b[<EMAIL>\u001b[1m]\u001b[0m                           \u001b[2mcore.py\u001b[0m\u001b[2m:\u001b[0m\u001b[2m160\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from edgar import *\n", "\n", "set_identity(\"<EMAIL>\")"]}, {"cell_type": "markdown", "id": "d8668b2c-4dce-480d-9806-ad5588b0fa4c", "metadata": {}, "source": ["## Getting a filing with XBRL"]}, {"cell_type": "code", "execution_count": 5, "id": "943cb51e-4c2f-4326-b3b7-2f19c3ff0559", "metadata": {}, "outputs": [], "source": "from legacy.xbrl import *"}, {"cell_type": "code", "execution_count": 18, "id": "1261ef8f-b77a-4847-a5a2-848e2cbc4f8a", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────────────────────────────────\u001b[1m Apple Inc. [320193] 10-Q 📊 \u001b[0m──────────────────────────────────────────╮\n", "│ ╭──────────────────────┬────────────╮                                                                           │\n", "│ │\u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m0000320193-24-000081\u001b[0m\u001b[1;38;5;39m \u001b[0m│ 2024-08-02 │                                                                           │\n", "│ ╰──────────────────────┴────────────╯                                                                           │\n", "│ ╭───────────────────────────────────────────────────────────────────────────────────────────╮                   │\n", "│ │\u001b[1m \u001b[0m\u001b[1mLinks\u001b[0m\u001b[1m: 🏠 Homepage 📄 Primary Document 📜 Full Submission Text                           \u001b[0m\u001b[1m \u001b[0m│                   │\n", "│ ├───────────────────────────────────────────────────────────────────────────────────────────┤                   │\n", "│ │ 🏠 https://sec.gov/Archives/edgar/data/320193/0000320193-24-000081-index.html             │                   │\n", "│ │ 📄 https://sec.gov/Archives/edgar/data/320193/000032019324000081/aapl-20240629.htm        │                   │\n", "│ │ 📜 https://sec.gov/Archives/edgar/data/320193/000032019324000081/0000320193-24-000081.txt │                   │\n", "│ ╰───────────────────────────────────────────────────────────────────────────────────────────╯                   │\n", "╰─────────────────────────────── Form 10-Q: Quarterly report for public companies ────────────────────────────────╯"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["filing = Company(\"AAPL\").get_filings(form=\"10-Q\").latest(1)\n", "filing"]}, {"cell_type": "markdown", "id": "847ed78f-49d5-43b2-aeae-96bad8269572", "metadata": {}, "source": ["## Getting XBRL from a filing\n", "The easiest way to parse XBRL is to call the `.xbrl()` function. This will return an `XbrlInstance` object for simple cases, and `XbrlData` object for more complicated XBRL, or `None` if the filing does not have XBRL."]}, {"cell_type": "code", "execution_count": 8, "id": "d9c431d4-2948-4dfc-bd6a-36cf79e7742f", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭─────────────────────────────────────────── XBRL Data for \u001b[1;38;5;32mApple Inc. \u001b[0m ───────────────────────────────────────────╮\n", "│ \u001b[3m              XBRL Instance Document              \u001b[0m                                                              │\n", "│ ╭────────────┬─────────────────┬─────────────────╮                                                              │\n", "│ │\u001b[1m \u001b[0m\u001b[1mCompany   \u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mNumber of Facts\u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mDocument Period\u001b[0m\u001b[1m \u001b[0m│                                                              │\n", "│ ├────────────┼─────────────────┼─────────────────┤                                                              │\n", "│ │ Apple Inc. │ 1,164           │ 2023-09-30      │                                                              │\n", "│ ╰────────────┴─────────────────┴─────────────────╯                                                              │\n", "│ ╭────┬────────────────────────────────────────────────────────────────────────────────────────────────────────╮ │\n", "│ │\u001b[1m \u001b[0m\u001b[1m  \u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mStatements                                                                                            \u001b[0m\u001b[1m \u001b[0m│ │\n", "│ ├────┼────────────────────────────────────────────────────────────────────────────────────────────────────────┤ │\n", "│ │ 0  │ \u001b[38;5;32mCover\u001b[0m\u001b[38;5;160mPage\u001b[0m                                                                                              │ │\n", "│ │ 1  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mInformation\u001b[0m                                                                                     │ │\n", "│ │ 2  │ \u001b[38;5;32mCONS<PERSON><PERSON>ATEDSTATEMENTSOFOPERATIONS\u001b[0m                                                                     │ │\n", "│ │ 3  │ \u001b[38;5;32mCONSOLIDATEDSTATEMENTSOFCOMPREHENSIVEINCOME\u001b[0m                                                            │ │\n", "│ │ 4  │ \u001b[38;5;32mCONSOLIDATEDBALANCESHEETS\u001b[0m                                                                              │ │\n", "│ │ 5  │ \u001b[38;5;32mCONSOLIDATEDBALANCESHEETS\u001b[0m\u001b[38;5;160mParenthetical\u001b[0m                                                                 │ │\n", "│ │ 6  │ \u001b[38;5;32mCONSOLIDATEDSTATEMENTSOFSHAREHOLDERSEQUITY\u001b[0m                                                             │ │\n", "│ │ 7  │ \u001b[38;5;32mCONSOLIDATEDSTATEMENTSOFCASHFLOWS\u001b[0m                                                                      │ │\n", "│ │ 8  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m                                                                 │ │\n", "│ │ 9  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>e\u001b[0m                                                                                                │ │\n", "│ │ 10 │ \u001b[38;5;32mEarnings\u001b[0m\u001b[38;5;160mPer\u001b[0m\u001b[38;5;71mShare\u001b[0m                                                                                       │ │\n", "│ │ 11 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m                                                                                   │ │\n", "│ │ 12 │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m                                                                              │ │\n", "│ │ 13 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                                  │ │\n", "│ │ 14 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m                                                                                            │ │\n", "│ │ 15 │ \u001b[38;5;32mLeases\u001b[0m                                                                                                 │ │\n", "│ │ 16 │ \u001b[38;5;32mDebt\u001b[0m                                                                                                   │ │\n", "│ │ 17 │ \u001b[38;5;32mShareholders\u001b[0m\u001b[38;5;160mEquity\u001b[0m                                                                                     │ │\n", "│ │ 18 │ \u001b[38;5;32mShare\u001b[0m\u001b[38;5;160mBased\u001b[0m\u001b[38;5;71mCompensation\u001b[0m                                                                                 │ │\n", "│ │ 19 │ \u001b[38;5;32mCommitments\u001b[0m\u001b[38;5;160mContingenciesand\u001b[0m\u001b[38;5;71mSupply\u001b[0m\u001b[38;5;32mConcentrations\u001b[0m                                                        │ │\n", "│ │ 20 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m                                                                    │ │\n", "│ │ 21 │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mPolicies\u001b[0m                                                         │ │\n", "│ │ 22 │ \u001b[38;5;32mRevenue\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                                          │ │\n", "│ │ 23 │ \u001b[38;5;32mEarnings\u001b[0m\u001b[38;5;160mPer\u001b[0m\u001b[38;5;71mShare\u001b[0m\u001b[38;5;32mTables\u001b[0m                                                                                 │ │\n", "│ │ 24 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71mTables\u001b[0m                                                                             │ │\n", "│ │ 25 │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m\u001b[38;5;32mTables\u001b[0m                                                                        │ │\n", "│ │ 26 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                            │ │\n", "│ │ 27 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mTables\u001b[0m                                                                                      │ │\n", "│ │ 28 │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                                           │ │\n", "│ │ 29 │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                                             │ │\n", "│ │ 30 │ \u001b[38;5;32mShareholders\u001b[0m\u001b[38;5;160mEquity\u001b[0m\u001b[38;5;71mTables\u001b[0m                                                                               │ │\n", "│ │ 31 │ \u001b[38;5;32mShare\u001b[0m\u001b[38;5;160mBased\u001b[0m\u001b[38;5;71mCompensation\u001b[0m\u001b[38;5;32mTables\u001b[0m                                                                           │ │\n", "│ │ 32 │ \u001b[38;5;32mCommitments\u001b[0m\u001b[38;5;160mContingenciesand\u001b[0m\u001b[38;5;71mSupply\u001b[0m\u001b[38;5;32mConcentrations\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                  │ │\n", "│ │ 33 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                              │ │\n", "│ │ 34 │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                                    │ │\n", "│ │ 35 │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mNet\u001b[0m\u001b[38;5;71mSales\u001b[0m\u001b[38;5;32mDisaggregatedby\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mProductsand\u001b[0m\u001b[38;5;32mServices\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                    │ │\n", "│ │ 36 │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mRevenue\u001b[0m\u001b[38;5;32mExpected\u001b[0m\u001b[38;5;160mTimingof\u001b[0m\u001b[38;5;71mRealization\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                               │ │\n", "│ │ 37 │ \u001b[38;5;32mEarnings\u001b[0m\u001b[38;5;160mPer\u001b[0m\u001b[38;5;71mShare\u001b[0m\u001b[38;5;32mComputationof\u001b[0m\u001b[38;5;160mBasicand\u001b[0m\u001b[38;5;71mDiluted\u001b[0m\u001b[38;5;32mEarnings\u001b[0m\u001b[38;5;160mPer\u001b[0m\u001b[38;5;71mShare\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                    │ │\n", "│ │ 38 │ \u001b[38;5;32mEarnings\u001b[0m\u001b[38;5;160mPer\u001b[0m\u001b[38;5;71mShare\u001b[0m\u001b[38;5;32mAdditional\u001b[0m\u001b[38;5;160mInformation\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                           │ │\n", "│ │ 39 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71mCash\u001b[0m\u001b[38;5;32mCash\u001b[0m\u001b[38;5;160mEquivalentsand\u001b[0m\u001b[38;5;71mMarketable\u001b[0m\u001b[38;5;32mSecurities\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                  │ │\n", "│ │ 40 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71m<PERSON>on\u001b[0m\u001b[38;5;32mCurrent\u001b[0m\u001b[38;5;160mMarketable\u001b[0m\u001b[38;5;71mDebt\u001b[0m\u001b[38;5;32mSecuritiesby\u001b[0m\u001b[38;5;160mContractual\u001b[0m\u001b[38;5;71mMaturity\u001b[0m\u001b[38;5;32mDetails\u001b[0m                     │ │\n", "│ │ 41 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71mAdditional\u001b[0m\u001b[38;5;32mInformation\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                       │ │\n", "│ │ 42 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71mNotional\u001b[0m\u001b[38;5;32mAmounts\u001b[0m\u001b[38;5;160mAssociatedwith\u001b[0m\u001b[38;5;71mDerivative\u001b[0m\u001b[38;5;32mInstruments\u001b[0m\u001b[38;5;160mDetails\u001b[0m                          │ │\n", "│ │ 43 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71m<PERSON><PERSON>\u001b[0m\u001b[38;5;32m<PERSON>air\u001b[0m\u001b[38;5;160mV<PERSON>uesof\u001b[0m\u001b[38;5;71mDerivative\u001b[0m\u001b[38;5;32mAssetsand\u001b[0m\u001b[38;5;160mLiabilities\u001b[0m\u001b[38;5;71mDetails\u001b[0m                             │ │\n", "│ │ 44 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71mDerivative\u001b[0m\u001b[38;5;32mInstruments\u001b[0m\u001b[38;5;160mDesignatedas\u001b[0m\u001b[38;5;71mFair\u001b[0m\u001b[38;5;32mValue\u001b[0m\u001b[38;5;160mHedgesand\u001b[0m\u001b[38;5;71mRelated\u001b[0m\u001b[38;5;32mHedged\u001b[0m\u001b[38;5;160mItems\u001b[0m\u001b[38;5;71mDetails\u001b[0m       │ │\n", "│ │ 45 │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m\u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160mProperty\u001b[0m\u001b[38;5;71mPlantand\u001b[0m\u001b[38;5;32mEquipmentby\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mAsset\u001b[0m\u001b[38;5;32mClassand\u001b[0m\u001b[38;5;160mAccumulated\u001b[0m\u001b[38;5;71mDepreciation\u001b[0m\u001b[38;5;32mDet…\u001b[0m │ │\n", "│ │ 46 │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m\u001b[38;5;32mAdditional\u001b[0m\u001b[38;5;160mInformation\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                  │ │\n", "│ │ 47 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m\u001b[38;5;160mOther\u001b[0m\u001b[38;5;71mNon\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mAssets\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                      │ │\n", "│ │ 48 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m\u001b[38;5;160mOther\u001b[0m\u001b[38;5;71mCurrent\u001b[0m\u001b[38;5;32mLiabilities\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                    │ │\n", "│ │ 49 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m\u001b[38;5;160mOther\u001b[0m\u001b[38;5;71mNon\u001b[0m\u001b[38;5;32m<PERSON>urrent\u001b[0m\u001b[38;5;160mLiabilities\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                 │ │\n", "│ │ 50 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m\u001b[38;5;160mOther\u001b[0m\u001b[38;5;71mIncome\u001b[0m\u001b[38;5;32mExpense\u001b[0m\u001b[38;5;160mNet\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                      │ │\n", "│ │ 51 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mProvisionfor\u001b[0m\u001b[38;5;32mInco<PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                              │ │\n", "│ │ 52 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mAdditional\u001b[0m\u001b[38;5;32mInformation\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                                │ │\n", "│ │ 53 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mReconciliationof\u001b[0m\u001b[38;5;32mProvisionfor\u001b[0m\u001b[38;5;160mIncome\u001b[0m\u001b[38;5;71m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160mComputedby\u001b[0m\u001b[38;5;71mApplyingthe\u001b[0m\u001b[38;5;32mStatutory\u001b[0m\u001b[38;5;160mFederal\u001b[0m\u001b[38;5;71mIncome\u001b[0m\u001b[38;5;32m…\u001b[0m │ │\n", "│ │ 54 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mSignificant\u001b[0m\u001b[38;5;32mComponentsof\u001b[0m\u001b[38;5;160mDeferred\u001b[0m\u001b[38;5;71mTax\u001b[0m\u001b[38;5;32mAssetsand\u001b[0m\u001b[38;5;160mLiabilities\u001b[0m\u001b[38;5;71mDetails\u001b[0m                               │ │\n", "│ │ 55 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mAggregate\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON>n\u001b[0m\u001b[38;5;160mGross\u001b[0m\u001b[38;5;71mUnrecognized\u001b[0m\u001b[38;5;32mTax\u001b[0m\u001b[38;5;160mBenefits\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                       │ │\n", "│ │ 56 │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                                     │ │\n", "│ │ 57 │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mROU\u001b[0m\u001b[38;5;71mAssetsand\u001b[0m\u001b[38;5;32mLease\u001b[0m\u001b[38;5;160mLiabilities\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                              │ │\n", "│ │ 58 │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mLease\u001b[0m\u001b[38;5;71mLiability\u001b[0m\u001b[38;5;32mMaturities\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                                  │ │\n", "│ │ 59 │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                                       │ │\n", "│ │ 60 │ \u001b[38;5;32mD<PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mCash\u001b[0m\u001b[38;5;32m<PERSON>lows\u001b[0m\u001b[38;5;160mAssociatedwith\u001b[0m\u001b[38;5;71mCommercial\u001b[0m\u001b[38;5;32m<PERSON>aper\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                             │ │\n", "│ │ 61 │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mTerm\u001b[0m\u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                                           │ │\n", "│ │ 62 │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mFuture\u001b[0m\u001b[38;5;71m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;32mPaymentsfor\u001b[0m\u001b[38;5;160mTerm\u001b[0m\u001b[38;5;71mDebt\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                          │ │\n", "│ │ 63 │ \u001b[38;5;32mShareholders\u001b[0m\u001b[38;5;160mEquity\u001b[0m\u001b[38;5;71mAdditional\u001b[0m\u001b[38;5;32mInformation\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                         │ │\n", "│ │ 64 │ \u001b[38;5;32mShareholders\u001b[0m\u001b[38;5;160mEquity\u001b[0m\u001b[38;5;71mShare<PERSON>f\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mStock\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                           │ │\n", "│ │ 65 │ \u001b[38;5;32mShare\u001b[0m\u001b[38;5;160mBased\u001b[0m\u001b[38;5;71mCompensation\u001b[0m\u001b[38;5;32mAdditional\u001b[0m\u001b[38;5;160mInformation\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                     │ │\n", "│ │ 66 │ \u001b[38;5;32mShare\u001b[0m\u001b[38;5;160mBased\u001b[0m\u001b[38;5;71mCompensation\u001b[0m\u001b[38;5;32mRestricted\u001b[0m\u001b[38;5;160mStock\u001b[0m\u001b[38;5;71mUnit\u001b[0m\u001b[38;5;32mActivityand\u001b[0m\u001b[38;5;160mRelated\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetails\u001b[0m                          │ │\n", "│ │ 67 │ \u001b[38;5;32mShare\u001b[0m\u001b[38;5;160mBased\u001b[0m\u001b[38;5;71mCompensation\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mShare\u001b[0m\u001b[38;5;71mBased\u001b[0m\u001b[38;5;32mCompensation\u001b[0m\u001b[38;5;160mExpenseandthe\u001b[0m\u001b[38;5;71mRelated\u001b[0m\u001b[38;5;32mI<PERSON><PERSON>\u001b[0m\u001b[38;5;160mTax\u001b[0m\u001b[38;5;71mBenefit\u001b[0m\u001b[38;5;32mDetails\u001b[0m       │ │\n", "│ │ 68 │ \u001b[38;5;32mCommitments\u001b[0m\u001b[38;5;160mContingenciesand\u001b[0m\u001b[38;5;71mSupply\u001b[0m\u001b[38;5;32mConcentrations\u001b[0m\u001b[38;5;160mFuture\u001b[0m\u001b[38;5;71mPayments\u001b[0m\u001b[38;5;32mUnder\u001b[0m\u001b[38;5;160mUnconditional\u001b[0m\u001b[38;5;71mPurchase\u001b[0m\u001b[38;5;32mObligations\u001b[0m\u001b[38;5;160mDet…\u001b[0m │ │\n", "│ │ 69 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m\u001b[38;5;160mInformationby\u001b[0m\u001b[38;5;71mReportable\u001b[0m\u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mDetails\u001b[0m                               │ │\n", "│ │ 70 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m\u001b[38;5;160mReconciliationof\u001b[0m\u001b[38;5;71mSegment\u001b[0m\u001b[38;5;32mOperating\u001b[0m\u001b[38;5;160mIncometothe\u001b[0m\u001b[38;5;71mConsolidated\u001b[0m\u001b[38;5;32mStatementso…\u001b[0m │ │\n", "│ │ 71 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m\u001b[38;5;160mNet\u001b[0m\u001b[38;5;71mSales\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                     │ │\n", "│ │ 72 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m\u001b[38;5;160mLong\u001b[0m\u001b[38;5;71mLived\u001b[0m\u001b[38;5;32mAssets\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                              │ │\n", "│ │ 73 │ \u001b[38;5;32mAward\u001b[0m\u001b[38;5;160mTiming\u001b[0m\u001b[38;5;71mDisclosure\u001b[0m                                                                                  │ │\n", "│ │ 74 │ \u001b[38;5;32mInsider\u001b[0m\u001b[38;5;160mTrading\u001b[0m\u001b[38;5;71mArrangements\u001b[0m                                                                             │ │\n", "│ │ 75 │ \u001b[38;5;32mErr\u001b[0m\u001b[38;5;160mComp\u001b[0m\u001b[38;5;71mDisclosure\u001b[0m                                                                                      │ │\n", "│ │ 76 │ \u001b[38;5;32mPvp\u001b[0m\u001b[38;5;160mDisclosure\u001b[0m                                                                                          │ │\n", "│ │ 77 │ \u001b[38;5;32mInsider\u001b[0m\u001b[38;5;160mTrading\u001b[0m\u001b[38;5;71mPolicies\u001b[0m\u001b[38;5;32m<PERSON>roc\u001b[0m                                                                             │ │\n", "│ ╰────┴────────────────────────────────────────────────────────────────────────────────────────────────────────╯ │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["xbrl_data = filing.xbrl()\n", "xbrl_data"]}, {"cell_type": "code", "execution_count": 27, "id": "2ed67c6b-a4de-4fdc-8fe6-f2f471e966b3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>value</th>\n", "      <th>units</th>\n", "      <th>decimals</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>period_type</th>\n", "      <th>duration</th>\n", "      <th>context_id</th>\n", "      <th>entity_id</th>\n", "      <th>dimensions</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>298085000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-14</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'us-gaap:ProductM...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>316199000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-15</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'us-gaap:ProductM...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>297392000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-16</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'us-gaap:ProductM...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>85200000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-17</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>78129000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-18</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>68425000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-19</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>383285000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-1</td>\n", "      <td>0000320193</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>394328000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-20</td>\n", "      <td>0000320193</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>365817000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-21</td>\n", "      <td>0000320193</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>395</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>200583000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-47</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:IPhoneMember'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>396</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>205489000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-48</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:IPhoneMember'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>397</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>191973000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-49</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:IPhoneMember'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>398</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>29357000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-50</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:MacMember'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>399</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>40177000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-51</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:MacMember'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>400</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>35190000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-52</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:MacMember'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>28300000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-53</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:IPadMember'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>402</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>29292000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-54</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:IPadMember'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>403</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>31862000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-55</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:IPadMember'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>404</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>39845000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-56</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:WearablesHo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>405</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>41241000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-57</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:WearablesHo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>406</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>38367000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-58</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'aapl:WearablesHo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>407</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>85200000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-17</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>408</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>78129000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-18</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>409</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>68425000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-19</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>410</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>383285000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-1</td>\n", "      <td>0000320193</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>411</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>394328000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-20</td>\n", "      <td>0000320193</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>412</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>365817000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-21</td>\n", "      <td>0000320193</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1082</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>162560000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-162</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1083</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>169658000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-163</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1084</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>153306000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-164</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1088</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-165</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1089</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-166</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1090</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-167</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1094</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-168</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1095</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-169</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1096</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-170</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1100</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-171</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1101</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-172</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1102</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-173</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1106</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-174</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1107</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-175</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1108</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-176</td>\n", "      <td>0000320193</td>\n", "      <td>{'us-gaap:StatementBusinessSegmentsAxis': 'aap...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1126</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>138573000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-186</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:StatementGeographicalAxis': 'country:US'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1127</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>147859000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-187</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:StatementGeographicalAxis': 'country:US'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1128</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>133803000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-188</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:StatementGeographicalAxis': 'country:US'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1129</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-189</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:StatementGeographicalAxis': 'country:CN'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1130</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-190</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:StatementGeographicalAxis': 'country:CN'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1131</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>***********</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-191</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:StatementGeographicalAxis': 'country:CN'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1132</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>172153000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-192</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:StatementGeographicalAxis': 'aapl:OtherC...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1133</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>172269000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-193</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:StatementGeographicalAxis': 'aapl:OtherC...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1134</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>163648000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-194</td>\n", "      <td>0000320193</td>\n", "      <td>{'srt:StatementGeographicalAxis': 'aapl:OtherC...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1135</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>383285000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2022-09-25</td>\n", "      <td>2023-09-30</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-1</td>\n", "      <td>0000320193</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1136</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>394328000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2021-09-26</td>\n", "      <td>2022-09-24</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-20</td>\n", "      <td>0000320193</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1137</th>\n", "      <td>us-gaap:RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>365817000000</td>\n", "      <td>usd</td>\n", "      <td>-6</td>\n", "      <td>2020-09-27</td>\n", "      <td>2021-09-25</td>\n", "      <td>duration</td>\n", "      <td>annual</td>\n", "      <td>c-21</td>\n", "      <td>0000320193</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                concept         value units  \\\n", "88    us-gaap:RevenueFromContractWithCustomerExcludi...  298085000000   usd   \n", "89    us-gaap:RevenueFromContractWithCustomerExcludi...  316199000000   usd   \n", "90    us-gaap:RevenueFromContractWithCustomerExcludi...  297392000000   usd   \n", "91    us-gaap:RevenueFromContractWithCustomerExcludi...   85200000000   usd   \n", "92    us-gaap:RevenueFromContractWithCustomerExcludi...   78129000000   usd   \n", "93    us-gaap:RevenueFromContractWithCustomerExcludi...   68425000000   usd   \n", "94    us-gaap:RevenueFromContractWithCustomerExcludi...  383285000000   usd   \n", "95    us-gaap:RevenueFromContractWithCustomerExcludi...  394328000000   usd   \n", "96    us-gaap:RevenueFromContractWithCustomerExcludi...  365817000000   usd   \n", "395   us-gaap:RevenueFromContractWithCustomerExcludi...  200583000000   usd   \n", "396   us-gaap:RevenueFromContractWithCustomerExcludi...  205489000000   usd   \n", "397   us-gaap:RevenueFromContractWithCustomerExcludi...  191973000000   usd   \n", "398   us-gaap:RevenueFromContractWithCustomerExcludi...   29357000000   usd   \n", "399   us-gaap:RevenueFromContractWithCustomerExcludi...   40177000000   usd   \n", "400   us-gaap:RevenueFromContractWithCustomerExcludi...   35190000000   usd   \n", "401   us-gaap:RevenueFromContractWithCustomerExcludi...   28300000000   usd   \n", "402   us-gaap:RevenueFromContractWithCustomerExcludi...   29292000000   usd   \n", "403   us-gaap:RevenueFromContractWithCustomerExcludi...   31862000000   usd   \n", "404   us-gaap:RevenueFromContractWithCustomerExcludi...   39845000000   usd   \n", "405   us-gaap:RevenueFromContractWithCustomerExcludi...   41241000000   usd   \n", "406   us-gaap:RevenueFromContractWithCustomerExcludi...   38367000000   usd   \n", "407   us-gaap:RevenueFromContractWithCustomerExcludi...   85200000000   usd   \n", "408   us-gaap:RevenueFromContractWithCustomerExcludi...   78129000000   usd   \n", "409   us-gaap:RevenueFromContractWithCustomerExcludi...   68425000000   usd   \n", "410   us-gaap:RevenueFromContractWithCustomerExcludi...  383285000000   usd   \n", "411   us-gaap:RevenueFromContractWithCustomerExcludi...  394328000000   usd   \n", "412   us-gaap:RevenueFromContractWithCustomerExcludi...  365817000000   usd   \n", "1082  us-gaap:RevenueFromContractWithCustomerExcludi...  162560000000   usd   \n", "1083  us-gaap:RevenueFromContractWithCustomerExcludi...  169658000000   usd   \n", "1084  us-gaap:RevenueFromContractWithCustomerExcludi...  153306000000   usd   \n", "1088  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1089  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1090  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1094  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1095  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1096  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1100  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1101  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1102  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1106  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1107  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1108  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1126  us-gaap:RevenueFromContractWithCustomerExcludi...  138573000000   usd   \n", "1127  us-gaap:RevenueFromContractWithCustomerExcludi...  147859000000   usd   \n", "1128  us-gaap:RevenueFromContractWithCustomerExcludi...  133803000000   usd   \n", "1129  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1130  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1131  us-gaap:RevenueFromContractWithCustomerExcludi...   ***********   usd   \n", "1132  us-gaap:RevenueFromContractWithCustomerExcludi...  172153000000   usd   \n", "1133  us-gaap:RevenueFromContractWithCustomerExcludi...  172269000000   usd   \n", "1134  us-gaap:RevenueFromContractWithCustomerExcludi...  163648000000   usd   \n", "1135  us-gaap:RevenueFromContractWithCustomerExcludi...  383285000000   usd   \n", "1136  us-gaap:RevenueFromContractWithCustomerExcludi...  394328000000   usd   \n", "1137  us-gaap:RevenueFromContractWithCustomerExcludi...  365817000000   usd   \n", "\n", "     decimals  start_date    end_date period_type duration context_id  \\\n", "88         -6  2022-09-25  2023-09-30    duration   annual       c-14   \n", "89         -6  2021-09-26  2022-09-24    duration   annual       c-15   \n", "90         -6  2020-09-27  2021-09-25    duration   annual       c-16   \n", "91         -6  2022-09-25  2023-09-30    duration   annual       c-17   \n", "92         -6  2021-09-26  2022-09-24    duration   annual       c-18   \n", "93         -6  2020-09-27  2021-09-25    duration   annual       c-19   \n", "94         -6  2022-09-25  2023-09-30    duration   annual        c-1   \n", "95         -6  2021-09-26  2022-09-24    duration   annual       c-20   \n", "96         -6  2020-09-27  2021-09-25    duration   annual       c-21   \n", "395        -6  2022-09-25  2023-09-30    duration   annual       c-47   \n", "396        -6  2021-09-26  2022-09-24    duration   annual       c-48   \n", "397        -6  2020-09-27  2021-09-25    duration   annual       c-49   \n", "398        -6  2022-09-25  2023-09-30    duration   annual       c-50   \n", "399        -6  2021-09-26  2022-09-24    duration   annual       c-51   \n", "400        -6  2020-09-27  2021-09-25    duration   annual       c-52   \n", "401        -6  2022-09-25  2023-09-30    duration   annual       c-53   \n", "402        -6  2021-09-26  2022-09-24    duration   annual       c-54   \n", "403        -6  2020-09-27  2021-09-25    duration   annual       c-55   \n", "404        -6  2022-09-25  2023-09-30    duration   annual       c-56   \n", "405        -6  2021-09-26  2022-09-24    duration   annual       c-57   \n", "406        -6  2020-09-27  2021-09-25    duration   annual       c-58   \n", "407        -6  2022-09-25  2023-09-30    duration   annual       c-17   \n", "408        -6  2021-09-26  2022-09-24    duration   annual       c-18   \n", "409        -6  2020-09-27  2021-09-25    duration   annual       c-19   \n", "410        -6  2022-09-25  2023-09-30    duration   annual        c-1   \n", "411        -6  2021-09-26  2022-09-24    duration   annual       c-20   \n", "412        -6  2020-09-27  2021-09-25    duration   annual       c-21   \n", "1082       -6  2022-09-25  2023-09-30    duration   annual      c-162   \n", "1083       -6  2021-09-26  2022-09-24    duration   annual      c-163   \n", "1084       -6  2020-09-27  2021-09-25    duration   annual      c-164   \n", "1088       -6  2022-09-25  2023-09-30    duration   annual      c-165   \n", "1089       -6  2021-09-26  2022-09-24    duration   annual      c-166   \n", "1090       -6  2020-09-27  2021-09-25    duration   annual      c-167   \n", "1094       -6  2022-09-25  2023-09-30    duration   annual      c-168   \n", "1095       -6  2021-09-26  2022-09-24    duration   annual      c-169   \n", "1096       -6  2020-09-27  2021-09-25    duration   annual      c-170   \n", "1100       -6  2022-09-25  2023-09-30    duration   annual      c-171   \n", "1101       -6  2021-09-26  2022-09-24    duration   annual      c-172   \n", "1102       -6  2020-09-27  2021-09-25    duration   annual      c-173   \n", "1106       -6  2022-09-25  2023-09-30    duration   annual      c-174   \n", "1107       -6  2021-09-26  2022-09-24    duration   annual      c-175   \n", "1108       -6  2020-09-27  2021-09-25    duration   annual      c-176   \n", "1126       -6  2022-09-25  2023-09-30    duration   annual      c-186   \n", "1127       -6  2021-09-26  2022-09-24    duration   annual      c-187   \n", "1128       -6  2020-09-27  2021-09-25    duration   annual      c-188   \n", "1129       -6  2022-09-25  2023-09-30    duration   annual      c-189   \n", "1130       -6  2021-09-26  2022-09-24    duration   annual      c-190   \n", "1131       -6  2020-09-27  2021-09-25    duration   annual      c-191   \n", "1132       -6  2022-09-25  2023-09-30    duration   annual      c-192   \n", "1133       -6  2021-09-26  2022-09-24    duration   annual      c-193   \n", "1134       -6  2020-09-27  2021-09-25    duration   annual      c-194   \n", "1135       -6  2022-09-25  2023-09-30    duration   annual        c-1   \n", "1136       -6  2021-09-26  2022-09-24    duration   annual       c-20   \n", "1137       -6  2020-09-27  2021-09-25    duration   annual       c-21   \n", "\n", "       entity_id                                         dimensions  \n", "88    0000320193  {'srt:ProductOrServiceAxis': 'us-gaap:ProductM...  \n", "89    0000320193  {'srt:ProductOrServiceAxis': 'us-gaap:ProductM...  \n", "90    0000320193  {'srt:ProductOrServiceAxis': 'us-gaap:ProductM...  \n", "91    0000320193  {'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...  \n", "92    0000320193  {'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...  \n", "93    0000320193  {'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...  \n", "94    0000320193                                                 {}  \n", "95    0000320193                                                 {}  \n", "96    0000320193                                                 {}  \n", "395   0000320193  {'srt:ProductOrServiceAxis': 'aapl:IPhoneMember'}  \n", "396   0000320193  {'srt:ProductOrServiceAxis': 'aapl:IPhoneMember'}  \n", "397   0000320193  {'srt:ProductOrServiceAxis': 'aapl:IPhoneMember'}  \n", "398   0000320193     {'srt:ProductOrServiceAxis': 'aapl:MacMember'}  \n", "399   0000320193     {'srt:ProductOrServiceAxis': 'aapl:MacMember'}  \n", "400   0000320193     {'srt:ProductOrServiceAxis': 'aapl:MacMember'}  \n", "401   0000320193    {'srt:ProductOrServiceAxis': 'aapl:IPadMember'}  \n", "402   0000320193    {'srt:ProductOrServiceAxis': 'aapl:IPadMember'}  \n", "403   0000320193    {'srt:ProductOrServiceAxis': 'aapl:IPadMember'}  \n", "404   0000320193  {'srt:ProductOrServiceAxis': 'aapl:WearablesHo...  \n", "405   0000320193  {'srt:ProductOrServiceAxis': 'aapl:WearablesHo...  \n", "406   0000320193  {'srt:ProductOrServiceAxis': 'aapl:WearablesHo...  \n", "407   0000320193  {'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...  \n", "408   0000320193  {'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...  \n", "409   0000320193  {'srt:ProductOrServiceAxis': 'us-gaap:ServiceM...  \n", "410   0000320193                                                 {}  \n", "411   0000320193                                                 {}  \n", "412   0000320193                                                 {}  \n", "1082  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1083  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1084  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1088  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1089  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1090  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1094  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1095  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1096  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1100  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1101  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1102  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1106  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1107  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1108  0000320193  {'us-gaap:StatementBusinessSegmentsAxis': 'aap...  \n", "1126  0000320193    {'srt:StatementGeographicalAxis': 'country:US'}  \n", "1127  0000320193    {'srt:StatementGeographicalAxis': 'country:US'}  \n", "1128  0000320193    {'srt:StatementGeographicalAxis': 'country:US'}  \n", "1129  0000320193    {'srt:StatementGeographicalAxis': 'country:CN'}  \n", "1130  0000320193    {'srt:StatementGeographicalAxis': 'country:CN'}  \n", "1131  0000320193    {'srt:StatementGeographicalAxis': 'country:CN'}  \n", "1132  0000320193  {'srt:StatementGeographicalAxis': 'aapl:OtherC...  \n", "1133  0000320193  {'srt:StatementGeographicalAxis': 'aapl:OtherC...  \n", "1134  0000320193  {'srt:StatementGeographicalAxis': 'aapl:OtherC...  \n", "1135  0000320193                                                 {}  \n", "1136  0000320193                                                 {}  \n", "1137  0000320193                                                 {}  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["(xbrl_data.instance\n", ".query_facts(concept=\"us-gaap:RevenueFromContractWithCustomerExcludingAssessedTax\")\n", ")"]}, {"cell_type": "code", "execution_count": 13, "id": "8527f75c-85eb-41bf-88a8-19ec4c6a1f4f", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["                                                    \u001b[1;38;5;196mApple Inc.\u001b[0m                                                     \n", "                                          \u001b[1mStatement of Financial Position\u001b[0m                                          \n", "                                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m                                                                         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m      2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m      2022\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "  \u001b[1;38;5;32mStatement of Financial Position [Abstract]                               \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "  \u001b[1;38;5;32m ASSETS:                                                                 \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "  \u001b[1;38;5;32m  Current assets:                                                        \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "     Cash and cash equivalents                                                \u001b[2;38;5;249mmillions \u001b[0m       29,965       23,646  \n", "     Marketable securities                                                    \u001b[2;38;5;249mmillions \u001b[0m       31,590       24,658  \n", "     Accounts receivable, net                                                 \u001b[2;38;5;249mmillions \u001b[0m       29,508       28,184  \n", "     Vendor non-trade receivables                                             \u001b[2;38;5;249mmillions \u001b[0m       31,477       32,748  \n", "     Inventories                                                              \u001b[2;38;5;249mmillions \u001b[0m        6,331        4,946  \n", "     Other current assets                                                     \u001b[2;38;5;249mmillions \u001b[0m       14,695       21,223  \n", "  \u001b[1m   Total current assets                                                  \u001b[0m   \u001b[2;38;5;249mmillions \u001b[0m   \u001b[1m   143,566\u001b[0m   \u001b[1m   135,405\u001b[0m  \n", "                                                                                                                   \n", "  \u001b[1;38;5;32m  Non-current assets:                                                    \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "     Marketable securities                                                    \u001b[2;38;5;249mmillions \u001b[0m      100,544      120,805  \n", "     Property, plant and equipment, net                                       \u001b[2;38;5;249mmillions \u001b[0m       43,715       42,117  \n", "     Other non-current assets                                                 \u001b[2;38;5;249mmillions \u001b[0m       64,758       54,428  \n", "     Total non-current assets                                                 \u001b[2;38;5;249mmillions \u001b[0m      209,017      217,350  \n", "  \u001b[1m  Total assets                                                           \u001b[0m   \u001b[2;38;5;249mmillions \u001b[0m   \u001b[1m   352,583\u001b[0m   \u001b[1m   352,755\u001b[0m  \n", "                                                                                                                   \n", "  \u001b[1;38;5;32m LIABILITIES AND SHAREHOLDERS’ EQUITY:                                   \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "  \u001b[1;38;5;32m  Current liabilities:                                                   \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "     Accounts payable                                                         \u001b[2;38;5;249mmillions \u001b[0m       62,611       64,115  \n", "     Other current liabilities                                                \u001b[2;38;5;249mmillions \u001b[0m       58,829       60,845  \n", "     Deferred revenue                                                         \u001b[2;38;5;249mmillions \u001b[0m        8,061        7,912  \n", "     Commercial paper                                                         \u001b[2;38;5;249mmillions \u001b[0m        5,985        9,982  \n", "     Term debt                                                                \u001b[2;38;5;249mmillions \u001b[0m        9,822       11,128  \n", "  \u001b[1m   Total current liabilities                                             \u001b[0m   \u001b[2;38;5;249mmillions \u001b[0m   \u001b[1m   145,308\u001b[0m   \u001b[1m   153,982\u001b[0m  \n", "                                                                                                                   \n", "  \u001b[1;38;5;32m  Non-current liabilities:                                               \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "     Term debt                                                                \u001b[2;38;5;249mmillions \u001b[0m       95,281       98,959  \n", "     Other non-current liabilities                                            \u001b[2;38;5;249mmillions \u001b[0m       49,848       49,142  \n", "     Total non-current liabilities                                            \u001b[2;38;5;249mmillions \u001b[0m      145,129      148,101  \n", "    Total liabilities                                                         \u001b[2;38;5;249mmillions \u001b[0m      290,437      302,083  \n", "    Commitments and contingencies                                             \u001b[2;38;5;249m         \u001b[0m                            \n", "    Common stock, shares outstanding (in shares)                              \u001b[2;38;5;249mthousands\u001b[0m   15,550,061   15,943,425  \n", "    Common stock, shares issued (in shares)                                   \u001b[2;38;5;249mthousands\u001b[0m   15,550,061   15,943,425  \n", "  \u001b[1;38;5;32m  Shareholders’ equity:                                                  \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "     Common stock and additional paid-in capital, $0.00001 par value:         \u001b[2;38;5;249mmillions \u001b[0m       73,812       64,849  \n", "  50,400,000 shares authorized; 15,550,061 and 15,943,425 shares issued and                                        \n", "  outstanding, respectively                                                                                        \n", "     Accumulated deficit                                                      \u001b[2;38;5;249mmillions \u001b[0m         -214       -3,068  \n", "     Accumulated other comprehensive loss                                     \u001b[2;38;5;249mmillions \u001b[0m      -11,452      -11,109  \n", "     Total shareholders’ equity                                               \u001b[2;38;5;249mmillions \u001b[0m       62,146       50,672  \n", "  \u001b[1m  Total liabilities and shareholders’ equity                             \u001b[0m   \u001b[2;38;5;249mmillions \u001b[0m   \u001b[1m   352,583\u001b[0m   \u001b[1m   352,755\u001b[0m  \n", "                                                                                                                   "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["statements = xbrl_data.statements\n", "statements['CONSOLIDATEDBALANCESHEETS']"]}, {"cell_type": "code", "execution_count": 15, "id": "ac3959b1-28a3-4ab0-86bb-ae86d3f2f123", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["                                                    \u001b[1;38;5;196mApple Inc.\u001b[0m                                                     \n", "                                          \u001b[1mStatement of Financial Position\u001b[0m                                          \n", "                                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m                                                                         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m         \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m      2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m      2022\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "  \u001b[1;38;5;32mStatement of Financial Position [Abstract]                               \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "  \u001b[1;38;5;32m ASSETS:                                                                 \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "  \u001b[1;38;5;32m  Current assets:                                                        \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "     Cash and cash equivalents                                                \u001b[2;38;5;249mmillions \u001b[0m       29,965       23,646  \n", "     Marketable securities                                                    \u001b[2;38;5;249mmillions \u001b[0m       31,590       24,658  \n", "     Accounts receivable, net                                                 \u001b[2;38;5;249mmillions \u001b[0m       29,508       28,184  \n", "     Vendor non-trade receivables                                             \u001b[2;38;5;249mmillions \u001b[0m       31,477       32,748  \n", "     Inventories                                                              \u001b[2;38;5;249mmillions \u001b[0m        6,331        4,946  \n", "     Other current assets                                                     \u001b[2;38;5;249mmillions \u001b[0m       14,695       21,223  \n", "  \u001b[1m   Total current assets                                                  \u001b[0m   \u001b[2;38;5;249mmillions \u001b[0m   \u001b[1m   143,566\u001b[0m   \u001b[1m   135,405\u001b[0m  \n", "                                                                                                                   \n", "  \u001b[1;38;5;32m  Non-current assets:                                                    \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "     Marketable securities                                                    \u001b[2;38;5;249mmillions \u001b[0m      100,544      120,805  \n", "     Property, plant and equipment, net                                       \u001b[2;38;5;249mmillions \u001b[0m       43,715       42,117  \n", "     Other non-current assets                                                 \u001b[2;38;5;249mmillions \u001b[0m       64,758       54,428  \n", "     Total non-current assets                                                 \u001b[2;38;5;249mmillions \u001b[0m      209,017      217,350  \n", "  \u001b[1m  Total assets                                                           \u001b[0m   \u001b[2;38;5;249mmillions \u001b[0m   \u001b[1m   352,583\u001b[0m   \u001b[1m   352,755\u001b[0m  \n", "                                                                                                                   \n", "  \u001b[1;38;5;32m LIABILITIES AND SHAREHOLDERS’ EQUITY:                                   \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "  \u001b[1;38;5;32m  Current liabilities:                                                   \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "     Accounts payable                                                         \u001b[2;38;5;249mmillions \u001b[0m       62,611       64,115  \n", "     Other current liabilities                                                \u001b[2;38;5;249mmillions \u001b[0m       58,829       60,845  \n", "     Deferred revenue                                                         \u001b[2;38;5;249mmillions \u001b[0m        8,061        7,912  \n", "     Commercial paper                                                         \u001b[2;38;5;249mmillions \u001b[0m        5,985        9,982  \n", "     Term debt                                                                \u001b[2;38;5;249mmillions \u001b[0m        9,822       11,128  \n", "  \u001b[1m   Total current liabilities                                             \u001b[0m   \u001b[2;38;5;249mmillions \u001b[0m   \u001b[1m   145,308\u001b[0m   \u001b[1m   153,982\u001b[0m  \n", "                                                                                                                   \n", "  \u001b[1;38;5;32m  Non-current liabilities:                                               \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "     Term debt                                                                \u001b[2;38;5;249mmillions \u001b[0m       95,281       98,959  \n", "     Other non-current liabilities                                            \u001b[2;38;5;249mmillions \u001b[0m       49,848       49,142  \n", "     Total non-current liabilities                                            \u001b[2;38;5;249mmillions \u001b[0m      145,129      148,101  \n", "    Total liabilities                                                         \u001b[2;38;5;249mmillions \u001b[0m      290,437      302,083  \n", "    Commitments and contingencies                                             \u001b[2;38;5;249m         \u001b[0m                            \n", "    Common stock, shares outstanding (in shares)                              \u001b[2;38;5;249mthousands\u001b[0m   15,550,061   15,943,425  \n", "    Common stock, shares issued (in shares)                                   \u001b[2;38;5;249mthousands\u001b[0m   15,550,061   15,943,425  \n", "  \u001b[1;38;5;32m  Shareholders’ equity:                                                  \u001b[0m   \u001b[2;38;5;249m         \u001b[0m                            \n", "     Common stock and additional paid-in capital, $0.00001 par value:         \u001b[2;38;5;249mmillions \u001b[0m       73,812       64,849  \n", "  50,400,000 shares authorized; 15,550,061 and 15,943,425 shares issued and                                        \n", "  outstanding, respectively                                                                                        \n", "     Accumulated deficit                                                      \u001b[2;38;5;249mmillions \u001b[0m         -214       -3,068  \n", "     Accumulated other comprehensive loss                                     \u001b[2;38;5;249mmillions \u001b[0m      -11,452      -11,109  \n", "     Total shareholders’ equity                                               \u001b[2;38;5;249mmillions \u001b[0m       62,146       50,672  \n", "  \u001b[1m  Total liabilities and shareholders’ equity                             \u001b[0m   \u001b[2;38;5;249mmillions \u001b[0m   \u001b[1m   352,583\u001b[0m   \u001b[1m   352,755\u001b[0m  \n", "                                                                                                                   "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["xbrl_data.get_statement('CONSOLIDATEDBALANCESHEETS')"]}, {"cell_type": "markdown", "id": "6f4c59ee-eb61-4948-b2a7-702cbac562f2", "metadata": {}, "source": ["## View the structure of the XBRL"]}, {"cell_type": "code", "execution_count": 7, "id": "aa7c715e-22c0-44ca-9e68-a46bd17a354e", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1;32mXBRL Presentation Structure\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/CoverPage\u001b[0m\n", "│   └── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mCoverAbstract\u001b[0m\n", "│       └── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mEntitiesTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementClassOfStockAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mClassOfStockDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockMember\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mA1.375NotesDue2024Member\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mA0.000Notesdue2025Member\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mA0.875NotesDue2025Member\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mA1.625NotesDue2026Member\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mA2.000NotesDue2027Member\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mA1.375NotesDue2029Member\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mA3.050NotesDue2029Member\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mA0.500Notesdue2031Member\u001b[0m\n", "│           │       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mA3.600NotesDue2042Member\u001b[0m\n", "│           └── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mEntityInformationLineItems\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mDocumentType\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mDocumentAnnualReport\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mCurrentFiscalYearEndDate\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mDocumentPeriodEndDate\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mDocumentTransitionReport\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mEntityFileNumber\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mEntityRegistrantName\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mEntityIncorporationStateCountryCode\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mEntityTaxIdentificationNumber\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mEntityAddressAddressLine1\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mEntityAddressCityOrTown\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mEntityAddressStateOrProvince\u001b[0m\n", "│               ├── \u001b[1;33mde<PERSON>\u001b[0m \u001b[1;38;5;39mEntityAddressPostalZipCode\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mCityAreaCode\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mLocalPhoneNumber\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mSecurity12bTitle\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mTradingSymbol\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mNoTradingSymbolFlag\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mSecurityExchangeName\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mEntityWellKnownSeasonedIssuer\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mEntityVoluntaryFilers\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mEntityCurrentReportingStatus\u001b[0m\n", "│               ├── \u001b[1;33mde<PERSON>\u001b[0m \u001b[1;38;5;39mEntityInteractiveDataCurrent\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mEntityFilerCategory\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mEntitySmallBusiness\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mEntityEmergingGrowthCompany\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mIcfrAuditorAttestationFlag\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mDocumentFinStmtErrorCorrectionFlag\u001b[0m\n", "│               ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mEntityShellCompany\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mEntityPublicFloat\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mEntityCommonStockSharesOutstanding\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mDocumentsIncorporatedByReferenceTextBlock\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mAmendmentFlag\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mDocumentFiscalYearFocus\u001b[0m\n", "│               ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mDocumentFiscalPeriodFocus\u001b[0m\n", "│               └── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mEntityCentralIndexKey\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/AuditorInformation\u001b[0m\n", "│   └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mAuditorInformationAbstract\u001b[0m\n", "│       ├── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\n", "│       ├── \u001b[1;33mdei\u001b[0m \u001b[1;38;5;39mAuditorLocation\u001b[0m\n", "│       └── \u001b[1;33m<PERSON><PERSON>\u001b[0m \u001b[1;38;5;39mAuditorFirmId\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/CONSOLIDATEDSTATEMENTSOFOPERATIONS\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeStatementAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementTable\u001b[0m\n", "│           ├── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mProductOrServiceAxis\u001b[0m\n", "│           │   └── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mProductsAndServicesDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProductMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mServiceMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueFromContractWithCustomerExcludingAssessedTax\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCostOfGoodsAndServicesSold\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mGrossProfit\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingExpensesAbstract\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mResearchAndDevelopmentExpense\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSellingGeneralAndAdministrativeExpense\u001b[0m\n", "│               │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingExpenses\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingIncomeLoss\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNonoperatingIncomeExpense\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   \u001b[1;38;5;39mIncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxExpenseBenefit\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetIncomeLoss\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEarningsPerShareAbstract\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEarningsPerShareBasic\u001b[0m\n", "│               │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEarningsPerShareDiluted\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mWeightedAverageNumberOfSharesOutstandingAbstract\u001b[0m\n", "│                   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mWeightedAverageNumberOfSharesOutstandingBasic\u001b[0m\n", "│                   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mWeightedAverageNumberOfDilutedSharesOutstanding\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/CONSOLIDATEDSTATEMENTSOFCOMPREHENSIVEINCOME\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementOfIncomeAndComprehensiveIncomeAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetIncomeLoss\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mComprehensiveIncomeNetOfTaxAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherComprehensiveIncomeLossForeignCurrencyTransactionAndTranslationAdjustmentNetOfTax\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherComprehensiveIncomeDerivativesQualifyingAsHedgesNetOfTaxPeriodIncreaseDecreaseAbstract\u001b[0m\n", "│       │   │   ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOtherComprehensiveIncomeLossDerivativeInstrumentGainLossbeforeReclassificationafterTax\u001b[0m\n", "│       │   │   ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOtherComprehensiveIncomeLossDerivativeInstrumentGainLossReclassificationAfterTax\u001b[0m\n", "│       │   │   └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOtherComprehensiveIncomeLossDerivativeInstrumentGainLossafterReclassificationandTax\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \n", "│       │   │   \u001b[1;38;5;39mOtherComprehensiveIncomeAvailableForSaleSecuritiesAdjustmentNetOfTaxPeriodIncreaseDecreaseAbstract\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \n", "│       │   │   │   \u001b[1;38;5;39mOtherComprehensiveIncomeUnrealizedHoldingGainLossOnSecuritiesArisingDuringPeriodNetOfTax\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \n", "│       │   │   │   \u001b[1;38;5;39mOtherComprehensiveIncomeLossReclassificationAdjustmentFromAOCIForSaleOfSecuritiesNetOfTax\u001b[0m\n", "│       │   │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherComprehensiveIncomeLossAvailableForSaleSecuritiesAdjustmentNetOfTax\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherComprehensiveIncomeLossNetOfTaxPortionAttributableToParent\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mComprehensiveIncomeNetOfTax\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/CONSOLIDATEDBALANCESHEETS\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementOfFinancialPositionAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAssetsAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAssetsCurrentAbstract\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCashAndCashEquivalentsAtCarryingValue\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMarketableSecuritiesCurrent\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAccountsReceivableNetCurrent\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNontradeReceivablesCurrent\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInventoryNet\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherAssetsCurrent\u001b[0m\n", "│       │   │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAssetsNoncurrentAbstract\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMarketableSecuritiesNoncurrent\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentNet\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherAssetsNoncurrent\u001b[0m\n", "│       │   │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAssetsNoncurrent\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAssets\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLiabilitiesAndStockholdersEquityAbstract\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLiabilitiesCurrentAbstract\u001b[0m\n", "│           │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAccountsPayableCurrent\u001b[0m\n", "│           │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherLiabilitiesCurrent\u001b[0m\n", "│           │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mContractWithCustomerLiabilityCurrent\u001b[0m\n", "│           │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommercialPaper\u001b[0m\n", "│           │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtCurrent\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLiabilitiesCurrent\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLiabilitiesNoncurrentAbstract\u001b[0m\n", "│           │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtNoncurrent\u001b[0m\n", "│           │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherLiabilitiesNoncurrent\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLiabilitiesNoncurrent\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLiabilities\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;******************************\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockSharesOutstanding\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockSharesIssued\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockholdersEquityAbstract\u001b[0m\n", "│           │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStocksIncludingAdditionalPaidInCapital\u001b[0m\n", "│           │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRetainedEarningsAccumulatedDeficit\u001b[0m\n", "│           │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAccumulatedOtherComprehensiveIncomeLossNetOfTax\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockholdersEquity\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLiabilitiesAndStockholdersEquity\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/CONSOLIDATEDBALANCESHEETSParenthetical\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementOfFinancialPositionAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockParOrStatedValuePerShare\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockSharesAuthorized\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockSharesIssued\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockSharesOutstanding\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/CONSOLIDATEDSTATEMENTSOFSHAREHOLDERSEQUITY\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementOfStockholdersEquityAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementEquityComponentsAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEquityComponentDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockIncludingAdditionalPaidInCapitalMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRetainedEarningsMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAccumulatedOtherComprehensiveIncomeMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncreaseDecreaseInStockholdersEquityRollForward\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockholdersEquity\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockIssuedDuringPeriodValueNewIssues\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAdjustmentsRelatedToTaxWithholdingForShareBasedCompensation\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   │   \u001b[1;38;5;39mAdjustmentsToAdditionalPaidInCapitalSharebasedCompensationRequisiteServicePeriodRecognition\u001b[0m\n", "│               │   │   \u001b[1;38;5;39mValue\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetIncomeLoss\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDividends\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockRepurchasedAndRetiredDuringPeriodValue\u001b[0m\n", "│               │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherComprehensiveIncomeLossNetOfTaxPortionAttributableToParent\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockDividendsPerShareDeclared\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/CONSOLIDATEDSTATEMENTSOFCASHFLOWS\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementOfCashFlowsAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetCashProvidedByUsedInOperatingActivitiesContinuingOperationsAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetIncomeLoss\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAdjustmentsToReconcileNetIncomeLossToCashProvidedByUsedInOperatingActivitiesAbstract\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDepreciationDepletionAndAmortization\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShareBasedCompensation\u001b[0m\n", "│       │   │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherNoncashIncomeExpense\u001b[0m\n", "│       │   │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncreaseDecreaseInOperatingCapitalAbstract\u001b[0m\n", "│       │   │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncreaseDecreaseInAccountsReceivable\u001b[0m\n", "│       │   │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncreaseDecreaseInOtherReceivables\u001b[0m\n", "│       │   │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncreaseDecreaseInInventories\u001b[0m\n", "│       │   │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncreaseDecreaseInOtherOperatingAssets\u001b[0m\n", "│       │   │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncreaseDecreaseInAccountsPayable\u001b[0m\n", "│       │   │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncreaseDecreaseInOtherOperatingLiabilities\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetCashProvidedByUsedInOperatingActivities\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetCashProvidedByUsedInInvestingActivitiesContinuingOperationsAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPaymentsToAcquireAvailableForSaleSecuritiesDebt\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromMaturitiesPrepaymentsAndCallsOfAvailableForSaleSecurities\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromSaleOfAvailableForSaleSecuritiesDebt\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPaymentsToAcquirePropertyPlantAndEquipment\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPaymentsForProceedsFromOtherInvestingActivities\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetCashProvidedByUsedInInvestingActivities\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetCashProvidedByUsedInFinancingActivitiesContinuingOperationsAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPaymentsRelatedToTaxWithholdingForShareBasedCompensation\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPaymentsOfDividends\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPaymentsForRepurchaseOfCommonStock\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromIssuanceOfLongTermDebt\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRepaymentsOfLongTermDebt\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromRepaymentsOfCommercialPaper\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromPaymentsForOtherFinancingActivities\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetCashProvidedByUsedInFinancingActivities\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \n", "│       │   \u001b[1;38;5;39mCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRat\u001b[0m\n", "│       │   \u001b[1;38;5;39meEffect\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSupplementalCashFlowInformationAbstract\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxesPaidNet\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInterestPaidNet\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/SummaryofSignificantAccountingPolicies\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAccountingPoliciesAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mBasisOfPresentationAndSignificantAccountingPoliciesTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/Revenue\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueFromContractWithCustomerAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueFromContractWithCustomerTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/EarningsPerShare\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEarningsPerShareAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEarningsPerShareTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/FinancialInstruments\u001b[0m\n", "│   └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mFinancialInstrumentsAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinancialInstrumentsDisclosureTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/PropertyPlantandEquipment\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentDisclosureTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ConsolidatedFinancialStatementDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOrganizationConsolidationAndPresentationOfFinancialStatementsAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAdditionalFinancialInformationDisclosureTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/IncomeTaxes\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxDisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxDisclosureTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/Leases\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLeasesAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeOperatingLeasesTextBlock\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeFinanceLeasesTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/Debt\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtDisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtDisclosureTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ShareholdersEquity\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEquityAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockholdersEquityNoteDisclosureTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ShareBasedCompensation\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDisclosureOfCompensationRelatedCostsSharebasedPaymentsAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDisclosureOfCompensationRelatedCostsShareBasedPaymentsTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/CommitmentsContingenciesandSupplyConcentrations\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;******************************DisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;******************************DisclosureTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/SegmentInformationandGeographicData\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSegmentReportingAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSegmentReportingDisclosureTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/SummaryofSignificantAccountingPoliciesPolicies\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAccountingPoliciesAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mBasisOfAccountingPolicyPolicyTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39m<PERSON>iscalPeriod\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueRecognitionPolicyTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCompensationRelatedCostsPolicyTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCashAndCashEquivalentsPolicyTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMarketableSecuritiesPolicy\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInventoryPolicyTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentPolicyTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativesPolicyTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxPolicyTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeLeasesPolicyTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueMeasurementPolicyPolicyTextBlock\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSegmentReportingPolicyPolicyTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/RevenueTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueFromContractWithCustomerAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDisaggregationOfRevenueTableTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/EarningsPerShareTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEarningsPerShareAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfEarningsPerShareBasicAndDilutedTableTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/FinancialInstrumentsTables\u001b[0m\n", "│   └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mFinancialInstrumentsAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfCashCashEquivalentsAndShortTermInvestmentsTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInvestmentsClassifiedByContractualMaturityDateTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfNotionalAmountsOfOutstandingDerivativePositionsTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfDerivativeInstrumentsInStatementOfFinancialPositionFairValueTextBlock\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \n", "│           \u001b[1;38;5;39mScheduleOfFairValueHedgingInstrumentsStatementsOfFinancialPerformanceAndFinancialPositionLocationTableT\u001b[0m\n", "│           \u001b[1;38;5;39mextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/PropertyPlantandEquipmentTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ConsolidatedFinancialStatementDetailsTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOrganizationConsolidationAndPresentationOfFinancialStatementsAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfOtherAssetsNoncurrentTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherCurrentLiabilitiesTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherNoncurrentLiabilitiesTableTextBlock\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInterestAndOtherIncomeTableTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/IncomeTaxesTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxDisclosureAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfComponentsOfIncomeTaxExpenseBenefitTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfEffectiveIncomeTaxRateReconciliationTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfDeferredTaxAssetsAndLiabilitiesTableTextBlock\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfUnrecognizedTaxBenefitsRollForwardTableTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/LeasesTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLeasesAbstract\u001b[0m\n", "│       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOperatingandFinanceLeaseRightofUseAssetsandLeaseLiabilitiesTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeOperatingLeaseLiabilityMaturityTableTextBlock\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityMaturityTableTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/DebtTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtDisclosureAbstract\u001b[0m\n", "│       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mCommercialPaperCashFlowSummaryTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfDebtInstrumentsTextBlock\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfMaturitiesOfLongTermDebtTableTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ShareholdersEquityTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEquityAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfCommonStockOutstandingRollForwardTableTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ShareBasedCompensationTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDisclosureOfCompensationRelatedCostsSharebasedPaymentsAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfNonvestedRestrictedStockUnitsActivityTableTextBlock\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfEmployeeServiceShareBasedCompensationAllocationOfRecognizedPeriodCostsTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/CommitmentsContingenciesandSupplyConcentrationsTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;******************************DisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecordedUnconditionalPurchaseObligationsDisclosureTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/SegmentInformationandGeographicDataTables\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSegmentReportingAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfSegmentReportingInformationBySegmentTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mReconciliationOfOperatingProfitLossFromSegmentsToConsolidatedTextBlock\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfRevenuesFromExternalCustomersAndLongLivedAssetsByGeographicalAreasTableTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/RevenueAdditionalInformationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueFromContractWithCustomerAbstract\u001b[0m\n", "│       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mPerformanceObligationsinArrangements\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mContractWithCustomerLiabilityRevenueRecognized\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mContractWithCustomerLiability\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/RevenueNetSalesDisaggregatedbySignificantProductsandServicesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueFromContractWithCustomerAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDisaggregationOfRevenueTable\u001b[0m\n", "│           ├── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mProductOrServiceAxis\u001b[0m\n", "│           │   └── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mProductsAndServicesDomain\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mIPhoneMember\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mMacMember\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mIPadMember\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mWearablesHomeandAccessoriesMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mServiceMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDisaggregationOfRevenueLineItems\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueFromContractWithCustomerExcludingAssessedTax\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/RevenueDeferredRevenueExpectedTimingofRealizationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueFromContractWithCustomerAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueRemainingPerformanceObligationExpectedTimingOfSatisfactionTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueRemainingPerformanceObligationExpectedTimingOfSatisfactionStartDateAxis\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueRemainingPerformanceObligationExpectedTimingOfSatisfactionLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueRemainingPerformanceObligationPercentage\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueRemainingPerformanceObligationExpectedTimingOfSatisfactionPeriod1\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/EarningsPerShareComputationofBasicandDilutedEarningsPerShareDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEarningsPerShareAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetIncomeLossAbstract\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetIncomeLoss\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mWeightedAverageNumberOfSharesOutstandingAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mWeightedAverageNumberOfSharesOutstandingBasic\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncrementalCommonSharesAttributableToShareBasedPaymentArrangements\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mWeightedAverageNumberOfDilutedSharesOutstanding\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEarningsPerShareBasic\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEarningsPerShareDiluted\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/EarningsPerShareAdditionalInformationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEarningsPerShareAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfAntidilutiveSecuritiesExcludedFromComputationOfEarningsPerShareTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAntidilutiveSecuritiesExcludedFromComputationOfEarningsPerShareByAntidilutiveSecuritiesAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAntidilutiveSecuritiesNameDomain\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRestrictedStockUnitsRSUMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAntidilutiveSecuritiesExcludedFromComputationOfEarningsPerShareLineItems\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAntidilutiveSecuritiesExcludedFromComputationOfEarningsPerShareAmount\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/FinancialInstrumentsCashCashEquivalentsandMarketableSecuritiesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInvestmentsDebtAndEquitySecuritiesAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMarketableSecuritiesTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinancialInstrumentAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mTransfersAndServicingOfFinancialInstrumentsTypesOfFinancialInstrumentsDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCashMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMoneyMarketFundsMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEquitySecuritiesMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMutualFundMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUSTreasurySecuritiesMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUSGovernmentAgenciesDebtSecuritiesMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mForeignGovernmentDebtSecuritiesMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mBankTimeDepositsMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommercialPaperMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCorporateDebtSecuritiesMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUSStatesAndPoliticalSubdivisionsMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAssetBackedSecuritiesMember\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueByFairValueHierarchyLevelAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueMeasurementsFairValueHierarchyDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueInputsLevel1Member\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueInputsLevel2Member\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMarketableSecuritiesLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCash\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEquitySecuritiesFvNiCost\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mEquitySecuritiesFVNIAccumulatedGrossUnrealizedGainBeforeTax\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mEquitySecuritiesFVNIAccumulatedGrossUnrealizedLossBeforeTax\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEquitySecuritiesFvNiCurrentAndNoncurrent\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAvailableForSaleDebtSecuritiesAmortizedCostBasis\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAvailableForSaleDebtSecuritiesAccumulatedGrossUnrealizedGainBeforeTax\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAvailableForSaleDebtSecuritiesAccumulatedGrossUnrealizedLossBeforeTax\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAvailableForSaleSecuritiesDebtSecurities\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mCashCashEquivalentsAndMarketableSecuritiesCost\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mCashEquivalentsAndMarketableSecuritiesAccumulatedGrossUnrealizedGainBeforeTax\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mCashEquivalentsAndMarketableSecuritiesAccumulatedGrossUnrealizedLossBeforeTax\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mCashCashEquivalentsAndMarketableSecurities\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCashAndCashEquivalentsAtCarryingValue\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMarketableSecuritiesCurrent\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMarketableSecuritiesNoncurrent\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtSecuritiesAvailableForSaleRestricted\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/FinancialInstrumentsNonCurrentMarketableDebtSecuritiesbyContractualMaturityDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInvestmentsDebtAndEquitySecuritiesAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAvailableForSaleSecuritiesDebtMaturitiesSingleMaturityDateRollingMaturityAbstract\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAvailableForSaleSecuritiesDebtMaturitiesRollingYearTwoThroughFiveFairValue\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAvailableForSaleSecuritiesDebtMaturitiesRollingYearSixThroughTenFairValue\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAvailableForSaleSecuritiesDebtMaturitiesRollingAfterYearTenFairValue\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAvailableForSaleSecuritiesDebtMaturitiesSingleMaturityDate\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/FinancialInstrumentsAdditionalInformationDetails\u001b[0m\n", "│   └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mFinancialInstrumentsAbstract\u001b[0m\n", "│       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mFinancialInstrumentsTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeInstrumentRiskAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeContractTypeDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mForeignExchangeContractMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCrossCurrencyInterestRateContractMember\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mConcentrationRiskByBenchmarkAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mConcentrationRiskBenchmarkDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mTradeAccountsReceivableMember\u001b[0m\n", "│           │       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mNonTradeReceivableMember\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mConcentrationRiskByTypeAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mConcentrationRiskTypeDomain\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCreditConcentrationRiskMember\u001b[0m\n", "│           ├── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mMajorCustomersAxis\u001b[0m\n", "│           │   └── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mNameOfMajorCustomerDomain\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mCustomerOneMember\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mCellularNetworkCarriersMember\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mVendorOneMember\u001b[0m\n", "│           │       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mVendorTwoMember\u001b[0m\n", "│           └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mFinancialInstrumentsLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMaximumLengthOfTimeForeignCurrencyCashFlowHedge\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueConcentrationOfRiskDerivativeFinancialInstrumentsAssets\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mDerivativeAssetsReductionForMasterNettingArrangementsIncludingTheEffectsOfCollateral\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mDerivativeLiabilitiesReductionForMasterNettingArrangementsIncludingTheEffectsOfCollateral\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeFairValueOfDerivativeNet\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mNumberOfCustomersWithSignificantAccountsReceivableBalance\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mConcentrationRiskPercentage1\u001b[0m\n", "│               └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mNumberOfSignificantVendors\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/FinancialInstrumentsNotionalAmountsAssociatedwithDerivativeInstrumentsDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeInstrumentsAndHedgingActivitiesDisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValuesDerivativesBalanceSheetLocationByDerivativeContractTypeByHedgingDesignationTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mHedgingDesignationAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mHedgingDesignationDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDesignatedAsHedgingInstrumentMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNondesignatedMember\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeInstrumentRiskAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeContractTypeDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mForeignExchangeContractMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInterestRateContractMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativesFairValueLineItems\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeNotionalAmount\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/FinancialInstrumentsGrossFairValuesofDerivativeAssetsandLiabilitiesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeInstrumentsAndHedgingActivitiesDisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValuesDerivativesBalanceSheetLocationByDerivativeContractTypeByHedgingDesignationTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mBalanceSheetLocationAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mBalanceSheetLocationDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherAssetsMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherLiabilitiesMember\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeInstrumentRiskAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeContractTypeDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mForeignExchangeContractMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInterestRateContractMember\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueByFairValueHierarchyLevelAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueMeasurementsFairValueHierarchyDomain\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueInputsLevel2Member\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mHedgingDesignationAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mHedgingDesignationDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDesignatedAsHedgingInstrumentMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNondesignatedMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeInstrumentsAndHedgingActivitiesDisclosuresLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeAssetsAbstract\u001b[0m\n", "│               │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeAssetFairValueGrossAssetIncludingNotSubjectToMasterNettingArrangement\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeLiabilitiesAbstract\u001b[0m\n", "│                   └── \u001b[1;33mus-gaap\u001b[0m \n", "│                       \u001b[1;38;5;39mDerivativeLiabilityFairValueGrossLiabilityIncludingNotSubjectToMasterNettingArrangement\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/FinancialInstrumentsDerivativeInstrumentsDesignatedasFairValueHedgesandRelatedHedgedI\u001b[0m\n", "│   \u001b[1;34mtemsDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDerivativeInstrumentsAndHedgingActivitiesDisclosureAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mHedgedAssetFairValueHedge\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mHedgedAssetStatementOfFinancialPositionExtensibleEnumeration\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mHedgedLiabilityFairValueHedge\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mHedgedLiabilityStatementOfFinancialPositionExtensibleEnumeration\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/PropertyPlantandEquipmentGrossPropertyPlantandEquipmentbyMajorAssetClassandAccumulate\u001b[0m\n", "│   \u001b[1;34mdDepreciationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfPropertyPlantAndEquipmentTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentByTypeAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentTypeDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLandAndBuildingMember\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mMachineryEquipmentandInternalUseSoftwareMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLeaseholdImprovementsMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentGross\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAccumulatedDepreciationDepletionAndAmortizationPropertyPlantAndEquipment\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentNet\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/PropertyPlantandEquipmentAdditionalInformationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPropertyPlantAndEquipmentAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDepreciation\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ConsolidatedFinancialStatementDetailsOtherNonCurrentAssetsDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOrganizationConsolidationAndPresentationOfFinancialStatementsAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredIncomeTaxAssetsNet\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherAssetsMiscellaneousNoncurrent\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherAssetsNoncurrent\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ConsolidatedFinancialStatementDetailsOtherCurrentLiabilitiesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOrganizationConsolidationAndPresentationOfFinancialStatementsAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAccruedIncomeTaxesCurrent\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherAccruedLiabilitiesCurrent\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherLiabilitiesCurrent\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ConsolidatedFinancialStatementDetailsOtherNonCurrentLiabilitiesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOrganizationConsolidationAndPresentationOfFinancialStatementsAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAccruedIncomeTaxesNoncurrent\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherAccruedLiabilitiesNoncurrent\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherLiabilitiesNoncurrent\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ConsolidatedFinancialStatementDetailsOtherIncomeExpenseNetDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOrganizationConsolidationAndPresentationOfFinancialStatementsAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInvestmentIncomeInterestAndDividend\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInterestExpense\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherNonoperatingIncomeExpense\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNonoperatingIncomeExpense\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/IncomeTaxesProvisionforIncomeTaxesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxDisclosureAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFederalIncomeTaxExpenseBenefitContinuingOperationsAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCurrentFederalTaxExpenseBenefit\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredFederalIncomeTaxExpenseBenefit\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFederalIncomeTaxExpenseBenefitContinuingOperations\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStateAndLocalIncomeTaxExpenseBenefitContinuingOperationsAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCurrentStateAndLocalTaxExpenseBenefit\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredStateAndLocalIncomeTaxExpenseBenefit\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStateAndLocalIncomeTaxExpenseBenefitContinuingOperations\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mForeignIncomeTaxExpenseBenefitContinuingOperationsAbstract\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCurrentForeignTaxExpenseBenefit\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredForeignIncomeTaxExpenseBenefit\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mForeignIncomeTaxExpenseBenefitContinuingOperations\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxExpenseBenefit\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/IncomeTaxesAdditionalInformationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxDisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxContingencyTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLossContingenciesByNatureOfContingencyAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLossContingencyNatureDomain\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mUnfavorableInvestigationOutcomeEUStateAidRulesMember\u001b[0m\n", "│           │       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mUnfavorableInvestigationOutcomeEUStateAidRulesInterestComponentMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxContingencyLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeLossFromContinuingOperationsBeforeIncomeTaxesForeign\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEffectiveIncomeTaxRateReconciliationAtFederalStatutoryIncomeTaxRate\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsTaxCreditCarryforwardsForeign\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsTaxCreditCarryforwardsResearch\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecognizedTaxBenefits\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecognizedTaxBenefitsThatWouldImpactEffectiveTaxRate\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDecreaseInUnrecognizedTaxBenefitsIsReasonablyPossible\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mIncomeTaxContingencyNumberOfSubsidiaries\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLossContingencyEstimateOfPossibleLoss\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/IncomeTaxesReconciliationofProvisionforIncomeTaxestoAmountComputedbyApplyingtheStatut\u001b[0m\n", "│   \u001b[1;34moryFederalIncomeTaxRatetoIncomeBeforeProvisionforIncomeTaxesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxDisclosureAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxReconciliationIncomeTaxExpenseBenefitAtFederalStatutoryIncomeTaxRate\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxReconciliationStateAndLocalIncomeTaxes\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxReconciliationForeignIncomeTaxRateDifferential\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxReconciliationTaxCreditsResearch\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEffectiveIncomeTaxRateReconciliationShareBasedCompensationExcessTaxBenefitAmount\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEffectiveIncomeTaxRateReconciliationFdiiAmount\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxReconciliationOtherAdjustments\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxExpenseBenefit\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEffectiveIncomeTaxRateContinuingOperations\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/IncomeTaxesSignificantComponentsofDeferredTaxAssetsandLiabilitiesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxDisclosureAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsGrossAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsTaxCreditCarryforwards\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsTaxDeferredExpenseReservesAndAccruals\u001b[0m\n", "│       │   ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsCapitalizedResearchAndDevelopment\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsDeferredIncome\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsOtherComprehensiveLoss\u001b[0m\n", "│       │   ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsLeaseLiabilities\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsOther\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mD<PERSON><PERSON>redTaxAssetsGross\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsValuationAllowance\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsNet\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxLiabilitiesAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxLiabilitiesLeasingArrangements\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxLiabilitiesPropertyPlantAndEquipment\u001b[0m\n", "│       │   ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mDeferredTaxLiabilitiesMinimumTaxonForeignEarnings\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxLiabilitiesOtherComprehensiveIncome\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxLiabilitiesOther\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredIncomeTaxLiabilities\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDeferredTaxAssetsLiabilitiesNet\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/IncomeTaxesAggregateChangesinGrossUnrecognizedTaxBenefitsDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncomeTaxDisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \n", "│           \u001b[1;38;5;39mReconciliationOfUnrecognizedTaxBenefitsExcludingAmountsPertainingToExaminedTaxReturnsRollForward\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecognizedTaxBenefits\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecognizedTaxBenefitsIncreasesResultingFromPriorPeriodTaxPositions\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecognizedTaxBenefitsDecreasesResultingFromPriorPeriodTaxPositions\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecognizedTaxBenefitsIncreasesResultingFromCurrentPeriodTaxPositions\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecognizedTaxBenefitsDecreasesResultingFromSettlementsWithTaxingAuthorities\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecognizedTaxBenefitsReductionsResultingFromLapseOfApplicableStatuteOfLimitations\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/LeasesAdditionalInformationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLeasesAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeLeaseDescriptionTable\u001b[0m\n", "│           ├── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\n", "│           │   └── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39m<PERSON><PERSON>eMember\u001b[0m\n", "│           │       ├── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mMinimumMember\u001b[0m\n", "│           │       └── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mMaximumMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeLeaseDescriptionLineItems\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingandFinanceLeaseTermofContract\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingLeaseCost\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mVariableLeaseCost\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingLeasePayments\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mRightofUseAssetsObtainedinExchangeforOperatingandFinanceLeaseLiabilities\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOperatingandFinanceLeaseWeightedAverageRemainingLeaseTerm\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOperatingandFinanceLeaseWeightedAverageDiscountRatePercent\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingAndFinanceLeaseLeaseNotYetCommencedPaymentsDue\u001b[0m\n", "│               └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingAndFinanceLeaseLeaseNotYetCommencedTermOfContract\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/LeasesROUAssetsandLeaseLiabilitiesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLeasesAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAssetsAndLiabilitiesLesseeAbstract\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingLeaseRightOfUseAsset\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingLeaseRightOfUseAssetStatementOfFinancialPositionExtensibleList\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseRightOfUseAsset\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseRightOfUseAssetStatementOfFinancialPositionExtensibleList\u001b[0m\n", "│           ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOperatingandFinanceLeaseRightofUseAsset\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingLeaseLiabilityCurrent\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingLeaseLiabilityCurrentStatementOfFinancialPositionExtensibleList\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingLeaseLiabilityNoncurrent\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingLeaseLiabilityNoncurrentStatementOfFinancialPositionExtensibleList\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityCurrent\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityCurrentStatementOfFinancialPositionExtensibleList\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityNoncurrent\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityNoncurrentStatementOfFinancialPositionExtensibleList\u001b[0m\n", "│           └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOperatingandFinanceLeaseLiability\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/LeasesLeaseLiabilityMaturitiesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLeasesAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingLeaseLiabilitiesPaymentsDueAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeOperatingLeaseLiabilityPaymentsDueNextTwelveMonths\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeOperatingLeaseLiabilityPaymentsDueYearTwo\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeOperatingLeaseLiabilityPaymentsDueYearThree\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeOperatingLeaseLiabilityPaymentsDueYearFour\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeOperatingLeaseLiabilityPaymentsDueYearFive\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeOperatingLeaseLiabilityPaymentsDueAfterYearFive\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeOperatingLeaseLiabilityPaymentsDue\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLesseeOperatingLeaseLiabilityUndiscountedExcessAmount\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingLeaseLiability\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilitiesPaymentsDueAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityPaymentsDueNextTwelveMonths\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityPaymentsDueYearTwo\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityPaymentsDueYearThree\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityPaymentsDueYearFour\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityPaymentsDueYearFive\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityPaymentsDueAfterYearFive\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityPaymentsDue\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiabilityUndiscountedExcessAmount\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFinanceLeaseLiability\u001b[0m\n", "│       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingandFinanceLeaseLiabilityPaymentDueAbstract\u001b[0m\n", "│           ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingAndFinanceLeaseLiabilityToBePaidYearOne\u001b[0m\n", "│           ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingAndFinanceLeaseLiabilityToBePaidYearTwo\u001b[0m\n", "│           ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingAndFinanceLeaseLiabilityToBePaidYearThree\u001b[0m\n", "│           ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingAndFinanceLeaseLiabilityToBePaidYearFour\u001b[0m\n", "│           ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingAndFinanceLeaseLiabilityToBePaidYearFive\u001b[0m\n", "│           ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingAndFinanceLeaseLiabilityToBePaidAfterYearFive\u001b[0m\n", "│           ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingAndFinanceLeaseLiabilityToBePaid\u001b[0m\n", "│           ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mLesseeOperatingandFinanceLeaseLiabilityUndiscountedExcessAmount\u001b[0m\n", "│           └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOperatingandFinanceLeaseLiability\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/DebtAdditionalInformationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtDisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShortTermDebtTypeAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShortTermDebtTypeDomain\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommercialPaperMember\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueByFairValueHierarchyLevelAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueMeasurementsFairValueHierarchyDomain\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mFairValueInputsLevel2Member\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommercialPaper\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentTerm\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShortTermDebtWeightedAverageInterestRate\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mInterestCostsIncurred\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtFairValue\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/DebtSummaryofCashFlowsAssociatedwithCommercialPaperDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtDisclosureAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromRepaymentsOfShortTermDebtMaturingInThreeMonthsOrLessAlternativeAbstract\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromRepaymentsOfShortTermDebtMaturingInThreeMonthsOrLess\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromRepaymentsOfShortTermDebtMaturingInMoreThanThreeMonthsAlternativeAbstract\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromShortTermDebtMaturingInMoreThanThreeMonths\u001b[0m\n", "│       │   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRepaymentsOfShortTermDebtMaturingInMoreThanThreeMonths\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromRepaymentsOfShortTermDebtMaturingInMoreThanThreeMonths\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mProceedsFromRepaymentsOfCommercialPaper\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/DebtSummaryofTermDebtDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtDisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentNameDomain\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mA20132022DebtIssuancesMember\u001b[0m\n", "│           │       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mThirdQuarter2023DebtIssuanceMember\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongtermDebtTypeAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongtermDebtTypeDomain\u001b[0m\n", "│           │       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mFixedRateNotesMember\u001b[0m\n", "│           ├── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\n", "│           │   └── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39m<PERSON><PERSON>eMember\u001b[0m\n", "│           │       ├── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mMinimumMember\u001b[0m\n", "│           │       └── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mMaximumMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentCarryingAmount\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentUnamortizedDiscountPremiumAndDebtIssuanceCostsNet\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mHedgeAccountingAdjustmentsRelatedToLongTermDebt\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebt\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtCurrent\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtNoncurrent\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mDebtInstrumentMaturityYearRangeStart\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mDebtInstrumentMaturityYearRangeEnd\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentInterestRateStatedPercentage\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentInterestRateEffectivePercentage\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/DebtFuturePrincipalPaymentsforTermDebtDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtDisclosureAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtMaturitiesRepaymentsOfPrincipalInNextTwelveMonths\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtMaturitiesRepaymentsOfPrincipalInYearTwo\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtMaturitiesRepaymentsOfPrincipalInYearThree\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtMaturitiesRepaymentsOfPrincipalInYearFour\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtMaturitiesRepaymentsOfPrincipalInYearFive\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mLongTermDebtMaturitiesRepaymentsOfPrincipalAfterYearFive\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDebtInstrumentCarryingAmount\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ShareholdersEquityAdditionalInformationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEquityAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockholdersEquityNoteAbstract\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockRepurchasedAndRetiredDuringPeriodShares\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockRepurchasedAndRetiredDuringPeriodValue\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ShareholdersEquitySharesofCommonStockDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementOfStockholdersEquityAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementEquityComponentsAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEquityComponentDomain\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementLineItems\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mIncreaseDecreaseInStockholdersEquityRollForward\u001b[0m\n", "│                   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCommonStockSharesOutstanding\u001b[0m\n", "│                   ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockRepurchasedAndRetiredDuringPeriodShares\u001b[0m\n", "│                   └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mStockIssuedDuringPeriodSharesSharebasedPaymentArrangementNetofSharesWithheldforTaxes\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ShareBasedCompensationAdditionalInformationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDisclosureOfCompensationRelatedCostsSharebasedPaymentsAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfShareBasedCompensationArrangementsByShareBasedPaymentAwardTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPlanNameAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPlanNameDomain\u001b[0m\n", "│           │       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mEmployeeStockPlan2022PlanMember\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAwardTypeAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShareBasedCompensationArrangementsByShareBasedPaymentAwardAwardTypeAndPlanNameDomain\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRestrictedStockUnitsRSUMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardAwardVestingPeriod1\u001b[0m\n", "│               ├── \u001b[1;33maapl\u001b[0m \n", "│               │   \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsNumbe\u001b[0m\n", "│               │   \u001b[1;38;5;39mrOfSharesOfCommonStockIssuedPerUnitUponVesting\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardNumberOfSharesAuthorized\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsVeste\u001b[0m\n", "│               │   \u001b[1;38;5;39mdInPeriodTotalFairValue\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSharesPaidForTaxWithholdingForShareBasedCompensation\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mPaymentsRelatedToTaxWithholdingForShareBasedCompensation\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   \u001b[1;38;5;39mEmployeeServiceShareBasedCompensationNonvestedAwardsTotalCompensationCostNotYetRecognized\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \n", "│                   \u001b[1;38;5;39mEmployeeServiceShareBasedCompensationNonvestedAwardsTotalCompensationCostNotYetRecognizedPeriod\u001b[0m\n", "│                   \u001b[1;38;5;39mForRecognition1\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ShareBasedCompensationRestrictedStockUnitActivityandRelatedInformationDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDisclosureOfCompensationRelatedCostsSharebasedPaymentsAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfShareBasedCompensationArrangementsByShareBasedPaymentAwardTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAwardTypeAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShareBasedCompensationArrangementsByShareBasedPaymentAwardAwardTypeAndPlanNameDomain\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRestrictedStockUnitsRSUMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsNonve\u001b[0m\n", "│               │   \u001b[1;38;5;39mstedRollForward\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   │   \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsN\u001b[0m\n", "│               │   │   \u001b[1;38;5;39monvestedNumber\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   │   \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsG\u001b[0m\n", "│               │   │   \u001b[1;38;5;39mrantsInPeriod\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   │   \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsV\u001b[0m\n", "│               │   │   \u001b[1;38;5;39mestedInPeriod\u001b[0m\n", "│               │   └── \u001b[1;33mus-gaap\u001b[0m \n", "│               │       \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsF\u001b[0m\n", "│               │       \u001b[1;38;5;39morfeitedInPeriod\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsNonve\u001b[0m\n", "│               │   \u001b[1;38;5;39mstedWeightedAverageGrantDateFairValueRollForward\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   │   \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsN\u001b[0m\n", "│               │   │   \u001b[1;38;5;39monvestedWeightedAverageGrantDateFairValue\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   │   \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsG\u001b[0m\n", "│               │   │   \u001b[1;38;5;39mrantsInPeriodWeightedAverageGrantDateFairValue\u001b[0m\n", "│               │   ├── \u001b[1;33mus-gaap\u001b[0m \n", "│               │   │   \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsV\u001b[0m\n", "│               │   │   \u001b[1;38;5;39mestedInPeriodWeightedAverageGrantDateFairValue\u001b[0m\n", "│               │   └── \u001b[1;33mus-gaap\u001b[0m \n", "│               │       \u001b[1;38;5;39mShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsF\u001b[0m\n", "│               │       \u001b[1;38;5;39morfeituresWeightedAverageGrantDateFairValue\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \n", "│                   \u001b[1;38;5;39mSharebasedCompensationArrangementBySharebasedPaymentAwardEquityInstrumentsOtherThanOptionsAggre\u001b[0m\n", "│                   \u001b[1;38;5;39mgateIntrinsicValueAbstract\u001b[0m\n", "│                   └── \u001b[1;33mus-gaap\u001b[0m \n", "│                       \u001b[1;38;5;39mSharebasedCompensationArrangementBySharebasedPaymentAwardEquityInstrumentsOtherThanOptionsA\u001b[0m\n", "│                       \u001b[1;38;5;39mggregateIntrinsicValueNonvested\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/ShareBasedCompensationSummaryofShareBasedCompensationExpenseandtheRelatedIncomeTaxBen\u001b[0m\n", "│   \u001b[1;34mefitDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mDisclosureOfCompensationRelatedCostsSharebasedPaymentsAbstract\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAllocatedShareBasedCompensationExpense\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEmployeeServiceShareBasedCompensationTaxBenefitFromCompensationExpense\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/CommitmentsContingenciesandSupplyConcentrationsFuturePaymentsUnderUnconditionalPurcha\u001b[0m\n", "│   \u001b[1;34mseObligationsDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;******************************DisclosureAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecordedUnconditionalPurchaseObligationAbstract\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecordedUnconditionalPurchaseObligationBalanceOnFirstAnniversary\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecordedUnconditionalPurchaseObligationBalanceOnSecondAnniversary\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecordedUnconditionalPurchaseObligationBalanceOnThirdAnniversary\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecordedUnconditionalPurchaseObligationBalanceOnFourthAnniversary\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecordedUnconditionalPurchaseObligationBalanceOnFifthAnniversary\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecordedUnconditionalPurchaseObligationDueAfterFiveYears\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mUnrecordedUnconditionalPurchaseObligationBalanceSheetAmount\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/SegmentInformationandGeographicDataInformationbyReportableSegmentDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSegmentReportingAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfSegmentReportingInformationBySegmentTable\u001b[0m\n", "│           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStatementBusinessSegmentsAxis\u001b[0m\n", "│           │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSeg<PERSON><PERSON><PERSON><PERSON>\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mAmericasSegmentMember\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mEuropeSegmentMember\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mGreaterChinaSegmentMember\u001b[0m\n", "│           │       ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mJapanSegmentMember\u001b[0m\n", "│           │       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mRestOfAsiaPacificSegmentMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSegmentReportingInformationLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueFromContractWithCustomerExcludingAssessedTax\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingIncomeLoss\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/SegmentInformationandGeographicDataReconciliationofSegmentOperatingIncometotheConsoli\u001b[0m\n", "│   \u001b[1;34mdatedStatementsofOperationsDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSegmentReportingAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mReconciliationOfOperatingProfitLossFromSegmentsToConsolidatedTable\u001b[0m\n", "│           ├── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mConsolidationItemsAxis\u001b[0m\n", "│           │   └── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mConsolidationItemsDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingSegmentsMember\u001b[0m\n", "│           │       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mMaterialReconcilingItemsMember\u001b[0m\n", "│           │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mCorporateNonSegmentMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSegmentReportingReconcilingItemForOperatingProfitLossFromSegmentToConsolidatedLineItems\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOperatingIncomeLoss\u001b[0m\n", "│               ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mResearchAndDevelopmentExpense\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mOtherGeneralAndAdministrativeExpense\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/SegmentInformationandGeographicDataNetSalesDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSegmentReportingAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfRevenuesFromExternalCustomersAndLongLivedAssetsTable\u001b[0m\n", "│           ├── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mStatementGeographicalAxis\u001b[0m\n", "│           │   └── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mSegmentGeographicalDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mcountry\u001b[0m \u001b[1;38;5;39mUS\u001b[0m\n", "│           │       ├── \u001b[1;33mcountry\u001b[0m \u001b[1;38;5;39mCN\u001b[0m\n", "│           │       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOtherCountriesMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenuesFromExternalCustomersAndLongLivedAssetsLineItems\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenueFromContractWithCustomerExcludingAssessedTax\u001b[0m\n", "├── \u001b[1;34mhttp://www.apple.com/role/SegmentInformationandGeographicDataLongLivedAssetsDetails\u001b[0m\n", "│   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mSegmentReportingAbstract\u001b[0m\n", "│       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mScheduleOfRevenuesFromExternalCustomersAndLongLivedAssetsTable\u001b[0m\n", "│           ├── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mStatementGeographicalAxis\u001b[0m\n", "│           │   └── \u001b[1;33msrt\u001b[0m \u001b[1;38;5;39mSegmentGeographicalDomain\u001b[0m\n", "│           │       ├── \u001b[1;33mcountry\u001b[0m \u001b[1;38;5;39mUS\u001b[0m\n", "│           │       ├── \u001b[1;33mcountry\u001b[0m \u001b[1;38;5;39mCN\u001b[0m\n", "│           │       └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39mOtherCountriesMember\u001b[0m\n", "│           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRevenuesFromExternalCustomersAndLongLivedAssetsLineItems\u001b[0m\n", "│               └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNoncurrentAssets\u001b[0m\n", "├── \u001b[1;34mhttp://xbrl.sec.gov/ecd/role/AwardTimingDisclosure\u001b[0m\n", "│   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardTmgDiscLineItems\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardTmgMnpiDiscTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardTmgMethodTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardTmgPredtrmndFlag\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardTmgMnpiCnsdrdFlag\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardTmgHowMnpiCnsdrdTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mMnpiDiscTimedForCompValFlag\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardsCloseToMnpiDiscTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardsCloseToMnpiDiscTable\u001b[0m\n", "│       │   ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mIndividualAxis\u001b[0m\n", "│       │   │   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAllIndividualsMember\u001b[0m\n", "│       │   └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mAwardTypeAxis\u001b[0m\n", "│       │       └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mShareBasedCompensationArrangementsByShareBasedPaymentAwardAwardTypeAndPlanNameDomain\u001b[0m\n", "│       │           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mEmployeeStockOptionMember\u001b[0m\n", "│       │           ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mStockAppreciationRightsSARSMember\u001b[0m\n", "│       │           └── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mRestrictedStockUnitsRSUMember\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardsCloseToMnpiDiscIndName\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardUndrlygSecuritiesAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardExrcPrice\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAwardGrantDateFairValue\u001b[0m\n", "│       └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mUndrlygSecurityMktPriceChngPct\u001b[0m\n", "├── \u001b[1;34mhttp://xbrl.sec.gov/ecd/role/InsiderTradingArrangements\u001b[0m\n", "│   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mInsiderTradingArrLineItems\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTradingArrByIndTable\u001b[0m\n", "│       │   ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTradingArrAxis\u001b[0m\n", "│       │   │   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAllTradingArrangementsMember\u001b[0m\n", "│       │   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mIndividualAxis\u001b[0m\n", "│       │       └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAllIndividualsMember\u001b[0m\n", "│       │           ├── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39m<PERSON><PERSON><PERSON><PERSON><PERSON>B<PERSON>Member\u001b[0m\n", "│       │           └── \u001b[1;33maapl\u001b[0m \u001b[1;38;5;39m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s<PERSON>ember\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mMtrlTermsOfTrdArrTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTrdArrIndName\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTrdArrIndTitle\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mRule10b51ArrAdoptedFlag\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mNonRule10b51ArrAdoptedFlag\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTrdArrAdoptionDate\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mRule10b51ArrTrmntdFlag\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mNonRule10b51ArrTrmntdFlag\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTrdArrTerminationDate\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTrdArrDuration\u001b[0m\n", "│       └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTrdArrSecuritiesAggAvailAmt\u001b[0m\n", "├── \u001b[1;34mhttp://xbrl.sec.gov/ecd/role/ErrCompDisclosure\u001b[0m\n", "│   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mRecoveryOfErrCompDisclosureLineItems\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mErrCompRecoveryTable\u001b[0m\n", "│       │   ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mRestatementDateAxis\u001b[0m\n", "│       │   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mIndividualAxis\u001b[0m\n", "│       │       └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAllIndividualsMember\u001b[0m\n", "│       │           └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mNonNeosMember\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;*******************************\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAggtErrCompAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mErrCompAnalysisTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mStkPrcOrTsrEstimationMethodTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mOutstandingAggtErrCompAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAggtErrCompNotYetDeterminedTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mForgoneRecoveryIndName\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mForgoneRecoveryDueToExpenseOfEnforcementAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mForgoneRecoveryDueToViolationOfHomeCountryLawAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mForgoneRecoveryDueToDisqualificationOfTaxBenefitsAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mForgoneRecoveryExplanationOfImpracticabilityTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mOutstandingRecoveryIndName\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mOutstandingRecoveryCompAmt\u001b[0m\n", "│       └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mRestatementDoesNotRequireRecoveryTextBlock\u001b[0m\n", "├── \u001b[1;34mhttp://xbrl.sec.gov/ecd/role/PvpDisclosure\u001b[0m\n", "│   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mPayVsPerformanceDisclosureLineItems\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mPvpTable\u001b[0m\n", "│       │   ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mExecutiveCategoryAxis\u001b[0m\n", "│       │   │   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAllExecutiveCategoriesMember\u001b[0m\n", "│       │   │       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39m<PERSON>eoMember\u001b[0m\n", "│       │   │       └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mNonPeoNeoMember\u001b[0m\n", "│       │   ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mIndividualAxis\u001b[0m\n", "│       │   │   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAllIndividualsMember\u001b[0m\n", "│       │   ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAdjToCompAxis\u001b[0m\n", "│       │   │   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAllAdjToCompMember\u001b[0m\n", "│       │   └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mMeasureAxis\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mPvpTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mCoSelectedMeasureName\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mNamedExecutiveOfficersFnTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mPeerGroupIssuersFnTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mChangedPeerGroupFnTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mPeoTotalCompAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mPeoActuallyPaidCompAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAdjToPeoCompFnTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mNonPeoNeoAvgTotalCompAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mNonPeoNeoAvgCompActuallyPaidAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAdjToNonPeoNeoCompFnTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mEquityValuationAssumptionDifferenceFnTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mCompActuallyPaidVsTotalShareholderRtnTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mCompActuallyPaidVsNetIncomeTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mCompActuallyPaidVsCoSelectedMeasureTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTotalShareholderRtnVsPeerGroupTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mCompActuallyPaidVsOtherMeasureTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTabularListTableTextBlock\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mTotalShareholderRtnAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mPeerGroupTotalShareholderRtnAmt\u001b[0m\n", "│       ├── \u001b[1;33mus-gaap\u001b[0m \u001b[1;38;5;39mNetIncomeLoss\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mCoSelectedMeasureAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mOtherPerfMeasureAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAdjToCompAmt\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mMeasureName\u001b[0m\n", "│       ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mNonGaapMeasureDescriptionTextBlock\u001b[0m\n", "│       └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mAdditional402vDisclosureTextBlock\u001b[0m\n", "└── \u001b[1;34mhttp://xbrl.sec.gov/ecd/role/InsiderTradingPoliciesProc\u001b[0m\n", "    └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mInsiderTradingPoliciesProcLineItems\u001b[0m\n", "        ├── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mInsiderTrdPoliciesProcAdoptedFlag\u001b[0m\n", "        └── \u001b[1;33mecd\u001b[0m \u001b[1;38;5;39mInsiderTrdPoliciesProcNotAdoptedTextBlock\u001b[0m"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["xbrl_data.presentation"]}, {"cell_type": "code", "execution_count": 6, "id": "eb6c6483-e4d1-47fb-9ead-b22ad4f7dc95", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭─────────────────────────────────────────── XBRL Data for \u001b[1;38;5;32mApple Inc. \u001b[0m ───────────────────────────────────────────╮\n", "│ \u001b[3m              XBRL Instance Document              \u001b[0m                                                              │\n", "│ ╭────────────┬─────────────────┬─────────────────╮                                                              │\n", "│ │\u001b[1m \u001b[0m\u001b[1mCompany   \u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mNumber of Facts\u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mDocument Period\u001b[0m\u001b[1m \u001b[0m│                                                              │\n", "│ ├────────────┼─────────────────┼─────────────────┤                                                              │\n", "│ │ Apple Inc. │ 1,164           │ 2023-09-30      │                                                              │\n", "│ ╰────────────┴─────────────────┴─────────────────╯                                                              │\n", "│ ╭────┬────────────────────────────────────────────────────────────────────────────────────────────────────────╮ │\n", "│ │\u001b[1m \u001b[0m\u001b[1m  \u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mStatements                                                                                            \u001b[0m\u001b[1m \u001b[0m│ │\n", "│ ├────┼────────────────────────────────────────────────────────────────────────────────────────────────────────┤ │\n", "│ │ 0  │ \u001b[38;5;32mCover\u001b[0m\u001b[38;5;160mPage\u001b[0m                                                                                              │ │\n", "│ │ 1  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mInformation\u001b[0m                                                                                     │ │\n", "│ │ 2  │ \u001b[38;5;32mCONS<PERSON><PERSON>ATEDSTATEMENTSOFOPERATIONS\u001b[0m                                                                     │ │\n", "│ │ 3  │ \u001b[38;5;32mCONSOLIDATEDSTATEMENTSOFCOMPREHENSIVEINCOME\u001b[0m                                                            │ │\n", "│ │ 4  │ \u001b[38;5;32mCONSOLIDATEDBALANCESHEETS\u001b[0m                                                                              │ │\n", "│ │ 5  │ \u001b[38;5;32mCONSOLIDATEDBALANCESHEETS\u001b[0m\u001b[38;5;160mParenthetical\u001b[0m                                                                 │ │\n", "│ │ 6  │ \u001b[38;5;32mCONSOLIDATEDSTATEMENTSOFSHAREHOLDERSEQUITY\u001b[0m                                                             │ │\n", "│ │ 7  │ \u001b[38;5;32mCONSOLIDATEDSTATEMENTSOFCASHFLOWS\u001b[0m                                                                      │ │\n", "│ │ 8  │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m                                                                 │ │\n", "│ │ 9  │ \u001b[38;5;32m<PERSON><PERSON><PERSON>e\u001b[0m                                                                                                │ │\n", "│ │ 10 │ \u001b[38;5;32mEarnings\u001b[0m\u001b[38;5;160mPer\u001b[0m\u001b[38;5;71mShare\u001b[0m                                                                                       │ │\n", "│ │ 11 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m                                                                                   │ │\n", "│ │ 12 │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m                                                                              │ │\n", "│ │ 13 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                                  │ │\n", "│ │ 14 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m                                                                                            │ │\n", "│ │ 15 │ \u001b[38;5;32mLeases\u001b[0m                                                                                                 │ │\n", "│ │ 16 │ \u001b[38;5;32mDebt\u001b[0m                                                                                                   │ │\n", "│ │ 17 │ \u001b[38;5;32mShareholders\u001b[0m\u001b[38;5;160mEquity\u001b[0m                                                                                     │ │\n", "│ │ 18 │ \u001b[38;5;32mShare\u001b[0m\u001b[38;5;160mBased\u001b[0m\u001b[38;5;71mCompensation\u001b[0m                                                                                 │ │\n", "│ │ 19 │ \u001b[38;5;32mCommitments\u001b[0m\u001b[38;5;160mContingenciesand\u001b[0m\u001b[38;5;71mSupply\u001b[0m\u001b[38;5;32mConcentrations\u001b[0m                                                        │ │\n", "│ │ 20 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m                                                                    │ │\n", "│ │ 21 │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mAccounting\u001b[0m\u001b[38;5;32mPolicies\u001b[0m\u001b[38;5;160mPolicies\u001b[0m                                                         │ │\n", "│ │ 22 │ \u001b[38;5;32mRevenue\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                                          │ │\n", "│ │ 23 │ \u001b[38;5;32mEarnings\u001b[0m\u001b[38;5;160mPer\u001b[0m\u001b[38;5;71mShare\u001b[0m\u001b[38;5;32mTables\u001b[0m                                                                                 │ │\n", "│ │ 24 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71mTables\u001b[0m                                                                             │ │\n", "│ │ 25 │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m\u001b[38;5;32mTables\u001b[0m                                                                        │ │\n", "│ │ 26 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                            │ │\n", "│ │ 27 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mTables\u001b[0m                                                                                      │ │\n", "│ │ 28 │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                                           │ │\n", "│ │ 29 │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                                                             │ │\n", "│ │ 30 │ \u001b[38;5;32mShareholders\u001b[0m\u001b[38;5;160mEquity\u001b[0m\u001b[38;5;71mTables\u001b[0m                                                                               │ │\n", "│ │ 31 │ \u001b[38;5;32mShare\u001b[0m\u001b[38;5;160mBased\u001b[0m\u001b[38;5;71mCompensation\u001b[0m\u001b[38;5;32mTables\u001b[0m                                                                           │ │\n", "│ │ 32 │ \u001b[38;5;32mCommitments\u001b[0m\u001b[38;5;160mContingenciesand\u001b[0m\u001b[38;5;71mSupply\u001b[0m\u001b[38;5;32mConcentrations\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                  │ │\n", "│ │ 33 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m\u001b[38;5;160mTables\u001b[0m                                                              │ │\n", "│ │ 34 │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                                    │ │\n", "│ │ 35 │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mNet\u001b[0m\u001b[38;5;71mSales\u001b[0m\u001b[38;5;32mDisaggregatedby\u001b[0m\u001b[38;5;160mSignificant\u001b[0m\u001b[38;5;71mProductsand\u001b[0m\u001b[38;5;32mServices\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                    │ │\n", "│ │ 36 │ \u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mRevenue\u001b[0m\u001b[38;5;32mExpected\u001b[0m\u001b[38;5;160mTimingof\u001b[0m\u001b[38;5;71mRealization\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                               │ │\n", "│ │ 37 │ \u001b[38;5;32mEarnings\u001b[0m\u001b[38;5;160mPer\u001b[0m\u001b[38;5;71mShare\u001b[0m\u001b[38;5;32mComputationof\u001b[0m\u001b[38;5;160mBasicand\u001b[0m\u001b[38;5;71mDiluted\u001b[0m\u001b[38;5;32mEarnings\u001b[0m\u001b[38;5;160mPer\u001b[0m\u001b[38;5;71mShare\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                    │ │\n", "│ │ 38 │ \u001b[38;5;32mEarnings\u001b[0m\u001b[38;5;160mPer\u001b[0m\u001b[38;5;71mShare\u001b[0m\u001b[38;5;32mAdditional\u001b[0m\u001b[38;5;160mInformation\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                           │ │\n", "│ │ 39 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71mCash\u001b[0m\u001b[38;5;32mCash\u001b[0m\u001b[38;5;160mEquivalentsand\u001b[0m\u001b[38;5;71mMarketable\u001b[0m\u001b[38;5;32mSecurities\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                  │ │\n", "│ │ 40 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71m<PERSON>on\u001b[0m\u001b[38;5;32mCurrent\u001b[0m\u001b[38;5;160mMarketable\u001b[0m\u001b[38;5;71mDebt\u001b[0m\u001b[38;5;32mSecuritiesby\u001b[0m\u001b[38;5;160mContractual\u001b[0m\u001b[38;5;71mMaturity\u001b[0m\u001b[38;5;32mDetails\u001b[0m                     │ │\n", "│ │ 41 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71mAdditional\u001b[0m\u001b[38;5;32mInformation\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                       │ │\n", "│ │ 42 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71mNotional\u001b[0m\u001b[38;5;32mAmounts\u001b[0m\u001b[38;5;160mAssociatedwith\u001b[0m\u001b[38;5;71mDerivative\u001b[0m\u001b[38;5;32mInstruments\u001b[0m\u001b[38;5;160mDetails\u001b[0m                          │ │\n", "│ │ 43 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71m<PERSON><PERSON>\u001b[0m\u001b[38;5;32m<PERSON>air\u001b[0m\u001b[38;5;160mV<PERSON>uesof\u001b[0m\u001b[38;5;71mDerivative\u001b[0m\u001b[38;5;32mAssetsand\u001b[0m\u001b[38;5;160mLiabilities\u001b[0m\u001b[38;5;71mDetails\u001b[0m                             │ │\n", "│ │ 44 │ \u001b[38;5;32mFinancial\u001b[0m\u001b[38;5;160mInstruments\u001b[0m\u001b[38;5;71mDerivative\u001b[0m\u001b[38;5;32mInstruments\u001b[0m\u001b[38;5;160mDesignatedas\u001b[0m\u001b[38;5;71mFair\u001b[0m\u001b[38;5;32mValue\u001b[0m\u001b[38;5;160mHedgesand\u001b[0m\u001b[38;5;71mRelated\u001b[0m\u001b[38;5;32mHedged\u001b[0m\u001b[38;5;160mItems\u001b[0m\u001b[38;5;71mDetails\u001b[0m       │ │\n", "│ │ 45 │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m\u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160mProperty\u001b[0m\u001b[38;5;71mPlantand\u001b[0m\u001b[38;5;32mEquipmentby\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mAsset\u001b[0m\u001b[38;5;32mClassand\u001b[0m\u001b[38;5;160mAccumulated\u001b[0m\u001b[38;5;71mDepreciation\u001b[0m\u001b[38;5;32mDet…\u001b[0m │ │\n", "│ │ 46 │ \u001b[38;5;32mProperty\u001b[0m\u001b[38;5;160mPlantand\u001b[0m\u001b[38;5;71mEquipment\u001b[0m\u001b[38;5;32mAdditional\u001b[0m\u001b[38;5;160mInformation\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                  │ │\n", "│ │ 47 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m\u001b[38;5;160mOther\u001b[0m\u001b[38;5;71mNon\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mAssets\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                      │ │\n", "│ │ 48 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m\u001b[38;5;160mOther\u001b[0m\u001b[38;5;71mCurrent\u001b[0m\u001b[38;5;32mLiabilities\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                    │ │\n", "│ │ 49 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m\u001b[38;5;160mOther\u001b[0m\u001b[38;5;71mNon\u001b[0m\u001b[38;5;32m<PERSON>urrent\u001b[0m\u001b[38;5;160mLiabilities\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                 │ │\n", "│ │ 50 │ \u001b[38;5;32mConsolidated\u001b[0m\u001b[38;5;160mFinancial\u001b[0m\u001b[38;5;71mStatement\u001b[0m\u001b[38;5;32mDetails\u001b[0m\u001b[38;5;160mOther\u001b[0m\u001b[38;5;71mIncome\u001b[0m\u001b[38;5;32mExpense\u001b[0m\u001b[38;5;160mNet\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                      │ │\n", "│ │ 51 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mProvisionfor\u001b[0m\u001b[38;5;32mInco<PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                              │ │\n", "│ │ 52 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mAdditional\u001b[0m\u001b[38;5;32mInformation\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                                │ │\n", "│ │ 53 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mReconciliationof\u001b[0m\u001b[38;5;32mProvisionfor\u001b[0m\u001b[38;5;160mIncome\u001b[0m\u001b[38;5;71m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;32m<PERSON><PERSON>\u001b[0m\u001b[38;5;160mComputedby\u001b[0m\u001b[38;5;71mApplyingthe\u001b[0m\u001b[38;5;32mStatutory\u001b[0m\u001b[38;5;160mFederal\u001b[0m\u001b[38;5;71mIncome\u001b[0m\u001b[38;5;32m…\u001b[0m │ │\n", "│ │ 54 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mSignificant\u001b[0m\u001b[38;5;32mComponentsof\u001b[0m\u001b[38;5;160mDeferred\u001b[0m\u001b[38;5;71mTax\u001b[0m\u001b[38;5;32mAssetsand\u001b[0m\u001b[38;5;160mLiabilities\u001b[0m\u001b[38;5;71mDetails\u001b[0m                               │ │\n", "│ │ 55 │ \u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mTaxes\u001b[0m\u001b[38;5;71mAggregate\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON>n\u001b[0m\u001b[38;5;160mGross\u001b[0m\u001b[38;5;71mUnrecognized\u001b[0m\u001b[38;5;32mTax\u001b[0m\u001b[38;5;160mBenefits\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                       │ │\n", "│ │ 56 │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                                     │ │\n", "│ │ 57 │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mROU\u001b[0m\u001b[38;5;71mAssetsand\u001b[0m\u001b[38;5;32mLease\u001b[0m\u001b[38;5;160mLiabilities\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                              │ │\n", "│ │ 58 │ \u001b[38;5;32mLeases\u001b[0m\u001b[38;5;160mLease\u001b[0m\u001b[38;5;71mLiability\u001b[0m\u001b[38;5;32mMaturities\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                                  │ │\n", "│ │ 59 │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mAdditional\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                                       │ │\n", "│ │ 60 │ \u001b[38;5;32mD<PERSON><PERSON>\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mCash\u001b[0m\u001b[38;5;32m<PERSON>lows\u001b[0m\u001b[38;5;160mAssociatedwith\u001b[0m\u001b[38;5;71mCommercial\u001b[0m\u001b[38;5;32m<PERSON>aper\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                             │ │\n", "│ │ 61 │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;71mTerm\u001b[0m\u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                                           │ │\n", "│ │ 62 │ \u001b[38;5;32mDebt\u001b[0m\u001b[38;5;160mFuture\u001b[0m\u001b[38;5;71m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;32mPaymentsfor\u001b[0m\u001b[38;5;160mTerm\u001b[0m\u001b[38;5;71mDebt\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                          │ │\n", "│ │ 63 │ \u001b[38;5;32mShareholders\u001b[0m\u001b[38;5;160mEquity\u001b[0m\u001b[38;5;71mAdditional\u001b[0m\u001b[38;5;32mInformation\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                                         │ │\n", "│ │ 64 │ \u001b[38;5;32mShareholders\u001b[0m\u001b[38;5;160mEquity\u001b[0m\u001b[38;5;71mShare<PERSON>f\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mStock\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                           │ │\n", "│ │ 65 │ \u001b[38;5;32mShare\u001b[0m\u001b[38;5;160mBased\u001b[0m\u001b[38;5;71mCompensation\u001b[0m\u001b[38;5;32mAdditional\u001b[0m\u001b[38;5;160mInformation\u001b[0m\u001b[38;5;71mDetails\u001b[0m                                                     │ │\n", "│ │ 66 │ \u001b[38;5;32mShare\u001b[0m\u001b[38;5;160mBased\u001b[0m\u001b[38;5;71mCompensation\u001b[0m\u001b[38;5;32mRestricted\u001b[0m\u001b[38;5;160mStock\u001b[0m\u001b[38;5;71mUnit\u001b[0m\u001b[38;5;32mActivityand\u001b[0m\u001b[38;5;160mRelated\u001b[0m\u001b[38;5;71mInformation\u001b[0m\u001b[38;5;32mDetails\u001b[0m                          │ │\n", "│ │ 67 │ \u001b[38;5;32mShare\u001b[0m\u001b[38;5;160mBased\u001b[0m\u001b[38;5;71mCompensation\u001b[0m\u001b[38;5;32m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;160mShare\u001b[0m\u001b[38;5;71mBased\u001b[0m\u001b[38;5;32mCompensation\u001b[0m\u001b[38;5;160mExpenseandthe\u001b[0m\u001b[38;5;71mRelated\u001b[0m\u001b[38;5;32mI<PERSON><PERSON>\u001b[0m\u001b[38;5;160mTax\u001b[0m\u001b[38;5;71mBenefit\u001b[0m\u001b[38;5;32mDetails\u001b[0m       │ │\n", "│ │ 68 │ \u001b[38;5;32mCommitments\u001b[0m\u001b[38;5;160mContingenciesand\u001b[0m\u001b[38;5;71mSupply\u001b[0m\u001b[38;5;32mConcentrations\u001b[0m\u001b[38;5;160mFuture\u001b[0m\u001b[38;5;71mPayments\u001b[0m\u001b[38;5;32mUnder\u001b[0m\u001b[38;5;160mUnconditional\u001b[0m\u001b[38;5;71mPurchase\u001b[0m\u001b[38;5;32mObligations\u001b[0m\u001b[38;5;160mDet…\u001b[0m │ │\n", "│ │ 69 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m\u001b[38;5;160mInformationby\u001b[0m\u001b[38;5;71mReportable\u001b[0m\u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mDetails\u001b[0m                               │ │\n", "│ │ 70 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m\u001b[38;5;160mReconciliationof\u001b[0m\u001b[38;5;71mSegment\u001b[0m\u001b[38;5;32mOperating\u001b[0m\u001b[38;5;160mIncometothe\u001b[0m\u001b[38;5;71mConsolidated\u001b[0m\u001b[38;5;32mStatementso…\u001b[0m │ │\n", "│ │ 71 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m\u001b[38;5;160mNet\u001b[0m\u001b[38;5;71mSales\u001b[0m\u001b[38;5;32mDetails\u001b[0m                                                     │ │\n", "│ │ 72 │ \u001b[38;5;32mSegment\u001b[0m\u001b[38;5;160mInformationand\u001b[0m\u001b[38;5;71mGeographic\u001b[0m\u001b[38;5;32mData\u001b[0m\u001b[38;5;160mLong\u001b[0m\u001b[38;5;71mLived\u001b[0m\u001b[38;5;32mAssets\u001b[0m\u001b[38;5;160mDetails\u001b[0m                                              │ │\n", "│ │ 73 │ \u001b[38;5;32mAward\u001b[0m\u001b[38;5;160mTiming\u001b[0m\u001b[38;5;71mDisclosure\u001b[0m                                                                                  │ │\n", "│ │ 74 │ \u001b[38;5;32mInsider\u001b[0m\u001b[38;5;160mTrading\u001b[0m\u001b[38;5;71mArrangements\u001b[0m                                                                             │ │\n", "│ │ 75 │ \u001b[38;5;32mErr\u001b[0m\u001b[38;5;160mComp\u001b[0m\u001b[38;5;71mDisclosure\u001b[0m                                                                                      │ │\n", "│ │ 76 │ \u001b[38;5;32mPvp\u001b[0m\u001b[38;5;160mDisclosure\u001b[0m                                                                                          │ │\n", "│ │ 77 │ \u001b[38;5;32mInsider\u001b[0m\u001b[38;5;160mTrading\u001b[0m\u001b[38;5;71mPolicies\u001b[0m\u001b[38;5;32m<PERSON>roc\u001b[0m                                                                             │ │\n", "│ ╰────┴────────────────────────────────────────────────────────────────────────────────────────────────────────╯ │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["xbrl_data"]}, {"cell_type": "code", "execution_count": 7, "id": "18977678-e5dd-48e6-802b-225f35ab48c1", "metadata": {}, "outputs": [], "source": ["statements = xbrl_data.statements"]}, {"cell_type": "markdown", "id": "4c9a79de-4bc8-407f-b3a9-f4e813c46a33", "metadata": {}, "source": ["## Get the statement by name"]}, {"cell_type": "code", "execution_count": 8, "id": "4a909ac4-6831-42e6-8afc-4b21f5c15e17", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["                                                    \u001b[1;38;5;196mApple Inc.\u001b[0m                                                     \n", "                                                       \u001b[1mCover\u001b[0m                                                       \n", "                                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m                                                      \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2023                                                  \u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "  \u001b[1;38;5;32mCover [Abstract]                                      \u001b[0m                                                           \n", "   Entities [Table]                                                                                                \n", "    Class of Stock [Axis]                                                                                          \n", "     Class of Stock [Domain]                                                                                       \n", "      Common Stock, $0.00001 par value per share                                                                   \n", "      1.375% Notes due 2024                                                                                        \n", "      0.000% Notes due 2025                                                                                        \n", "      0.875% Notes due 2025                                                                                        \n", "      1.625% Notes due 2026                                                                                        \n", "      2.000% Notes due 2027                                                                                        \n", "      1.375% Notes due 2029                                                                                        \n", "      3.050% Notes due 2029                                                                                        \n", "      0.500% Notes due 2031                                                                                        \n", "      3.600% Notes due 2042                                                                                        \n", "    Entity Information [Line Items]                                                                                \n", "     Document Type                                         10-K                                                    \n", "     Document Annual Report                                true                                                    \n", "     Current Fiscal Year End Date                          --09-30                                                 \n", "     Document Period End Date                              2023-09-30                                              \n", "     Document Transition Report                            false                                                   \n", "     Entity File Number                                    001-36743                                               \n", "     Entity Registrant Name                                Apple Inc.                                              \n", "     Entity Incorporation, State or Country Code           CA                                                      \n", "     Entity Tax Identification Number                      94-2404110                                              \n", "     Entity Address, Address Line One                      One Apple Park Way                                      \n", "     Entity Address, City or Town                          Cupertino                                               \n", "     Entity Address, State or Province                     CA                                                      \n", "     Entity Address, Postal Zip Code                       95014                                                   \n", "     City Area Code                                        408                                                     \n", "     Local Phone Number                                    996-1010                                                \n", "     Title of 12(b) Security                               Common Stock, $0.00001 par value per share              \n", "     Trading Symbol                                        AAPL                                                    \n", "     No Trading Symbol Flag                                true                                                    \n", "     Security Exchange Name                                NASDAQ                                                  \n", "     Entity Well-known Seasoned Issuer                     Yes                                                     \n", "     Entity Voluntary Filers                               No                                                      \n", "     Entity Current Reporting Status                       Yes                                                     \n", "     Entity Interactive Data Current                       Yes                                                     \n", "     Entity Filer Category                                 Large Accelerated Filer                                 \n", "     Entity Small Business                                 false                                                   \n", "     Entity Emerging Growth Company                        false                                                   \n", "     ICFR Auditor Attestation Flag                         true                                                    \n", "     Document Financial Statement Error Correction Flag    false                                                   \n", "     Entity Shell Company                                  false                                                   \n", "     Entity Public Float                                   2591165000000                                           \n", "     Entity Common Stock, Shares Outstanding (in shares)   ***********                                             \n", "     Documents Incorporated by Reference                   Portions of the Registrant’s definitive proxy           \n", "                                                           statement relating to its 2024 annual meeting of        \n", "                                                           shareholders are incorporated by reference into Part    \n", "                                                           III of this Annual Report on Form 10-K where            \n", "                                                           indicated. The Registrant’s definitive proxy statement  \n", "                                                           will be filed with the U.S. Securities and Exchange     \n", "                                                           Commission within 120 days after the end of the fiscal  \n", "                                                           year to which this report relates.                      \n", "     Amendment Flag                                        false                                                   \n", "     Document Fiscal Year Focus                            2023                                                    \n", "     Document Fiscal Period Focus                          FY                                                      \n", "     Entity Central Index Key                              0000320193                                              \n", "                                                                                                                   "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["statements['CoverPage']"]}, {"cell_type": "markdown", "id": "8a30e1eb-1db9-45eb-89fb-4f18a576de91", "metadata": {}, "source": ["## Get a statement using the bracket accessor []"]}, {"cell_type": "code", "execution_count": 9, "id": "8ec5d458-a3b1-4fae-85a9-b536b24e84a0", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["                                               \u001b[1;38;5;196mApple Inc.\u001b[0m                                                \n", "                                            \u001b[1mIncome Statement\u001b[0m                                             \n", "                                                                                                         \n", " \u001b[1m \u001b[0m\u001b[1m                                               \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2023           \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2022           \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2021           \u001b[0m\u001b[1m \u001b[0m \n", " ─────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "  \u001b[1;38;5;32mIncome Statement [Abstract]                    \u001b[0m                                                        \n", "   Statement [Table]                                                                                     \n", "    Product and Service [Axis]                                                                           \n", "     Product and Service [Domain]                                                                        \n", "      Products                                                                                           \n", "      Services                                                                                           \n", "    Statement [Line Items]                                                                               \n", "     Net sales                                      383,285,000,000   394,328,000,000   365,817,000,000  \n", "     Cost of sales                                  214,137,000,000   223,546,000,000   212,981,000,000  \n", "     Gross margin                                   169,148,000,000   170,782,000,000   152,836,000,000  \n", "  \u001b[1;38;5;32m   Operating expenses:                         \u001b[0m                                                        \n", "      Research and development                       29,915,000,000    26,251,000,000    21,914,000,000  \n", "      Selling, general and administrative            24,932,000,000    25,094,000,000    21,973,000,000  \n", "      Total operating expenses                       54,847,000,000    51,345,000,000    43,887,000,000  \n", "     Operating income                               114,301,000,000   119,437,000,000   108,949,000,000  \n", "     Other income/(expense), net                       -565,000,000      -334,000,000       258,000,000  \n", "     Income before provision for income taxes       113,736,000,000   119,103,000,000   109,207,000,000  \n", "     Provision for income taxes                      16,741,000,000    19,300,000,000    14,527,000,000  \n", "     Net income                                      96,995,000,000    99,803,000,000    94,680,000,000  \n", "  \u001b[1;38;5;32m   Earnings per share:                         \u001b[0m                                                        \n", "      Basic (in dollars per share)                             6.16              6.15              5.67  \n", "      Diluted (in dollars per share)                           6.13              6.11              5.61  \n", "  \u001b[1;38;5;32m   Shares used in computing earnings per share:\u001b[0m                                                        \n", "      Basic (in shares)                              15,744,231,000    16,215,963,000    16,701,272,000  \n", "      Diluted (in shares)                            15,812,547,000    16,325,819,000    16,864,919,000  \n", "                                                                                                         "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["statements['CONSOLIDATEDSTATEMENTSOFOPERATIONS']"]}, {"cell_type": "markdown", "id": "d9d7106b-6c45-47ce-8898-b2acdb9843a8", "metadata": {}, "source": ["## Get the statement by index"]}, {"cell_type": "code", "execution_count": 10, "id": "83910bb3-2fae-4913-b3db-4f145edbac50", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["                       \u001b[1;38;5;196mApple Inc.\u001b[0m                        \n", "                   \u001b[1mAuditor Information\u001b[0m                   \n", "                                                         \n", " \u001b[1m \u001b[0m\u001b[1m                              \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m2023                \u001b[0m\u001b[1m \u001b[0m \n", " ─────────────────────────────────────────────────────── \n", "  \u001b[1;38;5;32mAuditor Information [Abstract]\u001b[0m                         \n", "   Auditor Name                    Ernst & Young LLP     \n", "   Auditor Location                San Jose, California  \n", "   Auditor Firm ID                 42                    \n", "                                                         "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["statements[1]"]}, {"cell_type": "markdown", "id": "82e6554b-ed31-42d7-acd8-9b72588e76d2", "metadata": {}, "source": ["## Get the dataframe from the statement"]}, {"cell_type": "code", "execution_count": 16, "id": "379ab41b-6afc-4de6-963e-447dba317dce", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>2023</th>\n", "      <th>2022</th>\n", "      <th>2021</th>\n", "    </tr>\n", "    <tr>\n", "      <th>label</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Statement of Comprehensive Income [Abstract]</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>Net income</th>\n", "      <td>96995000000</td>\n", "      <td>99803000000</td>\n", "      <td>94680000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Other comprehensive income/(loss):</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>Change in foreign currency translation, net of tax</th>\n", "      <td>-*********</td>\n", "      <td>-1511000000</td>\n", "      <td>*********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Change in unrealized gains/losses on derivative instruments, net of tax:</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>Change in fair value of derivative instruments</th>\n", "      <td>*********</td>\n", "      <td>3212000000</td>\n", "      <td>32000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Adjustment for net (gains)/losses realized and included in net income</th>\n", "      <td>-1717000000</td>\n", "      <td>-1074000000</td>\n", "      <td>-1003000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Total change in unrealized gains/losses on derivative instruments</th>\n", "      <td>-1394000000</td>\n", "      <td>2138000000</td>\n", "      <td>1035000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Change in unrealized gains/losses on marketable debt securities, net of tax:</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>Change in fair value of marketable debt securities</th>\n", "      <td>1563000000</td>\n", "      <td>-12104000000</td>\n", "      <td>-*********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Adjustment for net (gains)/losses realized and included in net income</th>\n", "      <td>-*********</td>\n", "      <td>-*********</td>\n", "      <td>-*********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Total change in unrealized gains/losses on marketable debt securities</th>\n", "      <td>1816000000</td>\n", "      <td>-11899000000</td>\n", "      <td>-*********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Total other comprehensive income/(loss)</th>\n", "      <td>-*********</td>\n", "      <td>-11272000000</td>\n", "      <td>*********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Total comprehensive income</th>\n", "      <td>96652000000</td>\n", "      <td>88531000000</td>\n", "      <td>95249000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                           2023          2022  \\\n", "label                                                                           \n", "Statement of Comprehensive Income [Abstract]                                    \n", "Net income                                          96995000000   99803000000   \n", "Other comprehensive income/(loss):                                              \n", "Change in foreign currency translation, net of tax   -*********   -1511000000   \n", "Change in unrealized gains/losses on derivative...                              \n", "Change in fair value of derivative instruments        *********    3212000000   \n", "Adjustment for net (gains)/losses realized and ...  -1717000000   -1074000000   \n", "Total change in unrealized gains/losses on deri...  -1394000000    2138000000   \n", "Change in unrealized gains/losses on marketable...                              \n", "Change in fair value of marketable debt securities   1563000000  -12104000000   \n", "Adjustment for net (gains)/losses realized and ...   -*********    -*********   \n", "Total change in unrealized gains/losses on mark...   1816000000  -11899000000   \n", "Total other comprehensive income/(loss)              -*********  -11272000000   \n", "Total comprehensive income                          96652000000   88531000000   \n", "\n", "                                                           2021  \n", "label                                                            \n", "Statement of Comprehensive Income [Abstract]                     \n", "Net income                                          94680000000  \n", "Other comprehensive income/(loss):                               \n", "Change in foreign currency translation, net of tax    *********  \n", "Change in unrealized gains/losses on derivative...               \n", "Change in fair value of derivative instruments         32000000  \n", "Adjustment for net (gains)/losses realized and ...  -1003000000  \n", "Total change in unrealized gains/losses on deri...   1035000000  \n", "Change in unrealized gains/losses on marketable...               \n", "Change in fair value of marketable debt securities   -*********  \n", "Adjustment for net (gains)/losses realized and ...   -*********  \n", "Total change in unrealized gains/losses on mark...   -*********  \n", "Total other comprehensive income/(loss)               *********  \n", "Total comprehensive income                          95249000000  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["statements['CONSOLIDATEDSTATEMENTSOFCOMPR<PERSON>H<PERSON><PERSON><PERSON>INCO<PERSON>'].get_dataframe()"]}, {"cell_type": "markdown", "id": "027ce053-4f53-4ba3-b813-fe7729fc5e9c", "metadata": {}, "source": ["## Concepts"]}, {"cell_type": "code", "execution_count": 12, "id": "2b86dad1-24cc-486f-876a-0e7edf1b79be", "metadata": {}, "outputs": [{"data": {"text/plain": ["['us-gaap_StatementOfIncomeAndComprehensiveIncomeAbstract',\n", " 'us-gaap_NetIncomeLoss',\n", " 'us-gaap_ComprehensiveIncomeNetOfTaxAbstract',\n", " 'us-gaap_OtherComprehensiveIncomeLossForeignCurrencyTransactionAndTranslationAdjustmentNetOfTax',\n", " 'us-gaap_OtherComprehensiveIncomeDerivativesQualifyingAsHedgesNetOfTaxPeriodIncreaseDecreaseAbstract',\n", " 'aapl_OtherComprehensiveIncomeLossDerivativeInstrumentGainLossbeforeReclassificationafterTax',\n", " 'us-gaap_OtherComprehensiveIncomeLossReclassificationAdjustmentFromAOCIForSaleOfSecuritiesNetOfTax',\n", " 'aapl_OtherComprehensiveIncomeLossDerivativeInstrumentGainLossafterReclassificationandTax',\n", " 'us-gaap_OtherComprehensiveIncomeAvailableForSaleSecuritiesAdjustmentNetOfTaxPeriodIncreaseDecreaseAbstract',\n", " 'us-gaap_OtherComprehensiveIncomeUnrealizedHoldingGainLossOnSecuritiesArisingDuringPeriodNetOfTax',\n", " 'us-gaap_OtherComprehensiveIncomeLossReclassificationAdjustmentFromAOCIForSaleOfSecuritiesNetOfTax',\n", " 'us-gaap_OtherComprehensiveIncomeLossAvailableForSaleSecuritiesAdjustmentNetOfTax',\n", " 'us-gaap_OtherComprehensiveIncomeLossNetOfTaxPortionAttributableToParent',\n", " 'us-gaap_ComprehensiveIncomeNetOfTax']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["statement = statements['CONSOLIDATEDSTATEMENTSOFCOMPREH<PERSON><PERSON>VEINCOME']\n", "statement.concepts"]}, {"cell_type": "markdown", "id": "707f0354-8c7a-4071-8bf9-27d620ba2c3c", "metadata": {}, "source": ["## Labels"]}, {"cell_type": "code", "execution_count": 13, "id": "c28b1af7-e6fa-42bb-8fc9-fd8dc02714b8", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Statement of Comprehensive Income [Abstract]',\n", " 'Net income',\n", " 'Other comprehensive income/(loss):',\n", " 'Change in foreign currency translation, net of tax',\n", " 'Change in unrealized gains/losses on derivative instruments, net of tax:',\n", " 'Change in fair value of derivative instruments',\n", " 'Adjustment for net (gains)/losses realized and included in net income',\n", " 'Total change in unrealized gains/losses on derivative instruments',\n", " 'Change in unrealized gains/losses on marketable debt securities, net of tax:',\n", " 'Change in fair value of marketable debt securities',\n", " 'Adjustment for net (gains)/losses realized and included in net income',\n", " 'Total change in unrealized gains/losses on marketable debt securities',\n", " 'Total other comprehensive income/(loss)',\n", " 'Total comprehensive income']"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["statement.labels"]}, {"cell_type": "code", "execution_count": 14, "id": "88f9baff-0283-4a85-afd8-1e143c267557", "metadata": {}, "outputs": [{"data": {"text/plain": ["'96995000000'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["statement.get_concept('us-gaap:NetIncomeLoss').value.get('2023')"]}, {"cell_type": "markdown", "id": "8d8fb670-f8b0-4406-be37-271427510715", "metadata": {}, "source": ["## XBRL Instance"]}, {"cell_type": "code", "execution_count": 17, "id": "544c9194-cb42-43d5-b577-576e5789aebc", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["╭──────────────────────────────────────────────────── Filings ────────────────────────────────────────────────────╮\n", "│                                                                                                                 │\n", "│  \u001b[1m \u001b[0m\u001b[1m  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mcik    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mcompany                                 \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71mform  \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39mfiling_date\u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1maccession_number    \u001b[0m\u001b[1m \u001b[0m        │\n", "│  ───────────────────────────────────────────────────────────────────────────────────────────────────────        │\n", "│  \u001b[1m \u001b[0m\u001b[1m0 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1000275\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mROYAL BANK OF CANADA                    \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m424B2 \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-012010\u001b[0m\u001b[1m \u001b[0m        │\n", "│   1    1000275   ROYAL BANK OF CANADA                      \u001b[38;5;71m \u001b[0m\u001b[38;5;71m424B2 \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-012025         │\n", "│  \u001b[1m \u001b[0m\u001b[1m2 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1001115\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mGEOSPACE TECHNOLOGIES CORP              \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-Q  \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-025790\u001b[0m\u001b[1m \u001b[0m        │\n", "│   3    100122    TUCSON ELECTRIC POWER CO                  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m8-K   \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-197596         │\n", "│  \u001b[1m \u001b[0m\u001b[1m4 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1001316\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mTG THERAPEUTICS, INC.                   \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-K/A\u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-025850\u001b[0m\u001b[1m \u001b[0m        │\n", "│   5    1001316   TG THERAPEUTICS, INC.                     \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-025853         │\n", "│  \u001b[1m \u001b[0m\u001b[1m6 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1002910\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mAMEREN CORP                             \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-087819\u001b[0m\u001b[1m \u001b[0m        │\n", "│   7    1003815   BCTC IV ASSIGNOR CORP                     \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-001261         │\n", "│  \u001b[1m \u001b[0m\u001b[1m8 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1005286\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mLIFECORE BIOMEDICAL, INC. \\DE\\          \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-Q  \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-000110\u001b[0m\u001b[1m \u001b[0m        │\n", "│   9    1005286   LIFECORE BIOMEDICAL, INC. \\DE\\            \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-000111         │\n", "│  \u001b[1m \u001b[0m\u001b[1m10\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1005286\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mLIFECORE BIOMEDICAL, INC. \\DE\\          \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-Q  \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-000112\u001b[0m\u001b[1m \u001b[0m        │\n", "│   11   1006028   PURE BIOSCIENCE, INC.                     \u001b[38;5;71m \u001b[0m\u001b[38;5;71m8-K   \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-030966         │\n", "│  \u001b[1m \u001b[0m\u001b[1m12\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1008848\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mAcorda Therapeutics, Inc.               \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0000950170-24-094382\u001b[0m\u001b[1m \u001b[0m        │\n", "│   13   1010324   DRH Phoenix East Construction, Inc.       \u001b[38;5;71m \u001b[0m\u001b[38;5;71m424B2 \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-197822         │\n", "│  \u001b[1m \u001b[0m\u001b[1m14\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1010326\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCONTINENTAL HOMES INC                   \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m424B2 \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-197822\u001b[0m\u001b[1m \u001b[0m        │\n", "│   15   1010337   KDB HOMES INC                             \u001b[38;5;71m \u001b[0m\u001b[38;5;71m424B2 \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-197822         │\n", "│  \u001b[1m \u001b[0m\u001b[1m16\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1010338\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCONTINENTAL RESIDENTIAL INC             \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m424B2 \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-197822\u001b[0m\u001b[1m \u001b[0m        │\n", "│   17   1012019   RUSH ENTERPRISES INC \\TX\\                 \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-025891         │\n", "│  \u001b[1m \u001b[0m\u001b[1m18\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m101295 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mUNITED GUARDIAN INC                     \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001171843-24-004632\u001b[0m\u001b[1m \u001b[0m        │\n", "│   19   1013880   TTEC Holdings, Inc.                       \u001b[38;5;71m \u001b[0m\u001b[38;5;71m8-K   \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-087830         │\n", "│  \u001b[1m \u001b[0m\u001b[1m20\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1014763\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mAinos, Inc.                             \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-030970\u001b[0m\u001b[1m \u001b[0m        │\n", "│   21   101538    UNITED STATES ANTIMONY CORP               \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  0001654954-24-010214         │\n", "│  \u001b[1m \u001b[0m\u001b[1m22\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m101538 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mUNITED STATES ANTIMONY CORP             \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001654954-24-010187\u001b[0m\u001b[1m \u001b[0m        │\n", "│   23   101538    UNITED STATES ANTIMONY CORP               \u001b[38;5;71m \u001b[0m\u001b[38;5;71m8-K   \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  0001654954-24-010212         │\n", "│  \u001b[1m \u001b[0m\u001b[1m24\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1015739\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mAWARE INC /MA/                          \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-Q  \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0000950170-24-094716\u001b[0m\u001b[1m \u001b[0m        │\n", "│   25   1017303   TRANSACT TECHNOLOGIES INC                 \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  0001140361-24-036631         │\n", "│  \u001b[1m \u001b[0m\u001b[1m26\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1021561\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mNU SKIN ENTERPRISES, INC.               \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-Q  \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001140361-24-036467\u001b[0m\u001b[1m \u001b[0m        │\n", "│   27   1031235   Global Self Storage, Inc.                 \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  0000950170-24-094717         │\n", "│  \u001b[1m \u001b[0m\u001b[1m28\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1031235\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mGlobal Self Storage, Inc.               \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0000950170-24-094755\u001b[0m\u001b[1m \u001b[0m        │\n", "│   29   103145    VEECO INSTRUMENTS INC                     \u001b[38;5;71m \u001b[0m\u001b[38;5;71mS-8   \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-087798         │\n", "│  \u001b[1m \u001b[0m\u001b[1m30\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1032208\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSEMPRA                                  \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-197901\u001b[0m\u001b[1m \u001b[0m        │\n", "│   31   1035092   SHORE BANCSHARES INC                      \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  0001628280-24-036376         │\n", "│  \u001b[1m \u001b[0m\u001b[1m32\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1036044\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mIdentiv, Inc.                           \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-Q  \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0000950170-24-094283\u001b[0m\u001b[1m \u001b[0m        │\n", "│   33   1036262   INTEST CORP                               \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-025878         │\n", "│  \u001b[1m \u001b[0m\u001b[1m34\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1038773\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSMARTFINANCIAL INC.                     \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m10-Q  \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m0001558370-24-011759\u001b[0m\u001b[1m \u001b[0m        │\n", "│   35   1041657   URBAN ONE, INC.                           \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-000006         │\n", "│  \u001b[1m \u001b[0m\u001b[1m36\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1041657\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mURBAN ONE, INC.                         \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-000008\u001b[0m\u001b[1m \u001b[0m        │\n", "│   37   1045520   CANADIAN IMPERIAL BANK OF COMMERCE /CAN/  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m424B2 \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-087685         │\n", "│  \u001b[1m \u001b[0m\u001b[1m38\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1045520\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCANADIAN IMPERIAL BANK OF COMMERCE /CAN/\u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m424B2 \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-087699\u001b[0m\u001b[1m \u001b[0m        │\n", "│   39   1045942   Track Group, Inc.                         \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-025664         │\n", "│  \u001b[1m \u001b[0m\u001b[1m40\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1045942\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mTrack Group, Inc.                       \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-025666\u001b[0m\u001b[1m \u001b[0m        │\n", "│   41   1046025   HERITAGE FINANCIAL CORP /WA/              \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-000164         │\n", "│  \u001b[1m \u001b[0m\u001b[1m42\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1047335\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mNATIONAL HEALTHCARE CORP                \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-025685\u001b[0m\u001b[1m \u001b[0m        │\n", "│   43   1047335   NATIONAL HEALTHCARE CORP                  \u001b[38;5;71m \u001b[0m\u001b[38;5;71m8-K   \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-025687         │\n", "│  \u001b[1m \u001b[0m\u001b[1m44\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1048286\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mMARRIOTT INTERNATIONAL INC /MD/         \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m424B5 \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-197825\u001b[0m\u001b[1m \u001b[0m        │\n", "│   45   1050915   QUANTA SERVICES, INC.                     \u001b[38;5;71m \u001b[0m\u001b[38;5;71m424B5 \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-197431         │\n", "│  \u001b[1m \u001b[0m\u001b[1m46\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1050915\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mQUANTA SERVICES, INC.                   \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-197895\u001b[0m\u001b[1m \u001b[0m        │\n", "│   47   1051627   AXT INC                                   \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  0001558370-24-011747         │\n", "│  \u001b[1m \u001b[0m\u001b[1m48\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1m1052752\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mGETTY REALTY CORP /MD/                  \u001b[0m\u001b[1m \u001b[0m \u001b[1;38;5;71m \u001b[0m\u001b[1;38;5;71m8-K   \u001b[0m\u001b[1;38;5;71m \u001b[0m \u001b[1;38;5;39m \u001b[0m\u001b[1;38;5;39m2024-08-09 \u001b[0m\u001b[1;38;5;39m \u001b[0m \u001b[1m \u001b[0m\u001b[1m**********-24-197878\u001b[0m\u001b[1m \u001b[0m        │\n", "│   49   1053691   CervoMed Inc.                             \u001b[38;5;71m \u001b[0m\u001b[38;5;71m10-Q  \u001b[0m\u001b[38;5;71m \u001b[0m \u001b[38;5;39m \u001b[0m\u001b[38;5;39m2024-08-09 \u001b[0m\u001b[38;5;39m \u001b[0m  **********-24-025875         │\n", "│                                                                                                                 │\n", "│ Showing 50 of 16,836 filings                                                                                    │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["filings = get_filings(index=\"xbrl\")\n", "filings"]}, {"cell_type": "code", "execution_count": 21, "id": "44bb87fe-a6bf-4a52-bc71-92f715f69b88", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m          XBRL Instance Document          \u001b[0m\n", "╭──────────────────────┬─────────────────╮\n", "│\u001b[1m \u001b[0m\u001b[1mCompany             \u001b[0m\u001b[1m \u001b[0m│\u001b[1m \u001b[0m\u001b[1mNumber of Facts\u001b[0m\u001b[1m \u001b[0m│\n", "├──────────────────────┼─────────────────┤\n", "│ ROYAL BANK OF CANADA │ 8               │\n", "╰──────────────────────┴─────────────────╯"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["filing = Filing(form='424B2', filing_date='2024-08-09', company='ROYAL BANK OF CANADA', cik=1000275, accession_no='**********-24-012010')\n", "instance = filing.xbrl()\n", "instance"]}, {"cell_type": "code", "execution_count": 22, "id": "d095d24f-f134-4625-8d4d-42e51482d9f9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>value</th>\n", "      <th>units</th>\n", "      <th>decimals</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>period_type</th>\n", "      <th>duration</th>\n", "      <th>context_id</th>\n", "      <th>entity_id</th>\n", "      <th>dimensions</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ffd:SubmissnTp</td>\n", "      <td>424B2</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ffd:FeeExhibitTp</td>\n", "      <td>EX-FILING FEES</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>dei:EntityCentralIndexKey</td>\n", "      <td>**********</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ffd:RegnFileNb</td>\n", "      <td>333-275898</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ffd:FormTp</td>\n", "      <td>F-3</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>dei:EntityRegistrantName</td>\n", "      <td>ROYAL BANK OF CANADA</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>ffd:NrrtvMaxAggtOfferingPric</td>\n", "      <td>1269000</td>\n", "      <td>USD</td>\n", "      <td>INF</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>ffd:FnlPrspctsFlg</td>\n", "      <td>true</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        concept                 value units decimals  \\\n", "0                ffd:SubmissnTp                 424B2  None     None   \n", "1              ffd:FeeExhibitTp        EX-FILING FEES  None     None   \n", "2     dei:EntityCentralIndexKey            **********  None     None   \n", "3                ffd:RegnFileNb            333-275898  None     None   \n", "4                    ffd:FormTp                   F-3  None     None   \n", "5      dei:EntityRegistrantName  ROYAL BANK OF CANADA  None     None   \n", "6  ffd:NrrtvMaxAggtOfferingPric               1269000   USD      INF   \n", "7             ffd:FnlPrspctsFlg                  true  None     None   \n", "\n", "   start_date    end_date period_type duration context_id   entity_id  \\\n", "0  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "1  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "2  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "3  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "4  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "5  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "6  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "7  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "\n", "  dimensions  \n", "0         {}  \n", "1         {}  \n", "2         {}  \n", "3         {}  \n", "4         {}  \n", "5         {}  \n", "6         {}  \n", "7         {}  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["instance.facts"]}, {"cell_type": "code", "execution_count": 29, "id": "2b40b822-f66a-4af9-a5e1-87d5901f206e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>value</th>\n", "      <th>units</th>\n", "      <th>decimals</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>period_type</th>\n", "      <th>duration</th>\n", "      <th>context_id</th>\n", "      <th>entity_id</th>\n", "      <th>dimensions</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ffd:FormTp</td>\n", "      <td>F-3</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      concept value units decimals  start_date    end_date period_type  \\\n", "4  ffd:FormTp   F-3  None     None  2024-08-09  2024-08-09     instant   \n", "\n", "  duration context_id   entity_id dimensions  \n", "4  1 month   c_report  **********         {}  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["instance.facts.query(\"concept=='ffd:FormTp'\")"]}, {"cell_type": "code", "execution_count": 26, "id": "416d5aca-98d0-4828-9884-41c9152b7fd9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>value</th>\n", "      <th>units</th>\n", "      <th>decimals</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>period_type</th>\n", "      <th>duration</th>\n", "      <th>context_id</th>\n", "      <th>entity_id</th>\n", "      <th>dimensions</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>dei:EntityRegistrantName</td>\n", "      <td>ROYAL BANK OF CANADA</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    concept                 value units decimals  start_date  \\\n", "5  dei:EntityRegistrantName  ROYAL BANK OF CANADA  None     None  2024-08-09   \n", "\n", "     end_date period_type duration context_id   entity_id dimensions  \n", "5  2024-08-09     instant  1 month   c_report  **********         {}  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["instance.query_facts(concept=\"dei:EntityRegistrantName\")"]}, {"cell_type": "code", "execution_count": 28, "id": "a09238e8-7bc9-4c0d-b74b-7e7717268c70", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>value</th>\n", "      <th>units</th>\n", "      <th>decimals</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>period_type</th>\n", "      <th>duration</th>\n", "      <th>context_id</th>\n", "      <th>entity_id</th>\n", "      <th>dimensions</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ffd:SubmissnTp</td>\n", "      <td>424B2</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ffd:FeeExhibitTp</td>\n", "      <td>EX-FILING FEES</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>dei:EntityCentralIndexKey</td>\n", "      <td>**********</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ffd:RegnFileNb</td>\n", "      <td>333-275898</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ffd:FormTp</td>\n", "      <td>F-3</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>dei:EntityRegistrantName</td>\n", "      <td>ROYAL BANK OF CANADA</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>ffd:NrrtvMaxAggtOfferingPric</td>\n", "      <td>1269000</td>\n", "      <td>USD</td>\n", "      <td>INF</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>ffd:FnlPrspctsFlg</td>\n", "      <td>true</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2024-08-09</td>\n", "      <td>2024-08-09</td>\n", "      <td>instant</td>\n", "      <td>1 month</td>\n", "      <td>c_report</td>\n", "      <td>**********</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        concept                 value units decimals  \\\n", "0                ffd:SubmissnTp                 424B2  None     None   \n", "1              ffd:FeeExhibitTp        EX-FILING FEES  None     None   \n", "2     dei:EntityCentralIndexKey            **********  None     None   \n", "3                ffd:RegnFileNb            333-275898  None     None   \n", "4                    ffd:FormTp                   F-3  None     None   \n", "5      dei:EntityRegistrantName  ROYAL BANK OF CANADA  None     None   \n", "6  ffd:NrrtvMaxAggtOfferingPric               1269000   USD      INF   \n", "7             ffd:FnlPrspctsFlg                  true  None     None   \n", "\n", "   start_date    end_date period_type duration context_id   entity_id  \\\n", "0  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "1  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "2  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "3  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "4  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "5  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "6  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "7  2024-08-09  2024-08-09     instant  1 month   c_report  **********   \n", "\n", "  dimensions  \n", "0         {}  \n", "1         {}  \n", "2         {}  \n", "3         {}  \n", "4         {}  \n", "5         {}  \n", "6         {}  \n", "7         {}  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["instance.query_facts(start_date='2024-08-09')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}