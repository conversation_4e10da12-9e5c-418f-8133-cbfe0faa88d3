{"cells": [{"cell_type": "markdown", "id": "196860f0-9768-498f-bc70-42afc3253385", "metadata": {}, "source": [" # XBRL2 - Rewrite of XBRL Functionality\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](http://colab.research.google.com/github/dgunning/edgartools/blob/main/notebooks/XBRL2-Rewrite-of-XBRL.ipynb)"]}, {"metadata": {}, "cell_type": "markdown", "source": "## <PERSON>up <PERSON>", "id": "79c7eb224ae2de22"}, {"cell_type": "code", "id": "10a5a043-4204-44b1-9b50-b00885cd6328", "metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:53:57.084840Z", "start_time": "2025-04-12T22:53:55.209805Z"}}, "source": "!pip install -U edgartools", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting edgartools\r\n", "  Downloading edgartools-3.15.0-py3-none-any.whl.metadata (17 kB)\r\n", "Requirement already satisfied: beautifulsoup4>=4.10.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (4.12.3)\r\n", "Requirement already satisfied: fastcore>=1.5.29 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (1.5.38)\r\n", "Requirement already satisfied: httpx>=0.25.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (0.27.0)\r\n", "Requirement already satisfied: humanize>=4.0.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (4.9.0)\r\n", "Requirement already satisfied: lxml>=4.4 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (5.2.2)\r\n", "Requirement already satisfied: nest-asyncio>=1.5.1 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (1.6.0)\r\n", "Requirement already satisfied: orjson>=3.6.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (3.10.3)\r\n", "Requirement already satisfied: pandas>=2.0.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (2.2.2)\r\n", "Requirement already satisfied: pyarrow>=14.0.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (19.0.0)\r\n", "Requirement already satisfied: pydantic>=2.0.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (2.7.1)\r\n", "Requirement already satisfied: rank-bm25>=0.2.1 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (0.2.1)\r\n", "Requirement already satisfied: rapidfuzz>=3.5.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (3.9.4)\r\n", "Requirement already satisfied: rich>=13.0.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (13.7.1)\r\n", "Requirement already satisfied: stamina>=24.2.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (24.3.0)\r\n", "Requirement already satisfied: tabulate>=0.9.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (0.9.0)\r\n", "Requirement already satisfied: textdistance>=4.5.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (4.6.2)\r\n", "Requirement already satisfied: tqdm>=4.62.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (4.62.0)\r\n", "Requirement already satisfied: unidecode>=1.2.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from edgartools) (1.3.8)\r\n", "Requirement already satisfied: soupsieve>1.2 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from beautifulsoup4>=4.10.0->edgartools) (2.5)\r\n", "Requirement already satisfied: packaging in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from fastcore>=1.5.29->edgartools) (24.2)\r\n", "Requirement already satisfied: anyio in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpx>=0.25.0->edgartools) (4.4.0)\r\n", "Requirement already satisfied: certifi in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpx>=0.25.0->edgartools) (2024.2.2)\r\n", "Requirement already satisfied: httpcore==1.* in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpx>=0.25.0->edgartools) (1.0.5)\r\n", "Requirement already satisfied: idna in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpx>=0.25.0->edgartools) (3.7)\r\n", "Requirement already satisfied: sniffio in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpx>=0.25.0->edgartools) (1.3.1)\r\n", "Requirement already satisfied: h11<0.15,>=0.13 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from httpcore==1.*->httpx>=0.25.0->edgartools) (0.14.0)\r\n", "Requirement already satisfied: numpy>=1.23.2 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pandas>=2.0.0->edgartools) (1.26.4)\r\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pandas>=2.0.0->edgartools) (2.9.0.post0)\r\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pandas>=2.0.0->edgartools) (2024.1)\r\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pandas>=2.0.0->edgartools) (2024.1)\r\n", "Requirement already satisfied: annotated-types>=0.4.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pydantic>=2.0.0->edgartools) (0.7.0)\r\n", "Requirement already satisfied: pydantic-core==2.18.2 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pydantic>=2.0.0->edgartools) (2.18.2)\r\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from pydantic>=2.0.0->edgartools) (4.12.0)\r\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from rich>=13.0.0->edgartools) (3.0.0)\r\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from rich>=13.0.0->edgartools) (2.18.0)\r\n", "Requirement already satisfied: tenacity in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from stamina>=24.2.0->edgartools) (9.0.0)\r\n", "Requirement already satisfied: mdurl~=0.1 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich>=13.0.0->edgartools) (0.1.2)\r\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/lib/python3.11/site-packages (from python-dateutil>=2.8.2->pandas>=2.0.0->edgartools) (1.16.0)\r\n", "Downloading edgartools-3.15.0-py3-none-any.whl (1.2 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m21.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hInstalling collected packages: edgartools\r\n", "Successfully installed edgartools-3.15.0\r\n"]}], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:53:57.565967Z", "start_time": "2025-04-12T22:53:57.563397Z"}}, "cell_type": "code", "source": ["from edgar import *\n", "from rich import print\n", "\n", "set_identity('<PERSON><PERSON><PERSON>@mcmaster.ca')"], "id": "18a0c5bf92f1ef8c", "outputs": [], "execution_count": 2}, {"metadata": {}, "cell_type": "markdown", "source": ["## Import XBRL2\n", "\n", "The new functionality is available in the `edgar.xbrl2` module."], "id": "2790cb7f4112a970"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:54:01.390009Z", "start_time": "2025-04-12T22:54:01.388021Z"}}, "cell_type": "code", "source": "from edgar.xbrl import *", "id": "53e4fd712fedbfa7", "outputs": [], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:54:04.053039Z", "start_time": "2025-04-12T22:54:03.334245Z"}}, "cell_type": "code", "source": "c = Company(\"AAPL\")", "id": "de22e82b916bbb1a", "outputs": [], "execution_count": 4}, {"metadata": {}, "cell_type": "markdown", "source": ["# XBRL for a Single Filing\n", "\n", "<hr/>"], "id": "c476377e22de1c66"}, {"metadata": {}, "cell_type": "markdown", "source": "## Get the latest filing for a company", "id": "5376c4bf795fd662"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:54:05.086695Z", "start_time": "2025-04-12T22:54:05.083877Z"}}, "cell_type": "code", "source": "filing = c.latest(\"10-K\")", "id": "8fa1b2657e5d3cfa", "outputs": [], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-14T18:43:09.129262Z", "start_time": "2025-03-14T18:43:09.127470Z"}}, "cell_type": "markdown", "source": "### Create an xbrl object from the filing", "id": "983ed818-d957-4af8-a561-5d0254e17a89"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:54:07.220245Z", "start_time": "2025-04-12T22:54:07.021923Z"}}, "cell_type": "code", "source": "xb = XBRL.from_filing(filing)", "id": "6ac5014c-4989-4943-822e-1582082b2251", "outputs": [], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:54:09.038280Z", "start_time": "2025-04-12T22:54:09.014228Z"}}, "cell_type": "code", "source": "xb", "id": "572a40b2d8c3b94", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32m╭─\u001b[0m\u001b[32m────────────────────────────────────────────────\u001b[0m\u001b[32m XBRL Document \u001b[0m\u001b[32m────────────────────────────────────────────────\u001b[0m\u001b[32m─╮\u001b[0m\n", "\u001b[32m│\u001b[0m \u001b[3m           Entity Information            \u001b[0m                                                                       \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1mProperty                \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mValue     \u001b[0m\u001b[1m \u001b[0m                                                                        \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  ───────────────────────────────────────                                                                        \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   entity_name                Apple Inc.                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   ticker                     AAPL                                                                               \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   identifier                 320193                                                                             \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   document_type              10-K                                                                               \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   reporting_end_date         2024-10-18                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   document_period_end_date   2024-09-28                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   fiscal_year                2024                                                                               \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   fiscal_period              FY                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   fiscal_year_end_month      9                                                                                  \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   fiscal_year_end_day        28                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   annual_report              True                                                                               \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   quarterly_report           False                                                                              \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   amendment                  False                                                                              \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m \u001b[3m                                             Financial Statements                                              \u001b[0m \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1mType                     \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDefinition                                                          \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mElements\u001b[0m\u001b[1m \u001b[0m  \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  ─────────────────────────────────────────────────────────────────────────────────────────────────────────────  \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   CoverPage                   CoverPage                                                              50         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       AuditorInformation                                                     4          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   IncomeStatement             CONSOLIDATEDSTATEMENTSOFOPERATIONS                                     25         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   ComprehensiveIncome         CONSOLIDATEDSTATEMENTSOFCOMPREHENSIVEINCOME                            14         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   BalanceSheet                CONSOLIDATEDBALANCESHEETS                                              38         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   BalanceSheetParenthetical   CONSOLIDATEDBALANCESHEETSParenthetical                                 5          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   StatementOfEquity           CONSOLIDATEDSTATEMENTSOFSHAREHOLDERSEQUITY                             18         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   CashFlowStatement           CONSOLIDATEDSTATEMENTSOFCASHFLOWS                                      35         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   AccountingPolicies          SummaryofSignificantAccountingPolicies                                 2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       Revenue                                                                2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       EarningsPerShare                                                       2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       FinancialInstruments                                                   2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       PropertyPlantandEquipment                                              2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ConsolidatedFinancialStatementDetails                                  2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       IncomeTaxes                                                            2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       Leases                                                                 3          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       Debt                                                                   2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ShareholdersEquity                                                     2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ShareBasedCompensation                                                 2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       CommitmentsContingenciesandSupplyConcentrations                        2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   SegmentDisclosure           SegmentInformationandGeographicData                                    2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   AccountingPolicies          SummaryofSignificantAccountingPoliciesPolicies                         14         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       RevenueTables                                                          2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       EarningsPerShareTables                                                 2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       FinancialInstrumentsTables                                             4          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       PropertyPlantandEquipmentTables                                        2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ConsolidatedFinancialStatementDetailsTables                            4          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       IncomeTaxesTables                                                      5          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       LeasesTables                                                           4          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       DebtTables                                                             4          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ShareholdersEquityTables                                               2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ShareBasedCompensationTables                                           3          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       CommitmentsContingenciesandSupplyConcentrationsTables                  2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   SegmentDisclosure           SegmentInformationandGeographicDataTables                              4          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       RevenueAdditionalInformationDetails                                    3          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       RevenueNetSalesDisaggregatedbySignificantProductsandServicesDetails    11         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       RevenueDeferredRevenueExpectedTimingofRealizationDetails               6          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       EarningsPerShareComputationofBasicandDilutedEarningsPerShareDetails    9          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       EarningsPerShareAdditionalInformationDetails                           7          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       FinancialInstrumentsCashCashEquivalentsandMarketableSecuritiesDetai…   42         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       FinancialInstrumentsAdditionalInformationDetails                       30         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       FinancialInstrumentsNotionalAmountsAssociatedwithDerivativeInstrume…   12         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       FinancialInstrumentsDerivativeInstrumentsDesignatedasFairValueHedge…   5          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       PropertyPlantandEquipmentGrossPropertyPlantandEquipmentbyMajorAsset…   11         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       PropertyPlantandEquipmentAdditionalInformationDetails                  2          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ConsolidatedFinancialStatementDetailsOtherNonCurrentAssetsDetails      4          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ConsolidatedFinancialStatementDetailsOtherCurrentLiabilitiesDetails    4          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ConsolidatedFinancialStatementDetailsOtherNonCurrentLiabilitiesDeta…   4          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       IncomeTaxesAdditionalInformationDetails                                28         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       IncomeTaxesProvisionforIncomeTaxesDetails                              14         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       IncomeTaxesReconciliationofProvisionforIncomeTaxestoAmountComputedb…   10         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       IncomeTaxesSignificantComponentsofDeferredTaxAssetsandLiabilitiesDe…   20         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       IncomeTaxesAggregateChangesinGrossUnrecognizedTaxBenefitsDetails       8          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       LeasesAdditionalInformationDetails                                     19         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       LeasesROUAssetsandLeaseLiabilitiesDetails                              16         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       LeasesLeaseLiabilityMaturitiesDetails                                  31         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       DebtAdditionalInformationDetails                                       13         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       DebtSummaryofCashFlowsAssociatedwithCommercialPaperDetails             8          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       DebtSummaryofTermDebtDetails                                           23         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       DebtFuturePrincipalPaymentsforTermDebtDetails                          8          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ShareholdersEquityAdditionalInformationDetails                         4          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   StatementOfEquity           ShareholdersEquitySharesofCommonStockDetails                           10         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ShareBasedCompensationAdditionalInformationDetails                     17         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ShareBasedCompensationRestrictedStockUnitActivityandRelatedInformat…   18         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       ShareBasedCompensationSummaryofShareBasedCompensationExpenseandtheR…   3          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       CommitmentsContingenciesandSupplyConcentrationsFuturePaymentsUnderU…   9          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   SegmentDisclosure           SegmentInformationandGeographicDataInformationbyReportableSegmentDe…   12         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   SegmentDisclosure           SegmentInformationandGeographicDataReconciliationofSegmentOperating…   11         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   SegmentDisclosure           SegmentInformationandGeographicDataNetSalesforCountriesthatIndividu…   9          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   SegmentDisclosure           SegmentInformationandGeographicDataLongLivedAssetsforCountriesthatI…   9          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Disclosures                 AwardTimingDisclosure                                                  21         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Disclosures                 ErrCompDisclosure                                                      20         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Disclosures                 PvpDisclosure                                                          54         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       InsiderTradingArrangements                                             20         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Other                       InsiderTradingPoliciesProc                                             3          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m \u001b[3m     Facts Summary     \u001b[0m                                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1mCategory   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCount\u001b[0m\u001b[1m \u001b[0m                                                                                          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  ─────────────────────                                                                                          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Total Facts   1042                                                                                            \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Contexts      193                                                                                             \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Units         7                                                                                               \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Elements      684                                                                                             \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m \u001b[3m           Reporting Periods           \u001b[0m                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  \u001b[1m \u001b[0m\u001b[1mType    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mPeriod                  \u001b[0m\u001b[1m \u001b[0m                                                                          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m  ─────────────────────────────────────                                                                          \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Instant    2024-10-18                                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Instant    2024-09-28                                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Duration   2023-10-01 to 2024-09-28                                                                           \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Duration   2024-06-30 to 2024-09-28                                                                           \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Instant    2024-03-29                                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Instant    2023-09-30                                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Duration   2022-09-25 to 2023-09-30                                                                           \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Instant    2022-09-24                                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Duration   2021-09-26 to 2022-09-24                                                                           \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Instant    2022-03-04                                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Instant    2021-09-25                                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Instant    2016-08-30                                                                                         \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m   Duration   2016-08-30 to 2016-08-30                                                                           \u001b[32m│\u001b[0m\n", "\u001b[32m│\u001b[0m                                                                                                                 \u001b[32m│\u001b[0m\n", "\u001b[32m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "execution_count": 7}, {"metadata": {}, "cell_type": "markdown", "source": ["# The Statements API\n", "\n", "<hr/>\n", "\n"], "id": "919013bfb99c9066"}, {"metadata": {}, "cell_type": "markdown", "source": "## Get statement by index", "id": "ef003e9399f583bd"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "xb.statements.income_statement()", "id": "bfb268c156c0dd1"}, {"metadata": {}, "cell_type": "markdown", "source": "## Get statement by name", "id": "3b6557f74952d640"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:54:32.093814Z", "start_time": "2025-04-12T22:54:32.087348Z"}}, "cell_type": "code", "source": "xb.statements['AuditorInformation']", "id": "95fd60dcf85ed6d7", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m      AuditorInformation (Standardized)      \u001b[0m\n", "\u001b[3m             \u001b[0m\u001b[1;3mThree Months Ended\u001b[0m\u001b[3m              \u001b[0m\n", "                                             \n", " \u001b[1m \u001b[0m\u001b[1m                  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024        \u001b[0m\u001b[1m \u001b[0m \n", " ─────────────────────────────────────────── \n", "    Auditor Name          Ernst & Young LLP  \n", "    Auditor Location   San Jose, California  \n", "    Auditor Firm ID                       0  \n", "                                             "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "execution_count": 14}, {"metadata": {}, "cell_type": "markdown", "source": ["# Financials Statements\n", "\n", "<hr/>"], "id": "c262de574d004318"}, {"metadata": {}, "cell_type": "markdown", "source": "## Balance Sheet", "id": "20d2f8fbd81402a9"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:55:00.376613Z", "start_time": "2025-04-12T22:55:00.358422Z"}}, "cell_type": "code", "source": ["balance_sheet = xb.statements.balance_sheet()\n", "balance_sheet"], "id": "6f960335f2d8797a", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                    Consolidated Balance Sheets (Standardized)                     \u001b[0m\n", "\u001b[3m            \u001b[0m\u001b[1;3mFiscal Year Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m            \u001b[0m\n", "                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m                                                 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 30, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────── \n", "    ASSETS:                                                                        \n", "      Current assets:                                                              \n", "        Cash and Cash Equivalents                          $29,965        $29,943  \n", "        Marketable Securities                              $31,590        $35,228  \n", "        Accounts Receivable                                $29,508        $33,410  \n", "        Vendor non-trade receivables                       $31,477        $32,833  \n", "        Inventory                                           $6,331         $7,286  \n", "        Other Assets                                       $14,695        $14,287  \n", "        Total Current Assets                              $143,566       $152,987  \n", "      Non-current assets:                                                          \n", "        Marketable securities                             $100,544        $91,479  \n", "        Property, Plant and Equipment                      $43,715        $45,680  \n", "        Other Assets                                       $64,758        $74,834  \n", "        Total Current Assets                              $209,017       $211,993  \n", "      Total Assets                                        $352,583       $364,980  \n", "    LIABILITIES AND SHAREH<PERSON>DERS’ EQUITY:                                          \n", "      Current liabilities:                                                         \n", "        Accounts Payable                                   $62,611        $68,960  \n", "        Other Liabilities                                  $58,829        $78,304  \n", "        Revenue                                             $8,061         $8,249  \n", "        Commercial paper                                    $5,985         $9,967  \n", "        Short-Term Debt                                     $9,822        $10,912  \n", "        Total Current Liabilities                         $145,308       $176,392  \n", "      Non-current liabilities:                                                     \n", "        Long-Term Debt                                     $95,281        $85,750  \n", "        Other Liabilities                                  $49,848        $45,888  \n", "        Total Current Liabilities                         $145,129       $131,638  \n", "      Total Liabilities                                   $290,437       $308,030  \n", "      Commitments and contingencies                                                \n", "      Common Stock Shares Outstanding                   15,550,061     15,116,786  \n", "      Common Stock Shares Issued                        15,550,061     15,116,786  \n", "      Shareholders’ equity:                                                        \n", "        Common Stock                                       $73,812        $83,276  \n", "        Retained Earnings                                   $(214)      $(19,154)  \n", "        Accumulated Other Comprehensive Income/Loss      $(11,452)       $(7,172)  \n", "        Total Stockholders' Equity                         $62,146        $56,950  \n", "      Total Liabilities and Stockholders' Equity          $352,583       $364,980  \n", "                                                                                   "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "execution_count": 16}, {"metadata": {}, "cell_type": "markdown", "source": "### Income Statement", "id": "f67315d7ca758a17"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:55:05.328603Z", "start_time": "2025-04-12T22:55:05.312301Z"}}, "cell_type": "code", "source": ["income_statement = xb.statements.income_statement()\n", "income_statement"], "id": "6bd8a1628f24b6f3", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                          Consolidated Statement of Income (Standardized)                           \u001b[0m\n", "\u001b[3m                        \u001b[0m\u001b[1;3mYear Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m                        \u001b[0m\n", "                                                                                                    \n", " \u001b[1m \u001b[0m\u001b[1m                                                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 30, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 24, 2022\u001b[0m\u001b[1m \u001b[0m \n", " ────────────────────────────────────────────────────────────────────────────────────────────────── \n", "        Revenue                                             $391,035       $383,285       $394,328  \n", "        Cost of Revenue                                   $(210,352)     $(214,137)     $(223,546)  \n", "        Gross Profit                                        $180,683       $169,148       $170,782  \n", "        Operating Expenses                                                                          \n", "          Research and Development Expense                   $31,370        $29,915        $26,251  \n", "          Selling, General and Administrative Expense        $26,097        $24,932        $25,094  \n", "          Operating Expenses                               $(57,467)      $(54,847)      $(51,345)  \n", "        Operating Income                                    $123,216       $114,301       $119,437  \n", "        Nonoperating Income/Expense                             $269         $(565)         $(334)  \n", "        Income Before Tax                                   $123,485       $113,736       $119,103  \n", "        Income Tax Expense                                   $29,749        $16,741        $19,300  \n", "        Net Income                                           $93,736        $96,995        $99,803  \n", "        Earnings per share:                                                                         \n", "          Earnings Per Share                                    0.00           0.00           0.00  \n", "          Earnings Per Share (Diluted)                          0.00           0.00           0.00  \n", "        Shares used in computing earnings per share:                                                \n", "          Shares Outstanding                              15,343,783     15,744,231     16,215,963  \n", "          Shares Outstanding (Diluted)                    15,408,095     15,812,547     16,325,819  \n", "                                                                                                    "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "execution_count": 17}, {"metadata": {}, "cell_type": "markdown", "source": ["# Get Data as DataFrames\n", "<hr/>"], "id": "4e818cde3a1c2fbf"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:55:07.614691Z", "start_time": "2025-04-12T22:55:07.602893Z"}}, "cell_type": "code", "source": "income_statement.to_dataframe()", "id": "25579a682bd309f2", "outputs": [{"data": {"text/plain": ["                                              concept  \\\n", "0   us-gaap_RevenueFromContractWithCustomerExcludi...   \n", "1                  us-gaap_CostOfGoodsAndServicesSold   \n", "2                                 us-gaap_GrossProfit   \n", "3                   us-gaap_OperatingExpensesAbstract   \n", "4               us-gaap_ResearchAndDevelopmentExpense   \n", "5      us-gaap_SellingGeneralAndAdministrativeExpense   \n", "6                           us-gaap_OperatingExpenses   \n", "7                         us-gaap_OperatingIncomeLoss   \n", "8                   us-gaap_NonoperatingIncomeExpense   \n", "9   us-gaap_IncomeLossFromContinuingOperationsBefo...   \n", "10                    us-gaap_IncomeTaxExpenseBenefit   \n", "11                              us-gaap_NetIncomeLoss   \n", "12                   us-gaap_EarningsPerShareAbstract   \n", "13                      us-gaap_EarningsPerShareBasic   \n", "14                    us-gaap_EarningsPerShareDiluted   \n", "15  us-gaap_WeightedAverageNumberOfSharesOutstandi...   \n", "16  us-gaap_WeightedAverageNumberOfSharesOutstandi...   \n", "17  us-gaap_WeightedAverageNumberOfDilutedSharesOu...   \n", "\n", "                                           label      2024-09-28  \\\n", "0                                        Revenue  391035000000.0   \n", "1                                Cost of Revenue -210352000000.0   \n", "2                                   Gross Profit  180683000000.0   \n", "3                             Operating Expenses                   \n", "4               Research and Development Expense   31370000000.0   \n", "5    Selling, General and Administrative Expense   26097000000.0   \n", "6                             Operating Expenses  -57467000000.0   \n", "7                               Operating Income  123216000000.0   \n", "8                    Nonoperating Income/Expense     *********.0   \n", "9                              Income Before Tax  123485000000.0   \n", "10                            Income Tax Expense   29749000000.0   \n", "11                                    Net Income   93736000000.0   \n", "12                           Earnings per share:                   \n", "13                            Earnings Per Share            6.11   \n", "14                  Earnings Per Share (Diluted)            6.08   \n", "15  Shares used in computing earnings per share:                   \n", "16                            Shares Outstanding   15343783000.0   \n", "17                  Shares Outstanding (Diluted)   15408095000.0   \n", "\n", "        2023-09-30      2022-09-24  level  abstract  dimension  \n", "0   383285000000.0  394328000000.0      3     False      False  \n", "1  -214137000000.0 -223546000000.0      3     False      False  \n", "2   169148000000.0  170782000000.0      3     False      False  \n", "3                                       3     False      False  \n", "4    29915000000.0   26251000000.0      4     False      False  \n", "5    24932000000.0   25094000000.0      4     False      False  \n", "6   -54847000000.0  -51345000000.0      4     False      False  \n", "7   114301000000.0  119437000000.0      3     False      False  \n", "8     -*********.0    -*********.0      3     False      False  \n", "9   113736000000.0  119103000000.0      3     False      False  \n", "10   16741000000.0   19300000000.0      3     False      False  \n", "11   96995000000.0   99803000000.0      3     False      False  \n", "12                                      3     False      False  \n", "13            6.16            6.15      4     False      False  \n", "14            6.13            6.11      4     False      False  \n", "15                                      3     False      False  \n", "16   15744231000.0   16215963000.0      4     False      False  \n", "17   15812547000.0   16325819000.0      4     False      False  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>2024-09-28</th>\n", "      <th>2023-09-30</th>\n", "      <th>2022-09-24</th>\n", "      <th>level</th>\n", "      <th>abstract</th>\n", "      <th>dimension</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap_RevenueFromContractWithCustomerExcludi...</td>\n", "      <td>Revenue</td>\n", "      <td>391035000000.0</td>\n", "      <td>383285000000.0</td>\n", "      <td>394328000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap_CostOfGoodsAndServicesSold</td>\n", "      <td>Cost of Revenue</td>\n", "      <td>-210352000000.0</td>\n", "      <td>-214137000000.0</td>\n", "      <td>-223546000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap_GrossProfit</td>\n", "      <td>Gross Profit</td>\n", "      <td>180683000000.0</td>\n", "      <td>169148000000.0</td>\n", "      <td>170782000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap_OperatingExpensesAbstract</td>\n", "      <td>Operating Expenses</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap_ResearchAndDevelopmentExpense</td>\n", "      <td>Research and Development Expense</td>\n", "      <td>31370000000.0</td>\n", "      <td>29915000000.0</td>\n", "      <td>26251000000.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap_SellingGeneralAndAdministrativeExpense</td>\n", "      <td>Selling, General and Administrative Expense</td>\n", "      <td>26097000000.0</td>\n", "      <td>24932000000.0</td>\n", "      <td>25094000000.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap_OperatingExpenses</td>\n", "      <td>Operating Expenses</td>\n", "      <td>-57467000000.0</td>\n", "      <td>-54847000000.0</td>\n", "      <td>-51345000000.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap_OperatingIncomeLoss</td>\n", "      <td>Operating Income</td>\n", "      <td>123216000000.0</td>\n", "      <td>114301000000.0</td>\n", "      <td>119437000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap_NonoperatingIncomeExpense</td>\n", "      <td>Nonoperating Income/Expense</td>\n", "      <td>*********.0</td>\n", "      <td>-*********.0</td>\n", "      <td>-*********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap_IncomeLossFromContinuingOperationsBefo...</td>\n", "      <td>Income Before Tax</td>\n", "      <td>123485000000.0</td>\n", "      <td>113736000000.0</td>\n", "      <td>119103000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>us-gaap_IncomeTaxExpenseBenefit</td>\n", "      <td>Income Tax Expense</td>\n", "      <td>29749000000.0</td>\n", "      <td>16741000000.0</td>\n", "      <td>19300000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>us-gaap_NetIncomeLoss</td>\n", "      <td>Net Income</td>\n", "      <td>93736000000.0</td>\n", "      <td>96995000000.0</td>\n", "      <td>99803000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>us-gaap_EarningsPerShareAbstract</td>\n", "      <td>Earnings per share:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>us-gaap_EarningsPerShareBasic</td>\n", "      <td>Earnings Per Share</td>\n", "      <td>6.11</td>\n", "      <td>6.16</td>\n", "      <td>6.15</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>us-gaap_EarningsPerShareDiluted</td>\n", "      <td>Earnings Per Share (Diluted)</td>\n", "      <td>6.08</td>\n", "      <td>6.13</td>\n", "      <td>6.11</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>us-gaap_WeightedAverageNumberOfSharesOutstandi...</td>\n", "      <td>Shares used in computing earnings per share:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>us-gaap_WeightedAverageNumberOfSharesOutstandi...</td>\n", "      <td>Shares Outstanding</td>\n", "      <td>15343783000.0</td>\n", "      <td>15744231000.0</td>\n", "      <td>16215963000.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>us-gaap_WeightedAverageNumberOfDilutedSharesOu...</td>\n", "      <td>Shares Outstanding (Diluted)</td>\n", "      <td>15408095000.0</td>\n", "      <td>15812547000.0</td>\n", "      <td>16325819000.0</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "execution_count": 18}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:55:10.940335Z", "start_time": "2025-04-12T22:55:10.926598Z"}}, "cell_type": "code", "source": "balance_sheet.to_dataframe()", "id": "468511036e41f0fc", "outputs": [{"data": {"text/plain": ["                                              concept  \\\n", "0                              us-gaap_AssetsAbstract   \n", "1                       us-gaap_AssetsCurrentAbstract   \n", "2       us-gaap_CashAndCashEquivalentsAtCarryingValue   \n", "3                 us-gaap_MarketableSecuritiesCurrent   \n", "4                us-gaap_AccountsReceivableNetCurrent   \n", "5                  us-gaap_NontradeReceivablesCurrent   \n", "6                                us-gaap_InventoryNet   \n", "7                          us-gaap_OtherAssetsCurrent   \n", "8                               us-gaap_AssetsCurrent   \n", "9                    us-gaap_AssetsNoncurrentAbstract   \n", "10             us-gaap_MarketableSecuritiesNoncurrent   \n", "11               us-gaap_PropertyPlantAndEquipmentNet   \n", "12                      us-gaap_OtherAssetsNoncurrent   \n", "13                           us-gaap_AssetsNoncurrent   \n", "14                                     us-gaap_Assets   \n", "15   us-gaap_LiabilitiesAndStockholdersEquityAbstract   \n", "16                 us-gaap_LiabilitiesCurrentAbstract   \n", "17                     us-gaap_AccountsPayableCurrent   \n", "18                    us-gaap_OtherLiabilitiesCurrent   \n", "19       us-gaap_ContractWithCustomerLiabilityCurrent   \n", "20                            us-gaap_CommercialPaper   \n", "21                        us-gaap_LongTermDebtCurrent   \n", "22                         us-gaap_LiabilitiesCurrent   \n", "23              us-gaap_LiabilitiesNoncurrentAbstract   \n", "24                     us-gaap_LongTermDebtNoncurrent   \n", "25                 us-gaap_OtherLiabilitiesNoncurrent   \n", "26                      us-gaap_LiabilitiesNoncurrent   \n", "27                                us-gaap_Liabilities   \n", "28                us-gaap_CommitmentsAndContingencies   \n", "29               us-gaap_CommonStockSharesOutstanding   \n", "30                    us-gaap_CommonStockSharesIssued   \n", "31                 us-gaap_StockholdersEquityAbstract   \n", "32  us-gaap_CommonStocksIncludingAdditionalPaidInC...   \n", "33         us-gaap_RetainedEarningsAccumulatedDeficit   \n", "34  us-gaap_AccumulatedOtherComprehensiveIncomeLos...   \n", "35                         us-gaap_StockholdersEquity   \n", "36           us-gaap_LiabilitiesAndStockholdersEquity   \n", "\n", "                                          label      2023-09-30  \\\n", "0                                       ASSETS:                   \n", "1                               Current assets:                   \n", "2                     Cash and Cash Equivalents   ***********.0   \n", "3                         Marketable Securities   ***********.0   \n", "4                           Accounts Receivable   ***********.0   \n", "5                  Vendor non-trade receivables   ***********.0   \n", "6                                     Inventory    **********.0   \n", "7                                  Other Assets   ***********.0   \n", "8                          Total Current Assets  ************.0   \n", "9                           Non-current assets:                   \n", "10                        Marketable securities  ************.0   \n", "11                Property, Plant and Equipment   ***********.0   \n", "12                                 Other Assets   ***********.0   \n", "13                         Total Current Assets  ************.0   \n", "14                                 Total Assets  ************.0   \n", "15        LIABI<PERSON><PERSON>IE<PERSON> AND SHAREH<PERSON>DERS’ EQUITY:                   \n", "16                         Current liabilities:                   \n", "17                             Accounts Payable   ***********.0   \n", "18                            Other Liabilities   ***********.0   \n", "19                                      Revenue    **********.0   \n", "20                             Commercial paper    **********.0   \n", "21                              Short-Term Debt    **********.0   \n", "22                    Total Current Liabilities  ************.0   \n", "23                     Non-current liabilities:                   \n", "24                               Long-Term Debt   ***********.0   \n", "25                            Other Liabilities   ***********.0   \n", "26                    Total Current Liabilities  ************.0   \n", "27                            Total Liabilities  ************.0   \n", "28                Commitments and contingencies                   \n", "29              Common Stock Shares Outstanding   ***********.0   \n", "30                   Common Stock Shares Issued   ***********.0   \n", "31                        Shareholders’ equity:                   \n", "32                                 Common Stock   73812000000.0   \n", "33                            Retained Earnings    -214000000.0   \n", "34  Accumulated Other Comprehensive Income/Loss  -11452000000.0   \n", "35                   Total Stockholders' Equity   62146000000.0   \n", "36   Total Liabilities and Stockholders' Equity  ************.0   \n", "\n", "        2024-09-28  level  abstract  dimension  \n", "0                       1     False      False  \n", "1                       2     False      False  \n", "2    ***********.0      3     False      False  \n", "3    ***********.0      3     False      False  \n", "4    ***********.0      3     False      False  \n", "5    ***********.0      3     False      False  \n", "6     **********.0      3     False      False  \n", "7    ***********.0      3     False      False  \n", "8   152987000000.0      3     False      False  \n", "9                       2     False      False  \n", "10   91479000000.0      3     False      False  \n", "11   45680000000.0      3     False      False  \n", "12   74834000000.0      3     False      False  \n", "13  211993000000.0      3     False      False  \n", "14  364980000000.0      2     False      False  \n", "15                      1     False      False  \n", "16                      2     False      False  \n", "17   ***********.0      3     False      False  \n", "18   ***********.0      3     False      False  \n", "19    **********.0      3     False      False  \n", "20    **********.0      3     False      False  \n", "21   10912000000.0      3     False      False  \n", "22  176392000000.0      3     False      False  \n", "23                      2     False      False  \n", "24   85750000000.0      3     False      False  \n", "25   45888000000.0      3     False      False  \n", "26  131638000000.0      3     False      False  \n", "27  308030000000.0      2     False      False  \n", "28                      2     False      False  \n", "29   15116786000.0      2     False      False  \n", "30   15116786000.0      2     False      False  \n", "31                      2     False      False  \n", "32   83276000000.0      3     False      False  \n", "33  -19154000000.0      3     False      False  \n", "34   -7172000000.0      3     False      False  \n", "35   56950000000.0      3     False      False  \n", "36  364980000000.0      2     False      False  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept</th>\n", "      <th>label</th>\n", "      <th>2023-09-30</th>\n", "      <th>2024-09-28</th>\n", "      <th>level</th>\n", "      <th>abstract</th>\n", "      <th>dimension</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>us-gaap_AssetsAbstract</td>\n", "      <td>ASSETS:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>us-gaap_AssetsCurrentAbstract</td>\n", "      <td>Current assets:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>us-gaap_CashAndCashEquivalentsAtCarryingValue</td>\n", "      <td>Cash and Cash Equivalents</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>us-gaap_MarketableSecuritiesCurrent</td>\n", "      <td>Marketable Securities</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>us-gaap_AccountsReceivableNetCurrent</td>\n", "      <td>Accounts Receivable</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>us-gaap_NontradeReceivablesCurrent</td>\n", "      <td>Vendor non-trade receivables</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>us-gaap_InventoryNet</td>\n", "      <td>Inventory</td>\n", "      <td>**********.0</td>\n", "      <td>**********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>us-gaap_OtherAssetsCurrent</td>\n", "      <td>Other Assets</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>us-gaap_AssetsCurrent</td>\n", "      <td>Total Current Assets</td>\n", "      <td>************.0</td>\n", "      <td>152987000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>us-gaap_AssetsNoncurrentAbstract</td>\n", "      <td>Non-current assets:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>us-gaap_MarketableSecuritiesNoncurrent</td>\n", "      <td>Marketable securities</td>\n", "      <td>************.0</td>\n", "      <td>91479000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>us-gaap_PropertyPlantAndEquipmentNet</td>\n", "      <td>Property, Plant and Equipment</td>\n", "      <td>***********.0</td>\n", "      <td>45680000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>us-gaap_OtherAssetsNoncurrent</td>\n", "      <td>Other Assets</td>\n", "      <td>***********.0</td>\n", "      <td>74834000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>us-gaap_AssetsNoncurrent</td>\n", "      <td>Total Current Assets</td>\n", "      <td>************.0</td>\n", "      <td>211993000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>us-gaap_Assets</td>\n", "      <td>Total Assets</td>\n", "      <td>************.0</td>\n", "      <td>364980000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>us-gaap_LiabilitiesAndStockholdersEquityAbstract</td>\n", "      <td>LIABILITIES AND SHAREHOLDERS’ EQUITY:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>us-gaap_LiabilitiesCurrentAbstract</td>\n", "      <td>Current liabilities:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>us-gaap_AccountsPayableCurrent</td>\n", "      <td>Accounts Payable</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>us-gaap_OtherLiabilitiesCurrent</td>\n", "      <td>Other Liabilities</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>us-gaap_ContractWithCustomerLiabilityCurrent</td>\n", "      <td>Revenue</td>\n", "      <td>**********.0</td>\n", "      <td>**********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>us-gaap_CommercialPaper</td>\n", "      <td>Commercial paper</td>\n", "      <td>**********.0</td>\n", "      <td>**********.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>us-gaap_LongTermDebtCurrent</td>\n", "      <td>Short-Term Debt</td>\n", "      <td>**********.0</td>\n", "      <td>10912000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>us-gaap_LiabilitiesCurrent</td>\n", "      <td>Total Current Liabilities</td>\n", "      <td>************.0</td>\n", "      <td>176392000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>us-gaap_LiabilitiesNoncurrentAbstract</td>\n", "      <td>Non-current liabilities:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>us-gaap_LongTermDebtNoncurrent</td>\n", "      <td>Long-Term Debt</td>\n", "      <td>***********.0</td>\n", "      <td>85750000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>us-gaap_OtherLiabilitiesNoncurrent</td>\n", "      <td>Other Liabilities</td>\n", "      <td>***********.0</td>\n", "      <td>45888000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>us-gaap_LiabilitiesNoncurrent</td>\n", "      <td>Total Current Liabilities</td>\n", "      <td>************.0</td>\n", "      <td>131638000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>us-gaap_Liabilities</td>\n", "      <td>Total Liabilities</td>\n", "      <td>************.0</td>\n", "      <td>308030000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>us-gaap_CommitmentsAndContingencies</td>\n", "      <td>Commitments and contingencies</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>us-gaap_CommonStockSharesOutstanding</td>\n", "      <td>Common Stock Shares Outstanding</td>\n", "      <td>***********.0</td>\n", "      <td>15116786000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>us-gaap_CommonStockSharesIssued</td>\n", "      <td>Common Stock Shares Issued</td>\n", "      <td>***********.0</td>\n", "      <td>15116786000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>us-gaap_StockholdersEquityAbstract</td>\n", "      <td>Shareholders’ equity:</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>us-gaap_CommonStocksIncludingAdditionalPaidInC...</td>\n", "      <td>Common Stock</td>\n", "      <td>73812000000.0</td>\n", "      <td>83276000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>us-gaap_RetainedEarningsAccumulatedDeficit</td>\n", "      <td>Retained Earnings</td>\n", "      <td>-214000000.0</td>\n", "      <td>-19154000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>us-gaap_AccumulatedOtherComprehensiveIncomeLos...</td>\n", "      <td>Accumulated Other Comprehensive Income/Loss</td>\n", "      <td>-11452000000.0</td>\n", "      <td>-7172000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>us-gaap_StockholdersEquity</td>\n", "      <td>Total Stockholders' Equity</td>\n", "      <td>62146000000.0</td>\n", "      <td>56950000000.0</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>us-gaap_LiabilitiesAndStockholdersEquity</td>\n", "      <td>Total Liabilities and Stockholders' Equity</td>\n", "      <td>************.0</td>\n", "      <td>364980000000.0</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "execution_count": 19}, {"metadata": {}, "cell_type": "markdown", "source": ["# Stitching Statements\n", "<hr/>"], "id": "de74493ae84812f3"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:55:15.883669Z", "start_time": "2025-04-12T22:55:14.227416Z"}}, "cell_type": "code", "source": ["filings = c.latest(\"10-K\", 4)\n", "xbrls = XBRLS.from_filings(filings)\n", "xbrls.statements"], "id": "107687534f201101", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m     Available Stitched Statements     \u001b[0m\n", "                                       \n", " \u001b[1m \u001b[0m\u001b[1mStatement Type           \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mPeriods\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────── \n", "  AccountingPolicies          32       \n", "  BalanceSheet                32       \n", "  BalanceSheetParenthetical   32       \n", "  CashFlowStatement           32       \n", "  ComprehensiveIncome         32       \n", "  CoverPage                   32       \n", "  Disclosures                 32       \n", "  IncomeStatement             32       \n", "  SegmentDisclosure           32       \n", "  StatementOfEquity           32       \n", "                                       "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "execution_count": 20}, {"metadata": {}, "cell_type": "markdown", "source": "## Balance Sheet", "id": "26389377e1cb24ee"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:55:18.550084Z", "start_time": "2025-04-12T22:55:18.516295Z"}}, "cell_type": "code", "source": "xbrls.statements.balance_sheet()", "id": "dfe8c5a057371642", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                            CONSOLIDATED BALANCE SHEET (4-Period View) (Standardized)                            \u001b[0m\n", "\u001b[3m                           \u001b[0m\u001b[1;3mFiscal Year Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m                           \u001b[0m\n", "                                                                                                                 \n", " \u001b[1m \u001b[0m\u001b[1m                                                 \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 30, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 24, 2022\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 25, 2021\u001b[0m\u001b[1m \u001b[0m \n", " ─────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "      Commitments and contingencies                                                                              \n", "      Common Stock Shares Issued                        15,116,786     15,550,061                                \n", "      Common Stock Shares Outstanding                   15,116,786     15,550,061                                \n", "      Total Assets                                        $364,980       $352,583       $352,755       $351,002  \n", "      Total Liabilities                                   $308,030       $290,437       $302,083       $287,912  \n", "      Total Liabilities and Stockholders' Equity          $364,980       $352,583       $352,755       $351,002  \n", "        Accounts Payable                                   $68,960        $62,611        $64,115        $54,763  \n", "        Accounts Receivable                                $33,410        $29,508        $28,184        $26,278  \n", "        Accumulated Other Comprehensive Income/Loss       $(7,172)      $(11,452)      $(11,109)           $163  \n", "        Cash and Cash Equivalents                          $29,943        $29,965        $23,646        $34,940  \n", "        Commercial paper                                    $9,967         $5,985         $9,982         $6,000  \n", "        Common Stock                                       $83,276        $73,812        $64,849        $57,365  \n", "        Inventory                                           $7,286         $6,331         $4,946         $6,580  \n", "        Long-Term Debt                                     $85,750        $95,281        $98,959       $109,106  \n", "        Marketable Securities                              $35,228        $31,590        $24,658        $27,699  \n", "        Marketable securities                              $91,479       $100,544       $120,805       $127,877  \n", "        Other Assets                                       $74,834        $64,758        $54,428        $48,849  \n", "        Other Liabilities                                  $45,888        $49,848        $49,142        $53,325  \n", "        Property, Plant and Equipment                      $45,680        $43,715        $42,117        $39,440  \n", "        Retained Earnings                                $(19,154)         $(214)       $(3,068)         $5,562  \n", "        Revenue                                             $8,249         $8,061         $7,912         $7,612  \n", "        Short-Term Debt                                    $10,912         $9,822        $11,128         $9,613  \n", "        Total Current Assets                              $211,993       $209,017       $217,350       $216,166  \n", "        Total Current Liabilities                         $131,638       $145,129       $148,101       $162,431  \n", "        Total Stockholders' Equity                         $56,950        $62,146        $50,672        $63,090  \n", "        Vendor non-trade receivables                       $32,833        $31,477        $32,748        $25,228  \n", "                                                                                                                 "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "execution_count": 21}, {"metadata": {}, "cell_type": "markdown", "source": "## Income Statement", "id": "93d9b6fb593447f4"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T22:55:21.572066Z", "start_time": "2025-04-12T22:55:21.550390Z"}}, "cell_type": "code", "source": "xbrls.statements.income_statement()", "id": "290a08f25d49e985", "outputs": [{"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[3m                           CONSOLIDATED INCOME STATEMENT (4-Period View) (Standardized)                            \u001b[0m\n", "\u001b[3m                               \u001b[0m\u001b[1;3mYear Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except shares in thousands)\u001b[0m\u001b[3m                                \u001b[0m\n", "                                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m                                                   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 30, 2023\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 24, 2022\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 25, 2021\u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "        Cost of Revenue                                   $(210,352)     $(214,137)     $(223,546)     $(212,981)  \n", "        Gross Profit                                        $180,683       $169,148       $170,782       $152,836  \n", "        Income Before Tax                                   $123,485       $113,736       $119,103       $109,207  \n", "        Income Tax Expense                                   $29,749        $16,741        $19,300        $14,527  \n", "        Net Income                                           $93,736        $96,995        $99,803        $94,680  \n", "        Nonoperating Income/Expense                             $269         $(565)         $(334)           $258  \n", "        Operating Expenses                                 $(57,467)      $(54,847)      $(51,345)      $(43,887)  \n", "        Operating Income                                    $123,216       $114,301       $119,437       $108,949  \n", "        Revenue                                             $391,035       $383,285       $394,328       $365,817  \n", "          Earnings Per Share                                    0.00           0.00           0.00           0.00  \n", "          Earnings Per Share (Diluted)                          0.00           0.00           0.00           0.00  \n", "          Research and Development Expense                   $31,370        $29,915        $26,251        $21,914  \n", "          Selling, General and Administrative Expense        $26,097        $24,932        $25,094        $21,973  \n", "          Shares Outstanding                              15,343,783     15,744,231     16,215,963     16,701,272  \n", "          Shares Outstanding (Diluted)                    15,408,095     15,812,547     16,325,819     16,864,919  \n", "                                                                                                                   "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "execution_count": 22}, {"metadata": {}, "cell_type": "markdown", "source": ["# Entity Information\n", "<hr/>"], "id": "ec40f8d70eeb38e7"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-27T22:17:33.268371Z", "start_time": "2025-03-27T22:17:33.253621Z"}}, "cell_type": "code", "source": ["for key, value in xbrl.entity_info.items():\n", "    print(f\"{key}: {value}\")"], "id": "14c3e67a33fc100e", "outputs": [{"data": {"text/plain": ["entity_name: Apple Inc.\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">entity_name: Apple Inc.\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["ticker: AAPL\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">ticker: AAPL\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["identifier: \u001b[1;36m320193\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">identifier: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">320193</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["document_type: \u001b[1;36m10\u001b[0m-K\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">document_type: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>-K\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["reporting_end_date: \u001b[1;36m2024\u001b[0m-\u001b[1;36m10\u001b[0m-\u001b[1;36m18\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">reporting_end_date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">18</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["document_period_end_date: \u001b[1;36m2024\u001b[0m-\u001b[1;36m09\u001b[0m-\u001b[1;36m28\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">document_period_end_date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["fiscal_year: \u001b[1;36m2024\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">fiscal_year: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["fiscal_period: FY\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">fiscal_period: FY\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["fiscal_year_end_month: \u001b[1;36m9\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">fiscal_year_end_month: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["fiscal_year_end_day: \u001b[1;36m28\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">fiscal_year_end_day: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["annual_report: \u001b[3;92mTrue\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">annual_report: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-style: italic\">True</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["quarterly_report: \u001b[3;91mFalse\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">quarterly_report: <span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["amendment: \u001b[3;91mF<PERSON>e\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">amendment: <span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 18}, {"metadata": {}, "cell_type": "markdown", "source": "### Available Reporting Periods", "id": "57325ccd1572d9e"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["print(\"\\n[bold]Available Reporting Periods:[/bold]\")\n", "for i, period in enumerate(xbrl.reporting_periods):\n", "    if period['type'] == 'instant':\n", "        print(f\"{i+1}. As of {period['date']}\")\n", "    else:\n", "        print(f\"{i+1}. {period['start_date']} to {period['end_date']}\")"], "id": "ab6ec922497a7214"}, {"metadata": {}, "cell_type": "markdown", "source": "### Period Views", "id": "c1841eb13e088a8d"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-14T18:46:32.276048Z", "start_time": "2025-03-14T18:46:32.267676Z"}}, "cell_type": "code", "source": ["# Show available period views for each statement\n", "print(\"\\n[bold]Available Period Views for Balance Sheet:[/bold]\")\n", "bs_views = xbrl.get_period_views(\"BalanceSheet\")\n", "for view in bs_views:\n", "    print(f\"- {view['name']}: {view['description']}\")\n", "    \n", "print(\"\\n[bold]Available Period Views for Income Statement:[/bold]\")\n", "is_views = xbrl.get_period_views(\"IncomeStatement\")\n", "for view in is_views:\n", "    print(f\"- {view['name']}: {view['description']}\")"], "id": "8d8e0d45924b0773", "outputs": [{"data": {"text/plain": ["\n", "\u001b[1mAvailable Period Views for Balance Sheet:\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"font-weight: bold\">Available Period Views for Balance Sheet:</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["- Three-Year Comparison: Shows the most recent three periods\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">- Three-Year Comparison: Shows the most recent three periods\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["- Current vs. Previous Periods: Shows the current period and two previous periods\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">- Current vs. Previous Periods: Shows the current period and two previous periods\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["- Three-Year Annual Comparison: Shows three fiscal years for comparison\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">- Three-Year Annual Comparison: Shows three fiscal years for comparison\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["- Annual Comparison: Shows two fiscal years for comparison\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">- Annual Comparison: Shows two fiscal years for comparison\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\n", "\u001b[1mAvailable Period Views for Income Statement:\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"font-weight: bold\">Available Period Views for Income Statement:</span>\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["- Three-Year Comparison: Compares three fiscal years\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">- Three-Year Comparison: Compares three fiscal years\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["- Annual Comparison: Compares recent fiscal years\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">- Annual Comparison: Compares recent fiscal years\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": null}, {"cell_type": "markdown", "id": "fa2f3a67-39e0-44b4-afc1-57c6da68a5a6", "metadata": {"ExecuteTime": {"end_time": "2025-03-14T18:49:56.424203Z", "start_time": "2025-03-14T18:49:56.422179Z"}}, "source": "### Period Views for INTC"}, {"cell_type": "code", "id": "7aa663b5-a624-473a-9f80-41106a82bbcc", "metadata": {"ExecuteTime": {"end_time": "2025-03-14T18:50:38.217248Z", "start_time": "2025-03-14T18:50:36.859230Z"}}, "source": ["c = Company(\"INTC\")\n", "filing = c.latest(\"10-K\")\n", "xbrl = XBRL.from_filing(filing)\n", "bs_views = xbrl.get_period_views(\"BalanceSheet\")\n", "for view in bs_views:\n", "    print(f\"- {view['name']}: {view['description']}\")"], "outputs": [{"data": {"text/plain": ["- Three-Year Comparison: Shows the most recent three periods\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">- Three-Year Comparison: Shows the most recent three periods\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["- Current vs. Previous Periods: Shows the current period and two previous periods\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">- Current vs. Previous Periods: Shows the current period and two previous periods\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["- Three-Year Annual Comparison: Shows three fiscal years for comparison\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">- Three-Year Annual Comparison: Shows three fiscal years for comparison\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["- Annual Comparison: Shows two fiscal years for comparison\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">- Annual Comparison: Shows two fiscal years for comparison\n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 17}, {"cell_type": "markdown", "id": "557cb032-ad08-42e9-b418-b87b299d1b23", "metadata": {}, "source": "### Render Balance Sheet with Current vs. Previous Period view if available"}, {"cell_type": "code", "execution_count": 13, "id": "47e9467c-4ead-4e6c-bc29-f35f96c89217", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-style: italic\">                                             CONSOLIDATEDBALANCESHEETS                                             </span>\n", "<span style=\"font-style: italic\">                              </span><span style=\"font-weight: bold; font-style: italic\">Fiscal Year Ended</span><span style=\"font-style: italic\"> (In millions, except per share data)                               </span>\n", "                                                                                                                   \n", " <span style=\"font-weight: bold\"> Line Item                                                                     </span> <span style=\"font-weight: bold\"> Sep 28, 2024   </span> <span style=\"font-weight: bold\"> Sep 30, 2023   </span> \n", " ───────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "    ASSETS:                                                                                                        \n", "      Current assets:                                                                                              \n", "        Cash and cash equivalents                                                        $29,943          $29,965  \n", "        Marketable securities                                                            $35,228          $31,590  \n", "        Accounts receivable, net                                                         $33,410          $29,508  \n", "        Vendor non-trade receivables                                                     $32,833          $31,477  \n", "        Inventories                                                                       $7,286           $6,331  \n", "        Other current assets                                                             $14,287          $14,695  \n", "        Total current assets                                                            $152,987         $143,566  \n", "      Non-current assets:                                                                                          \n", "        Marketable securities                                                            $91,479         $100,544  \n", "        Property, plant and equipment, net                                               $45,680          $43,715  \n", "        Other non-current assets                                                         $74,834          $64,758  \n", "        Total non-current assets                                                        $211,993         $209,017  \n", "      Total assets                                                                      $364,980         $352,583  \n", "    LIABILITIES AND SHAREH<PERSON>DERS’ EQUITY:                                                                          \n", "      Current liabilities:                                                                                         \n", "        Accounts payable                                                                 $68,960          $62,611  \n", "        Other current liabilities                                                        $78,304          $58,829  \n", "        Deferred revenue                                                                  $8,249           $8,061  \n", "        Commercial paper                                                                  $9,967           $5,985  \n", "        Term debt                                                                        $10,912           $9,822  \n", "        Total current liabilities                                                       $176,392         $145,308  \n", "      Non-current liabilities:                                                                                     \n", "        Term debt                                                                        $85,750          $95,281  \n", "        Other non-current liabilities                                                    $45,888          $49,848  \n", "        Total non-current liabilities                                                   $131,638         $145,129  \n", "      Total liabilities                                                                 $308,030         $290,437  \n", "      Commitments and contingencies                                                                                \n", "      Common stock, shares outstanding (in shares)                                15,116,786,000   15,550,061,000  \n", "      Common stock, shares issued (in shares)                                     15,116,786,000   15,550,061,000  \n", "      Shareholders’ equity:                                                                                        \n", "        Common stock and additional paid-in capital, $0.00001 par value:                 $83,276          $73,812  \n", "  50,400,000 shares authorized; 15,116,786 and 15,550,061 shares issued and                                        \n", "  outstanding, respectively                                                                                        \n", "        Accumulated deficit                                                            $(19,154)           $(214)  \n", "        Accumulated other comprehensive loss                                            $(7,172)        $(11,452)  \n", "        Total shareholders’ equity                                                       $56,950          $62,146  \n", "      Total liabilities and shareholders’ equity                                        $364,980         $352,583  \n", "                                                                                                                   \n", "</pre>\n"], "text/plain": ["\u001b[3m                                             CONSOLIDATEDBALANCESHEETS                                             \u001b[0m\n", "\u001b[3m                              \u001b[0m\u001b[1;3mFiscal Year Ended\u001b[0m\u001b[3m \u001b[0m\u001b[3m(In millions, except per share data)\u001b[0m\u001b[3m                               \u001b[0m\n", "                                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1mLine Item                                                                    \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 28, 2024  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mSep 30, 2023  \u001b[0m\u001b[1m \u001b[0m \n", " ───────────────────────────────────────────────────────────────────────────────────────────────────────────────── \n", "    ASSETS:                                                                                                        \n", "      Current assets:                                                                                              \n", "        Cash and cash equivalents                                                        $29,943          $29,965  \n", "        Marketable securities                                                            $35,228          $31,590  \n", "        Accounts receivable, net                                                         $33,410          $29,508  \n", "        Vendor non-trade receivables                                                     $32,833          $31,477  \n", "        Inventories                                                                       $7,286           $6,331  \n", "        Other current assets                                                             $14,287          $14,695  \n", "        Total current assets                                                            $152,987         $143,566  \n", "      Non-current assets:                                                                                          \n", "        Marketable securities                                                            $91,479         $100,544  \n", "        Property, plant and equipment, net                                               $45,680          $43,715  \n", "        Other non-current assets                                                         $74,834          $64,758  \n", "        Total non-current assets                                                        $211,993         $209,017  \n", "      Total assets                                                                      $364,980         $352,583  \n", "    LIABILITIES AND SHAREH<PERSON>DERS’ EQUITY:                                                                          \n", "      Current liabilities:                                                                                         \n", "        Accounts payable                                                                 $68,960          $62,611  \n", "        Other current liabilities                                                        $78,304          $58,829  \n", "        Deferred revenue                                                                  $8,249           $8,061  \n", "        Commercial paper                                                                  $9,967           $5,985  \n", "        Term debt                                                                        $10,912           $9,822  \n", "        Total current liabilities                                                       $176,392         $145,308  \n", "      Non-current liabilities:                                                                                     \n", "        Term debt                                                                        $85,750          $95,281  \n", "        Other non-current liabilities                                                    $45,888          $49,848  \n", "        Total non-current liabilities                                                   $131,638         $145,129  \n", "      Total liabilities                                                                 $308,030         $290,437  \n", "      Commitments and contingencies                                                                                \n", "      Common stock, shares outstanding (in shares)                                15,116,786,000   15,550,061,000  \n", "      Common stock, shares issued (in shares)                                     15,116,786,000   15,550,061,000  \n", "      Shareholders’ equity:                                                                                        \n", "        Common stock and additional paid-in capital, $0.00001 par value:                 $83,276          $73,812  \n", "  50,400,000 shares authorized; 15,116,786 and 15,550,061 shares issued and                                        \n", "  outstanding, respectively                                                                                        \n", "        Accumulated deficit                                                            $(19,154)           $(214)  \n", "        Accumulated other comprehensive loss                                            $(7,172)        $(11,452)  \n", "        Total shareholders’ equity                                                       $56,950          $62,146  \n", "      Total liabilities and shareholders’ equity                                        $364,980         $352,583  \n", "                                                                                                                   \n"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["current_vs_prev_bs = xbrl.render_statement(\"BalanceSheet\", period_view=\"Current vs. Previous Period\")\n", "current_vs_prev_bs"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}