# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: Smoke test recent filings

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

permissions:
  contents: read

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.10
      uses: actions/setup-python@v3
      with:
        python-version: "3.10"
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install hatch
    - name: Batch test sample of recent filings
      run: |
        # Setting PYTHONPATH to include the directory containing 'edgar' package
        export PYTHONPATH=$PYTHONPATH:$(pwd)
        export EDGAR_IDENTITY="<NAME_EMAIL>"
        # Smoke test recent filings
        hatch run smoke-filings -y 2024 -s 40
