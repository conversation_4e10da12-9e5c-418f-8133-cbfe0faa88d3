"/Users/<USER>/Library/Application Support/hatch/env/virtual/edgartools/jY6urB4z/edgartools/bin/python" /Users/<USER>/PycharmProjects/edgartools/tests/perf/perf_xbrl2.py

  _     ._   __/__   _ _  _  _ _/_   Recorded: 18:59:25  Samples:  613
 /_//_/// /_\ / //_// / //_'/ //     Duration: 0.961     CPU time: 0.686
/   _/                      v4.6.2

Program: /Users/<USER>/PycharmProjects/edgartools/tests/perf/perf_xbrl2.py

0.961 <module>  perf_xbrl2.py:1
└─ 0.961 main  perf_xbrl2.py:5
   ├─ 0.892 XBRL.from_filing  xbrl/xbrl.py:269
   │  ├─ 0.506 Filing.attachments  _filings.py:1346
   │  │  ├─ 0.442 Filing.sgml  _filings.py:1485
   │  │  │  └─ 0.442 FilingSGML.from_filing  sgml/sgml_common.py:424
   │  │  │     └─ 0.442 FilingSGML.from_source  sgml/sgml_common.py:361
   │  │  │        ├─ 0.392 read_content_as_string  sgml/sgml_common.py:66
   │  │  │        │  ├─ 0.350 read_content  sgml/sgml_common.py:38
   │  │  │        │  │  ├─ 0.178 Response.iter_lines  httpx/_models.py:858
   │  │  │        │  │  │     [18 frames hidden]  httpx, httpcore, ssl, <built-in>
   │  │  │        │  │  ├─ 0.155 stream_with_retry  httprequests.py:257
   │  │  │        │  │  │  └─ 0.155 _GeneratorContextManager.__enter__  contextlib.py:132
   │  │  │        │  │  │        [25 frames hidden]  contextlib, httpx, httpcore, ssl, <bu...
   │  │  │        │  │  │           0.013 _get_client  httpclient.py:35
   │  │  │        │  │  │           └─ 0.013 _client_factory  httpclient.py:22
   │  │  │        │  │  │              └─ 0.013 Client.__init__  httpx/_client.py:618
   │  │  │        │  │  │                    [8 frames hidden]  httpx, <built-in>
   │  │  │        │  │  └─ 0.017 [self]  sgml/sgml_common.py
   │  │  │        │  ├─ 0.024 [self]  sgml/sgml_common.py
   │  │  │        │  └─ 0.011 list.append  <built-in>
   │  │  │        └─ 0.050 parse_submission_text  sgml/sgml_common.py:145
   │  │  │           └─ 0.049 SGMLParser.parse  sgml/sgml_parser.py:110
   │  │  │              └─ 0.049 SGMLParser._parse_sec_document_format  sgml/sgml_parser.py:123
   │  │  │                 └─ 0.049 SecDocumentFormatParser.parse  sgml/sgml_parser.py:392
   │  │  │                    └─ 0.036 [self]  sgml/sgml_parser.py
   │  │  └─ 0.064 cached_property.__get__  functools.py:981
   │  │     └─ 0.064 FilingSGML.attachments  sgml/sgml_common.py:273
   │  │        └─ 0.060 cached_property.__get__  functools.py:981
   │  │           └─ 0.060 FilingSGML.filing_summary  sgml/sgml_common.py:316
   │  │              └─ 0.060 FilingSummary.parse  sgml/filing_summary.py:335
   │  │                 ├─ 0.040 child_text  xmltools.py:78
   │  │                 │  └─ 0.036 Tag.find  bs4/element.py:1987
   │  │                 │        [5 frames hidden]  bs4
   │  │                 └─ 0.013 BeautifulSoup.__init__  bs4/__init__.py:122
   │  │                       [4 frames hidden]  bs4
   │  ├─ 0.252 XBRLParser.parse_labels_content  xbrl/parser.py:325
   │  │  └─ 0.243 find  xml/etree/ElementPath.py:404
   │  │        [6 frames hidden]  xml, <built-in>
   │  ├─ 0.051 XBRLParser.parse_presentation_content  xbrl/parser.py:400
   │  │  ├─ 0.021 XML  xml/etree/ElementTree.py:1325
   │  │  │     [2 frames hidden]  xml, <built-in>
   │  │  ├─ 0.015 find  xml/etree/ElementPath.py:404
   │  │  └─ 0.012 XBRLParser._build_presentation_tree  xbrl/parser.py:470
   │  │     └─ 0.012 XBRLParser._build_presentation_subtree  xbrl/parser.py:515
   │  │        └─ 0.010 XBRLParser._build_presentation_subtree  xbrl/parser.py:515
   │  ├─ 0.030 XBRLParser.parse_instance_content  xbrl/parser.py:945
   │  │  └─ 0.016 XBRLParser._extract_facts  xbrl/parser.py:1085
   │  │     └─ 0.010 process_element  xbrl/parser.py:1126
   │  ├─ 0.022 Attachment.content  attachments.py:187
   │  │  └─ 0.022 SGMLDocument.content  sgml/sgml_parser.py:37
   │  │     └─ 0.022 get_content_between_tags  sgml/tools.py:31
   │  │        └─ 0.022 search  re/__init__.py:173
   │  │              [2 frames hidden]  re, <built-in>
   │  └─ 0.010 XBRLParser.parse_definition_content  xbrl/parser.py:752
   └─ 0.069 Statement.to_dataframe  xbrl/statements.py:160
      └─ 0.068 Statement.render  xbrl/statements.py:116
         └─ 0.068 XBRL.render_statement  xbrl/xbrl.py:1045
            ├─ 0.057 render_statement  xbrl/rendering.py:929
            │  └─ 0.055 standardize_statement  xbrl/standardization/core.py:552
            │     └─ 0.055 ConceptMapper.map_concept  xbrl/standardization/core.py:352
            │        └─ 0.055 ConceptMapper._infer_mapping  xbrl/standardization/core.py:388
            │           ├─ 0.041 SequenceMatcher.ratio  difflib.py:597
            │           │     [4 frames hidden]  difflib
            │           └─ 0.010 SequenceMatcher.__init__  difflib.py:120
            │                 [3 frames hidden]  difflib
            └─ 0.010 XBRL.get_statement  xbrl/xbrl.py:523
               └─ 0.010 XBRL._generate_line_items  xbrl/xbrl.py:568