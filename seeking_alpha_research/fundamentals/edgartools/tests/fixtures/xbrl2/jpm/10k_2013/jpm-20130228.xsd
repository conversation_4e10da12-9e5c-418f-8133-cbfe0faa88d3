<?xml version="1.0" encoding="US-ASCII"?>
<!--XBRL Document Created with WebFilings-->
<!-- -->
<xsd:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://www.jpmorganchase.com/20121231" xmlns:jpm="http://www.jpmorganchase.com/20121231" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:nonnum="http://www.xbrl.org/dtr/type/non-numeric" xmlns:num="http://www.xbrl.org/dtr/type/numeric" xmlns:us-types="http://fasb.org/us-types/2012-01-31" xmlns:xbrldt="http://xbrl.org/2005/xbrldt" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import namespace="http://fasb.org/us-gaap/2012-01-31" schemaLocation="http://xbrl.fasb.org/us-gaap/2012/elts/us-gaap-2012-01-31.xsd" />
  <xsd:import namespace="http://fasb.org/us-roles/2012-01-31" schemaLocation="http://xbrl.fasb.org/us-gaap/2012/elts/us-roles-2012-01-31.xsd" />
  <xsd:import namespace="http://fasb.org/us-types/2012-01-31" schemaLocation="http://xbrl.fasb.org/us-gaap/2012/elts/us-types-2012-01-31.xsd" />
  <xsd:import namespace="http://www.xbrl.org/2003/instance" schemaLocation="http://www.xbrl.org/2003/xbrl-instance-2003-12-31.xsd" />
  <xsd:import namespace="http://www.xbrl.org/2003/linkbase" schemaLocation="http://www.xbrl.org/2003/xbrl-linkbase-2003-12-31.xsd" />
  <xsd:import namespace="http://www.xbrl.org/2009/arcrole/fact-explanatoryFact" schemaLocation="http://www.xbrl.org/lrr/arcrole/factExplanatory-2009-12-16.xsd" />
  <xsd:import namespace="http://www.xbrl.org/2009/role/negated" schemaLocation="http://www.xbrl.org/lrr/role/negated-2009-12-16.xsd" />
  <xsd:import namespace="http://www.xbrl.org/2009/role/net" schemaLocation="http://www.xbrl.org/lrr/role/net-2009-12-16.xsd" />
  <xsd:import namespace="http://www.xbrl.org/dtr/type/non-numeric" schemaLocation="http://www.xbrl.org/dtr/type/nonNumeric-2009-12-16.xsd" />
  <xsd:import namespace="http://www.xbrl.org/dtr/type/numeric" schemaLocation="http://www.xbrl.org/dtr/type/numeric-2009-12-16.xsd" />
  <xsd:import namespace="http://xbrl.org/2005/xbrldt" schemaLocation="http://www.xbrl.org/2005/xbrldt-2005.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/country/2012-01-31" schemaLocation="http://xbrl.sec.gov/country/2012/country-2012-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/currency/2012-01-31" schemaLocation="http://xbrl.sec.gov/currency/2012/currency-2012-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/dei/2012-01-31" schemaLocation="http://xbrl.sec.gov/dei/2012/dei-2012-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/exch/2012-01-31" schemaLocation="http://xbrl.sec.gov/exch/2012/exch-2012-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/invest/2012-01-31" schemaLocation="http://xbrl.sec.gov/invest/2012/invest-2012-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/naics/2011-01-31" schemaLocation="http://xbrl.sec.gov/naics/2011/naics-2011-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/sic/2011-01-31" schemaLocation="http://xbrl.sec.gov/sic/2011/sic-2011-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/stpr/2011-01-31" schemaLocation="http://xbrl.sec.gov/stpr/2011/stpr-2011-01-31.xsd" />
  <xsd:annotation>
    <xsd:appinfo>
      <link:linkbaseRef xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:href="jpm-20121231_cal.xml" xlink:role="http://www.xbrl.org/2003/role/calculationLinkbaseRef" xlink:type="simple" />
      <link:linkbaseRef xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:href="jpm-20121231_def.xml" xlink:role="http://www.xbrl.org/2003/role/definitionLinkbaseRef" xlink:type="simple" />
      <link:linkbaseRef xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:href="jpm-20121231_lab.xml" xlink:role="http://www.xbrl.org/2003/role/labelLinkbaseRef" xlink:type="simple" />
      <link:linkbaseRef xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:href="jpm-20121231_pre.xml" xlink:role="http://www.xbrl.org/2003/role/presentationLinkbaseRef" xlink:type="simple" />
      <link:roleType id="AccountsPayableAndOtherLiabilities" roleURI="http://www.jpmorganchase.com/role/AccountsPayableAndOtherLiabilities">
        <link:definition>2126100 - Disclosure - Accounts Payable and Other Liabilities</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AccountsPayableAndOtherLiabilitiesDetails" roleURI="http://www.jpmorganchase.com/role/AccountsPayableAndOtherLiabilitiesDetails">
        <link:definition>2426402 - Disclosure - Accounts Payable and Other Liabilities (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AccountsPayableAndOtherLiabilitiesTables" roleURI="http://www.jpmorganchase.com/role/AccountsPayableAndOtherLiabilitiesTables">
        <link:definition>2326301 - Disclosure - Accounts Payable and Other Liabilities (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AccumulatedOtherComprehensiveIncomeLossDetails" roleURI="http://www.jpmorganchase.com/role/AccumulatedOtherComprehensiveIncomeLossDetails">
        <link:definition>2430402 - Disclosure - Accumulated Other Comprehensive Income/(Loss) (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AccumulatedOtherComprehensiveIncomeLossDetailsCalc2" roleURI="http://www.jpmorganchase.com/role/AccumulatedOtherComprehensiveIncomeLossDetailsCalc2">
        <link:definition>2430402 - Disclosure - Accumulated Other Comprehensive Income/(Loss) (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AccumulatedOtherComprehensiveIncomeloss" roleURI="http://www.jpmorganchase.com/role/AccumulatedOtherComprehensiveIncomeloss">
        <link:definition>2130100 - Disclosure - Accumulated Other Comprehensive Income/(Loss)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AccumulatedOtherComprehensiveIncomelossTables" roleURI="http://www.jpmorganchase.com/role/AccumulatedOtherComprehensiveIncomelossTables">
        <link:definition>2330301 - Disclosure - Accumulated Other Comprehensive Income/(Loss) (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AllowanceForCreditLosses" roleURI="http://www.jpmorganchase.com/role/AllowanceForCreditLosses">
        <link:definition>2121100 - Disclosure - Allowance for Credit Losses</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AllowanceForCreditLossesDetails" roleURI="http://www.jpmorganchase.com/role/AllowanceForCreditLossesDetails">
        <link:definition>2421403 - Disclosure - Allowance for Credit Losses (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AllowanceForCreditLossesPolicies" roleURI="http://www.jpmorganchase.com/role/AllowanceForCreditLossesPolicies">
        <link:definition>2221201 - Disclosure - Allowance for Credit Losses (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AllowanceForCreditLossesTables" roleURI="http://www.jpmorganchase.com/role/AllowanceForCreditLossesTables">
        <link:definition>2321302 - Disclosure - Allowance for Credit Losses (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BasisOfPresentation" roleURI="http://www.jpmorganchase.com/role/BasisOfPresentation">
        <link:definition>2101100 - Disclosure - Basis of Presentation</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BasisOfPresentationPolicies" roleURI="http://www.jpmorganchase.com/role/BasisOfPresentationPolicies">
        <link:definition>2201201 - Disclosure - Basis of Presentation (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessChangesAndDevelopments" roleURI="http://www.jpmorganchase.com/role/BusinessChangesAndDevelopments">
        <link:definition>2107100 - Disclosure - Business Changes and Developments</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessChangesAndDevelopmentsDetails" roleURI="http://www.jpmorganchase.com/role/BusinessChangesAndDevelopmentsDetails">
        <link:definition>2407401 - Disclosure - Business Changes and Developments (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessChangesAndDevelopmentsDetails1" roleURI="http://www.jpmorganchase.com/role/BusinessChangesAndDevelopmentsDetails1">
        <link:definition>2407402 - Disclosure - Business Changes and Developments (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessChangesAndDevelopmentsOtherBusinessEventsDetails2" roleURI="http://www.jpmorganchase.com/role/BusinessChangesAndDevelopmentsOtherBusinessEventsDetails2">
        <link:definition>2407403 - Disclosure - Business Changes and Developments - Other Business Events (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessChangesAndDevelopmentsSubsequentEventsDetails3" roleURI="http://www.jpmorganchase.com/role/BusinessChangesAndDevelopmentsSubsequentEventsDetails3">
        <link:definition>2407404 - Disclosure - Business Changes and Developments - Subsequent Events (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessSegments" roleURI="http://www.jpmorganchase.com/role/BusinessSegments">
        <link:definition>2139100 - Disclosure - Business Segments</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessSegmentsDetails" roleURI="http://www.jpmorganchase.com/role/BusinessSegmentsDetails">
        <link:definition>2439402 - Disclosure - Business Segments (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessSegmentsTables" roleURI="http://www.jpmorganchase.com/role/BusinessSegmentsTables">
        <link:definition>2339301 - Disclosure - Business Segments (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommitmentsPledgedAssetsAndCollateral" roleURI="http://www.jpmorganchase.com/role/CommitmentsPledgedAssetsAndCollateral">
        <link:definition>2135100 - Disclosure - Commitments, Pledged Assets, and Collateral</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommitmentsPledgedAssetsAndCollateralLeaseCommitmentsDetails" roleURI="http://www.jpmorganchase.com/role/CommitmentsPledgedAssetsAndCollateralLeaseCommitmentsDetails">
        <link:definition>2435402 - Disclosure - Commitments, Pledged Assets and Collateral - Lease Commitments (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommitmentsPledgedAssetsAndCollateralTables" roleURI="http://www.jpmorganchase.com/role/CommitmentsPledgedAssetsAndCollateralTables">
        <link:definition>2335301 - Disclosure - Commitments, Pledged Assets, and Collateral (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommitmentsPledgedAssetsCollateralAndContingenciesDetails1" roleURI="http://www.jpmorganchase.com/role/CommitmentsPledgedAssetsCollateralAndContingenciesDetails1">
        <link:definition>2435403 - Disclosure - Commitments, Pledged Assets, Collateral and Contingencies (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommonStock" roleURI="http://www.jpmorganchase.com/role/CommonStock">
        <link:definition>2129100 - Disclosure - Common stock</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommonStockDetails" roleURI="http://www.jpmorganchase.com/role/CommonStockDetails">
        <link:definition>2429402 - Disclosure - Common stock (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommonStockTables" roleURI="http://www.jpmorganchase.com/role/CommonStockTables">
        <link:definition>2329301 - Disclosure - Common stock (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedBalanceSheets" roleURI="http://www.jpmorganchase.com/role/ConsolidatedBalanceSheets">
        <link:definition>1004000 - Statement - Consolidated Balance Sheets</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedBalanceSheetsParenthetical" roleURI="http://www.jpmorganchase.com/role/ConsolidatedBalanceSheetsParenthetical">
        <link:definition>1004501 - Statement - Consolidated Balance Sheets (Parenthetical)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedStatementsOfCashFlows" roleURI="http://www.jpmorganchase.com/role/ConsolidatedStatementsOfCashFlows">
        <link:definition>1006000 - Statement - Consolidated Statements of Cash Flows</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedStatementsOfChangesInStockholdersEquity" roleURI="http://www.jpmorganchase.com/role/ConsolidatedStatementsOfChangesInStockholdersEquity">
        <link:definition>1005000 - Statement - Consolidated Statements of Changes in Stockholders' Equity</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedStatementsOfChangesInStockholdersEquityParenthetical" roleURI="http://www.jpmorganchase.com/role/ConsolidatedStatementsOfChangesInStockholdersEquityParenthetical">
        <link:definition>1005501 - Statement - Consolidated Statements of Changes in Stockholders' Equity (Parenthetical)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedStatementsOfComprehensiveIncome" roleURI="http://www.jpmorganchase.com/role/ConsolidatedStatementsOfComprehensiveIncome">
        <link:definition>1003000 - Statement - Consolidated Statements of Comprehensive Income</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedStatementsOfIncome" roleURI="http://www.jpmorganchase.com/role/ConsolidatedStatementsOfIncome">
        <link:definition>1002000 - Statement - Consolidated Statements of Income</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CreditRiskConcentrations" roleURI="http://www.jpmorganchase.com/role/CreditRiskConcentrations">
        <link:definition>2110100 - Disclosure - Credit Risk Concentrations</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CreditRiskConcentrationsDetails" roleURI="http://www.jpmorganchase.com/role/CreditRiskConcentrationsDetails">
        <link:definition>2410402 - Disclosure - Credit Risk Concentrations (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CreditRiskConcentrationsTables" roleURI="http://www.jpmorganchase.com/role/CreditRiskConcentrationsTables">
        <link:definition>2310301 - Disclosure - Credit Risk Concentrations (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="Deposits" roleURI="http://www.jpmorganchase.com/role/Deposits">
        <link:definition>2125100 - Disclosure - Deposits</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DepositsNoninterestAndInterestBearingDetails" roleURI="http://www.jpmorganchase.com/role/DepositsNoninterestAndInterestBearingDetails">
        <link:definition>2425402 - Disclosure - Deposits - Noninterest and Interest-bearing (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DepositsTables" roleURI="http://www.jpmorganchase.com/role/DepositsTables">
        <link:definition>2325301 - Disclosure - Deposits (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DepositsTimeDepositsDetails1" roleURI="http://www.jpmorganchase.com/role/DepositsTimeDepositsDetails1">
        <link:definition>2425403 - Disclosure - Deposits - Time Deposits (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstruments" roleURI="http://www.jpmorganchase.com/role/DerivativeInstruments">
        <link:definition>2111100 - Disclosure - Derivative Instruments</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsCreditAndLiquidityRiskAndRelatedContingentFeaturesDetails3" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsCreditAndLiquidityRiskAndRelatedContingentFeaturesDetails3">
        <link:definition>2411410 - Disclosure - Derivative Instruments - Credit and Liquidity Risk and Related Contingent Features (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsCreditDerivativesDetails4A" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsCreditDerivativesDetails4A">
        <link:definition>2411411 - Disclosure - Derivative Instruments - Credit Derivatives (Details 4a)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsCreditDerivativesProtectionSoldNotionalAndFairValueDetails4B" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsCreditDerivativesProtectionSoldNotionalAndFairValueDetails4B">
        <link:definition>2411412 - Disclosure - Derivative Instruments - Credit Derivatives, Protection Sold, Notional and Fair Value (Details 4b)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsDetails" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsDetails">
        <link:definition>2411403 - Disclosure - Derivative Instruments (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsImpactOnBalanceSheetDetails1A" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsImpactOnBalanceSheetDetails1A">
        <link:definition>2411404 - Disclosure - Derivative Instruments - Impact on Balance Sheet (Details 1a)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsImpactOnStatementsOfIncomeCashFlowHedgesDetails2B" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsImpactOnStatementsOfIncomeCashFlowHedgesDetails2B">
        <link:definition>2411406 - Disclosure - Derivative Instruments - Impact on Statements of Income, Cash Flow Hedges (Details 2b)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsImpactOnStatementsOfIncomeFairValueHedgesDetails2A" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsImpactOnStatementsOfIncomeFairValueHedgesDetails2A">
        <link:definition>2411405 - Disclosure - Derivative Instruments - Impact on Statements of Income, Fair Value Hedges (Details 2a)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsImpactOnStatementsOfIncomeFairValueHedgesDetails2ACalc2" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsImpactOnStatementsOfIncomeFairValueHedgesDetails2ACalc2">
        <link:definition>2411405 - Disclosure - Derivative Instruments - Impact on Statements of Income, Fair Value Hedges (Details 2a)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsImpactOnStatementsOfIncomeNetInvestmentHedgesDetails2C" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsImpactOnStatementsOfIncomeNetInvestmentHedgesDetails2C">
        <link:definition>2411407 - Disclosure - Derivative Instruments - Impact on Statements of Income, Net Investment Hedges (Details 2c)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsImpactOnStatementsOfIncomeRiskManagementDerivativesDetails2D" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsImpactOnStatementsOfIncomeRiskManagementDerivativesDetails2D">
        <link:definition>2411408 - Disclosure - Derivative Instruments - Impact on Statements of Income, Risk Management Derivatives (Details 2d)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsImpactOnStatementsOfIncomeTradingDerivativesDetails2E" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsImpactOnStatementsOfIncomeTradingDerivativesDetails2E">
        <link:definition>2411409 - Disclosure - Derivative Instruments - Impact on Statements of Income, Trading Derivatives (Details 2e)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsPolicies" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsPolicies">
        <link:definition>2211201 - Disclosure - Derivative Instruments (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DerivativeInstrumentsTables" roleURI="http://www.jpmorganchase.com/role/DerivativeInstrumentsTables">
        <link:definition>2311302 - Disclosure - Derivative Instruments (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DocumentAndEntityInformation" roleURI="http://www.jpmorganchase.com/role/DocumentAndEntityInformation">
        <link:definition>0001000 - Document - Document and Entity Information</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EarningsPerShare" roleURI="http://www.jpmorganchase.com/role/EarningsPerShare">
        <link:definition>2129100 - Disclosure - Earnings Per Share</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EarningsPerShareDetails" roleURI="http://www.jpmorganchase.com/role/EarningsPerShareDetails">
        <link:definition>2429403 - Disclosure - Earnings Per Share (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EarningsPerSharePolicies" roleURI="http://www.jpmorganchase.com/role/EarningsPerSharePolicies">
        <link:definition>2229201 - Disclosure - Earnings Per Share (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EarningsPerShareTables" roleURI="http://www.jpmorganchase.com/role/EarningsPerShareTables">
        <link:definition>2329302 - Disclosure - Earnings Per Share (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EmployeeStockBasedIncentives" roleURI="http://www.jpmorganchase.com/role/EmployeeStockBasedIncentives">
        <link:definition>2116100 - Disclosure - Employee Stock Based Incentives</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EmployeeStockBasedIncentivesEmployeeStockBasedIncentivesPolicies" roleURI="http://www.jpmorganchase.com/role/EmployeeStockBasedIncentivesEmployeeStockBasedIncentivesPolicies">
        <link:definition>2216201 - Disclosure - Employee Stock Based Incentives Employee Stock-based Incentives (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EmployeeStockBasedIncentivesLongTermIncentivePlansDetails" roleURI="http://www.jpmorganchase.com/role/EmployeeStockBasedIncentivesLongTermIncentivePlansDetails">
        <link:definition>2416403 - Disclosure - Employee Stock Based Incentives - Long-Term Incentive Plans (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EmployeeStockBasedIncentivesSupplementalInformationDetails1" roleURI="http://www.jpmorganchase.com/role/EmployeeStockBasedIncentivesSupplementalInformationDetails1">
        <link:definition>2416404 - Disclosure - Employee Stock Based Incentives - Supplemental Information  (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EmployeeStockBasedIncentivesTables" roleURI="http://www.jpmorganchase.com/role/EmployeeStockBasedIncentivesTables">
        <link:definition>2316302 - Disclosure - Employee Stock Based Incentives (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurement" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurement">
        <link:definition>2108100 - Disclosure - Fair Value Measurement</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementCarryingValueAndEstimatedFairValueDetails7" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurementCarryingValueAndEstimatedFairValueDetails7">
        <link:definition>2408410 - Disclosure - Fair Value Measurement - Carrying Value and Estimated Fair Value  (Details 7)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementChangesInLevel3RecurringMeasurementsDetails1" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurementChangesInLevel3RecurringMeasurementsDetails1">
        <link:definition>2408404 - Disclosure - Fair Value Measurement - Changes in level 3 recurring measurements (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementCreditAdjustmentsReflectedOnBalanceSheetDetails5" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurementCreditAdjustmentsReflectedOnBalanceSheetDetails5">
        <link:definition>2408407 - Disclosure - Fair Value Measurement - Credit Adjustments Reflected on Balance Sheet (Details 5)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementFairValueMeasurementAssetsAndLiabilitiesMeasuredAtFairValueOnNonrecurringBasisDetails6A" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurementFairValueMeasurementAssetsAndLiabilitiesMeasuredAtFairValueOnNonrecurringBasisDetails6A">
        <link:definition>2408409 - Disclosure - Fair Value Measurement Fair Value Measurement - Assets and Liabilities Measured at Fair Value on a Nonrecurring Basis (Details 6a)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementImpactOfCreditAdjustmentsDetails6" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurementImpactOfCreditAdjustmentsDetails6">
        <link:definition>2408408 - Disclosure - Fair Value Measurement - Impact of Credit Adjustments (Details 6)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementLevel3AnalysisDetails4" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurementLevel3AnalysisDetails4">
        <link:definition>2408406 - Disclosure - Fair Value Measurement - Level 3 Analysis (Details 4)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementPolicies" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurementPolicies">
        <link:definition>2208201 - Disclosure - Fair Value Measurement (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementRecurringBasisDetails" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurementRecurringBasisDetails">
        <link:definition>2408403 - Disclosure - Fair Value Measurement - Recurring Basis (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementSupplementalCaptionDataDetails1A" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurementSupplementalCaptionDataDetails1A">
        <link:definition>2408405 - Disclosure - Fair Value Measurement - Supplemental Caption Data (Details 1a)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementTables" roleURI="http://www.jpmorganchase.com/role/FairValueMeasurementTables">
        <link:definition>2308302 - Disclosure - Fair Value Measurement (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueOption" roleURI="http://www.jpmorganchase.com/role/FairValueOption">
        <link:definition>2109100 - Disclosure - Fair Value Option</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueOptionAggregateDifferencesDetails1" roleURI="http://www.jpmorganchase.com/role/FairValueOptionAggregateDifferencesDetails1">
        <link:definition>2409404 - Disclosure - Fair Value Option - Aggregate Differences (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueOptionDetails" roleURI="http://www.jpmorganchase.com/role/FairValueOptionDetails">
        <link:definition>2409403 - Disclosure - Fair Value Option (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueOptionPolicies" roleURI="http://www.jpmorganchase.com/role/FairValueOptionPolicies">
        <link:definition>2209201 - Disclosure - Fair Value Option (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueOptionTables" roleURI="http://www.jpmorganchase.com/role/FairValueOptionTables">
        <link:definition>2309302 - Disclosure - Fair Value Option (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssets" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssets">
        <link:definition>2123100 - Disclosure - Goodwill and Other Intangible Assets</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsAmortizationExpenseDetails7" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsAmortizationExpenseDetails7">
        <link:definition>2423410 - Disclosure - Goodwill and Other Intangible Assets - Amortization Expense (Details 7)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsByBusinessSegmentDetails1" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsByBusinessSegmentDetails1">
        <link:definition>2423404 - Disclosure - Goodwill and Other Intangible Assets - by Business Segment (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsChangesDuringPeriodDetails2" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsChangesDuringPeriodDetails2">
        <link:definition>2423405 - Disclosure - Goodwill and Other Intangible Assets - Changes During Period (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsDetails" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsDetails">
        <link:definition>2423403 - Disclosure - Goodwill and Other Intangible Assets (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsFutureAmortizationExpenseDetails8" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsFutureAmortizationExpenseDetails8">
        <link:definition>2423411 - Disclosure - Goodwill and Other Intangible Assets - Future Amortization Expense (Details 8)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsKeyEconomicAssumptionsDetails5" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsKeyEconomicAssumptionsDetails5">
        <link:definition>2423408 - Disclosure - Goodwill and Other Intangible Assets - Key Economic Assumptions (Details 5)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsMortgageFeesAndRelatedIncomeDetails4" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsMortgageFeesAndRelatedIncomeDetails4">
        <link:definition>2423407 - Disclosure - Goodwill and Other Intangible Assets - Mortgage Fees and Related Income (Details 4)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsMortgageServicingRightsDetails3" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsMortgageServicingRightsDetails3">
        <link:definition>2423406 - Disclosure - Goodwill and Other Intangible Assets - Mortgage Servicing Rights (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsOtherIntangibleAssetsDetails6" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsOtherIntangibleAssetsDetails6">
        <link:definition>2423409 - Disclosure - Goodwill and Other Intangible Assets - Other Intangible Assets (Details 6)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsPolicies" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsPolicies">
        <link:definition>2223201 - Disclosure - Goodwill and Other Intangible Assets (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="GoodwillAndOtherIntangibleAssetsTables" roleURI="http://www.jpmorganchase.com/role/GoodwillAndOtherIntangibleAssetsTables">
        <link:definition>2323302 - Disclosure - Goodwill and Other Intangible Assets (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxes" roleURI="http://www.jpmorganchase.com/role/IncomeTaxes">
        <link:definition>2130100 - Disclosure - Income Taxes</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesComponentsOfIncomeTaxExpensebenefitDetails" roleURI="http://www.jpmorganchase.com/role/IncomeTaxesComponentsOfIncomeTaxExpensebenefitDetails">
        <link:definition>2430403 - Disclosure - Income Taxes - Components of Income Tax Expense/(Benefit) (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesDeferredTaxAssetsAndLiabilitiesDetails2" roleURI="http://www.jpmorganchase.com/role/IncomeTaxesDeferredTaxAssetsAndLiabilitiesDetails2">
        <link:definition>2430405 - Disclosure - Income Taxes - Deferred Tax Assets and Liabilities (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesPolicies" roleURI="http://www.jpmorganchase.com/role/IncomeTaxesPolicies">
        <link:definition>2230201 - Disclosure - Income Taxes (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesReconciliationOfEffectiveIncomeTaxRateDetails1" roleURI="http://www.jpmorganchase.com/role/IncomeTaxesReconciliationOfEffectiveIncomeTaxRateDetails1">
        <link:definition>2430404 - Disclosure - Income Taxes - Reconciliation of Effective Income Tax Rate (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesTables" roleURI="http://www.jpmorganchase.com/role/IncomeTaxesTables">
        <link:definition>2330302 - Disclosure - Income Taxes (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesUnrecognizedTaxBenefitsDetails3" roleURI="http://www.jpmorganchase.com/role/IncomeTaxesUnrecognizedTaxBenefitsDetails3">
        <link:definition>2430406 - Disclosure - Income Taxes - Unrecognized Tax Benefits (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesUsAndNonUsComponentsDetails4" roleURI="http://www.jpmorganchase.com/role/IncomeTaxesUsAndNonUsComponentsDetails4">
        <link:definition>2430407 - Disclosure - Income Taxes - US and non-US components (Details 4)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InterestIncomeAndInterestExpense" roleURI="http://www.jpmorganchase.com/role/InterestIncomeAndInterestExpense">
        <link:definition>2114100 - Disclosure - Interest Income and Interest Expense</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InterestIncomeAndInterestExpenseDetails" roleURI="http://www.jpmorganchase.com/role/InterestIncomeAndInterestExpenseDetails">
        <link:definition>2414403 - Disclosure - Interest Income and Interest Expense (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InterestIncomeAndInterestExpenseInterestIncomeAndInterestExpensePolicies" roleURI="http://www.jpmorganchase.com/role/InterestIncomeAndInterestExpenseInterestIncomeAndInterestExpensePolicies">
        <link:definition>2214201 - Disclosure - Interest Income and Interest Expense Interest Income and Interest Expense (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InterestIncomeAndInterestExpenseTables" roleURI="http://www.jpmorganchase.com/role/InterestIncomeAndInterestExpenseTables">
        <link:definition>2314302 - Disclosure - Interest Income and Interest Expense (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InternationalOperations" roleURI="http://www.jpmorganchase.com/role/InternationalOperations">
        <link:definition>2138100 - Disclosure - International Operations</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InternationalOperationsDetails" roleURI="http://www.jpmorganchase.com/role/InternationalOperationsDetails">
        <link:definition>2438402 - Disclosure - International Operations (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InternationalOperationsTables" roleURI="http://www.jpmorganchase.com/role/InternationalOperationsTables">
        <link:definition>2338301 - Disclosure - International Operations (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="Litigation" roleURI="http://www.jpmorganchase.com/role/Litigation">
        <link:definition>2136100 - Disclosure - Litigation</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LitigationDetails" roleURI="http://www.jpmorganchase.com/role/LitigationDetails">
        <link:definition>2436401 - Disclosure - Litigation (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="Loans" roleURI="http://www.jpmorganchase.com/role/Loans">
        <link:definition>2120100 - Disclosure - Loans</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LoansByPortfolioSegmentAndClassDetails" roleURI="http://www.jpmorganchase.com/role/LoansByPortfolioSegmentAndClassDetails">
        <link:definition>2420403 - Disclosure - Loans - by Portfolio Segment and Class (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LoansCreditQualityIndicatorsAndOtherInformationDetails" roleURI="http://www.jpmorganchase.com/role/LoansCreditQualityIndicatorsAndOtherInformationDetails">
        <link:definition>2420406 - Disclosure - Loans - Credit Quality Indicators and Other Information (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LoansImpairedDetails" roleURI="http://www.jpmorganchase.com/role/LoansImpairedDetails">
        <link:definition>2420407 - Disclosure - Loans - Impaired (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LoansNetGainsAndLossesOnSaleDetails" roleURI="http://www.jpmorganchase.com/role/LoansNetGainsAndLossesOnSaleDetails">
        <link:definition>2420405 - Disclosure - Loans - Net Gains and Losses on Sale (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LoansPolicies" roleURI="http://www.jpmorganchase.com/role/LoansPolicies">
        <link:definition>2220201 - Disclosure - Loans (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LoansPurchasedCreditImpairedAccretableYieldsDetails" roleURI="http://www.jpmorganchase.com/role/LoansPurchasedCreditImpairedAccretableYieldsDetails">
        <link:definition>2420409 - Disclosure - Loans - Purchased Credit-Impaired Accretable Yields (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LoansPurchasedSoldAndReclassifiedToHeldForSaleDetails" roleURI="http://www.jpmorganchase.com/role/LoansPurchasedSoldAndReclassifiedToHeldForSaleDetails">
        <link:definition>2420404 - Disclosure - Loans - Purchased, Sold and Reclassified to Held-for-Sale (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LoansTables" roleURI="http://www.jpmorganchase.com/role/LoansTables">
        <link:definition>2320302 - Disclosure - Loans (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LoansTroubledDebtRestructuringsDetails" roleURI="http://www.jpmorganchase.com/role/LoansTroubledDebtRestructuringsDetails">
        <link:definition>2420408 - Disclosure - Loans - Troubled Debt Restructurings (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LongTermDebt" roleURI="http://www.jpmorganchase.com/role/LongTermDebt">
        <link:definition>2127100 - Disclosure - Long-term debt</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LongTermDebtDetails" roleURI="http://www.jpmorganchase.com/role/LongTermDebtDetails">
        <link:definition>2427403 - Disclosure - Long-Term Debt (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LongTermDebtJuniorSubordinatedDebtDetails1" roleURI="http://www.jpmorganchase.com/role/LongTermDebtJuniorSubordinatedDebtDetails1">
        <link:definition>2427404 - Disclosure - Long-term debt - Junior Subordinated Debt (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LongTermDebtLongTermDebtPolicies" roleURI="http://www.jpmorganchase.com/role/LongTermDebtLongTermDebtPolicies">
        <link:definition>2227201 - Disclosure - Long-term debt Long-term debt (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="LongTermDebtTables" roleURI="http://www.jpmorganchase.com/role/LongTermDebtTables">
        <link:definition>2327302 - Disclosure - Long-Term Debt (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="NoninterestExpense" roleURI="http://www.jpmorganchase.com/role/NoninterestExpense">
        <link:definition>2117100 - Disclosure - Noninterest Expense</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="NoninterestExpenseDetails" roleURI="http://www.jpmorganchase.com/role/NoninterestExpenseDetails">
        <link:definition>2417402 - Disclosure - Noninterest Expense (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="NoninterestExpenseTables" roleURI="http://www.jpmorganchase.com/role/NoninterestExpenseTables">
        <link:definition>2317301 - Disclosure - Noninterest Expense (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="NoninterestRevenue" roleURI="http://www.jpmorganchase.com/role/NoninterestRevenue">
        <link:definition>2113100 - Disclosure - Noninterest Revenue</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="NoninterestRevenueDetails" roleURI="http://www.jpmorganchase.com/role/NoninterestRevenueDetails">
        <link:definition>2413403 - Disclosure - Noninterest Revenue (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="NoninterestRevenueNoninterestRevenuePolicies" roleURI="http://www.jpmorganchase.com/role/NoninterestRevenueNoninterestRevenuePolicies">
        <link:definition>2213201 - Disclosure - Noninterest Revenue Noninterest Revenue (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="NoninterestRevenueTables" roleURI="http://www.jpmorganchase.com/role/NoninterestRevenueTables">
        <link:definition>2313302 - Disclosure - Noninterest Revenue (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitments" roleURI="http://www.jpmorganchase.com/role/OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitments">
        <link:definition>2134100 - Disclosure - Off-Balance Sheet Lending-Related Financial Instruments, Guarantees and Other Commitments</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsDetails" roleURI="http://www.jpmorganchase.com/role/OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsDetails">
        <link:definition>2434403 - Disclosure - Off-Balance Sheet Lending-Related Financial Instruments, Guarantees and Other Commitments (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsPolicies" roleURI="http://www.jpmorganchase.com/role/OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsPolicies">
        <link:definition>2234201 - Disclosure - Off-Balance Sheet Lending Related Financial Instruments, Guarantees and Other Commitments (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsTables" roleURI="http://www.jpmorganchase.com/role/OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsTables">
        <link:definition>2334302 - Disclosure - Off-Balance Sheet Lending-Related Financial Instruments, Guarantees and Other Commitments (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OffBalanceSheetLoanSalesAndSecuritizationRelatedIndemnificationsDetails" roleURI="http://www.jpmorganchase.com/role/OffBalanceSheetLoanSalesAndSecuritizationRelatedIndemnificationsDetails">
        <link:definition>2434405 - Disclosure - Off-Balance Sheet - Loan Sales- and Securitization-Related Indemnifications (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OffBalanceSheetOtherOffBalanceSheetArrangementsDetails" roleURI="http://www.jpmorganchase.com/role/OffBalanceSheetOtherOffBalanceSheetArrangementsDetails">
        <link:definition>2434406 - Disclosure - Off-Balance Sheet - Other Off-Balance Sheet Arrangements (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OffBalanceSheetStandbyLettersOfCreditAndOtherFinancialGuaranteesDetails" roleURI="http://www.jpmorganchase.com/role/OffBalanceSheetStandbyLettersOfCreditAndOtherFinancialGuaranteesDetails">
        <link:definition>2434404 - Disclosure - Off-Balance Sheet - Standby Letters of Credit and Other Financial Guarantees (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ParentCompany" roleURI="http://www.jpmorganchase.com/role/ParentCompany">
        <link:definition>2140100 - Disclosure - Parent Company</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ParentCompanyBalanceSheetsDetails1" roleURI="http://www.jpmorganchase.com/role/ParentCompanyBalanceSheetsDetails1">
        <link:definition>2440403 - Disclosure - Parent Company - balance sheets (Details1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ParentCompanyStatementsOfCashFlowsDetails2" roleURI="http://www.jpmorganchase.com/role/ParentCompanyStatementsOfCashFlowsDetails2">
        <link:definition>2440404 - Disclosure - Parent Company - statements of cash flows (Details2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ParentCompanyStatementsOfIncomeDetails" roleURI="http://www.jpmorganchase.com/role/ParentCompanyStatementsOfIncomeDetails">
        <link:definition>2440402 - Disclosure - Parent Company - statements of income (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ParentCompanyTables" roleURI="http://www.jpmorganchase.com/role/ParentCompanyTables">
        <link:definition>2340301 - Disclosure - Parent Company (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlans" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlans">
        <link:definition>2115100 - Disclosure - Pension and Other Postretirement Employee Benefit Plans</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansActualRateOfReturnOnPlanAssetsDetails4" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansActualRateOfReturnOnPlanAssetsDetails4">
        <link:definition>2415407 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Actual Rate of Return on Plan Assets (Details 4)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansChangesInLevel3FairValueMeasurementsDetails10" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansChangesInLevel3FairValueMeasurementsDetails10">
        <link:definition>2415413 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Changes In Level 3 Fair Value Measurements (Details 10)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansDefinedBeniftPensionPlansDetails" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansDefinedBeniftPensionPlansDetails">
        <link:definition>2415403 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Defined Benift Pension Plans (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansEstimatedFutureBenefitPaymentsDetails11" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansEstimatedFutureBenefitPaymentsDetails11">
        <link:definition>2415414 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Estimated Future Benefit Payments (Details 11)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansNetPeriodicBenefitCostsDetails2" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansNetPeriodicBenefitCostsDetails2">
        <link:definition>2415405 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Net Periodic Benefit Costs (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansOnePercentagePointIncreaseEffectsDetails7" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansOnePercentagePointIncreaseEffectsDetails7">
        <link:definition>2415410 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - One Percentage Point Increase Effects (Details 7)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansPlanAssetsMeasuredAtFairValueDetails9" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansPlanAssetsMeasuredAtFairValueDetails9">
        <link:definition>2415412 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Plan Assets Measured At Fair Value (Details 9)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansPolicies" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansPolicies">
        <link:definition>2215201 - Disclosure - Pension and Other Postretirement Employee Benefit Plans (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansPretaxAmortizationFromAociDetails3" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansPretaxAmortizationFromAociDetails3">
        <link:definition>2415406 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Pretax Amortization from AOCI (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansPretaxPensionAndOpebInAociDetails1" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansPretaxPensionAndOpebInAociDetails1">
        <link:definition>2415404 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Pretax Pension and OPEB in AOCI (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansTables" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansTables">
        <link:definition>2315302 - Disclosure - Pension and Other Postretirement Employee Benefit Plans (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansWeightedAverageAssetAllocationDetails8" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansWeightedAverageAssetAllocationDetails8">
        <link:definition>2415411 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Weighted Average Asset Allocation (Details 8)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansWeightedAverageAssumptionsBenefitObligationsDetails5" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansWeightedAverageAssumptionsBenefitObligationsDetails5">
        <link:definition>2415408 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Weighted-Average Assumptions Benefit Obligations (Details 5)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementEmployeeBenefitPlansWeightedAverageAssumptionsNetPeriodicBenefitCostsDetails6" roleURI="http://www.jpmorganchase.com/role/PensionAndOtherPostretirementEmployeeBenefitPlansWeightedAverageAssumptionsNetPeriodicBenefitCostsDetails6">
        <link:definition>2415409 - Disclosure - Pension and Other Postretirement Employee Benefit Plans  - Weighted-Average Assumptions Net Periodic Benefit Costs (Details 6)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PreferredStock" roleURI="http://www.jpmorganchase.com/role/PreferredStock">
        <link:definition>2128100 - Disclosure - Preferred Stock</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PreferredStockDetails" roleURI="http://www.jpmorganchase.com/role/PreferredStockDetails">
        <link:definition>2428402 - Disclosure - Preferred Stock (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PreferredStockTables" roleURI="http://www.jpmorganchase.com/role/PreferredStockTables">
        <link:definition>2328301 - Disclosure - Preferred Stock (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PremisesAndEquipment" roleURI="http://www.jpmorganchase.com/role/PremisesAndEquipment">
        <link:definition>2124100 - Disclosure - Premises and Equipment</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PremisesAndEquipmentPremisesAndEquipmentPolicies" roleURI="http://www.jpmorganchase.com/role/PremisesAndEquipmentPremisesAndEquipmentPolicies">
        <link:definition>2224201 - Disclosure - Premises and Equipment Premises and Equipment (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="RegulatoryCapital" roleURI="http://www.jpmorganchase.com/role/RegulatoryCapital">
        <link:definition>2133100 - Disclosure - Regulatory Capital</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="RegulatoryCapitalDetails" roleURI="http://www.jpmorganchase.com/role/RegulatoryCapitalDetails">
        <link:definition>2433402 - Disclosure - Regulatory Capital (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="RegulatoryCapitalReconcilliationOfEquityToCapitalDetails1" roleURI="http://www.jpmorganchase.com/role/RegulatoryCapitalReconcilliationOfEquityToCapitalDetails1">
        <link:definition>2433403 - Disclosure - Regulatory Capital - Reconcilliation of Equity to Capital (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="RegulatoryCapitalTables" roleURI="http://www.jpmorganchase.com/role/RegulatoryCapitalTables">
        <link:definition>2333301 - Disclosure - Regulatory Capital (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="RestrictionsOnCashAndIntercompanyFundsTransfers" roleURI="http://www.jpmorganchase.com/role/RestrictionsOnCashAndIntercompanyFundsTransfers">
        <link:definition>2131100 - Disclosure - Restrictions on Cash and Intercompany Funds Transfers</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="RestrictionsOnCashAndIntercompanyFundsTransfersDetails" roleURI="http://www.jpmorganchase.com/role/RestrictionsOnCashAndIntercompanyFundsTransfersDetails">
        <link:definition>2431401 - Disclosure - Restrictions on Cash and Intercompany Funds Transfers (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="Securities" roleURI="http://www.jpmorganchase.com/role/Securities">
        <link:definition>2118100 - Disclosure - Securities</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesAmortizedCostFairValueByContractMaturityDetails6" roleURI="http://www.jpmorganchase.com/role/SecuritiesAmortizedCostFairValueByContractMaturityDetails6">
        <link:definition>2418408 - Disclosure - Securities - Amortized Cost, Fair Value, by Contract Maturity (Details 6)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesAmortizedCostsFairValueDetails1" roleURI="http://www.jpmorganchase.com/role/SecuritiesAmortizedCostsFairValueDetails1">
        <link:definition>2418404 - Disclosure - Securities - Amortized Costs, Fair Value (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesChangesInCreditLossDetails4" roleURI="http://www.jpmorganchase.com/role/SecuritiesChangesInCreditLossDetails4">
        <link:definition>2418407 - Disclosure - Securities - Changes in Credit Loss (Details 4)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesFinancingActivities" roleURI="http://www.jpmorganchase.com/role/SecuritiesFinancingActivities">
        <link:definition>2119100 - Disclosure - Securities Financing Activities</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesFinancingActivitiesDetails" roleURI="http://www.jpmorganchase.com/role/SecuritiesFinancingActivitiesDetails">
        <link:definition>2419403 - Disclosure - Securities Financing Activities (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesFinancingActivitiesSecuritiesFinancingActivitiesPolicies" roleURI="http://www.jpmorganchase.com/role/SecuritiesFinancingActivitiesSecuritiesFinancingActivitiesPolicies">
        <link:definition>2219201 - Disclosure - Securities Financing Activities Securities Financing Activities (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesFinancingActivitiesTables" roleURI="http://www.jpmorganchase.com/role/SecuritiesFinancingActivitiesTables">
        <link:definition>2319302 - Disclosure - Securities Financing Activities (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesImpairmentDetails2" roleURI="http://www.jpmorganchase.com/role/SecuritiesImpairmentDetails2">
        <link:definition>2418405 - Disclosure - Securities - Impairment (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesOtherThanTempoaryImpairmentDetails3" roleURI="http://www.jpmorganchase.com/role/SecuritiesOtherThanTempoaryImpairmentDetails3">
        <link:definition>2418406 - Disclosure - Securities - Other Than Tempoary Impairment (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesRealizedGainLossDetails" roleURI="http://www.jpmorganchase.com/role/SecuritiesRealizedGainLossDetails">
        <link:definition>2418403 - Disclosure - Securities - Realized Gain (Loss) (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesSecuritiesPolicies" roleURI="http://www.jpmorganchase.com/role/SecuritiesSecuritiesPolicies">
        <link:definition>2218201 - Disclosure - Securities Securities (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SecuritiesTables" roleURI="http://www.jpmorganchase.com/role/SecuritiesTables">
        <link:definition>2318302 - Disclosure - Securities (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntities" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntities">
        <link:definition>2122100 - Disclosure - Variable Interest Entities</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesConsolidatedVieAssetsAndLiabilitiesDetails3" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesConsolidatedVieAssetsAndLiabilitiesDetails3">
        <link:definition>2422408 - Disclosure - Variable Interest Entities - Consolidated VIE Assets and Liabilities (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesCreditRelatedNoteAssetSwapVehicleViesDetails2B" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesCreditRelatedNoteAssetSwapVehicleViesDetails2B">
        <link:definition>2422406 - Disclosure - Variable Interest Entities - Credit Related Note, Asset Swap Vehicle VIEs (Details 2b)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesFirmSponsoredVariableInterestEntitiesDetails0" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesFirmSponsoredVariableInterestEntitiesDetails0">
        <link:definition>2422403 - Disclosure - Variable Interest Entities - Firm Sponsored Variable Interest Entities (Details 0)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesInterestInSecuritizedAssetsHeldAtFairValueDetails6" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesInterestInSecuritizedAssetsHeldAtFairValueDetails6">
        <link:definition>2422411 - Disclosure - Variable Interest Entities - Interest in Securitized Assets Held at Fair Value (Details 6)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesLoanDelinquenciesAndNetChargeOffsDetails7" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesLoanDelinquenciesAndNetChargeOffsDetails7">
        <link:definition>2422412 - Disclosure - Variable Interest Entities - Loan Delinquencies and Net Charge-offs (Details 7)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesLoansSoldToThirdPartySponsoredSecuritizationEntitiesDetails5" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesLoansSoldToThirdPartySponsoredSecuritizationEntitiesDetails5">
        <link:definition>2422410 - Disclosure - Variable Interest Entities - Loans Sold to Third-Party Sponsored Securitization Entities (Details 5)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesMunicipalBondVehicleViesDetails2A" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesMunicipalBondVehicleViesDetails2A">
        <link:definition>2422405 - Disclosure - Variable Interest Entities - Municipal Bond Vehicle VIEs (Details 2a)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesPolicies" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesPolicies">
        <link:definition>2222201 - Disclosure - Variable Interest Entities (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesResecuritizationsMultiSellerConduitsDetails1" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesResecuritizationsMultiSellerConduitsDetails1">
        <link:definition>2422404 - Disclosure - Variable Interest Entities - Resecuritizations, Multi-seller Conduits (Details 1)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesSecuritizationActivityDetails4" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesSecuritizationActivityDetails4">
        <link:definition>2422409 - Disclosure - Variable Interest Entities - Securitization Activity (Details 4)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesTables" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesTables">
        <link:definition>2322302 - Disclosure - Variable Interest Entities (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="VariableInterestEntitiesThirdPartySponsoredViesDetails2C" roleURI="http://www.jpmorganchase.com/role/VariableInterestEntitiesThirdPartySponsoredViesDetails2C">
        <link:definition>2422407 - Disclosure - Variable Interest Entities - Third-party Sponsored VIEs (Details 2c)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
    </xsd:appinfo>
  </xsd:annotation>
  <xsd:element abstract="true" id="jpm_AccountsPayableAndOtherLiabilitiesLineItems" name="AccountsPayableAndOtherLiabilitiesLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AccountsPayableAndOtherLiabilitiesMember" name="AccountsPayableAndOtherLiabilitiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AccretableYieldPercentage" name="AccretableYieldPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AccruedInterestAndAccountsReceivable" name="AccruedInterestAndAccountsReceivable" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AccruedInterestAndAccountsReceivableAtFairValue" name="AccruedInterestAndAccountsReceivableAtFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_AccruedReceivablesMember" name="AccruedReceivablesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AccumulatedOtherComprehensiveIncomeLossRollForward" name="AccumulatedOtherComprehensiveIncomeLossRollForward" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AccumulatedOtherComprehensiveIncomeLossSupplementalInformationAbstract" name="AccumulatedOtherComprehensiveIncomeLossSupplementalInformationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ActionsTransferredMember" name="ActionsTransferredMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AdditionsAbstract" name="AdditionsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AdjustedAverageAssets" name="AdjustedAverageAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AdjustmentsToAdditionalPaidInCapitalFromShareBasedCompensationAndRelatedTaxEffects" name="AdjustmentsToAdditionalPaidInCapitalFromShareBasedCompensationAndRelatedTaxEffects" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AdjustmentsToCapitalForDeferredTaxLiabilitiesAbstract" name="AdjustmentsToCapitalForDeferredTaxLiabilitiesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AdjustmentsToCapitalForDeferredTaxLiabilitiesResultingFromNontaxableBusinessCombinations" name="AdjustmentsToCapitalForDeferredTaxLiabilitiesResultingFromNontaxableBusinessCombinations" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AdjustmentsToCapitalForDeferredTaxLiabilitiesResultingFromTaxDeductibleGoodwill" name="AdjustmentsToCapitalForDeferredTaxLiabilitiesResultingFromTaxDeductibleGoodwill" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AdvancesToReceivablesFromSubsidiariesBankAndBankHoldingCompanies" name="AdvancesToReceivablesFromSubsidiariesBankAndBankHoldingCompanies" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AdvancesToReceivablesFromSubsidiariesNonBankingCompanies" name="AdvancesToReceivablesFromSubsidiariesNonBankingCompanies" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_Aggregatevolumeprocessedbyelectronicpaymentservicesbusiness" name="Aggregatevolumeprocessedbyelectronicpaymentservicesbusiness" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AllOtherAssetClassesMember" name="AllOtherAssetClassesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AllOtherAssetManagementFees" name="AllOtherAssetManagementFees" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AllOtherStatesMember" name="AllOtherStatesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AllowanceForCreditLossesAbstract" name="AllowanceForCreditLossesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AllowanceForLendingRelatedCommitmentsEvaluatedAtAssetSpecificImpairmentMethodology" name="AllowanceForLendingRelatedCommitmentsEvaluatedAtAssetSpecificImpairmentMethodology" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AllowanceForLendingRelatedCommitmentsEvaluatedAtFormulaBasedImpairmentMethodology" name="AllowanceForLendingRelatedCommitmentsEvaluatedAtFormulaBasedImpairmentMethodology" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_AllowanceForLendingRelatedCommitmentsMember" name="AllowanceForLendingRelatedCommitmentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AlternativeInvestmentsMember" name="AlternativeInvestmentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AmortizationOfIntangibleAssetsAbstract" name="AmortizationOfIntangibleAssetsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AmortizationOfIntangibleAssetsTableTextBlock" name="AmortizationOfIntangibleAssetsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AmortizedCostAndEstimatedFairValueByContractualMaturityAbstract" name="AmortizedCostAndEstimatedFairValueByContractualMaturityAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AmortizedCostsAndEstimatedFairValuesAbstract" name="AmortizedCostsAndEstimatedFairValuesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AmortizedCostsAndEstimatedFairValuesTableTextBlock" name="AmortizedCostsAndEstimatedFairValuesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AmountOfTrustPreferredSecuritiesIssuedByTrust" name="AmountOfTrustPreferredSecuritiesIssuedByTrust" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_ApprovedRepurchaseProgramPeriodStartIn2011Member" name="ApprovedRepurchaseProgramPeriodStartIn2011Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ApprovedRepurchaseProgramPeriodStartIn2012Member" name="ApprovedRepurchaseProgramPeriodStartIn2012Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ArizonaMember" name="ArizonaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AsiaPacificMember" name="AsiaPacificMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AssetBackedSecuritiesTradingAccountMember" name="AssetBackedSecuritiesTradingAccountMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AssetBackedSecurityAvailableForSaleMember" name="AssetBackedSecurityAvailableForSaleMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AssetCategoryAbstract" name="AssetCategoryAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AssetManagementAdministrationAndCommissions" name="AssetManagementAdministrationAndCommissions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AssetManagersCreditRiskConcentrationMember" name="AssetManagersCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AssetSwapViesMember" name="AssetSwapViesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AssetsAndLiabilitiesOfFirmSponsoredCreditCardSecuritizationTrustsAbstract" name="AssetsAndLiabilitiesOfFirmSponsoredCreditCardSecuritizationTrustsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AssetsUnderSupervisionFairValue" name="AssetsUnderSupervisionFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_AuctionRateSecuritiesInvestigationsAndLitigationMember" name="AuctionRateSecuritiesInvestigationsAndLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_AutoAndStudentLoansMember" name="AutoAndStudentLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesDebtMaturitiesAverageYield" name="AvailableForSaleSecuritiesDebtMaturitiesAverageYield" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesDebtMaturitiesAverageYieldAfterFiveThroughTenYears" name="AvailableForSaleSecuritiesDebtMaturitiesAverageYieldAfterFiveThroughTenYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesDebtMaturitiesAverageYieldAfterOneThroughFiveYears" name="AvailableForSaleSecuritiesDebtMaturitiesAverageYieldAfterOneThroughFiveYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesDebtMaturitiesAverageYieldAfterTenYears" name="AvailableForSaleSecuritiesDebtMaturitiesAverageYieldAfterTenYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesDebtMaturitiesAverageYieldWithinOneYear" name="AvailableForSaleSecuritiesDebtMaturitiesAverageYieldWithinOneYear" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAfterFiveThroughTenYearsAmortizedCost" name="AvailableForSaleSecuritiesEquityMaturitiesAfterFiveThroughTenYearsAmortizedCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAfterFiveThroughTenYearsFairValue" name="AvailableForSaleSecuritiesEquityMaturitiesAfterFiveThroughTenYearsFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAfterOneThroughFiveYearsAmortizedCost" name="AvailableForSaleSecuritiesEquityMaturitiesAfterOneThroughFiveYearsAmortizedCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAfterOneThroughFiveYearsFairValue" name="AvailableForSaleSecuritiesEquityMaturitiesAfterOneThroughFiveYearsFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAfterTenYearsAmortizedCost" name="AvailableForSaleSecuritiesEquityMaturitiesAfterTenYearsAmortizedCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAfterTenYearsFairValue" name="AvailableForSaleSecuritiesEquityMaturitiesAfterTenYearsFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAmortizedCost" name="AvailableForSaleSecuritiesEquityMaturitiesAmortizedCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAverageYield" name="AvailableForSaleSecuritiesEquityMaturitiesAverageYield" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAverageYieldAfterFiveThroughTenYears" name="AvailableForSaleSecuritiesEquityMaturitiesAverageYieldAfterFiveThroughTenYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAverageYieldAfterOneThroughFiveYears" name="AvailableForSaleSecuritiesEquityMaturitiesAverageYieldAfterOneThroughFiveYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAverageYieldAfterTenYears" name="AvailableForSaleSecuritiesEquityMaturitiesAverageYieldAfterTenYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesAverageYieldWithinOneYear" name="AvailableForSaleSecuritiesEquityMaturitiesAverageYieldWithinOneYear" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesFairValue" name="AvailableForSaleSecuritiesEquityMaturitiesFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesWithinOneYearAmortizedCost" name="AvailableForSaleSecuritiesEquityMaturitiesWithinOneYearAmortizedCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesEquityMaturitiesWithinOneYearFairValue" name="AvailableForSaleSecuritiesEquityMaturitiesWithinOneYearFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesGainsLossesTableTextBlock" name="AvailableForSaleSecuritiesGainsLossesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAfterFiveThroughTenYearsAmortizedCost" name="AvailableForSaleSecuritiesMaturitiesAfterFiveThroughTenYearsAmortizedCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAfterFiveThroughTenYearsFairValue" name="AvailableForSaleSecuritiesMaturitiesAfterFiveThroughTenYearsFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAfterOneThroughFiveYearsAmortizedCost" name="AvailableForSaleSecuritiesMaturitiesAfterOneThroughFiveYearsAmortizedCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAfterOneThroughFiveYearsFairValue" name="AvailableForSaleSecuritiesMaturitiesAfterOneThroughFiveYearsFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAfterTenYearsAmortizedCost" name="AvailableForSaleSecuritiesMaturitiesAfterTenYearsAmortizedCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAfterTenYearsFairValue" name="AvailableForSaleSecuritiesMaturitiesAfterTenYearsFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAverageYield" name="AvailableForSaleSecuritiesMaturitiesAverageYield" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAverageYieldAfterFiveThroughTenYears" name="AvailableForSaleSecuritiesMaturitiesAverageYieldAfterFiveThroughTenYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAverageYieldAfterOneThroughFiveYears" name="AvailableForSaleSecuritiesMaturitiesAverageYieldAfterOneThroughFiveYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAverageYieldAfterTenYears" name="AvailableForSaleSecuritiesMaturitiesAverageYieldAfterTenYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesAverageYieldWithinOneYear" name="AvailableForSaleSecuritiesMaturitiesAverageYieldWithinOneYear" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesWithinOneYearAmortizedCost" name="AvailableForSaleSecuritiesMaturitiesWithinOneYearAmortizedCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AvailableForSaleSecuritiesMaturitiesWithinOneYearFairValue" name="AvailableForSaleSecuritiesMaturitiesWithinOneYearFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AverageBalancesOfUSTriPartyRepurchases" name="AverageBalancesOfUSTriPartyRepurchases" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_AverageFutureServicePeriod" name="AverageFutureServicePeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AverageRemainingAmortizationPeriod" name="AverageRemainingAmortizationPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_AverageUndividedInterestInPrincipalReceivablesPercentage" name="AverageUndividedInterestInPrincipalReceivablesPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BankAndBankHoldingCompanySubsidiariesMember" name="BankAndBankHoldingCompanySubsidiariesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BankOneCapitalIiiMember" name="BankOneCapitalIiiMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BankOneCapitalViMember" name="BankOneCapitalViMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BanksAndFinanceCompaniesCreditRiskConcentrationMember" name="BanksAndFinanceCompaniesCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BasisOfPresentationAbstract" name="BasisOfPresentationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_BasisOfPresentationPolicyPolicyTextBlock" name="BasisOfPresentationPolicyPolicyTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BearStearnsHedgeFundMattersFeederFundsEnhancedLeverageFundMember" name="BearStearnsHedgeFundMattersFeederFundsEnhancedLeverageFundMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BearStearnsHedgeFundMattersNotSpecificallyRelatedToFeederFundsMember" name="BearStearnsHedgeFundMattersNotSpecificallyRelatedToFeederFundsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BearStearnsHedgeFundMattersRelatedToFeederFundsMember" name="BearStearnsHedgeFundMattersRelatedToFeederFundsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BearStearnsShareholderLitigationAndRelatedMattersMember" name="BearStearnsShareholderLitigationAndRelatedMattersMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BeforeAndAfterTaxChangesOfComponentsOfAccumulatedOtherComprehensiveIncomeLossTablesAbstract" name="BeforeAndAfterTaxChangesOfComponentsOfAccumulatedOtherComprehensiveIncomeLossTablesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_BeforeAndAfterTaxChangesOfComponentsOfAccumulatedOtherComprehensiveIncomeLossTablesTableTextBlock" name="BeforeAndAfterTaxChangesOfComponentsOfAccumulatedOtherComprehensiveIncomeLossTablesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_BeneficialInterestFairValueDisclosure" name="BeneficialInterestFairValueDisclosure" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_BeneficialInterestsIssuedByConsolidatedVariableInterestEntitiesMember" name="BeneficialInterestsIssuedByConsolidatedVariableInterestEntitiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BreachOfFiduciaryDutyLitigationMember" name="BreachOfFiduciaryDutyLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BrokerPriceOpinionValuationTechniqueMember" name="BrokerPriceOpinionValuationTechniqueMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_BrokeragePayables" name="BrokeragePayables" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_BusinessAcquisitionDebtAssumed" name="BusinessAcquisitionDebtAssumed" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_BusinessAcquisitionNetAssetsAcquired" name="BusinessAcquisitionNetAssetsAcquired" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_BusinessAndSegmentDescriptionAbstract" name="BusinessAndSegmentDescriptionAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_BusinessChangesAndDevelopmentsAbstract" name="BusinessChangesAndDevelopmentsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_BusinessCombinationDisclosureAndOtherBusinessEventsDisclosuresAndSubsequentEventsTextBlock" name="BusinessCombinationDisclosureAndOtherBusinessEventsDisclosuresAndSubsequentEventsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_BusinessSegmentsNumberOfSegments" name="BusinessSegmentsNumberOfSegments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_CaliforniaMember" name="CaliforniaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CapitalPurchaseProgramAbstract" name="CapitalPurchaseProgramAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CarryingValueAndEstimatedFairValueOfFinancialAssetsAndLiabilitiesAbstract" name="CarryingValueAndEstimatedFairValueOfFinancialAssetsAndLiabilitiesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CarryingValueAndEstimatedFairValueOfWholesaleLendingRelatedCommitmentsTextBlock" name="CarryingValueAndEstimatedFairValueOfWholesaleLendingRelatedCommitmentsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CarryingValueOfResaleAgreementsAndRepurchaseAgreementsThatHaveBeenNetted" name="CarryingValueOfResaleAgreementsAndRepurchaseAgreementsThatHaveBeenNetted" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_CashCollateralNotSubjectToMasterNettingAgreementAndLiquidSecurityCollateralPledgedInDerivativeTransactions" name="CashCollateralNotSubjectToMasterNettingAgreementAndLiquidSecurityCollateralPledgedInDerivativeTransactions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_CashCollateralNotSubjectToMasterNettingAgreementAndLiquidSecurityCollateralReceivedInDerivativeTransactions" name="CashCollateralNotSubjectToMasterNettingAgreementAndLiquidSecurityCollateralReceivedInDerivativeTransactions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_CashFlowHedgeGainsAndLossesAbstract" name="CashFlowHedgeGainsAndLossesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CashPaymentMember" name="CashPaymentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_Cashcollateralheld" name="Cashcollateralheld" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_CentralGovernmentCreditRiskConcentrationMember" name="CentralGovernmentCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CertainLoansAcquiredInTransferNotAccountedForAsDebtSecuritiesAccretableYieldChangesInInterestRatesOnVariableRateLoans" name="CertainLoansAcquiredInTransferNotAccountedForAsDebtSecuritiesAccretableYieldChangesInInterestRatesOnVariableRateLoans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CertainLoansAcquiredInTransferNotAccountedForAsDebtSecuritiesAccretableYieldMovementAbstract" name="CertainLoansAcquiredInTransferNotAccountedForAsDebtSecuritiesAccretableYieldMovementAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CertainLoansAcquiredInTransferNotAccountedForAsDebtSecuritiesAccretableYieldMovementLineItems" name="CertainLoansAcquiredInTransferNotAccountedForAsDebtSecuritiesAccretableYieldMovementLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CertainLoansAcquiredInTransferNotAccountedForAsDebtSecuritiesAccretableYieldMovementRollForwardTableTextBlock" name="CertainLoansAcquiredInTransferNotAccountedForAsDebtSecuritiesAccretableYieldMovementRollForwardTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CertificatesOfDepositBankersAcceptancesCommercialPaperMember" name="CertificatesOfDepositBankersAcceptancesCommercialPaperMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CertifiedClassActionMember" name="CertifiedClassActionMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ChangesInCreditLossComponentOfCreditImpairedDebtSecuritiesSecuritiesNotHeldForSaleAbstractRollForward" name="ChangesInCreditLossComponentOfCreditImpairedDebtSecuritiesSecuritiesNotHeldForSaleAbstractRollForward" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ChangesInCreditLossComponentOfCreditimpairedDebtSecuritiesTableTextBlock" name="ChangesInCreditLossComponentOfCreditimpairedDebtSecuritiesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ChangesInFairValueBasedOnVariationInAssumptionsLimitFirst" name="ChangesInFairValueBasedOnVariationInAssumptionsLimitFirst" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ChangesInGoodwillDuringPeriodAbstract" name="ChangesInGoodwillDuringPeriodAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ChaseBankUsaNationalAssociationMember" name="ChaseBankUsaNationalAssociationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ChaseCapitalIiMember" name="ChaseCapitalIiMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ChaseCapitalIiiMember" name="ChaseCapitalIiiMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ChaseCapitalViMember" name="ChaseCapitalViMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ChasePaymentechSolutionsMember" name="ChasePaymentechSolutionsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ChiefInvestmentOfficeMember" name="ChiefInvestmentOfficeMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CioInvestigationsAndLitigationsErisaActionMember" name="CioInvestigationsAndLitigationsErisaActionMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CioInvestigationsAndLitigationsSecuritiesActOf1934ActionsMember" name="CioInvestigationsAndLitigationsSecuritiesActOf1934ActionsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CioInvestigationsAndLitigationsShareholderDerivativeActionsBasedUponAllegedBreachOfFiduciaryDutyMember" name="CioInvestigationsAndLitigationsShareholderDerivativeActionsBasedUponAllegedBreachOfFiduciaryDutyMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CityOfMilanLitigationAndCriminalInvestigationMember" name="CityOfMilanLitigationAndCriminalInvestigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ClassActionMember" name="ClassActionMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ClientRevenue" name="ClientRevenue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_CollateralHeldForDerivativeTransations" name="CollateralHeldForDerivativeTransations" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_CollateralPledgedForDerivativeTransactions" name="CollateralPledgedForDerivativeTransactions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_CommercialAndIndustrialMember" name="CommercialAndIndustrialMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CommercialBankingMember" name="CommercialBankingMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CommercialMortgageBackedAndOtherSecuritiesMember" name="CommercialMortgageBackedAndOtherSecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CommercialMortgageBackedSecuritesAndLoansMember" name="CommercialMortgageBackedSecuritesAndLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CommercialRealEstateFairValue" name="CommercialRealEstateFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_CommercialRealEstateFairValuePeriodIncreaseDecrease" name="CommercialRealEstateFairValuePeriodIncreaseDecrease" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_Commercialpaperissuedbyconsolidatedvariableinterestentitieseliminatedinconsolidation" name="Commercialpaperissuedbyconsolidatedvariableinterestentitieseliminatedinconsolidation" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_CommitmentsToExtendCreditLeveragedAndAcquisitionFinanceActivities" name="CommitmentsToExtendCreditLeveragedAndAcquisitionFinanceActivities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_CommonEquityRepurchaseProgramAbstract" name="CommonEquityRepurchaseProgramAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CommonSharesIssuedFromTreasuryAbstract" name="CommonSharesIssuedFromTreasuryAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CommonStockHeldInRestrictedStockUnitTrustShares" name="CommonStockHeldInRestrictedStockUnitTrustShares" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_CommonStockHeldInTrustMember" name="CommonStockHeldInTrustMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CommonStockQuarterlyDividendRatePerShare" name="CommonStockQuarterlyDividendRatePerShare" nillable="true" substitutionGroup="xbrli:item" type="num:perShareItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CompletionOfShortTermModificationMember" name="CompletionOfShortTermModificationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ComponentsOfAccountsPayableAndOtherLiabilitiesNumericAbstract" name="ComponentsOfAccountsPayableAndOtherLiabilitiesNumericAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ComponentsOfAssetManagementAdministrationAndCommissionsTextBlock" name="ComponentsOfAssetManagementAdministrationAndCommissionsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ComponentsOfDeferredTaxAssetsAndLiabilitiesSupplementalInformationAbstract" name="ComponentsOfDeferredTaxAssetsAndLiabilitiesSupplementalInformationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ComponentsOfIncomeTaxExpenseBenefitLineItems" name="ComponentsOfIncomeTaxExpenseBenefitLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ComponentsOfIncomeTaxExpenseBenefitsSupplementalInformationAbstract" name="ComponentsOfIncomeTaxExpenseBenefitsSupplementalInformationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ComponentsOfInvestmentBankingFeesTextBlock" name="ComponentsOfInvestmentBankingFeesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ComponentsOfMortgageFeesAndRelatedIncomeAbstract" name="ComponentsOfMortgageFeesAndRelatedIncomeAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ComponentsOfNoninterestExpenseAbstract" name="ComponentsOfNoninterestExpenseAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ComponentsOfNoninterestExpenseTextBlock" name="ComponentsOfNoninterestExpenseTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ComprehensiveIncomeLossLineItems" name="ComprehensiveIncomeLossLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ComprehensiveIncomeLossTable" name="ComprehensiveIncomeLossTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ConcentrationRiskCreditRiskFinancialInstrumentsCreditExposure" name="ConcentrationRiskCreditRiskFinancialInstrumentsCreditExposure" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_ConsolidatedSubsidiariesAndUnconsolidatedAffiliatesMember" name="ConsolidatedSubsidiariesAndUnconsolidatedAffiliatesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ConsumerBusinessBankingLoansMember" name="ConsumerBusinessBankingLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ConsumerCommunityBankingMember" name="ConsumerCommunityBankingMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ConsumerExcludingCreditCardLoanPortfolioSegmentMember" name="ConsumerExcludingCreditCardLoanPortfolioSegmentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ConsumerLoanExcludingCreditCardMember" name="ConsumerLoanExcludingCreditCardMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ConsumerLoanSecuritizationEntitiesMember" name="ConsumerLoanSecuritizationEntitiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ConsumerProductsCreditRiskConcentrationMember" name="ConsumerProductsCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ContractuallyRequiredPaymentsMember" name="ContractuallyRequiredPaymentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CorporateDebtSecuritiesObligationsOfUSStatesAndMunicipalitiesAndOtherMember" name="CorporateDebtSecuritiesObligationsOfUSStatesAndMunicipalitiesAndOtherMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CorporateInvestmentBankMember" name="CorporateInvestmentBankMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CorporatePrivateEquityMember" name="CorporatePrivateEquityMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CostsOfPrivateEquityInvestmentPortfolio" name="CostsOfPrivateEquityInvestmentPortfolio" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_CreditAdjustmentsAbstract" name="CreditAdjustmentsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditAdjustmentsDomainDomain" name="CreditAdjustmentsDomainDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditAndLiquidityRiskCreditRelatedContingencyFeaturesAbstract" name="CreditAndLiquidityRiskCreditRelatedContingencyFeaturesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditCardAndScoredBusinessBankingLoansMember" name="CreditCardAndScoredBusinessBankingLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditCardLoanModificationProgramsTotalMember" name="CreditCardLoanModificationProgramsTotalMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditCardLoanPortfolioSegmentMember" name="CreditCardLoanPortfolioSegmentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditCardModificationProgramTypeAxis" name="CreditCardModificationProgramTypeAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditCardModificationProgramsDomain" name="CreditCardModificationProgramsDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CreditCardRevenueSharingAgreementsGeneralPeriodLengthInYears" name="CreditCardRevenueSharingAgreementsGeneralPeriodLengthInYears" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CreditDerivativeOtherProtectionPurchased" name="CreditDerivativeOtherProtectionPurchased" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_CreditDerivativesByPortfolioAxis" name="CreditDerivativesByPortfolioAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditDerivativesPortfolioDomain" name="CreditDerivativesPortfolioDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CreditDerivativesPurchasedCreditProtection" name="CreditDerivativesPurchasedCreditProtection" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_CreditDerivativesWithUnderlyingMortgageRiskMember" name="CreditDerivativesWithUnderlyingMortgageRiskMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CreditEnhancementsAndBondAndCommercialPaperLiquidityCommitmentsToUsStatesAndMunicipalitiesHospitalsAndOtherNotForProfitEntities" name="CreditEnhancementsAndBondAndCommercialPaperLiquidityCommitmentsToUsStatesAndMunicipalitiesHospitalsAndOtherNotForProfitEntities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_CreditLinkedNotesMember" name="CreditLinkedNotesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditLossesInSecuritiesGainsAndLossesAbstract" name="CreditLossesInSecuritiesGainsAndLossesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CreditLossesInSecuritiesGainsAndLossesTableTextBlock" name="CreditLossesInSecuritiesGainsAndLossesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CreditRelatedAdjustmentsOnDerivativeAssetsAndLiabiltiesAndDebtInstrumentsTextBlock" name="CreditRelatedAdjustmentsOnDerivativeAssetsAndLiabiltiesAndDebtInstrumentsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditRelatedMember" name="CreditRelatedMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CreditRiskAdjustmentsOnDerivativeAssetsAndLiabiltiesAndDebtInstrumentsGainsLossesTextBlock" name="CreditRiskAdjustmentsOnDerivativeAssetsAndLiabiltiesAndDebtInstrumentsGainsLossesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditRiskConcentrationsAbstract" name="CreditRiskConcentrationsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_CreditRiskConcentrationsTableTextBlock" name="CreditRiskConcentrationsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CreditValuationAdjustmentMember" name="CreditValuationAdjustmentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CriticizedMember" name="CriticizedMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CriticizedNonaccrualMember" name="CriticizedNonaccrualMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CriticizedPerformingMember" name="CriticizedPerformingMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CumulativePreferredStockSeriesEMember" name="CumulativePreferredStockSeriesEMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CumulativePreferredStockSeriesFMember" name="CumulativePreferredStockSeriesFMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CumulativePreferredStockSeriesGMember" name="CumulativePreferredStockSeriesGMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CurrentAndLessThan30DaysPastDueAndStillAccruingMember" name="CurrentAndLessThan30DaysPastDueAndStillAccruingMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CurrentAndLessThan90DaysPastDueMember" name="CurrentAndLessThan90DaysPastDueMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CurrentCreditRiskOfDerivativeReceivablesAbstract" name="CurrentCreditRiskOfDerivativeReceivablesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CurrentEstimatedLtvRatioBetween101And125PercentMember" name="CurrentEstimatedLtvRatioBetween101And125PercentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CurrentEstimatedLtvRatioBetween80And100PercentMember" name="CurrentEstimatedLtvRatioBetween80And100PercentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CurrentEstimatedLtvRatioGreaterThan125PercentMember" name="CurrentEstimatedLtvRatioGreaterThan125PercentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_CurrentEstimatedLtvRatioLessThan80PercentMember" name="CurrentEstimatedLtvRatioLessThan80PercentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue120OrMoreMember" name="DaysPastDue120OrMoreMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue150OrMoreMember" name="DaysPastDue150OrMoreMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue180OrMoreMember" name="DaysPastDue180OrMoreMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue30OrMoreAndStillAccruingMember" name="DaysPastDue30OrMoreAndStillAccruingMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue30OrMoreMember" name="DaysPastDue30OrMoreMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue30To119Member" name="DaysPastDue30To119Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue30To149Member" name="DaysPastDue30To149Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue30To89AndStillAccruingMember" name="DaysPastDue30To89AndStillAccruingMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue30To89Member" name="DaysPastDue30To89Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue60OrLessMember" name="DaysPastDue60OrLessMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue60OrMoreMember" name="DaysPastDue60OrMoreMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue60OrMoreOrSoonerWithDeterminationOfCollateralDependenceMember" name="DaysPastDue60OrMoreOrSoonerWithDeterminationOfCollateralDependenceMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue60OrMoreWithNotificationOfBankruptcyFilingOrOtherEventMember" name="DaysPastDue60OrMoreWithNotificationOfBankruptcyFilingOrOtherEventMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue90OrMoreAndStillAccruingMember" name="DaysPastDue90OrMoreAndStillAccruingMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue90OrMoreMember" name="DaysPastDue90OrMoreMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysPastDue90To149Member" name="DaysPastDue90To149Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DaysUntilChargeOffLessThan60WithNotificationOfBankruptcyFilingOrOtherEventMember" name="DaysUntilChargeOffLessThan60WithNotificationOfBankruptcyFilingOrOtherEventMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DebitValuationAdjustmentMember" name="DebitValuationAdjustmentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DebtAndEquityInstrumentsMember" name="DebtAndEquityInstrumentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DebtAndEquitySecuritiesMember" name="DebtAndEquitySecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtInstrumentInterestRatePercentageModifiedForEffectsOfHedgeAccountingExcludingDebtAccountedForAtFairValueMaximum" name="DebtInstrumentInterestRatePercentageModifiedForEffectsOfHedgeAccountingExcludingDebtAccountedForAtFairValueMaximum" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtInstrumentInterestRatePercentageModifiedForEffectsOfHedgeAccountingExcludingDebtAccountedForAtFairValueMinimum" name="DebtInstrumentInterestRatePercentageModifiedForEffectsOfHedgeAccountingExcludingDebtAccountedForAtFairValueMinimum" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DebtInstrumentInterestRateTypeDomain" name="DebtInstrumentInterestRateTypeDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtInstrumentMaturitiesAfterYearFiveInterestRateStatedPercentageRateRangeMaximum" name="DebtInstrumentMaturitiesAfterYearFiveInterestRateStatedPercentageRateRangeMaximum" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtInstrumentMaturitiesAfterYearFiveInterestRateStatedPercentageRateRangeMinimum" name="DebtInstrumentMaturitiesAfterYearFiveInterestRateStatedPercentageRateRangeMinimum" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtInstrumentMaturitiesInNextTwelveMonthsInterestRateStatedPercentageRateRangeMaximum" name="DebtInstrumentMaturitiesInNextTwelveMonthsInterestRateStatedPercentageRateRangeMaximum" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtInstrumentMaturitiesInNextTwelveMonthsInterestRateStatedPercentageRateRangeMinimum" name="DebtInstrumentMaturitiesInNextTwelveMonthsInterestRateStatedPercentageRateRangeMinimum" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtInstrumentMaturitiesInYearsOneThroughFiveInterestRateStatedPercentageRateRangeMaximum" name="DebtInstrumentMaturitiesInYearsOneThroughFiveInterestRateStatedPercentageRateRangeMaximum" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtInstrumentMaturitiesInYearsOneThroughFiveInterestRateStatedPercentageRateRangeMinimum" name="DebtInstrumentMaturitiesInYearsOneThroughFiveInterestRateStatedPercentageRateRangeMinimum" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtInstrumentRedemptionDate" name="DebtInstrumentRedemptionDate" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtInstrumentWeightedAverageInterestRateModifiedForEffectsOfHedgeAccounting" name="DebtInstrumentWeightedAverageInterestRateModifiedForEffectsOfHedgeAccounting" nillable="true" substitutionGroup="xbrli:item" type="xbrli:pureItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_DebtInstrumentWeightedAverageInterestRateStatedPercentageExcludingStructuredNotes" name="DebtInstrumentWeightedAverageInterestRateStatedPercentageExcludingStructuredNotes" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_DebtInstrumentsMaturityDate" name="DebtInstrumentsMaturityDate" nillable="true" substitutionGroup="xbrli:item" type="us-types:dateStringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DebtInstrumentsMember" name="DebtInstrumentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DebtSecuritiesNotIntendToSellThatHaveCreditLossesAbstract" name="DebtSecuritiesNotIntendToSellThatHaveCreditLossesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DebtUnderwriting" name="DebtUnderwriting" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DeferredTaxAssetsAndLiabilitiesLineItems" name="DeferredTaxAssetsAndLiabilitiesLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DeferredTaxAssetsForeignOperations" name="DeferredTaxAssetsForeignOperations" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DeferredTaxAssetsTaxAttributeCarryForwards" name="DeferredTaxAssetsTaxAttributeCarryForwards" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_DefinedBenefitPensionPlansExcessRetirementPlanMember" name="DefinedBenefitPensionPlansExcessRetirementPlanMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanAccumulatedOtherComprehensiveIncomeBeforeTaxTableTextBlock" name="DefinedBenefitPlanAccumulatedOtherComprehensiveIncomeBeforeTaxTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanActualPlanAssetAllocationsTableTextBlock" name="DefinedBenefitPlanActualPlanAssetAllocationsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanActualReturnOnPlanAssetsPercentage" name="DefinedBenefitPlanActualReturnOnPlanAssetsPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DefinedBenefitPlanAmortizationAbstract" name="DefinedBenefitPlanAmortizationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanAmountOfPlanInvestmentsHeldInFundsThatAreSponsoredOrManagedByAffiliatesOfEntity" name="DefinedBenefitPlanAmountOfPlanInvestmentsHeldInFundsThatAreSponsoredOrManagedByAffiliatesOfEntity" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DefinedBenefitPlanAmountsNotMeasuredAtFairValue" name="DefinedBenefitPlanAmountsNotMeasuredAtFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DefinedBenefitPlanBenefitObligationExcessRetirementPlan" name="DefinedBenefitPlanBenefitObligationExcessRetirementPlan" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DefinedBenefitPlanBenefitsPaidBenefitObligation" name="DefinedBenefitPlanBenefitsPaidBenefitObligation" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanChangeInBenefitObligationAndFairValueOfPlanAssetsTextBlock" name="DefinedBenefitPlanChangeInBenefitObligationAndFairValueOfPlanAssetsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DefinedBenefitPlanChangesInBenefitObligationsAndPlanAssetsAndFundedStatusAmountsAbstract" name="DefinedBenefitPlanChangesInBenefitObligationsAndPlanAssetsAndFundedStatusAmountsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanContributionsByPlanParticipantsBenefitObligation" name="DefinedBenefitPlanContributionsByPlanParticipantsBenefitObligation" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanExcludedAmountOfNonUSReceivablesForInvestmentsSoldAndDividendsAndInterestReceivables" name="DefinedBenefitPlanExcludedAmountOfNonUSReceivablesForInvestmentsSoldAndDividendsAndInterestReceivables" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DefinedBenefitPlanExcludedAmountOfOtherLiabilities" name="DefinedBenefitPlanExcludedAmountOfOtherLiabilities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DefinedBenefitPlanExcludedAmountOfPayablesForInvestmentsPurchased" name="DefinedBenefitPlanExcludedAmountOfPayablesForInvestmentsPurchased" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DefinedBenefitPlanExcludedAmountOfUSReceivablesForInvestmentsSoldAndDividendsAndInterestReceivables" name="DefinedBenefitPlanExcludedAmountOfUSReceivablesForInvestmentsSoldAndDividendsAndInterestReceivables" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DefinedBenefitPlanFairValueInvestmentsValuedAtNetAssetValue" name="DefinedBenefitPlanFairValueInvestmentsValuedAtNetAssetValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_DefinedBenefitPlanFairValueOfPlanAssetsAndLiabilitiesAbstract" name="DefinedBenefitPlanFairValueOfPlanAssetsAndLiabilitiesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanFairValueOfPlanAssetsAndLiabilitiesTableTextBlock" name="DefinedBenefitPlanFairValueOfPlanAssetsAndLiabilitiesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DefinedBenefitPlanFairValueOfPlanAssetsSupplementalInformationAbstract" name="DefinedBenefitPlanFairValueOfPlanAssetsSupplementalInformationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanFairValueOfPlanLiabilities" name="DefinedBenefitPlanFairValueOfPlanLiabilities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DefinedBenefitPlanForeignCurrencyExchangeRateAndOtherChangesPlanAssets" name="DefinedBenefitPlanForeignCurrencyExchangeRateAndOtherChangesPlanAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanInterestCreditingRate" name="DefinedBenefitPlanInterestCreditingRate" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanNetPeriodicBenefitCostMaterialPlans" name="DefinedBenefitPlanNetPeriodicBenefitCostMaterialPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanPercentAboveWhichAmortizationOfNetGainsAndLossesIsIncludedInAnnualNetPeriodicBenefitCost" name="DefinedBenefitPlanPercentAboveWhichAmortizationOfNetGainsAndLossesIsIncludedInAnnualNetPeriodicBenefitCost" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanSpecialTerminationBenefitsCost" name="DefinedBenefitPlanSpecialTerminationBenefitsCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanUnfundedCommitmentsToPurchaseLimitedPartnershipInvestmentsForPlan" name="DefinedBenefitPlanUnfundedCommitmentsToPurchaseLimitedPartnershipInvestmentsForPlan" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DefinedBenefitPlanWeightedAverageAssumptionsUsedInCalculatingBenefitObligationTableTextBlock" name="DefinedBenefitPlanWeightedAverageAssumptionsUsedInCalculatingBenefitObligationTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedBenefitPlanWeightedAverageAssumptionsUsedInCalculatingNetPeriodicBenefitCostTableTextBlock" name="DefinedBenefitPlanWeightedAverageAssumptionsUsedInCalculatingNetPeriodicBenefitCostTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DefinedContributionPlanEmployeeAnnualCompensationAmountNotEligibleForEmployersMatchingContributions" name="DefinedContributionPlanEmployeeAnnualCompensationAmountNotEligibleForEmployersMatchingContributions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_DepositsLineItems" name="DepositsLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DepositsTable" name="DepositsTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DepositsWithBankingSubsidiaries" name="DepositsWithBankingSubsidiaries" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DepositsWithFederalReserveBanks" name="DepositsWithFederalReserveBanks" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DerivativeAssetFairValueAmountOffsetAgainstOtherDerivatives" name="DerivativeAssetFairValueAmountOffsetAgainstOtherDerivatives" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DerivativeGainLossOnDerivativeNetRealEstateMortgagesRelatedAndOtherAdjustments" name="DerivativeGainLossOnDerivativeNetRealEstateMortgagesRelatedAndOtherAdjustments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_DerivativeLiabilityFairValueAmountOffsetAgainstOtherDerivatives" name="DerivativeLiabilityFairValueAmountOffsetAgainstOtherDerivatives" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_DerivativePayablesMember" name="DerivativePayablesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DerivativeQualifyingAsGuaranteesPayables" name="DerivativeQualifyingAsGuaranteesPayables" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_DerivativeQualifyingAsGuaranteesReceivables" name="DerivativeQualifyingAsGuaranteesReceivables" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_DerivativeReceivablesNetOfPayablesMember" name="DerivativeReceivablesNetOfPayablesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DerivativesExcludedFromTradingAssetsAndTradingLiabilitiesAbstract" name="DerivativesExcludedFromTradingAssetsAndTradingLiabilitiesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DerivativesMaximumExposureToLoss" name="DerivativesMaximumExposureToLoss" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_DerivativesQualifyingAsGuaranteesAbstract" name="DerivativesQualifyingAsGuaranteesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DerivativesQualifyingAsGuaranteesMember" name="DerivativesQualifyingAsGuaranteesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DetailsOfInterestIncomeAndInterestExpenseAbstract" name="DetailsOfInterestIncomeAndInterestExpenseAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DetailsOfInterestIncomeAndInterestExpenseTableTextBlock" name="DetailsOfInterestIncomeAndInterestExpenseTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DifferenceBetweenAggregateFairValueAndAggregateRemainingContractualPrincipalBalanceOutstandingTableTextBlock" name="DifferenceBetweenAggregateFairValueAndAggregateRemainingContractualPrincipalBalanceOutstandingTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DisclosureOfCurrentCreditAndLiquidityRiskOfDerivativesTableTextBlock" name="DisclosureOfCurrentCreditAndLiquidityRiskOfDerivativesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DisclosureOfFairValueHedgeGainsAndLossesTableTextBlock" name="DisclosureOfFairValueHedgeGainsAndLossesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DiscountRateAbstract" name="DiscountRateAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DiscountRateAndExpectedCreditLossAssumptionsUsedToDetermineFvOfInterestsContinuedToBeHeldByTransferor" name="DiscountRateAndExpectedCreditLossAssumptionsUsedToDetermineFvOfInterestsContinuedToBeHeldByTransferor" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DiscountedCashFlowsValuationTechniqueMember" name="DiscountedCashFlowsValuationTechniqueMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DividendBasisSpreadOnVariableRate" name="DividendBasisSpreadOnVariableRate" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DividendDescriptionOfVariableRateBasis" name="DividendDescriptionOfVariableRateBasis" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DividendsFromGuaranteedCapitalDebtSecurities" name="DividendsFromGuaranteedCapitalDebtSecurities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_DocumentAndEntityInformationAbstract" name="DocumentAndEntityInformationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_DuePeriodOfMortgageBackedSecuritiesAndCollateralizedMortgageObligations" name="DuePeriodOfMortgageBackedSecuritiesAndCollateralizedMortgageObligations" nillable="true" substitutionGroup="xbrli:item" type="us-types:durationStringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EarningsPerShareBasicAndDilutedLineItems" name="EarningsPerShareBasicAndDilutedLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_EffectOfCertainItemsInAccumulatedOtherComprehensiveIncomeLossExcludedFromTierOneRiskBasedCapital" name="EffectOfCertainItemsInAccumulatedOtherComprehensiveIncomeLossExcludedFromTierOneRiskBasedCapital" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_EffectiveIncomeTaxRateReconciliationLineItems" name="EffectiveIncomeTaxRateReconciliationLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EmcMortgageLlcMember" name="EmcMortgageLlcMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EmeaMember" name="EmeaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EmployeeStockOptionsAndSarsMember" name="EmployeeStockOptionsAndSarsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EnronLitigationMember" name="EnronLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_EntityWideDisclosureOnGeographicAreasTotalAssetsInCountryOrGroupOfCountriesAmount" name="EntityWideDisclosureOnGeographicAreasTotalAssetsInCountryOrGroupOfCountriesAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_EquitySecuritiesIndustryBanksAndFinanceCompaniesMember" name="EquitySecuritiesIndustryBanksAndFinanceCompaniesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EquitySecuritiesIndustryBusinessServicesMember" name="EquitySecuritiesIndustryBusinessServicesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EquitySecuritiesIndustryCapitalEquipmentMember" name="EquitySecuritiesIndustryCapitalEquipmentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EquitySecuritiesIndustryConsumerGoodsMember" name="EquitySecuritiesIndustryConsumerGoodsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EquitySecuritiesIndustryEnergyMember" name="EquitySecuritiesIndustryEnergyMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EquitySecuritiesIndustryMaterialsMember" name="EquitySecuritiesIndustryMaterialsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EquitySecuritiesIndustryOtherMember" name="EquitySecuritiesIndustryOtherMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EquitySecuritiesIndustryRealEstateMember" name="EquitySecuritiesIndustryRealEstateMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_EquityUnderwriting" name="EquityUnderwriting" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EstimateOfFairValueLevel1InputsMember" name="EstimateOfFairValueLevel1InputsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EstimateOfFairValueLevel2InputsMember" name="EstimateOfFairValueLevel2InputsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_EstimateOfFairValueLevel3InputsMember" name="EstimateOfFairValueLevel3InputsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_EstimatedWeightedAverageLifeOfPortfolioOverWhichNetSpreadWillBeEarnedOnDecliningLoanBalance" name="EstimatedWeightedAverageLifeOfPortfolioOverWhichNetSpreadWillBeEarnedOnDecliningLoanBalance" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_ExcessDeficitOfAssetsOverAvailableLiquidity" name="ExcessDeficitOfAssetsOverAvailableLiquidity" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_ExpectedLongTermRateOfReturnOnPlanAssetsAbstract" name="ExpectedLongTermRateOfReturnOnPlanAssetsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_Expense" name="Expense" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ExpiresAfterFiveYearsMember" name="ExpiresAfterFiveYearsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ExpiresAfterOneYearThroughThreeYearsMember" name="ExpiresAfterOneYearThroughThreeYearsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ExpiresAfterThreeYearsThroughFiveYearsMember" name="ExpiresAfterThreeYearsThroughFiveYearsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ExpiresinoneyearorlessMember" name="ExpiresinoneyearorlessMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ExposureToNonconsolidatedCreditLinkedNoteAndAssetSwapViesAbstract" name="ExposureToNonconsolidatedCreditLinkedNoteAndAssetSwapViesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ExposureToNonconsolidatedCreditLinkedNoteAndAssetSwapViesTextBlock" name="ExposureToNonconsolidatedCreditLinkedNoteAndAssetSwapViesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisDisclosureItemsAxis" name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisDisclosureItemsAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisDisclosureItemsDomain" name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisDisclosureItemsDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisUnobservableInputReconciliationLineItems" name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisUnobservableInputReconciliationLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisUnobservableInputReconciliationTable" name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisUnobservableInputReconciliationTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisUnobservableInputReconciliationTextBlock" name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisUnobservableInputReconciliationTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FairValueAssetsMeasuredOnRecurringBasisNumericAbstract" name="FairValueAssetsMeasuredOnRecurringBasisNumericAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueDebitValuationAdjustmentOnDerivativeAndStructuredNoteLiabilitiesRelatedToFirmsCreditQualityExcludedFromTierOneRiskBasedCapital" name="FairValueDebitValuationAdjustmentOnDerivativeAndStructuredNoteLiabilitiesRelatedToFirmsCreditQualityExcludedFromTierOneRiskBasedCapital" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_FairValueHedgeGainsAndLossesAbstract" name="FairValueHedgeGainsAndLossesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsAssetsAndLiabilitiesQuantitativeInformationTableTextBlock" name="FairValueInputsAssetsAndLiabilitiesQuantitativeInformationTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsCommodityVolatility" name="FairValueInputsCommodityVolatility" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsConditionalDefaultRate" name="FairValueInputsConditionalDefaultRate" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsCreditCorrelation" name="FairValueInputsCreditCorrelation" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsCreditSpread" name="FairValueInputsCreditSpread" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsEbitdaMultiple" name="FairValueInputsEbitdaMultiple" nillable="true" substitutionGroup="xbrli:item" type="xbrli:pureItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsEquityCorrelation" name="FairValueInputsEquityCorrelation" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsEquityVolatility" name="FairValueInputsEquityVolatility" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsForeignExchangeCorrelation" name="FairValueInputsForeignExchangeCorrelation" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsInterestRateCorrelation" name="FairValueInputsInterestRateCorrelation" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsInterestRateSpreadVolatility" name="FairValueInputsInterestRateSpreadVolatility" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsLiquidationValueDiscount" name="FairValueInputsLiquidationValueDiscount" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsLiquidityAdjustment" name="FairValueInputsLiquidityAdjustment" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueInputsPrice" name="FairValueInputsPrice" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueLevelThreeToLevelTwoTransfersAmount" name="FairValueLevelThreeToLevelTwoTransfersAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetGainLoss" name="FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetGainLoss" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueMeasurementWithUnobservableInputsRecurringBasisAssetChangeInUnrealizedGainsLossesRelatedToFinancialInstrumentsStillHeld" name="FairValueMeasurementWithUnobservableInputsRecurringBasisAssetChangeInUnrealizedGainsLossesRelatedToFinancialInstrumentsStillHeld" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueMeasurementWithUnobservableInputsRecurringBasisLiabilitiesChangeInUnrealizedGainsLossesRelatedToFinancialInstrumentsStillHeld" name="FairValueMeasurementWithUnobservableInputsRecurringBasisLiabilitiesChangeInUnrealizedGainsLossesRelatedToFinancialInstrumentsStillHeld" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueMeasurementsValuationTechniquesApproximatePercentageOfInstrumentsMeasured" name="FairValueMeasurementsValuationTechniquesApproximatePercentageOfInstrumentsMeasured" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOfFinancialInstrumentsFairValueOptionPolicyPolicyTextBlock" name="FairValueOfFinancialInstrumentsFairValueOptionPolicyPolicyTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FairValueOptionAbstract" name="FairValueOptionAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionBeneficialInterestsIssuedByConsolidatedVIEsChangesInFairValue" name="FairValueOptionBeneficialInterestsIssuedByConsolidatedVIEsChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionDebtAndEquityTradingSecuritiesChangesInFairValue" name="FairValueOptionDebtAndEquityTradingSecuritiesChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionDepositsChangesInFairValue" name="FairValueOptionDepositsChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionFederalFundsPurchasedAndSecuritiesSoldUnderAgreementsToRepurchaseChangesInFairValue" name="FairValueOptionFederalFundsPurchasedAndSecuritiesSoldUnderAgreementsToRepurchaseChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionFederalFundsSoldAndSecuritiesPurchasedUnderResaleAgreementsChangesInFairValue" name="FairValueOptionFederalFundsSoldAndSecuritiesPurchasedUnderResaleAgreementsChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionLoansOtherChangesInFairValue" name="FairValueOptionLoansOtherChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionLoansReportedAsTradingAssetsChangesInFairValueFromInstrumentSpecificCreditRisk" name="FairValueOptionLoansReportedAsTradingAssetsChangesInFairValueFromInstrumentSpecificCreditRisk" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionLoansReportedAsTradingAssetsOtherChangesInFairValue" name="FairValueOptionLoansReportedAsTradingAssetsOtherChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionLongTermDebtOtherChangesInFairValue" name="FairValueOptionLongTermDebtOtherChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionOtherAssetsChangesInFairValue" name="FairValueOptionOtherAssetsChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionOtherBorrowedFundsChangesInFairValue" name="FairValueOptionOtherBorrowedFundsChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionOtherLiabilitiesChangesInFairValue" name="FairValueOptionOtherLiabilitiesChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionSecuritiesBorrowedChangesInFairValue" name="FairValueOptionSecuritiesBorrowedChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FairValueOptionsLoansHeldAsAssets" name="FairValueOptionsLoansHeldAsAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_FairValueOptionsOtherPerformingLoans" name="FairValueOptionsOtherPerformingLoans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_FairValuePlanAssetMeasurementMember" name="FairValuePlanAssetMeasurementMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FederalFundsPurchasedSecuritiesSoldUnderAgreementsToRepurchase" name="FederalFundsPurchasedSecuritiesSoldUnderAgreementsToRepurchase" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_FederalReserveBankOfNewYorkMember" name="FederalReserveBankOfNewYorkMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FederalRestrictionOnSecuredBorrowingsFromSubsidiariesAllLoansPortionOfSubsidiaryTotalCapitalPercentage" name="FederalRestrictionOnSecuredBorrowingsFromSubsidiariesAllLoansPortionOfSubsidiaryTotalCapitalPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FederalRestrictionOnSecuredBorrowingsFromSubsidiariesPerLoanPortionOfSubsidiaryTotalCapitalPercentage" name="FederalRestrictionOnSecuredBorrowingsFromSubsidiariesPerLoanPortionOfSubsidiaryTotalCapitalPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FeesAndCommissionsMortgageBankingAndRelatedAllOther" name="FeesAndCommissionsMortgageBankingAndRelatedAllOther" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FeesAndCommissionsMortgageBankingAndRelatedComponentsAbstract" name="FeesAndCommissionsMortgageBankingAndRelatedComponentsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FeesAndCommissionsMortgageBankingAndRelatedComponentsTableTextBlock" name="FeesAndCommissionsMortgageBankingAndRelatedComponentsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FercInvestigationMember" name="FercInvestigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancialAssetsAbstract" name="FinancialAssetsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancialEffectsOfModificationsAndRedefaultsAbstract" name="FinancialEffectsOfModificationsAndRedefaultsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancialInstitutionsGovernmentAgenciesAndOtherWholesaleMember" name="FinancialInstitutionsGovernmentAgenciesAndOtherWholesaleMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancialInstitutionsMember" name="FinancialInstitutionsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancialLiabilitiesAbstract" name="FinancialLiabilitiesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableByCurrentEstimatedLoanToValueRatioAxis" name="FinancingReceivableByCurrentEstimatedLoanToValueRatioAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableByCurrentEstimatedLoanToValueRatioDomain" name="FinancingReceivableByCurrentEstimatedLoanToValueRatioDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableByDelinquencyStatusAxis" name="FinancingReceivableByDelinquencyStatusAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableByDelinquencyStatusDomain" name="FinancingReceivableByDelinquencyStatusDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableImpairedTroubledDebtRestructuringChargeOffsPostModification" name="FinancingReceivableImpairedTroubledDebtRestructuringChargeOffsPostModification" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableImpairedTroubledDebtRestructuringForeclosuresAndOtherLiquidations" name="FinancingReceivableImpairedTroubledDebtRestructuringForeclosuresAndOtherLiquidations" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableImpairedTroubledDebtRestructuringIncreasesToExistingTroubledDebtRestructurings" name="FinancingReceivableImpairedTroubledDebtRestructuringIncreasesToExistingTroubledDebtRestructurings" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableImpairedTroubledDebtRestructuringPrincipalPaymentsSalesAndOther" name="FinancingReceivableImpairedTroubledDebtRestructuringPrincipalPaymentsSalesAndOther" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableModificationStatusAxis" name="FinancingReceivableModificationStatusAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableModificationStatusDomain" name="FinancingReceivableModificationStatusDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsAverageTermOrPaymentExtensionGranted" name="FinancingReceivableModificationsAverageTermOrPaymentExtensionGranted" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsNewTroubledDebtRestructurings" name="FinancingReceivableModificationsNewTroubledDebtRestructurings" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsNumberOfContractModificationsThatOccurred" name="FinancingReceivableModificationsNumberOfContractModificationsThatOccurred" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPercentageOfModificationsThatOccurredInterestRateReductions" name="FinancingReceivableModificationsPercentageOfModificationsThatOccurredInterestRateReductions" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPercentageOfModificationsThatOccurredOther" name="FinancingReceivableModificationsPercentageOfModificationsThatOccurredOther" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPercentageOfModificationsThatOccurredPrincipalAndInterestDeferred" name="FinancingReceivableModificationsPercentageOfModificationsThatOccurredPrincipalAndInterestDeferred" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPercentageOfModificationsThatOccurredPrincipalForgiveness" name="FinancingReceivableModificationsPercentageOfModificationsThatOccurredPrincipalForgiveness" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPercentageOfModificationsThatOccurredTermOrPaymentExtension" name="FinancingReceivableModificationsPercentageOfModificationsThatOccurredTermOrPaymentExtension" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPostModificationWeightedAverageInterestRateOfLoansWithRateReductions" name="FinancingReceivableModificationsPostModificationWeightedAverageInterestRateOfLoansWithRateReductions" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPostModificationWeightedAverageRemainingTerm" name="FinancingReceivableModificationsPostModificationWeightedAverageRemainingTerm" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPostModificationWeightedAverageRemainingTermOfFinancingReceivablesWithTermOrPaymentExtensions" name="FinancingReceivableModificationsPostModificationWeightedAverageRemainingTermOfFinancingReceivablesWithTermOrPaymentExtensions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPreModificationWeightedAverageInterestRateOfLoansWithRateReductions" name="FinancingReceivableModificationsPreModificationWeightedAverageInterestRateOfLoansWithRateReductions" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPreModificationWeightedAverageRemainingTerm" name="FinancingReceivableModificationsPreModificationWeightedAverageRemainingTerm" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPreModificationWeightedAverageRemainingTermOfFinancingReceivablesWithTermOrPaymentExtensions" name="FinancingReceivableModificationsPreModificationWeightedAverageRemainingTermOfFinancingReceivablesWithTermOrPaymentExtensions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPrincipalDeferred" name="FinancingReceivableModificationsPrincipalDeferred" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsPrincipalForgiven" name="FinancingReceivableModificationsPrincipalForgiven" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableModificationsRatiosAbstract" name="FinancingReceivableModificationsRatiosAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsSubsequentDefaultOccurredNumberOfContracts" name="FinancingReceivableModificationsSubsequentDefaultOccurredNumberOfContracts" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsSubsequentDefaultOccurredRecordedInvestment" name="FinancingReceivableModificationsSubsequentDefaultOccurredRecordedInvestment" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableModificationsWeightedAverageRemainingLife" name="FinancingReceivableModificationsWeightedAverageRemainingLife" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_FinancingReceivablePolicyAxis" name="FinancingReceivablePolicyAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivablePolicyCurrentMember" name="FinancingReceivablePolicyCurrentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivablePolicyDomain" name="FinancingReceivablePolicyDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivablePolicyPreviousMember" name="FinancingReceivablePolicyPreviousMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableRecordedInvestmentCreditQualityIndicatorAbstract" name="FinancingReceivableRecordedInvestmentCreditQualityIndicatorAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableRecordedInvestmentCreditQualityIndicatorLineItems" name="FinancingReceivableRecordedInvestmentCreditQualityIndicatorLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableRecordedInvestmentCreditQualityIndicatorRatiosAbstract" name="FinancingReceivableRecordedInvestmentCreditQualityIndicatorRatiosAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivableTrialModificationsSuccessfullyConvertedToPermanentModifications" name="FinancingReceivableTrialModificationsSuccessfullyConvertedToPermanentModifications" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableTrialModificationsTimePeriodOfTrialModificationProgramsAxis" name="FinancingReceivableTrialModificationsTimePeriodOfTrialModificationProgramsAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableTrialModificationsTimePeriodOfTrialModificationProgramsDomain" name="FinancingReceivableTrialModificationsTimePeriodOfTrialModificationProgramsDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivableTroubledDebtRestructuringsAbstract" name="FinancingReceivableTroubledDebtRestructuringsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivablesByDelinquencyStatusJuniorLiensThatAreSubordinateToDelinquentSeniorLiensAxis" name="FinancingReceivablesByDelinquencyStatusJuniorLiensThatAreSubordinateToDelinquentSeniorLiensAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FinancingReceivablesByDelinquencyStatusJuniorLiensThatAreSubordinateToDelinquentSeniorLiensDomain" name="FinancingReceivablesByDelinquencyStatusJuniorLiensThatAreSubordinateToDelinquentSeniorLiensDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FinancingReceivablesImpairedTroubledDebtRestructuringPerformingLoansRestructuredAtMarketRatesButNoLongerReportedAsTdrs" name="FinancingReceivablesImpairedTroubledDebtRestructuringPerformingLoansRestructuredAtMarketRatesButNoLongerReportedAsTdrs" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_FineLeviedByCourt" name="FineLeviedByCourt" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_FirmAdministeredMultiSellerConduitsMember" name="FirmAdministeredMultiSellerConduitsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FirmSponsoredCreditCardSecuritizationTrustsMember" name="FirmSponsoredCreditCardSecuritizationTrustsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FirmSponsoredMortgageAndOtherConsumerSecuritizationTrustsAbstract" name="FirmSponsoredMortgageAndOtherConsumerSecuritizationTrustsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FirmSponsoredMortgageAndOtherConsumerSecuritizationTrustsTextBlock" name="FirmSponsoredMortgageAndOtherConsumerSecuritizationTrustsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_FirmsExposureToNonconsolidatedMunicipalBondVIEsTextBlock" name="FirmsExposureToNonconsolidatedMunicipalBondVIEsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FirmsExposureToNonconsolidatedMunicipalBondVariableInterestEntitiesAbstract" name="FirmsExposureToNonconsolidatedMunicipalBondVariableInterestEntitiesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FirstChicagoNbdCapitalIMember" name="FirstChicagoNbdCapitalIMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FirstFiftyPercentMember" name="FirstFiftyPercentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FixedToFloatingRateNonCumulativePerpetualPreferredStockSeriesIMember" name="FixedToFloatingRateNonCumulativePerpetualPreferredStockSeriesIMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FloridaMember" name="FloridaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ForeclosurePreventionCommitmentActionsMember" name="ForeclosurePreventionCommitmentActionsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ForeignCurrencyDenominatedDebtMember" name="ForeignCurrencyDenominatedDebtMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ForeignMortgageBackedSecuritiesMember" name="ForeignMortgageBackedSecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ForeignPensionPlansAndOtherForeignPostretirementBenefitPlansMember" name="ForeignPensionPlansAndOtherForeignPostretirementBenefitPlansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FreeStandingDerivativesAbstract" name="FreeStandingDerivativesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_FutureAndForwardContractsMember" name="FutureAndForwardContractsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_GainOnExpectedRecoveryOfSubordinatedLoanPreTax" name="GainOnExpectedRecoveryOfSubordinatedLoanPreTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_GlobalSettlementOnServicingAndOriginationOfMortgagesMember" name="GlobalSettlementOnServicingAndOriginationOfMortgagesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_GoodwillAndOtherIntangibleAssetsTextBlock" name="GoodwillAndOtherIntangibleAssetsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_GoodwillBySegmentTableTextBlock" name="GoodwillBySegmentTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_GoodwillExcludedFromTierOneRiskBasedCapital" name="GoodwillExcludedFromTierOneRiskBasedCapital" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_GoodwillPeriodIncreaseDecreaseOther" name="GoodwillPeriodIncreaseDecreaseOther" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_GoodwillRollForwardTableTextBlock" name="GoodwillRollForwardTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_GovernmentAgenciesMember" name="GovernmentAgenciesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_GovernmentDebtSecuritiesMember" name="GovernmentDebtSecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_GuarantorObligationsAndCommitmentsCurrentCarryingValue" name="GuarantorObligationsAndCommitmentsCurrentCarryingValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_HealthcareCreditRiskConcentrationMember" name="HealthcareCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_HeldToMaturitySecuritiesDebtMaturitiesAverageYield" name="HeldToMaturitySecuritiesDebtMaturitiesAverageYield" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_HeldToMaturitySecuritiesDebtMaturitiesAverageYieldAfterFiveThroughTenYears" name="HeldToMaturitySecuritiesDebtMaturitiesAverageYieldAfterFiveThroughTenYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_HeldToMaturitySecuritiesDebtMaturitiesAverageYieldAfterOneThroughFiveYears" name="HeldToMaturitySecuritiesDebtMaturitiesAverageYieldAfterOneThroughFiveYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_HeldToMaturitySecuritiesDebtMaturitiesAverageYieldAfterTenYears" name="HeldToMaturitySecuritiesDebtMaturitiesAverageYieldAfterTenYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_HeldToMaturitySecuritiesDebtMaturitiesAverageYieldWithinOneYear" name="HeldToMaturitySecuritiesDebtMaturitiesAverageYieldWithinOneYear" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_HomeEquityJuniorLienExcludingLinesOfCreditMember" name="HomeEquityJuniorLienExcludingLinesOfCreditMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_HomeEquityJuniorLienMember" name="HomeEquityJuniorLienMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_HomeEquityLineOfCreditAmortizationPeriod" name="HomeEquityLineOfCreditAmortizationPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_HomeEquityLineOfCreditAmortizationStatusAmortizationPeriodMember" name="HomeEquityLineOfCreditAmortizationStatusAmortizationPeriodMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_HomeEquityLineOfCreditAmortizationStatusDomain" name="HomeEquityLineOfCreditAmortizationStatusDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_HomeEquityLineOfCreditAmortizationStatusRevolvingPeriodMember" name="HomeEquityLineOfCreditAmortizationStatusRevolvingPeriodMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_HomeEquityLineOfCreditByAmortizationStatusAxis" name="HomeEquityLineOfCreditByAmortizationStatusAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_HomeEquityLineOfCreditOpenEndedRevolvingPeriod" name="HomeEquityLineOfCreditOpenEndedRevolvingPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_HomeEquitySeniorLienMember" name="HomeEquitySeniorLienMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_HybridSecuritiesAndNoncontrollingInterestsQualifyingAsTierOneRiskBasedCapital" name="HybridSecuritiesAndNoncontrollingInterestsQualifyingAsTierOneRiskBasedCapital" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_IllinoisMember" name="IllinoisMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ImpactOfCreditAdjustmentsOnEarningsAbstract" name="ImpactOfCreditAdjustmentsOnEarningsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ImpactOfRegulatoryGuidanceAxis" name="ImpactOfRegulatoryGuidanceAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ImpairedCollateralDependentLoansAbstract" name="ImpairedCollateralDependentLoansAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ImpairedCollateralDependentLoansMember" name="ImpairedCollateralDependentLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ImpairedFinancingReceivablesAbstract" name="ImpairedFinancingReceivablesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ImpairedFinancingReceivablesAverageRecordedInvestmentTableTextBlock" name="ImpairedFinancingReceivablesAverageRecordedInvestmentTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ImpairmentMethodologyAbstract" name="ImpairmentMethodologyAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_IncomeTaxExpensePriorUndistributedEarningsOfForeignSubsidiariesToBeDistributed" name="IncomeTaxExpensePriorUndistributedEarningsOfForeignSubsidiariesToBeDistributed" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_IncreaseDecreaseInAccountsPayableAndOtherLiabilities" name="IncreaseDecreaseInAccountsPayableAndOtherLiabilities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_IncreaseDecreaseInAccruedInterestsAndAccountsReceivable" name="IncreaseDecreaseInAccruedInterestsAndAccountsReceivable" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_IncreaseDecreaseInBeneficialInterestsIssuedByConsolidatedVariableInterestEntities" name="IncreaseDecreaseInBeneficialInterestsIssuedByConsolidatedVariableInterestEntities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_IncreaseDecreaseInLevel3Assets" name="IncreaseDecreaseInLevel3Assets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_IncreaseDecreaseInOtherIntangibleAssets" name="IncreaseDecreaseInOtherIntangibleAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_IncreaseDecreaseInTaxRateResultingFromAbstract" name="IncreaseDecreaseInTaxRateResultingFromAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_IncreaseDecreaseInTreasuryStockRollForward" name="IncreaseDecreaseInTreasuryStockRollForward" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_IndemnificationAgreementSecuritiesLendingGuaranteesCollateralHeldInSupportOf" name="IndemnificationAgreementSecuritiesLendingGuaranteesCollateralHeldInSupportOf" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_IndemnificationAgreementsLoanSaleAndSecuritizationLoansSoldWithRecourseCarryingValue" name="IndemnificationAgreementsLoanSaleAndSecuritizationLoansSoldWithRecourseCarryingValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_IndemnificationAgreementsLoanSaleAndSecuritizationLoansSoldWithRecourseContractualAmount" name="IndemnificationAgreementsLoanSaleAndSecuritizationLoansSoldWithRecourseContractualAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_IndividualActionMember" name="IndividualActionMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_InformationAboutDelinquenciesNetChargeOffsAndComponentsOfOffBalanceSheetSecuritizedFinancialAssetsTextBlock" name="InformationAboutDelinquenciesNetChargeOffsAndComponentsOfOffBalanceSheetSecuritizedFinancialAssetsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_InformationOnAssetsAndLiabilitiesRelatedToVIEsThatAreConsolidatedByFirmTextBlock" name="InformationOnAssetsAndLiabilitiesRelatedToVIEsThatAreConsolidatedByFirmTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InformationOnAssetsAndLiabilitiesRelatedToVariableInterestEntitiesThatAreConsolidatedByFirmAbstract" name="InformationOnAssetsAndLiabilitiesRelatedToVariableInterestEntitiesThatAreConsolidatedByFirmAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_IntangibleAssetsByMajorClassLineItems" name="IntangibleAssetsByMajorClassLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_IntangibleAssetsNetExcludingGoodwillAndMortgageServicingRightsMSRsTextBlockTableTextBlock" name="IntangibleAssetsNetExcludingGoodwillAndMortgageServicingRightsMSRsTextBlockTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_IntangibleAssetsNetExcludingGoodwillAndMortgageServicingRightsMsrsAbstract" name="IntangibleAssetsNetExcludingGoodwillAndMortgageServicingRightsMsrsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_IntentToSellMember" name="IntentToSellMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InterchangeLitigationDefendantGroupMember" name="InterchangeLitigationDefendantGroupMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InterchangeLitigationMember" name="InterchangeLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_IntercompanytransactionsbysubsidiaryAxis" name="IntercompanytransactionsbysubsidiaryAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InterestIncomeAndInterestExpenseAxis" name="InterestIncomeAndInterestExpenseAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InterestIncomeAndInterestExpenseDomain" name="InterestIncomeAndInterestExpenseDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InterestIncomeAndInterestExpenseLineItems" name="InterestIncomeAndInterestExpenseLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_InterestIncomeAndInterestExpensePolicyTextBlock" name="InterestIncomeAndInterestExpensePolicyTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InterestIncomeAndInterestExpenseTable" name="InterestIncomeAndInterestExpenseTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_InterestIncomeSecuritiesBorrowed" name="InterestIncomeSecuritiesBorrowed" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_InterestIncomeTradingAssets" name="InterestIncomeTradingAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InternalCreditRatingInvestmentGradeFourMember" name="InternalCreditRatingInvestmentGradeFourMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InternalCreditRatingInvestmentGradeOneMember" name="InternalCreditRatingInvestmentGradeOneMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InternalCreditRatingInvestmentGradeOrBetterMember" name="InternalCreditRatingInvestmentGradeOrBetterMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InternalCreditRatingInvestmentGradeThreeMember" name="InternalCreditRatingInvestmentGradeThreeMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InternalCreditRatingInvestmentGradeTwoMember" name="InternalCreditRatingInvestmentGradeTwoMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InternationalEquitySecuritiesMember" name="InternationalEquitySecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InternationalOperationsDetailsAbstract" name="InternationalOperationsDetailsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_InternationalOperationsTextBlock" name="InternationalOperationsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InvestmentBankMember" name="InvestmentBankMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_InvestmentBankingAdvisoryFeeRevenue" name="InvestmentBankingAdvisoryFeeRevenue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InvestmentGradeMember" name="InvestmentGradeMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_InvestmentManagementLitigationMember" name="InvestmentManagementLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_InvestmentValuedAtNetAssetValue" name="InvestmentValuedAtNetAssetValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_InvestmentsInCertainSubsidiariesExcludedFromTierOneRiskBasedCapital" name="InvestmentsInCertainSubsidiariesExcludedFromTierOneRiskBasedCapital" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_InvestmentsInSubsidiariesBankAndBankHoldingCompanies" name="InvestmentsInSubsidiariesBankAndBankHoldingCompanies" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_InvestmentsInSubsidiariesNonBankingCompanies" name="InvestmentsInSubsidiariesNonBankingCompanies" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_IssuerTrustsThatGuaranteeCapitalDebtSecuritiesNumber" name="IssuerTrustsThatGuaranteeCapitalDebtSecuritiesNumber" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_JPMorganCazenoveMember" name="JPMorganCazenoveMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JPMorganChaseCapitalXMember" name="JPMorganChaseCapitalXMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JPMorganSecuritiesLlcMember" name="JPMorganSecuritiesLlcMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JeffersonCountyAlabamaMember" name="JeffersonCountyAlabamaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseBankNAMember" name="JpmorganChaseBankNAMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCapitalXiMember" name="JpmorganChaseCapitalXiMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCapitalXiiMember" name="JpmorganChaseCapitalXiiMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCapitalXiiiMember" name="JpmorganChaseCapitalXiiiMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCapitalXivMember" name="JpmorganChaseCapitalXivMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCapitalXixMember" name="JpmorganChaseCapitalXixMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCapitalXviMember" name="JpmorganChaseCapitalXviMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCapitalXxiMember" name="JpmorganChaseCapitalXxiMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCapitalXxiiiMember" name="JpmorganChaseCapitalXxiiiMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCapitalXxivMember" name="JpmorganChaseCapitalXxivMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCapitalXxixMember" name="JpmorganChaseCapitalXxixMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_JpmorganChaseCoMember" name="JpmorganChaseCoMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_JuniorSubordinatedDebentureOwedToUnconsolidatedSubsidiaryTrustTextBlock" name="JuniorSubordinatedDebentureOwedToUnconsolidatedSubsidiaryTrustTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_KeyEconomicAssumptionsUsedToDetermineFairValueAbstract" name="KeyEconomicAssumptionsUsedToDetermineFairValueAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LatinAmericaMember" name="LatinAmericaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LehmanBrothersBankruptcyProceedingsMember" name="LehmanBrothersBankruptcyProceedingsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LendingAndDepositRelatedFees" name="LendingAndDepositRelatedFees" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LendingRelatedCommitmentsMember" name="LendingRelatedCommitmentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LengthOfTimeBannedFromDealingWithItalianPublicBodies" name="LengthOfTimeBannedFromDealingWithItalianPublicBodies" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_LettersOfCreditHedgedByDerivativeTransactionsMember" name="LettersOfCreditHedgedByDerivativeTransactionsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_Level3AnalysisSupplementalDataAbstract" name="Level3AnalysisSupplementalDataAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LevelThreeLiabilitiesAsPercentageOfTotalFirmLiabilitiesAtFairValue" name="LevelThreeLiabilitiesAsPercentageOfTotalFirmLiabilitiesAtFairValue" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_LiborInvestigationsAndLitigationMember" name="LiborInvestigationsAndLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LimitedPartnershipsMember" name="LimitedPartnershipsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LimitedProgramWideCreditEnhancement" name="LimitedProgramWideCreditEnhancement" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_LineOfCreditCloseCriteriaPeriodPastDue" name="LineOfCreditCloseCriteriaPeriodPastDue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LitigationAbstract" name="LitigationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LitigationByCourtJurisdictionAxis" name="LitigationByCourtJurisdictionAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LitigationByCourtJurisdictionDomain" name="LitigationByCourtJurisdictionDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LitigationDetailsAbstract" name="LitigationDetailsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LoanModificationProgramFixedPaymentPlanPeriod" name="LoanModificationProgramFixedPaymentPlanPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LoanPortfolioSegmentDescriptionsTableTextBlock" name="LoanPortfolioSegmentDescriptionsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoanSalesAndSecuritizationRelatedIndemnificationsAbstract" name="LoanSalesAndSecuritizationRelatedIndemnificationsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LoansAndLeasesReceivableDeferredCosts" name="LoansAndLeasesReceivableDeferredCosts" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_LoansAndLeasesReceivableImpairedCollateralDependentLoansAtFairValue" name="LoansAndLeasesReceivableImpairedCollateralDependentLoansAtFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_LoansAndLeasesReceivableImpairedRevertedToOriginalPaymentTermsAmount" name="LoansAndLeasesReceivableImpairedRevertedToOriginalPaymentTermsAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_LoansAndLeasesReceivableImpairedTroubledDebtRestructuringFundedCommitmentToLend" name="LoansAndLeasesReceivableImpairedTroubledDebtRestructuringFundedCommitmentToLend" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_LoansAndLeasesReceivableImpairedTroubledDebtRestructuringsNotHavingYetMadeSixPayments" name="LoansAndLeasesReceivableImpairedTroubledDebtRestructuringsNotHavingYetMadeSixPayments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_LoansByImpairmentMethodologyAbstract" name="LoansByImpairmentMethodologyAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoansByPortfolioSegmentAndClassAbstract" name="LoansByPortfolioSegmentAndClassAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoansByPortfolioSegmentAndClassLineItems" name="LoansByPortfolioSegmentAndClassLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LoansChargeOffCriteriaPeriodPastDue" name="LoansChargeOffCriteriaPeriodPastDue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoansHeldForSaleAndLoansAtFairValueCreditRiskConcentrationMember" name="LoansHeldForSaleAndLoansAtFairValueCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoansInsuredOrGuaranteedByGovernmentsAxis" name="LoansInsuredOrGuaranteedByGovernmentsAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoansInsuredOrGuaranteedByGovernmentsDomain" name="LoansInsuredOrGuaranteedByGovernmentsDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoansModifiedInTroubledDebtRestructuringsAbstract" name="LoansModifiedInTroubledDebtRestructuringsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoansModifiedInTroubledDebtRestructuringsRollForward" name="LoansModifiedInTroubledDebtRestructuringsRollForward" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LoansReceivableAtFairValue" name="LoansReceivableAtFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_LoansReceivableHeldForInvestmentNetOfDeferredIncome" name="LoansReceivableHeldForInvestmentNetOfDeferredIncome" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_LoansReceivableHeldForSaleExcludingLoansAtFairValue" name="LoansReceivableHeldForSaleExcludingLoansAtFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_LoansReceivableNotForTradingMember" name="LoansReceivableNotForTradingMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoansReportedAsTradingAssetsAbstract" name="LoansReportedAsTradingAssetsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LoansRepurchasedOrLoansWithOptionToRepurchase" name="LoansRepurchasedOrLoansWithOptionToRepurchase" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_LoansRetainedMember" name="LoansRetainedMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoansSoldWithRecourseAbstract" name="LoansSoldWithRecourseAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LoansSoldWithRecourseMember" name="LoansSoldWithRecourseMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LongTermBeneficialInterests" name="LongTermBeneficialInterests" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_LongTermBeneficialInterestsAbstract" name="LongTermBeneficialInterestsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LongTermBeneficialInterestsMaturitiesAxis" name="LongTermBeneficialInterestsMaturitiesAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LongTermBeneficialInterestsMaturitiesBetweenOneAndFiveYearsMember" name="LongTermBeneficialInterestsMaturitiesBetweenOneAndFiveYearsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LongTermBeneficialInterestsMaturitiesDomain" name="LongTermBeneficialInterestsMaturitiesDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LongTermBeneficialInterestsMaturitiesOverFiveYearsMember" name="LongTermBeneficialInterestsMaturitiesOverFiveYearsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LongTermBeneficialInterestsMaturitiesUnderOneYearMember" name="LongTermBeneficialInterestsMaturitiesUnderOneYearMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LongTermBeneficialInterestsMember" name="LongTermBeneficialInterestsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LongTermCreditCardLoanModificationProgramsMember" name="LongTermCreditCardLoanModificationProgramsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LongTermDebtCarryingValuesByContractualMaturityTextBlock" name="LongTermDebtCarryingValuesByContractualMaturityTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LongTermDebtCollateral" name="LongTermDebtCollateral" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_LongTermDebtFixedInterestRateMember" name="LongTermDebtFixedInterestRateMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LongTermDebtMaturingInYearsOneThroughFive" name="LongTermDebtMaturingInYearsOneThroughFive" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_LongTermDebtOtherBorrowedFundsAndDepositsMember" name="LongTermDebtOtherBorrowedFundsAndDepositsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LongTermDebtPortionGuaranteedByFdicUnderTemporaryLiquidityGuaranteeProgram" name="LongTermDebtPortionGuaranteedByFdicUnderTemporaryLiquidityGuaranteeProgram" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_LongTermDebtSupplementalInformationAbstract" name="LongTermDebtSupplementalInformationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LongTermDebtVariableInterestRateMember" name="LongTermDebtVariableInterestRateMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LongtermDebtAndOtherInstrumentsQualifyingAsTierTwoRiskBasedCapital" name="LongtermDebtAndOtherInstrumentsQualifyingAsTierTwoRiskBasedCapital" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_LossContingenciesByAssociatedPartyLitigationRelatedDomain" name="LossContingenciesByAssociatedPartyLitigationRelatedDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LossContingenciesByNameAssociatedWithContingencyAxis" name="LossContingenciesByNameAssociatedWithContingencyAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LossContingenciesInConnectionWithSpecificNamedEventAxis" name="LossContingenciesInConnectionWithSpecificNamedEventAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LossContingenciesInConnectionWithSpecificNamedEventDomain" name="LossContingenciesInConnectionWithSpecificNamedEventDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LossContingenciesInConnectionWithSpecificTypeOfSettlementAxis" name="LossContingenciesInConnectionWithSpecificTypeOfSettlementAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LossContingenciesInConnectionWithSpecificTypeOfSettlementDomain" name="LossContingenciesInConnectionWithSpecificTypeOfSettlementDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LossContingencyBondsFaceOrParValue" name="LossContingencyBondsFaceOrParValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LossContingencyDamagesSoughtCounterclaimsValue" name="LossContingencyDamagesSoughtCounterclaimsValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_LossContingencyDamagesSoughtRecoveriesValue" name="LossContingencyDamagesSoughtRecoveriesValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_LossContingencyInformationAboutRelatedResourcesOrMarketsAbstract" name="LossContingencyInformationAboutRelatedResourcesOrMarketsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LossContingencyInsuredAggregatePrincipalAmountRelatedToValue" name="LossContingencyInsuredAggregatePrincipalAmountRelatedToValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_LossContingencyInvestmentsInMediumTermNotesRelatedToValue" name="LossContingencyInvestmentsInMediumTermNotesRelatedToValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_LossContingencyMortgageBackedSecuritiesTrustsOriginalPrincipalAmountRelatedToValue" name="LossContingencyMortgageBackedSecuritiesTrustsOriginalPrincipalAmountRelatedToValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_LossContingencySecuritiesIssuedBySecuritizationTrustsRelatedToValue" name="LossContingencySecuritiesIssuedBySecuritizationTrustsRelatedToValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_LossContingencySecuritizationRelatedToValue" name="LossContingencySecuritizationRelatedToValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_LossContingencySettlementAgreementConsiderationBasisPointsOfInterchange" name="LossContingencySettlementAgreementConsiderationBasisPointsOfInterchange" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LossContingencySettlementAgreementConsiderationPercentage" name="LossContingencySettlementAgreementConsiderationPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LossContingencySettlementAgreementConsiderationPeriodClassPlaintiffsToReceiveBasisPointsOfInterchange" name="LossContingencySettlementAgreementConsiderationPeriodClassPlaintiffsToReceiveBasisPointsOfInterchange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LossContingencySettlementAmountAgreedToPayByDefendantGroup" name="LossContingencySettlementAmountAgreedToPayByDefendantGroup" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_LossContingencyWarrantsIssuedRelatedToValue" name="LossContingencyWarrantsIssuedRelatedToValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_LowFicoScore" name="LowFicoScore" nillable="true" substitutionGroup="xbrli:item" type="xbrli:pureItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MachineryAndEquipmentManufacturingCreditRiskConcentrationMember" name="MachineryAndEquipmentManufacturingCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MadoffLitigationNotSpecificallyRelatedToFairfieldMember" name="MadoffLitigationNotSpecificallyRelatedToFairfieldMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MadoffLitigationRelatedToFairfieldMember" name="MadoffLitigationRelatedToFairfieldMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ManagedStructureMember" name="ManagedStructureMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MarketComparablesValuationTechniqueMember" name="MarketComparablesValuationTechniqueMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MediaCreditRiskConcentrationMember" name="MediaCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MetalsAndMiningCreditRiskConcentrationMember" name="MetalsAndMiningCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MfGlobalMember" name="MfGlobalMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MichiganMember" name="MichiganMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ModifiedLoansRepurchasedFromUSGovernmentAgenciesExcludedFromTroubledDebtRestructurings" name="ModifiedLoansRepurchasedFromUSGovernmentAgenciesExcludedFromTroubledDebtRestructurings" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_MortgageBackedSecuritiesLitigationAndRegulatoryInvestigationsMember" name="MortgageBackedSecuritiesLitigationAndRegulatoryInvestigationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageBackedSecuritiesLitigationRelatedToMbsOfferingsSponsoredByEmcMember" name="MortgageBackedSecuritiesLitigationRelatedToMbsOfferingsSponsoredByEmcMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageBackedSecuritiesLitigationRelatedToMbsOfferingsSponsoredByJpmorganChaseMember" name="MortgageBackedSecuritiesLitigationRelatedToMbsOfferingsSponsoredByJpmorganChaseMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageBackedSecuritiesLitigationRelatedToMbsOfferingsSponsoredByWashingtonMutualMember" name="MortgageBackedSecuritiesLitigationRelatedToMbsOfferingsSponsoredByWashingtonMutualMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_MortgageFeesAndRelatedIncome" name="MortgageFeesAndRelatedIncome" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageForeclosureInvestigationsAndLitigationActionsDismissedMember" name="MortgageForeclosureInvestigationsAndLitigationActionsDismissedMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageForeclosureInvestigationsAndLitigationActionsSettledOnIndividualBasisMember" name="MortgageForeclosureInvestigationsAndLitigationActionsSettledOnIndividualBasisMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageForeclosureInvestigationsAndLitigationMember" name="MortgageForeclosureInvestigationsAndLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageForeclosureInvestigationsAndLitigationShareholderDerivativeActionsMember" name="MortgageForeclosureInvestigationsAndLitigationShareholderDerivativeActionsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageLoansInsuredOrGuaranteedByUsGovernmentAgenciesMember" name="MortgageLoansInsuredOrGuaranteedByUsGovernmentAgenciesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_MortgageLoansServicedThirdPartyAtPeriodEnd" name="MortgageLoansServicedThirdPartyAtPeriodEnd" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_MortgageProductionRevenue" name="MortgageProductionRevenue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_MortgageProductionRevenueExcludingRepurchaseLosses" name="MortgageProductionRevenueExcludingRepurchaseLosses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageRepurchaseLiabilityMember" name="MortgageRepurchaseLiabilityMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageRepurchaseNewLoansSoldDuringPeriodMember" name="MortgageRepurchaseNewLoansSoldDuringPeriodMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageSecuritizationEntitiesMember" name="MortgageSecuritizationEntitiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageServicingRightsActivityAbstract" name="MortgageServicingRightsActivityAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageServicingRightsActivitySupplementalInfoAbstractAbstract" name="MortgageServicingRightsActivitySupplementalInfoAbstractAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_MortgageServicingRightsChangeInUnrealizedGainsLossesIncludedInIncome" name="MortgageServicingRightsChangeInUnrealizedGainsLossesIncludedInIncome" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageServicingRightsInputsAndAssumptionsUsedToDetermineFairValueAxis" name="MortgageServicingRightsInputsAndAssumptionsUsedToDetermineFairValueAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageServicingRightsInputsAndAssumptionsUsedToDetermineFairValueDomain" name="MortgageServicingRightsInputsAndAssumptionsUsedToDetermineFairValueDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_MortgageServicingRightsMSRsFairValueKeyEconomicAssumptionsUsedToDetermineTextBlockTableTextBlock" name="MortgageServicingRightsMSRsFairValueKeyEconomicAssumptionsUsedToDetermineTextBlockTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MortgageServicingRightsMSRsFairValueRollForwardRollForward" name="MortgageServicingRightsMSRsFairValueRollForwardRollForward" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_MortgageServicingRightsMSRsFairValueRollForwardTextBlockTableTextBlock" name="MortgageServicingRightsMSRsFairValueRollForwardTextBlockTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_MortgageServicingRightsNetAdditionsAndAmortization" name="MortgageServicingRightsNetAdditionsAndAmortization" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_MunicipalDerivativesInvestigationsAndLitigationMember" name="MunicipalDerivativesInvestigationsAndLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NatureAndExtentOfModificationsAbstract" name="NatureAndExtentOfModificationsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NetChargeOffs" name="NetChargeOffs" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NetDerivativeReceivablesMember" name="NetDerivativeReceivablesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NetGainsLossesOnLoanSalesByPortfolioSegmentAbstract" name="NetGainsLossesOnLoanSalesByPortfolioSegmentAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NetGainsLossesOnLoanSalesByPortfolioSegmentLineItems" name="NetGainsLossesOnLoanSalesByPortfolioSegmentLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NetIncomeApplicableToCommonEquity" name="NetIncomeApplicableToCommonEquity" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_NetInvestmentHedgeDerivativeLiabilitiesAtFairValueExcludedFromDerivativeFairValueOfDerivativeNet" name="NetInvestmentHedgeDerivativeLiabilitiesAtFairValueExcludedFromDerivativeFairValueOfDerivativeNet" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_NetInvestmentHedgeGainsAndLossesAbstract" name="NetInvestmentHedgeGainsAndLossesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NetMortgageServicingRevenueAbstract" name="NetMortgageServicingRevenueAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NetProductionRevenueAbstract" name="NetProductionRevenueAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NetProtectionSoldPurchased" name="NetProtectionSoldPurchased" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_NewAccountingPronouncementOrChangeInAccountingPrincipleEffectOfChangeOnAllowanceForCreditLosses" name="NewAccountingPronouncementOrChangeInAccountingPrincipleEffectOfChangeOnAllowanceForCreditLosses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_NewJerseyMember" name="NewJerseyMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NewYorkMember" name="NewYorkMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NewYorkStateCourtMember" name="NewYorkStateCourtMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NewYorkStateSupremeCourtMember" name="NewYorkStateSupremeCourtMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NonAgencyResidentialCollateralizedMortgageObligationsEstimatedDuration" name="NonAgencyResidentialCollateralizedMortgageObligationsEstimatedDuration" nillable="true" substitutionGroup="xbrli:item" type="us-types:durationStringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NonCumulativePreferredStockSeriesJMember" name="NonCumulativePreferredStockSeriesJMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NonCumulativePreferredStockSeriesOMember" name="NonCumulativePreferredStockSeriesOMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NonInterestExpenseLineItems" name="NonInterestExpenseLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NonPrincipalProtectedDebtMember" name="NonPrincipalProtectedDebtMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NonSubsidiariesMember" name="NonSubsidiariesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NonaccrualMember" name="NonaccrualMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NonbankSubsidiariesMember" name="NonbankSubsidiariesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NoncompensationExpenseAbstract" name="NoncompensationExpenseAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NoncomplianceWithModifiedTermsMember" name="NoncomplianceWithModifiedTermsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NonconsolidatedMunicipalBondVehiclesMember" name="NonconsolidatedMunicipalBondVehiclesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NonconsolidatedPrivateLabelReSecuritizationsMember" name="NonconsolidatedPrivateLabelReSecuritizationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NoncriticizedMember" name="NoncriticizedMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NoninterestBearingAndInterestBearingDepositsTableTextBlock" name="NoninterestBearingAndInterestBearingDepositsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NoninterestExpenseAndProvisionForLoanLeaseAndOtherLosses" name="NoninterestExpenseAndProvisionForLoanLeaseAndOtherLosses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_NoninterestExpenseOtherThanLaborAndRelatedExpenseTotal" name="NoninterestExpenseOtherThanLaborAndRelatedExpenseTotal" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_NoninterestExpenseTextBlock" name="NoninterestExpenseTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NoninterestRevenueTextBlock" name="NoninterestRevenueTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NoninvestmentGradeMember" name="NoninvestmentGradeMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_NorthAmericaMember" name="NorthAmericaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfActionsWithMotionsToDismiss" name="NumberOfActionsWithMotionsToDismiss" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_NumberOfAssertedCausesOfActionAgainstEntity" name="NumberOfAssertedCausesOfActionAgainstEntity" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfAssertedCausesOfActionAgainstEntityThatSeekToAvoidTransfersToEntity" name="NumberOfAssertedCausesOfActionAgainstEntityThatSeekToAvoidTransfersToEntity" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfConsolidatedLegalProceedings" name="NumberOfConsolidatedLegalProceedings" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_NumberOfCurrentAndFormerJpmorganChaseAndJpmorganChaseBankNAPersonnelDirectedToGoForwardToFullTrial" name="NumberOfCurrentAndFormerJpmorganChaseAndJpmorganChaseBankNAPersonnelDirectedToGoForwardToFullTrial" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_NumberOfDaysAfterFilingOfAmendedComplaintDefendentsHaveToRespond" name="NumberOfDaysAfterFilingOfAmendedComplaintDefendentsHaveToRespond" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfDaysCourtGavePlaintiffToFileAmendedComplaint" name="NumberOfDaysCourtGavePlaintiffToFileAmendedComplaint" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfJpmorganChasePersonnelAcquitted" name="NumberOfJpmorganChasePersonnelAcquitted" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_NumberOfJpmorganChasePersonnelFoundGuilty" name="NumberOfJpmorganChasePersonnelFoundGuilty" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_NumberOfLegalProceedings" name="NumberOfLegalProceedings" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_NumberOfLegalProceedingsInWhichAmendedComplaintWasFiled" name="NumberOfLegalProceedingsInWhichAmendedComplaintWasFiled" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_NumberOfMonthsBeforePaymentRedefaultUnderModifiedLoan" name="NumberOfMonthsBeforePaymentRedefaultUnderModifiedLoan" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfMonthsBeforeUpdatingCollateralValuesOnCommercialRealEstateLoans" name="NumberOfMonthsBeforeUpdatingCollateralValuesOnCommercialRealEstateLoans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfMonthsBeforeUpdatingExteriorOpinionOnHomeValuation" name="NumberOfMonthsBeforeUpdatingExteriorOpinionOnHomeValuation" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfMonthsBorrowerHasPerformedUnderModifiedTerms" name="NumberOfMonthsBorrowerHasPerformedUnderModifiedTerms" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfNasaasMembersWithWhichSettlementAgreementsHaveNotBeenFinalized" name="NumberOfNasaasMembersWithWhichSettlementAgreementsHaveNotBeenFinalized" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_NumberOfOfferingsByEntityRelatedToFiledSuit" name="NumberOfOfferingsByEntityRelatedToFiledSuit" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfOtherBanksWithPersonnelOrderedToGoToFullTrial" name="NumberOfOtherBanksWithPersonnelOrderedToGoToFullTrial" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_NumberOfPaymentsPastDueForDeemedPaymentDefault" name="NumberOfPaymentsPastDueForDeemedPaymentDefault" nillable="true" substitutionGroup="xbrli:item" type="xbrli:pureItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfPaymentsUnderModifiedTermsToRecognizeInterestOnCashBasis" name="NumberOfPaymentsUnderModifiedTermsToRecognizeInterestOnCashBasis" nillable="true" substitutionGroup="xbrli:item" type="xbrli:pureItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfPlaintiffsThatOptedOutOfSettlement" name="NumberOfPlaintiffsThatOptedOutOfSettlement" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_NumberOfTrustsRelatedToMbsSecuritization" name="NumberOfTrustsRelatedToMbsSecuritization" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_NumberOfYearsBeforePaymentDefaultUnderModifiedLoan" name="NumberOfYearsBeforePaymentDefaultUnderModifiedLoan" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OffBalanceSheetLendingRelatedCommitmentsWholesaleContractualAmountNetOfRiskParticipationsOtherLettersOfCredit" name="OffBalanceSheetLendingRelatedCommitmentsWholesaleContractualAmountNetOfRiskParticipationsOtherLettersOfCredit" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_OffBalanceSheetLendingRelatedCommitmentsWholesaleContractualAmountNetOfRiskParticipationsOtherUnfundedCommitmentsToExtendCredit" name="OffBalanceSheetLendingRelatedCommitmentsWholesaleContractualAmountNetOfRiskParticipationsOtherUnfundedCommitmentsToExtendCredit" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_OffBalanceSheetLendingRelatedCommitmentsWholesaleContractualAmountNetOfRiskParticipationsStandbyLettersOfCreditAndOtherFinancialGuarantees" name="OffBalanceSheetLendingRelatedCommitmentsWholesaleContractualAmountNetOfRiskParticipationsStandbyLettersOfCreditAndOtherFinancialGuarantees" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_OffBalanceSheetLendingRelatedFinancialCommitmentsContractualAmount" name="OffBalanceSheetLendingRelatedFinancialCommitmentsContractualAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_OffBalanceSheetLendingRelatedFinancialCommitmentsContractualAmountEvaluatedAtAssetSpecificImpairmentMethodology" name="OffBalanceSheetLendingRelatedFinancialCommitmentsContractualAmountEvaluatedAtAssetSpecificImpairmentMethodology" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_OffBalanceSheetLendingRelatedFinancialCommitmentsContractualAmountEvaluatedAtFormulaBasedImpairmentMethodology" name="OffBalanceSheetLendingRelatedFinancialCommitmentsContractualAmountEvaluatedAtFormulaBasedImpairmentMethodology" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_OffBalanceSheetLendingRelatedFinancialInstrumentsAndGuaranteesAndOtherCommitmentsTableTextBlock" name="OffBalanceSheetLendingRelatedFinancialInstrumentsAndGuaranteesAndOtherCommitmentsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OffBalanceSheetLendingRelatedFinancialInstrumentsCarryingValue" name="OffBalanceSheetLendingRelatedFinancialInstrumentsCarryingValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_OffBalanceSheetLendingRelatedFinancialInstrumentsContractualAmount" name="OffBalanceSheetLendingRelatedFinancialInstrumentsContractualAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsAbstract" name="OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsExpirationPeriodsAxis" name="OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsExpirationPeriodsAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsExpirationPeriodsDomain" name="OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsExpirationPeriodsDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsSupplementalInformationAbstract" name="OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsSupplementalInformationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsTextBlock" name="OffBalanceSheetLendingRelatedFinancialInstrumentsGuaranteesAndOtherCommitmentsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OffBalanceSheetRiskWeightedAssets" name="OffBalanceSheetRiskWeightedAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_OhioMember" name="OhioMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OilAndGasCreditRiskConcentrationMember" name="OilAndGasCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OperatingLeasesFutureMinimumPaymentsDueNetOfFutureMinimumSubleaseRentals" name="OperatingLeasesFutureMinimumPaymentsDueNetOfFutureMinimumSubleaseRentals" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_OperatingRevenueAbstract" name="OperatingRevenueAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OperatingRevenueNetRealEstateMortgages" name="OperatingRevenueNetRealEstateMortgages" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OperatingRevenueRealEstateMortgagesChangesInFairValueOfMortgageServicingRightsModeledServicingPortfolioRunoff" name="OperatingRevenueRealEstateMortgagesChangesInFairValueOfMortgageServicingRightsModeledServicingPortfolioRunoff" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OperatingRevenueServicingFeesNetRealEstateMortgages" name="OperatingRevenueServicingFeesNetRealEstateMortgages" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OptionAdjustableRateMortgageLitigationMember" name="OptionAdjustableRateMortgageLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OptionAdjustedSpreadAssumptionUsedToValueMortgageServicingRightsMember" name="OptionAdjustedSpreadAssumptionUsedToValueMortgageServicingRightsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OptionArmsMember" name="OptionArmsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OptionPricingValuationTechniqueMember" name="OptionPricingValuationTechniqueMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherAvailableForSaleSecuritiesMember" name="OtherAvailableForSaleSecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherBusinessEventsDisclousreLineItems" name="OtherBusinessEventsDisclousreLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherBusinessEventsDisclousreTable" name="OtherBusinessEventsDisclousreTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherComprehensiveIncomeForeignCurrencyHedgesGainLossAfterTax" name="OtherComprehensiveIncomeForeignCurrencyHedgesGainLossAfterTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherComprehensiveIncomeForeignCurrencyHedgesGainLossArisingDuringPeriodTax" name="OtherComprehensiveIncomeForeignCurrencyHedgesGainLossArisingDuringPeriodTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherComprehensiveIncomeForeignCurrencyHedgesGainLossBeforeTax" name="OtherComprehensiveIncomeForeignCurrencyHedgesGainLossBeforeTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherComprehensiveIncomeForeignCurrencyTranslationGainLossAfterTax" name="OtherComprehensiveIncomeForeignCurrencyTranslationGainLossAfterTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherComprehensiveIncomeForeignCurrencyTranslationGainLossBeforeTax" name="OtherComprehensiveIncomeForeignCurrencyTranslationGainLossBeforeTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherComprehensiveIncomeLossAvailableForSaleSecuritiesAdjustmentIncludingOttiBeforeTax" name="OtherComprehensiveIncomeLossAvailableForSaleSecuritiesAdjustmentIncludingOttiBeforeTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherComprehensiveIncomeLossAvailableForSaleSecuritiesAdjustmentIncludingOttiTax" name="OtherComprehensiveIncomeLossAvailableForSaleSecuritiesAdjustmentIncludingOttiTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherComprehensiveIncomeLossForeignCurrencyExchangeRateAndOtherChangesBeforeTax" name="OtherComprehensiveIncomeLossForeignCurrencyExchangeRateAndOtherChangesBeforeTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherComprehensiveIncomeLossForeignCurrencyExchangeRateAndOtherChangesNetOfTax" name="OtherComprehensiveIncomeLossForeignCurrencyExchangeRateAndOtherChangesNetOfTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherComprehensiveIncomeLossForeignCurrencyExchangeRateAndOtherChangesTax" name="OtherComprehensiveIncomeLossForeignCurrencyExchangeRateAndOtherChangesTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherConsumerMember" name="OtherConsumerMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherCreditCardRelatedIntangiblesMember" name="OtherCreditCardRelatedIntangiblesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherCreditRiskConcentrationMember" name="OtherCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherDebtAndEquityInstrumentsMember" name="OtherDebtAndEquityInstrumentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherDefinedBenefitPensionPlansNetPeriodicBenefitCostImmaterialPlans" name="OtherDefinedBenefitPensionPlansNetPeriodicBenefitCostImmaterialPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherExpensesAdditionalDetailsAbstract" name="OtherExpensesAdditionalDetailsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherGuaranteesAndCommitmentsMember" name="OtherGuaranteesAndCommitmentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherIntangibleAssetsAbstract" name="OtherIntangibleAssetsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherIntangibleAssetsAccumulatedAmortization" name="OtherIntangibleAssetsAccumulatedAmortization" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_OtherIntangibleAssetsExcludedFromTierOneRiskBasedCapital" name="OtherIntangibleAssetsExcludedFromTierOneRiskBasedCapital" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_OtherIntangibleAssetsGross" name="OtherIntangibleAssetsGross" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_OtherIntangiblesMember" name="OtherIntangiblesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherLettersOfCreditCollateralHeld" name="OtherLettersOfCreditCollateralHeld" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_OtherLettersOfCreditMember" name="OtherLettersOfCreditMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherPostretirementBenefitPlanAmountOfFundingWithCorporateOwnedLifeInsurancePoliciesClassifiedWithinLevelThreeOfValuationHierarchy" name="OtherPostretirementBenefitPlanAmountOfFundingWithCorporateOwnedLifeInsurancePoliciesClassifiedWithinLevelThreeOfValuationHierarchy" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_OtherPostretirementBenefitPlansExcludingPrescriptionDrugBenefitMember" name="OtherPostretirementBenefitPlansExcludingPrescriptionDrugBenefitMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherPostretirementBenefitPlansIncludingPrescriptionDrugBenefitMember" name="OtherPostretirementBenefitPlansIncludingPrescriptionDrugBenefitMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherThanTemporaryImpairmentLossesAbstract" name="OtherThanTemporaryImpairmentLossesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_OtherThanTemporaryImpairmentLossesInvestmentsPortionPreviouslyRecognizedInEarningsIntendsToSellNet" name="OtherThanTemporaryImpairmentLossesInvestmentsPortionPreviouslyRecognizedInEarningsIntendsToSellNet" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OtherUnfundedCommitmentsToExtendCreditMember" name="OtherUnfundedCommitmentsToExtendCreditMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OutstandingTrustPreferredDebtSecurityIssuanceAbstract" name="OutstandingTrustPreferredDebtSecurityIssuanceAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OverdraftFeeDebitPostingOrderLitigationMember" name="OverdraftFeeDebitPostingOrderLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_OverfundedPlansMember" name="OverfundedPlansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ParticipationRightsUnderParticipatingAnnuityContractsMember" name="ParticipationRightsUnderParticipatingAnnuityContractsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PaymentsForRepurchaseOfCommonStockAndWarrants" name="PaymentsForRepurchaseOfCommonStockAndWarrants" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PennsylvaniaMember" name="PennsylvaniaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PercentAxis" name="PercentAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PercentDomain" name="PercentDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PercentOfRetainedSecuritizationInterestsFairValueRatedOrBetter" name="PercentOfRetainedSecuritizationInterestsFairValueRatedOrBetter" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_PercentageOfCashOrLiquidCollateralRelativeToValueOfSecuritiesOnLoan" name="PercentageOfCashOrLiquidCollateralRelativeToValueOfSecuritiesOnLoan" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PercentageOfCriticizedLoansToTotalRetainedLoans" name="PercentageOfCriticizedLoansToTotalRetainedLoans" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_PercentageOfDeclineInHomePricesAssumption" name="PercentageOfDeclineInHomePricesAssumption" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PercentageOfDeclineInHomePricesAssumptionBeyondFirmsCurrentAssumptions" name="PercentageOfDeclineInHomePricesAssumptionBeyondFirmsCurrentAssumptions" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PercentageOfLevel3AssetsToTotalAssets" name="PercentageOfLevel3AssetsToTotalAssets" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_PercentageOfLoansNinetyPlusDaysPastDueToTotalRetainedLoans" name="PercentageOfLoansNinetyPlusDaysPastDueToTotalRetainedLoans" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_PercentageOfLoansOnNonaccrualStatusToTotalRetainedLoans" name="PercentageOfLoansOnNonaccrualStatusToTotalRetainedLoans" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_PercentageOfLoansThirtyPlusDaysPastDueToTotalRetainedLoans" name="PercentageOfLoansThirtyPlusDaysPastDueToTotalRetainedLoans" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_PercentageOfLoansThirtyPlusDaysPastDueToTotalUnpaidPrincipalBalance" name="PercentageOfLoansThirtyPlusDaysPastDueToTotalUnpaidPrincipalBalance" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_PercentageOfLoansWithFicoScoresLessThan660ToTotalRetainedLoans" name="PercentageOfLoansWithFicoScoresLessThan660ToTotalRetainedLoans" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_PercentageOfLoansWithFicoScoresOf660OrGreaterToTotalRetainedLoans" name="PercentageOfLoansWithFicoScoresOf660OrGreaterToTotalRetainedLoans" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_PercentageOfNetChargeOffsToTotalRetainedLoans" name="PercentageOfNetChargeOffsToTotalRetainedLoans" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PercentageOfPrincipalBalanceInsuredAndInterestGuaranteed" name="PercentageOfPrincipalBalanceInsuredAndInterestGuaranteed" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PercentageOfSeniorLiensToTotalFinancingReceivables" name="PercentageOfSeniorLiensToTotalFinancingReceivables" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_PercentageOfVotingRightsMbsTrustCertificateholders" name="PercentageOfVotingRightsMbsTrustCertificateholders" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PercentageSumOfItemsByTypeMayExceed" name="PercentageSumOfItemsByTypeMayExceed" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PeriodFromEndOfOptOutPeriod" name="PeriodFromEndOfOptOutPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PeriodMarketBasedRateAuthoritySuspended" name="PeriodMarketBasedRateAuthoritySuspended" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PeriodOfTimeUsedToProjectLongTermReturnsForDefinedBenefitPensionAndOpebPlans" name="PeriodOfTimeUsedToProjectLongTermReturnsForDefinedBenefitPensionAndOpebPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PeriodOverWhichFirmUsesCalculatedValueThatRecognizesChangesInFairValueToDetermineExpectedReturnOnPlanAssets" name="PeriodOverWhichFirmUsesCalculatedValueThatRecognizesChangesInFairValueToDetermineExpectedReturnOnPlanAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PeriodOverWhichVestingIsDeferred" name="PeriodOverWhichVestingIsDeferred" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PeriodPastDueCreditAnalysisFactorsChargeOffCriteria" name="PeriodPastDueCreditAnalysisFactorsChargeOffCriteria" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PermanentModificationMember" name="PermanentModificationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PettersBankruptcyAndRelatedMattersMember" name="PettersBankruptcyAndRelatedMattersMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PlaintiffBankOfAmericaMember" name="PlaintiffBankOfAmericaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PlaintiffDeutscheBankNationalTrustCompanyMember" name="PlaintiffDeutscheBankNationalTrustCompanyMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PlaintiffOneInsurerWithinMunicipalDerivativesInvestigationsAndLitigationMember" name="PlaintiffOneInsurerWithinMunicipalDerivativesInvestigationsAndLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PlaintiffOtherInsurerWithinMunicipalDerivativesInvestigationsAndLitigationMember" name="PlaintiffOtherInsurerWithinMunicipalDerivativesInvestigationsAndLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PlaintiffTrusteeForLiquidationOfBernardLMadoffInvestmentSecuritiesLlcMember" name="PlaintiffTrusteeForLiquidationOfBernardLMadoffInvestmentSecuritiesLlcMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PlanAssetCategoriesMember" name="PlanAssetCategoriesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PlanAssetCategoriesOtherMember" name="PlanAssetCategoriesOtherMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PossibilitiesDependingOnFactorsSpecifiedInRegulationsIssuedByFederalReserveAndOccAxis" name="PossibilitiesDependingOnFactorsSpecifiedInRegulationsIssuedByFederalReserveAndOccAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PossibilitiesDependingOnFactorsSpecifiedInRegulationsIssuedByFederalReserveAndOccDomain" name="PossibilitiesDependingOnFactorsSpecifiedInRegulationsIssuedByFederalReserveAndOccDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PossibilityOneMember" name="PossibilityOneMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PossibilityTwoMember" name="PossibilityTwoMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PreferredStockLiquidationPreferencePercentage" name="PreferredStockLiquidationPreferencePercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PrepaymentModelUsedToValueMortgageServicingRightsMember" name="PrepaymentModelUsedToValueMortgageServicingRightsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PriceValuationTechniqueMember" name="PriceValuationTechniqueMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PrimeAndAltAMember" name="PrimeAndAltAMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PrimeResidentialMortgageMember" name="PrimeResidentialMortgageMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PrincipalAmountOfFinancialAssetsSoldInSecuritizations" name="PrincipalAmountOfFinancialAssetsSoldInSecuritizations" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PrincipalProtectedDebtMember" name="PrincipalProtectedDebtMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PrivateEquityDirectInvestmentsMember" name="PrivateEquityDirectInvestmentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PrivateEquityFundInvestmentsMember" name="PrivateEquityFundInvestmentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PrivateLabelResecuritizationsMember" name="PrivateLabelResecuritizationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProceedsFromSaleOfMortgageLoans" name="ProceedsFromSaleOfMortgageLoans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProceedsFromSalesSecuritizationsAndPaydownsOfLoansHeldForSale" name="ProceedsFromSalesSecuritizationsAndPaydownsOfLoansHeldForSale" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProceedsFromSecuritiesSoldAsPercentageOfAmortizedCost" name="ProceedsFromSecuritiesSoldAsPercentageOfAmortizedCost" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProfitFromTransactionForfeited" name="ProfitFromTransactionForfeited" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_ProgramWideCreditEnhancementRequiredAmount" name="ProgramWideCreditEnhancementRequiredAmount" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProjectedIncreaseInNonUsDefinedBenefitPensionAndOpebPlanExpenseResultingFrom25BasisPointDeclineInDiscountRateForNonUsPlans" name="ProjectedIncreaseInNonUsDefinedBenefitPensionAndOpebPlanExpenseResultingFrom25BasisPointDeclineInDiscountRateForNonUsPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProjectedIncreaseInUsDefinedBenefitPensionAndOpebBenefitObligationsResultingFrom25BasisPointDeclineInDiscountRateForUsPlans" name="ProjectedIncreaseInUsDefinedBenefitPensionAndOpebBenefitObligationsResultingFrom25BasisPointDeclineInDiscountRateForUsPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProjectedIncreaseInUsDefinedBenefitPensionAndOpebPlanExpenseResultingFrom25BasisPointDeclineInDiscountRateForUsPlans" name="ProjectedIncreaseInUsDefinedBenefitPensionAndOpebPlanExpenseResultingFrom25BasisPointDeclineInDiscountRateForUsPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProjectedIncreaseInUsDefinedBenefitPensionAndOpebPlanExpenseResultingFrom25BasisPointDeclineInExpectedLongTermRateOfReturnOnUsPlanAssets" name="ProjectedIncreaseInUsDefinedBenefitPensionAndOpebPlanExpenseResultingFrom25BasisPointDeclineInExpectedLongTermRateOfReturnOnUsPlanAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProjectedIncreaseInUsDefinedBenefitPensionAndOpebPlanExpenseResultingFromDecreasedDiscountRatesForBenefitObligations" name="ProjectedIncreaseInUsDefinedBenefitPensionAndOpebPlanExpenseResultingFromDecreasedDiscountRatesForBenefitObligations" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProjectedIncreaseInUsDefinedBenefitPensionBenefitObligationsResultingFrom25BasisBointIncreaseInInterestCreditingRateForUsDefinedBenefitPensionPlan" name="ProjectedIncreaseInUsDefinedBenefitPensionBenefitObligationsResultingFrom25BasisBointIncreaseInInterestCreditingRateForUsDefinedBenefitPensionPlan" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProjectedIncreaseInUsDefinedBenefitPensionPlanExpenseResultingFrom25BasisPointIncreaseInInterestCreditingRateForUsPlans" name="ProjectedIncreaseInUsDefinedBenefitPensionPlanExpenseResultingFrom25BasisPointIncreaseInInterestCreditingRateForUsPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ProtectionSoldCreditDerivativesAndCreditRelatedNotesAbstract" name="ProtectionSoldCreditDerivativesAndCreditRelatedNotesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ProtectionSoldCreditDerivativesAndCreditRelatedNotesMoreThanFiveYears" name="ProtectionSoldCreditDerivativesAndCreditRelatedNotesMoreThanFiveYears" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_ProtectionSoldCreditDerivativesAndCreditRelatedNotesRatingsFromOneToFiveYears" name="ProtectionSoldCreditDerivativesAndCreditRelatedNotesRatingsFromOneToFiveYears" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_ProtectionSoldCreditDerivativesAndCreditRelatedNotesRatingsLessThanOneYear" name="ProtectionSoldCreditDerivativesAndCreditRelatedNotesRatingsLessThanOneYear" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_ProtectionSoldCreditDerivativesAndCreditRelatedNotesRatingsMaturityProfileTableTextBlock" name="ProtectionSoldCreditDerivativesAndCreditRelatedNotesRatingsMaturityProfileTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurchasedCreditCardRelationshipsMember" name="PurchasedCreditCardRelationshipsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurchasedCreditImpairedHomeEquityJuniorLienExcludingLinesOfCreditMember" name="PurchasedCreditImpairedHomeEquityJuniorLienExcludingLinesOfCreditMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurchasedCreditImpairedHomeEquityJuniorLienLinesOfCreditMember" name="PurchasedCreditImpairedHomeEquityJuniorLienLinesOfCreditMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurchasedCreditImpairedHomeEquityJuniorLienMember" name="PurchasedCreditImpairedHomeEquityJuniorLienMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurchasedCreditImpairedHomeEquityMember" name="PurchasedCreditImpairedHomeEquityMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurchasedCreditImpairedHomeEquitySeniorLienMember" name="PurchasedCreditImpairedHomeEquitySeniorLienMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurchasedCreditImpairedMember" name="PurchasedCreditImpairedMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurchasedCreditImpairedOptionArmsMember" name="PurchasedCreditImpairedOptionArmsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurchasedCreditImpairedPrimeMortgageMember" name="PurchasedCreditImpairedPrimeMortgageMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurchasedCreditImpairedSubprimeMortgageMember" name="PurchasedCreditImpairedSubprimeMortgageMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_PurportedClassActionMember" name="PurportedClassActionMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_PutableFloatingRateCertificatesOfMunicipalBondVehicles" name="PutableFloatingRateCertificatesOfMunicipalBondVehicles" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_PutableFloatingRateCertificatesOfMunicipalBondVehiclesHeldDuringPeriodPercent" name="PutableFloatingRateCertificatesOfMunicipalBondVehiclesHeldDuringPeriodPercent" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_QuantitativeInformationVariableInterestEntityPrincipalAmountOutstanding" name="QuantitativeInformationVariableInterestEntityPrincipalAmountOutstanding" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_RateOfDefaultForModifiedLoansEstimatedWeightedAverage" name="RateOfDefaultForModifiedLoansEstimatedWeightedAverage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_RatingsProfileOfVIEsAssetsTextBlock" name="RatingsProfileOfVIEsAssetsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RbsSempraMember" name="RbsSempraMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ReSecuritizationsMember" name="ReSecuritizationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RealAssetsMember" name="RealAssetsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RealEstateCreditRiskConcentrationMember" name="RealEstateCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RealEstateRealAssetsAndPrivateEquityMember" name="RealEstateRealAssetsAndPrivateEquityMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ReceivablesFromCustomersCreditRiskConcentrationMember" name="ReceivablesFromCustomersCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ReconciliationOfFirmsTotalStockholdersEquityToTier1CapitalAndTotalQualifyingCapitalAbstract" name="ReconciliationOfFirmsTotalStockholdersEquityToTier1CapitalAndTotalQualifyingCapitalAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ReconciliationOfTotalStockholdersEquityToTierOneCapitalAndTotalQualifyingCapitalTableTextBlock" name="ReconciliationOfTotalStockholdersEquityToTierOneCapitalAndTotalQualifyingCapitalTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_RedeemableLongTermDebt" name="RedeemableLongTermDebt" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_ReductionInLevelThreeDerivativeReceivableAndDerivativePayableBalances" name="ReductionInLevelThreeDerivativeReceivableAndDerivativePayableBalances" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_ReductionsAbstract" name="ReductionsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_RefinancingAndBorrowerReliefTargetToBeMetWithinThreeYears" name="RefinancingAndBorrowerReliefTargetToBeMetWithinThreeYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_RefinancingAndBorrowerReliefTargetToBeMetWithinTwoYears" name="RefinancingAndBorrowerReliefTargetToBeMetWithinTwoYears" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RefreshedFicoScoresEqualToOrGreaterThan660Member" name="RefreshedFicoScoresEqualToOrGreaterThan660Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RefreshedFicoScoresLessThan660Member" name="RefreshedFicoScoresLessThan660Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RegulatoryCapitalAssetsAndRiskBasedRatiosSupplementalInformationAbstract" name="RegulatoryCapitalAssetsAndRiskBasedRatiosSupplementalInformationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RegulatoryGuidanceRegardingChapter7LoansMember" name="RegulatoryGuidanceRegardingChapter7LoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RegulatoryGuidanceRegardingJuniorLiensSubordinateToSeniorLiensMember" name="RegulatoryGuidanceRegardingJuniorLiensSubordinateToSeniorLiensMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RegulatoryGuidanceRegardingPrimeIncludingOptionArmMortgagesMember" name="RegulatoryGuidanceRegardingPrimeIncludingOptionArmMortgagesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ReissuanceFromTrust" name="ReissuanceFromTrust" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_RepurchaseLosses" name="RepurchaseLosses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RepurchaseMakeWholeSettlementsMember" name="RepurchaseMakeWholeSettlementsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RepurchaseProgramAuthorizedPeriodOf2011Member" name="RepurchaseProgramAuthorizedPeriodOf2011Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RepurchaseProgramAuthorizedPeriodOf2012Member" name="RepurchaseProgramAuthorizedPeriodOf2012Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RepurchaseProgramAuthorizedPeriodOfFirstQuarterOf2013Member" name="RepurchaseProgramAuthorizedPeriodOfFirstQuarterOf2013Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ReserveBalancesDepositedWithFederalReserveBanksAbstract" name="ReserveBalancesDepositedWithFederalReserveBanksAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ResidentialConformingMortgageIntendedForSaleToGovernmentAgencyMember" name="ResidentialConformingMortgageIntendedForSaleToGovernmentAgencyMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ResidentialMortgageBackedSecuritiesAndLoansMember" name="ResidentialMortgageBackedSecuritiesAndLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ResidentialRealEstateAndAutoLoansMember" name="ResidentialRealEstateAndAutoLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ResidentialRealEstateExcludingPurchasedCreditImpairedMember" name="ResidentialRealEstateExcludingPurchasedCreditImpairedMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ResidentialRealEstateNonModifiedCreditCardAndScoredBusinessBankingLoansMember" name="ResidentialRealEstateNonModifiedCreditCardAndScoredBusinessBankingLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RestrictionsOnCashAndIntercompanyFundsTransfersDisclosureAbstract" name="RestrictionsOnCashAndIntercompanyFundsTransfersDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RestrictionsOnCashAndIntercompanyFundsTransfersLineItems" name="RestrictionsOnCashAndIntercompanyFundsTransfersLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RestrictionsOnCashAndIntercompanyFundsTransfersTable" name="RestrictionsOnCashAndIntercompanyFundsTransfersTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_RestrictionsOnCashAndIntercompanyFundsTransfersTextBlock" name="RestrictionsOnCashAndIntercompanyFundsTransfersTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RetailAndConsumerServicesCreditRiskConcentrationMember" name="RetailAndConsumerServicesCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RetainedLoansPurchasesSalesAndTransferIntoHeldForSaleByPortfolioSegmentAbstract" name="RetainedLoansPurchasesSalesAndTransferIntoHeldForSaleByPortfolioSegmentAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RetainedLoansPurchasesSalesAndTransferIntoHeldForSaleByPortfolioSegmentLineItems" name="RetainedLoansPurchasesSalesAndTransferIntoHeldForSaleByPortfolioSegmentLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ReverseMortgageMember" name="ReverseMortgageMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RiskManagementAbstract" name="RiskManagementAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_RiskManagementActivitiesMember" name="RiskManagementActivitiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_RiskManagementRevenueChangesInFairValueOfMortgageServicingRightsChangesInMarketInterestRates" name="RiskManagementRevenueChangesInFairValueOfMortgageServicingRightsChangesInMarketInterestRates" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_RiskManagementRevenueChangesInFairValueOfMortgageServicingRightsOtherChangesInFairValue" name="RiskManagementRevenueChangesInFairValueOfMortgageServicingRightsOtherChangesInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_RiskManagementRevenueNetRealEstateMortgages" name="RiskManagementRevenueNetRealEstateMortgages" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfAccountsPayableAndOtherLiabilitiesTable" name="ScheduleOfAccountsPayableAndOtherLiabilitiesTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfCashReceivedFromExerciseOfStockOptionsUnderAllStockBasedIncentiveArrangementsAndActualIncomeTaxBenefitRealizedRelatedToTaxDeductionsFromExerciseOfStockOptionsAbstract" name="ScheduleOfCashReceivedFromExerciseOfStockOptionsUnderAllStockBasedIncentiveArrangementsAndActualIncomeTaxBenefitRealizedRelatedToTaxDeductionsFromExerciseOfStockOptionsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfCertainLoansAcquiredInTransferNotAccountedForAsDebtSecuritiesAccretableYieldMovementTable" name="ScheduleOfCertainLoansAcquiredInTransferNotAccountedForAsDebtSecuritiesAccretableYieldMovementTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfComponentsOfIncomeTaxExpenseBenefitTable" name="ScheduleOfComponentsOfIncomeTaxExpenseBenefitTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ScheduleOfCondensedFinancialInformationOfParentCompanyOnlyTableTextBlock" name="ScheduleOfCondensedFinancialInformationOfParentCompanyOnlyTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfCondensedFinancialStatementsAbstract" name="ScheduleOfCondensedFinancialStatementsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfDeferredTaxAssetsAndLiabilitiesTable" name="ScheduleOfDeferredTaxAssetsAndLiabilitiesTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ScheduleOfDefinedBenefitPlanActualReturnOnPlanAssetsTableTextBlock" name="ScheduleOfDefinedBenefitPlanActualReturnOnPlanAssetsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ScheduleOfDefinedBenefitPlanNetPeriodicBenefitCostTableTextBlock" name="ScheduleOfDefinedBenefitPlanNetPeriodicBenefitCostTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfDerivativeFairValueHedgesGainsLossesIncludedInIncomeByContractTypeTable" name="ScheduleOfDerivativeFairValueHedgesGainsLossesIncludedInIncomeByContractTypeTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfEarningsPerShareBasicAndDilutedTable" name="ScheduleOfEarningsPerShareBasicAndDilutedTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfEffectiveIncomeTaxRateReconciliationTable" name="ScheduleOfEffectiveIncomeTaxRateReconciliationTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfIncomeBeforeIncomeTaxDomesticAndForeignTable" name="ScheduleOfIncomeBeforeIncomeTaxDomesticAndForeignTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfIntangibleAssetsByMajorClassTable" name="ScheduleOfIntangibleAssetsByMajorClassTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfLoansByPortfolioSegmentAndClassTable" name="ScheduleOfLoansByPortfolioSegmentAndClassTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ScheduleOfLoansByPortfolioSegmentAndClassTableTextBlock" name="ScheduleOfLoansByPortfolioSegmentAndClassTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfMortgageFeesAndRelatedIncomeLineItems" name="ScheduleOfMortgageFeesAndRelatedIncomeLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfMortgageFeesAndRelatedIncomeTable" name="ScheduleOfMortgageFeesAndRelatedIncomeTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfNetGainsLossesOnLoanSalesByPortfolioSegmentTable" name="ScheduleOfNetGainsLossesOnLoanSalesByPortfolioSegmentTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ScheduleOfNetGainsLossesOnLoanSalesByPortfolioSegmentTableTextBlock" name="ScheduleOfNetGainsLossesOnLoanSalesByPortfolioSegmentTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfNonInterestExpenseTable" name="ScheduleOfNonInterestExpenseTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfNonInterestRevenueLineItems" name="ScheduleOfNonInterestRevenueLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfNonInterestRevenueTable" name="ScheduleOfNonInterestRevenueTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfRetainedLoansPurchasesSalesAndTransferIntoHeldForSaleByPortfolioSegmentTable" name="ScheduleOfRetainedLoansPurchasesSalesAndTransferIntoHeldForSaleByPortfolioSegmentTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ScheduleOfRetainedLoansPurchasesSalesAndTransferIntoHeldForSaleByPortfolioSegmentTableTextBlock" name="ScheduleOfRetainedLoansPurchasesSalesAndTransferIntoHeldForSaleByPortfolioSegmentTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ScheduleOfSelectedRegulatoryCapitalDisclosuresTableTextBlock" name="ScheduleOfSelectedRegulatoryCapitalDisclosuresTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ScheduleOfSensitivityAnalysisOfFairValueOfInterestsContinuedToBeHeldByTransferorExcludingServicingAssetsOrServicingLiabilitiesTextBlock" name="ScheduleOfSensitivityAnalysisOfFairValueOfInterestsContinuedToBeHeldByTransferorExcludingServicingAssetsOrServicingLiabilitiesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScheduleOfUnrecognizedTaxBenefitsTable" name="ScheduleOfUnrecognizedTaxBenefitsTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ScoredAutoAndBusinessBankingLoansMember" name="ScoredAutoAndBusinessBankingLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SecondFiftyPercentMember" name="SecondFiftyPercentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SecuritiesFinancingActivitiesTablesAbstract" name="SecuritiesFinancingActivitiesTablesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_SecuritiesImpairmentTableTextBlock" name="SecuritiesImpairmentTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SecuritiesLendingIndemnificationsMember" name="SecuritiesLendingIndemnificationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SecuritiesLendingLitigationRelatedToMediumTermNotesOfLehmanBrothersMember" name="SecuritiesLendingLitigationRelatedToMediumTermNotesOfLehmanBrothersMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SecuritiesMember" name="SecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_SecuritiesProceedsFromSaleOfMortgageLoans" name="SecuritiesProceedsFromSaleOfMortgageLoans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_SecuritiesTextBlock" name="SecuritiesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_SecuritiesTransferredToAgencyResecuritizationVies" name="SecuritiesTransferredToAgencyResecuritizationVies" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_SecuritiesTransferredToPrivateLabelResecuritizationVies" name="SecuritiesTransferredToPrivateLabelResecuritizationVies" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_SecuritizationActivitiesTextBlock" name="SecuritizationActivitiesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SecuritizationActivityAbstract" name="SecuritizationActivityAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_SecuritizedLoansInWhichFirmHasNoContinuingInvolvement" name="SecuritizedLoansInWhichFirmHasNoContinuingInvolvement" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_SecuritizedLoansMember" name="SecuritizedLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SegmentGeographicalCountriesOtherThanEntitysCountryOfDomicileMember" name="SegmentGeographicalCountriesOtherThanEntitysCountryOfDomicileMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SegmentGeographicalEntitysCountryOfDomicileMember" name="SegmentGeographicalEntitysCountryOfDomicileMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SegmentManagedResultsTaxEquivalentAdjustmentAbstract" name="SegmentManagedResultsTaxEquivalentAdjustmentAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_SegmentReportingInformationAverageCommonEquity" name="SegmentReportingInformationAverageCommonEquity" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_SegmentReportingInformationOverheadRatio" name="SegmentReportingInformationOverheadRatio" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_SegmentReportingInformationReturnOnAverageCommonEquity" name="SegmentReportingInformationReturnOnAverageCommonEquity" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SegmentResultsAndReconciliationAbstract" name="SegmentResultsAndReconciliationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SeniorSecuritiesMember" name="SeniorSecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_SeniorSecuritiesPurchasedExcludedFromInterestsContinuedToBeHeldByTransferorFairValue" name="SeniorSecuritiesPurchasedExcludedFromInterestsContinuedToBeHeldByTransferorFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_SensitivityAnalysisOfFvOfInterestsContinuedToBeHeldByTransferorImpactOf10AdverseChangeInDiscountRateAndExpectedCreditLosses" name="SensitivityAnalysisOfFvOfInterestsContinuedToBeHeldByTransferorImpactOf10AdverseChangeInDiscountRateAndExpectedCreditLosses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_SensitivityAnalysisOfFvOfInterestsContinuedToBeHeldByTransferorImpactOf20AdverseChangeInDiscountRateAndExpectedCreditLosses" name="SensitivityAnalysisOfFvOfInterestsContinuedToBeHeldByTransferorImpactOf20AdverseChangeInDiscountRateAndExpectedCreditLosses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_ServicerAdvancesOnMortgageLoans" name="ServicerAdvancesOnMortgageLoans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_ServicingAssetAtFairValueAdditionsOriginations" name="ServicingAssetAtFairValueAdditionsOriginations" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ServicingAssetAtFairValueAdditionsPurchases" name="ServicingAssetAtFairValueAdditionsPurchases" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ServicingAssetAtFairValueChangeInOptionAdjustedSpreadAssumptions" name="ServicingAssetAtFairValueChangeInOptionAdjustedSpreadAssumptions" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ServicingAssetAtFairValueChangesInFairValueResultingFromChangesInCostToServiceAssumptions" name="ServicingAssetAtFairValueChangesInFairValueResultingFromChangesInCostToServiceAssumptions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ServicingAssetAtFairValueChangesInFairValueResultingFromChangesInMarketInterestRates" name="ServicingAssetAtFairValueChangesInFairValueResultingFromChangesInMarketInterestRates" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ServicingAssetAtFairValueChangesInFairValueResultingFromModeledServicingPortfolioRunoff" name="ServicingAssetAtFairValueChangesInFairValueResultingFromModeledServicingPortfolioRunoff" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ServicingAssetAtFairValueOtherChangesInFairValueResultingFromChangesInValuationInputsOrChangesInAssumptions" name="ServicingAssetAtFairValueOtherChangesInFairValueResultingFromChangesInValuationInputsOrChangesInAssumptions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ServicingRevenueNetRealEstateMortgages" name="ServicingRevenueNetRealEstateMortgages" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SettlementOfLitigationAdditionalReliefProvidedToCertainBorrowersMember" name="SettlementOfLitigationAdditionalReliefProvidedToCertainBorrowersMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SettlementOfLitigationCashPaymentsMember" name="SettlementOfLitigationCashPaymentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SettlementOfLitigationRefinancingReliefProvidedToCertainBorrowersMember" name="SettlementOfLitigationRefinancingReliefProvidedToCertainBorrowersMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ShareBasedCompensationAmortizationOfPriorGrantsOfShareBasedCompensation" name="ShareBasedCompensationAmortizationOfPriorGrantsOfShareBasedCompensation" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardExpirationPeriod" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardExpirationPeriod" nillable="true" substitutionGroup="xbrli:item" type="us-types:durationStringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ShareBasedCompensationEstimatedFutureGrantsToRetirementEligibleEmployees" name="ShareBasedCompensationEstimatedFutureGrantsToRetirementEligibleEmployees" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ShareRepurchasesRelatedToEmployeeStockBasedCompensationAwards" name="ShareRepurchasesRelatedToEmployeeStockBasedCompensationAwards" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_ShareRepurchasesRelatedToEmployeeStockBasedCompensationAwardsShares" name="ShareRepurchasesRelatedToEmployeeStockBasedCompensationAwardsShares" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ShortTermCreditCardLoanModificationProgramsMember" name="ShortTermCreditCardLoanModificationProgramsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SingleNotchDowngradeMember" name="SingleNotchDowngradeMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SpotFuturesAndForwardsMember" name="SpotFuturesAndForwardsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_StandbyAndOtherLettersOfCreditAllowance" name="StandbyAndOtherLettersOfCreditAllowance" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_StandbyAndOtherLettersOfCreditCarryingValue" name="StandbyAndOtherLettersOfCreditCarryingValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_StandbyAndOtherLettersOfCreditMember" name="StandbyAndOtherLettersOfCreditMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_StandbyLettersOfCreditAndOtherFinancialGuaranteesAndOtherLettersOfCreditAbstract" name="StandbyLettersOfCreditAndOtherFinancialGuaranteesAndOtherLettersOfCreditAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_StandbyLettersOfCreditAndOtherFinancialGuaranteesAndOtherLettersOfCreditTableTextBlock" name="StandbyLettersOfCreditAndOtherFinancialGuaranteesAndOtherLettersOfCreditTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_StandbyLettersOfCreditAndOtherFinancialGuaranteesInternalCreditRatingInvestmentGrade" name="StandbyLettersOfCreditAndOtherFinancialGuaranteesInternalCreditRatingInvestmentGrade" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_StandbyLettersOfCreditAndOtherFinancialGuaranteesInternalCreditRatingNonInvestmentGrade" name="StandbyLettersOfCreditAndOtherFinancialGuaranteesInternalCreditRatingNonInvestmentGrade" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_StandbyLettersOfCreditAndOtherFinancialGuaranteesMember" name="StandbyLettersOfCreditAndOtherFinancialGuaranteesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_StandbyLettersOfCreditCollateralHeld" name="StandbyLettersOfCreditCollateralHeld" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_StandbyLettersOfCreditUnissuedCommitments" name="StandbyLettersOfCreditUnissuedCommitments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_StateAndMunicipalGovernmentsCreditRiskConcentrationMember" name="StateAndMunicipalGovernmentsCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_StaticStructureMember" name="StaticStructureMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_StockIssuedDuringPeriodSharesTreasuryStockReissuedIncludingEmployeeStockPurchaseAndEquityBasedCompensationPlans" name="StockIssuedDuringPeriodSharesTreasuryStockReissuedIncludingEmployeeStockPurchaseAndEquityBasedCompensationPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_StructuredNotesBalanceUnderFairValueOption" name="StructuredNotesBalanceUnderFairValueOption" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_StudentAndOtherLoansInsuredOrGuaranteedByUSGovernmentAgenciesMember" name="StudentAndOtherLoansInsuredOrGuaranteedByUSGovernmentAgenciesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_StudentAndOtherLoansMember" name="StudentAndOtherLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_StudentLoansMember" name="StudentLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SubPrimeResidentialMortgageMember" name="SubPrimeResidentialMortgageMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SubordinatedSecuritiesMember" name="SubordinatedSecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_SubordinatedSecuritiesPurchasedExcludedFromInterestsContinuedToBeHeldByTransferorFairValue" name="SubordinatedSecuritiesPurchasedExcludedFromInterestsContinuedToBeHeldByTransferorFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_SubprimeMember" name="SubprimeMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SubsidiariesAndThirdPartiesDomain" name="SubsidiariesAndThirdPartiesDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SubsidiariesAndThirdPartiesMember" name="SubsidiariesAndThirdPartiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_SummaryOfChangesInMortgageRepurchaseLiabilityTableTextBlock" name="SummaryOfChangesInMortgageRepurchaseLiabilityTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SummaryOfChangesInRepurchaseLiabilityRollforward" name="SummaryOfChangesInRepurchaseLiabilityRollforward" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SummaryOfLoanSaleActivitiesAbstract" name="SummaryOfLoanSaleActivitiesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_SummaryOfLoanSaleActivitiesTextBlock" name="SummaryOfLoanSaleActivitiesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SupplementalInformationAbstract" name="SupplementalInformationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_SyntheticCreditDerivativesMember" name="SyntheticCreditDerivativesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TaxEquivalentAdjustmentTableTextBlock" name="TaxEquivalentAdjustmentTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TaxEquivalentAdjustmentsIncomeTaxExpenseBenefitReportableSegment" name="TaxEquivalentAdjustmentsIncomeTaxExpenseBenefitReportableSegment" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_TaxEquivalentAdjustmentsNetInterestIncomeReportableSegment" name="TaxEquivalentAdjustmentsNetInterestIncomeReportableSegment" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_TaxEquivalentAdjustmentsNoninterestRevenueReportableSegment" name="TaxEquivalentAdjustmentsNoninterestRevenueReportableSegment" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_TaxExpenseBenefitSecuritiesGainsAndLosses" name="TaxExpenseBenefitSecuritiesGainsAndLosses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TechnologyCreditRiskConcentrationMember" name="TechnologyCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TermOfIboxxCorporateAaBondIndexUsedDeterminingDiscountRateForUkDefinedBenefitPensionAndOpebPlans" name="TermOfIboxxCorporateAaBondIndexUsedDeterminingDiscountRateForUkDefinedBenefitPensionAndOpebPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TexasMember" name="TexasMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ThreatenedOrPendingLitigationMember" name="ThreatenedOrPendingLitigationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TierOneRiskBasedCapitalExcludingJuniorSubordinatedDebentureOwedToUnconsolidatedSubsidiaryTrust" name="TierOneRiskBasedCapitalExcludingJuniorSubordinatedDebentureOwedToUnconsolidatedSubsidiaryTrust" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_TierOneRiskBasedCapitalRatioExcludingJuniorSubordinatedDebentureOwedToUnconsolidatedSubsidiaryTrust" name="TierOneRiskBasedCapitalRatioExcludingJuniorSubordinatedDebentureOwedToUnconsolidatedSubsidiaryTrust" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_TierTwoRiskBasedCapitalAdjustmentForInvestmentsInCertainSubsidiariesAndOther" name="TierTwoRiskBasedCapitalAdjustmentForInvestmentsInCertainSubsidiariesAndOther" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_TierTwoRiskBasedCapitalQualifyingAllowanceForCreditLosses" name="TierTwoRiskBasedCapitalQualifyingAllowanceForCreditLosses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_TimeDepositsByMaturityLineItems" name="TimeDepositsByMaturityLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TimeDepositsByMaturityTable" name="TimeDepositsByMaturityTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TimeDepositsByMaturityTableTextBlock" name="TimeDepositsByMaturityTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TimeDepositsOneHundredThousandOrMoreTableTextBlock" name="TimeDepositsOneHundredThousandOrMoreTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TopOfMinimumRangeMember" name="TopOfMinimumRangeMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TotalCreditDerivativesAndCreditRelatedNotesAbstract" name="TotalCreditDerivativesAndCreditRelatedNotesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TotalCreditDerivativesMember" name="TotalCreditDerivativesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TotalInternationalMember" name="TotalInternationalMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TotalLiabilitiesAtFairValueMember" name="TotalLiabilitiesAtFairValueMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TradingAssets" name="TradingAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_TradingAssetsAndLiabilitiesAverageBalancesAbstract" name="TradingAssetsAndLiabilitiesAverageBalancesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TradingAssetsAndLiabilitiesAverageBalancesTableTextBlock" name="TradingAssetsAndLiabilitiesAverageBalancesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TradingAssetsAndLiabilitiesPolicyPolicyTextBlock" name="TradingAssetsAndLiabilitiesPolicyPolicyTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TradingAssetsAverageBalancesDebtAndEquityInstruments" name="TradingAssetsAverageBalancesDebtAndEquityInstruments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_TradingAssetsAverageBalancesDerivativeReceivables" name="TradingAssetsAverageBalancesDerivativeReceivables" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TradingDerivativeInstrumentsGainLossByIncomeStatementLocationByDerivativeInstrumentRiskTable" name="TradingDerivativeInstrumentsGainLossByIncomeStatementLocationByDerivativeInstrumentRiskTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TradingLiabilitiesAverageBalancesDebtAndEquityInstruments" name="TradingLiabilitiesAverageBalancesDebtAndEquityInstruments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_TradingLiabilitiesAverageBalancesDerivativePayables" name="TradingLiabilitiesAverageBalancesDerivativePayables" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_TradingLiabilitiesChangeInFairValue" name="TradingLiabilitiesChangeInFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TradingLoansMember" name="TradingLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TrancheCreditDefaultSwapPortfolioOfExposure" name="TrancheCreditDefaultSwapPortfolioOfExposure" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="jpm_TrancheCreditDefaultSwapRealizedCreditLossProtection" name="TrancheCreditDefaultSwapRealizedCreditLossProtection" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TransportationCreditRiskConcentrationMember" name="TransportationCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TrialModificationMember" name="TrialModificationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TrialModificationsApprovedOnOrAfterJuly12010Member" name="TrialModificationsApprovedOnOrAfterJuly12010Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TroubledDebtRestructuringsOnFinancingReceivablesFinancialEffectsOfModificationsAndRedefaultsTableTextBlock" name="TroubledDebtRestructuringsOnFinancingReceivablesFinancialEffectsOfModificationsAndRedefaultsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TroubledDebtRestructuringsOnFinancingReceivablesNatureAndExtentOfModificationsTableTextBlock" name="TroubledDebtRestructuringsOnFinancingReceivablesNatureAndExtentOfModificationsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_TroubledDebtRestructuringsOnFinancingReceivablesRollForwardTableTextBlock" name="TroubledDebtRestructuringsOnFinancingReceivablesRollForwardTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TrustFundsCommonOrCollectiveMember" name="TrustFundsCommonOrCollectiveMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TwoNotchDowngradeMember" name="TwoNotchDowngradeMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_TypeOfRegulatoryGuidanceDomain" name="TypeOfRegulatoryGuidanceDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_USGovernmentAndGovernmentAgenciesAndAuthoritiesAndUSGovernmentSponsoredEnterpriseSecuritiesInExcessOfTenPercentOfStockholdersEquity" name="USGovernmentAndGovernmentAgenciesAndAuthoritiesAndUSGovernmentSponsoredEnterpriseSecuritiesInExcessOfTenPercentOfStockholdersEquity" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_UnderfundedPlansMember" name="UnderfundedPlansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_UnderwritingAbstract" name="UnderwritingAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_UndistributedEarningsOfForeignSubsidiariesPretax" name="UndistributedEarningsOfForeignSubsidiariesPretax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_UndistributedNetIncomeSubsidiaries" name="UndistributedNetIncomeSubsidiaries" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="jpm_UnfundedCommitmentsInvestmentsOtherEquityInvestments" name="UnfundedCommitmentsInvestmentsOtherEquityInvestments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_UnfundedCommitmentsInvestmentsPrivateEquityFundsThirdParty" name="UnfundedCommitmentsInvestmentsPrivateEquityFundsThirdParty" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_UnfundedPostretirementBenefitObligationUKPlan" name="UnfundedPostretirementBenefitObligationUKPlan" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_UnitedKingdomMember" name="UnitedKingdomMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_UnitedStatesDistrictCourtSouthernDistrictOfNewYorkMember" name="UnitedStatesDistrictCourtSouthernDistrictOfNewYorkMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_UnitedStatesEquitySecuritiesMemberMember" name="UnitedStatesEquitySecuritiesMemberMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_UnrealizedGainsOnAfsSecurities" name="UnrealizedGainsOnAfsSecurities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_UnrecognizedTaxBenefitsLineItems" name="UnrecognizedTaxBenefitsLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_UnsettledForwardStartingReverseRepurchaseAndSecuritiesBorrowingAgreements" name="UnsettledForwardStartingReverseRepurchaseAndSecuritiesBorrowingAgreements" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_UnsettledReverseRepurchaseAndSecuritiesBorrowingAgreementsMember" name="UnsettledReverseRepurchaseAndSecuritiesBorrowingAgreementsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_UnsettledReverseRepurchaseAndSecuritiesBorrowingAgreementsWithRegularWaySettlementPeriods" name="UnsettledReverseRepurchaseAndSecuritiesBorrowingAgreementsWithRegularWaySettlementPeriods" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_UnusedLinesOfCreditAdvisedMember" name="UnusedLinesOfCreditAdvisedMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_UsGovernmentAgenciesAndUsGovernmentSponsoredEnterprisesResidentialCollateralizedMortgageObligationsEstimatedDuration" name="UsGovernmentAgenciesAndUsGovernmentSponsoredEnterprisesResidentialCollateralizedMortgageObligationsEstimatedDuration" nillable="true" substitutionGroup="xbrli:item" type="us-types:durationStringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_UsGovernmentAgenciesAndUsGovernmentSponsoredEnterprisesResidentialMortgageBackedSecuritiesEstimatedDuration" name="UsGovernmentAgenciesAndUsGovernmentSponsoredEnterprisesResidentialMortgageBackedSecuritiesEstimatedDuration" nillable="true" substitutionGroup="xbrli:item" type="us-types:durationStringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_UsPensionPlansAndOtherUsPostretirementEmployeeBenefitPlansMember" name="UsPensionPlansAndOtherUsPostretirementEmployeeBenefitPlansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_UsTreasuryWarrantExchangeAndSecondaryOfferingSaleAmount" name="UsTreasuryWarrantExchangeAndSecondaryOfferingSaleAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_UtilitiesCreditRiskConcentrationMember" name="UtilitiesCreditRiskConcentrationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_VIEProgramTypeThreeMember" name="VIEProgramTypeThreeMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_ValuationAdjustmentsToFairValueOfFinancialInstrumentsAxis" name="ValuationAdjustmentsToFairValueOfFinancialInstrumentsAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ValuationAllowancesAndReservesProbableRecoveriesFromThirdParties" name="ValuationAllowancesAndReservesProbableRecoveriesFromThirdParties" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_ValuationAllowancesAndReservesProvision" name="ValuationAllowancesAndReservesProvision" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_VariableInterestEntitiesAbstract" name="VariableInterestEntitiesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_VariableInterestEntitiesAndTransfersOfFinancialAssetsTextBlock" name="VariableInterestEntitiesAndTransfersOfFinancialAssetsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_VariableInterestEntitiesByProgramTypeAxis" name="VariableInterestEntitiesByProgramTypeAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_VariableInterestEntitiesDomain" name="VariableInterestEntitiesDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_VariableInterestEntityWeightedAverageExpectedLifeOfAssetsUnconsolidatedVIE" name="VariableInterestEntityWeightedAverageExpectedLifeOfAssetsUnconsolidatedVIE" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_VariableInterestSubordinatedLoanContractAmount" name="VariableInterestSubordinatedLoanContractAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="jpm_VariableInterestTermLoanContractAmount" name="VariableInterestTermLoanContractAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_VieProgramTypeOtherMember" name="VieProgramTypeOtherMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_VirginiaMember" name="VirginiaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_WarrantsRepurchasedDuringPeriod" name="WarrantsRepurchasedDuringPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_WashingtonMember" name="WashingtonMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_WashingtonMutualIncBankruptcyPlanConfirmationMember" name="WashingtonMutualIncBankruptcyPlanConfirmationMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_WashingtonMutualLitigationsNotSpecificallyInConnectionWithDisputedAssetsMember" name="WashingtonMutualLitigationsNotSpecificallyInConnectionWithDisputedAssetsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_WashingtonMutualLitigationsTexasActionMember" name="WashingtonMutualLitigationsTexasActionMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_WeightedAverageLifeOfSecuritizedAssets" name="WeightedAverageLifeOfSecuritizedAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:durationItemType" xbrli:periodType="instant" />
  <xsd:element id="jpm_WeightedAverageOptionAdjustedSpread" name="WeightedAverageOptionAdjustedSpread" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_WeightedAverageOptionAdjustedSpreadImpactOnFairValueOfHundredBasisPointsAdverseChange" name="WeightedAverageOptionAdjustedSpreadImpactOnFairValueOfHundredBasisPointsAdverseChange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_WeightedAverageOptionAdjustedSpreadImpactOnFairValueOfTwoHundredBasisPointsAdverseChange" name="WeightedAverageOptionAdjustedSpreadImpactOnFairValueOfTwoHundredBasisPointsAdverseChange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_WholesaleLendingRelatedCommitments" name="WholesaleLendingRelatedCommitments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="jpm_WholesaleOtherMember" name="WholesaleOtherMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_WholesaleRealEstateCommercialConstructionAndDevelopmentMember" name="WholesaleRealEstateCommercialConstructionAndDevelopmentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_WholesaleRealEstateCommercialLessorsMember" name="WholesaleRealEstateCommercialLessorsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_WholesaleRealEstateMultifamilyMember" name="WholesaleRealEstateMultifamilyMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_WholesaleRealEstateOtherLoansMember" name="WholesaleRealEstateOtherLoansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="jpm_WrittenOptionsMember" name="WrittenOptionsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="jpm_ZeroCouponNotesAggregateCarryingValue" name="ZeroCouponNotesAggregateCarryingValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="jpm_ZeroCouponNotesAggregatePrincipalAmountAtMaturity" name="ZeroCouponNotesAggregatePrincipalAmountAtMaturity" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
</xsd:schema>