<?xml version="1.0" encoding="US-ASCII"?>
<!--  EDGAR Online I-Metrix Xcelerate Taxonomy Schema, based on XBRL 2.1  http://www.edgar-online.com/  -->
<!--  Version: 6.3.11 -->
<!--  Round: a209e767-c6ef-47db-ada7-016466e52c28 -->
<!--  Creation date: 2010-02-19T03:25Z -->
<!--  Copyright (c) 2005-2010 EDGAR Online, Inc. All Rights Reserved. -->
<schema xmlns="http://www.w3.org/2001/XMLSchema"
  xmlns:xbrli="http://www.xbrl.org/2003/instance"
  xmlns:link="http://www.xbrl.org/2003/linkbase"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:nflx="http://www.netflix.com/20091231"
  xmlns:xbrldt="http://xbrl.org/2005/xbrldt"
  xmlns:us-types="http://xbrl.us/us-types/2009-01-31"
  targetNamespace="http://www.netflix.com/20091231"
  elementFormDefault="qualified" attributeFormDefault="unqualified">
  <annotation>
    <appinfo>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/StatementOfFinancialPositionClassified" id="IMetrix_StatementOfFinancialPositionClassified">
        <link:definition>101 - Statement - Statement Of Financial Position Classified</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/StatementOfFinancialPositionClassifiedParenthetical" id="IMetrix_StatementOfFinancialPositionClassifiedParen">
        <link:definition>102 - Statement - Statement Of Financial Position Classified (Parenthetical)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/StatementOfIncome" id="IMetrix_StatementOfIncome">
        <link:definition>103 - Statement - Statement Of Income</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/StatementOfIncomeParenthetical" id="IMetrix_StatementOfIncomeParen">
        <link:definition>104 - Statement - Statement Of Income (Parenthetical)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/StatementOfShareholdersEquityAndOtherComprehensiveIncome" id="Imetrix_ci-StatementOfShareholdersEquityAndOtherComprehensiveIncome">
        <link:definition>105 - Statement - Statement Of Shareholders Equity And Other Comprehensive Income</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/StatementOfCashFlowsIndirect" id="IMetrix_StatementOfCashFlowsIndirect">
        <link:definition>106 - Statement - Statement Of Cash Flows Indirect</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsOrganizationConsolidationAndPresentationOfFinancialStatementsDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsOrganizationConsolidationAndPresentationOfFinancialStatementsDisclosureTextBlock">
        <link:definition>107 - Disclosure - Organization and Summary of Significant Accounting Policies</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsShortTermInvestmentsTextBlock" id="IMetrix_NotesToFinancialStatementsShortTermInvestmentsTextBlock">
        <link:definition>108 - Disclosure - Short-term Investments</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsDetailsOfCertainBalanceSheetAccountsDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsDetailsOfCertainBalanceSheetAccountsDisclosureTextBlock">
        <link:definition>109 - Disclosure - Balance Sheet Components</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsLongTermDebtTextBlock" id="IMetrix_NotesToFinancialStatementsLongTermDebtTextBlock">
        <link:definition>110 - Disclosure - Long-term Debt</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsCommitmentsAndContingenciesDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsCommitmentsAndContingenciesDisclosureTextBlock">
        <link:definition>111 - Disclosure - Commitments and Contingencies</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsScheduleOfGuaranteeObligationsTextBlock" id="IMetrix_NotesToFinancialStatementsScheduleOfGuaranteeObligationsTextBlock">
        <link:definition>112 - Disclosure - Guarantees-Intellectual Property Indemnification Obligations</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsStockholdersEquityNoteDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsStockholdersEquityNoteDisclosureTextBlock">
        <link:definition>113 - Disclosure - Stockholders' Equity</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsIncomeTaxDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsIncomeTaxDisclosureTextBlock">
        <link:definition>114 - Disclosure - Income Taxes</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsScheduleOfDefinedBenefitPlansDisclosuresTextBlock" id="IMetrix_NotesToFinancialStatementsScheduleOfDefinedBenefitPlansDisclosuresTextBlock">
        <link:definition>115 - Disclosure - Employee Benefit Plan</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsRelatedPartyTransactionsDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsRelatedPartyTransactionsDisclosureTextBlock">
        <link:definition>116 - Disclosure - Related Party Transaction</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/NotesToFinancialStatementsQuarterlyFinancialInformationTextBlock" id="IMetrix_NotesToFinancialStatementsQuarterlyFinancialInformationTextBlock">
        <link:definition>117 - Disclosure - Selected Quarterly Financial Data (Unaudited)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/DocumentInformation" id="IMetrix_DocumentInformation">
        <link:definition>118 - Disclosure - Document Information</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/EntityInformation" id="IMetrix_EntityInformation">
        <link:definition>119 - Disclosure - Entity Information</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.netflix.com/taxonomy/role/CommonDomainMembers" id="IMetrix_CommonDomainMembers">
        <link:definition>120 - Disclosure - Common Domain Members</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:linkbaseRef xlink:type="simple" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:role="http://www.xbrl.org/2003/role/calculationLinkbaseRef" xlink:href="nflx-20091231_cal.xml" xlink:title="Calculation Links, all"/>
      <link:linkbaseRef xlink:type="simple" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:role="http://www.xbrl.org/2003/role/definitionLinkbaseRef" xlink:href="nflx-20091231_def.xml" xlink:title="Definition Links, all"/>
      <link:linkbaseRef xlink:type="simple" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:role="http://www.xbrl.org/2003/role/labelLinkbaseRef" xlink:href="nflx-20091231_lab.xml" xlink:title="Label Links, all"/>
      <link:linkbaseRef xlink:type="simple" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:role="http://www.xbrl.org/2003/role/presentationLinkbaseRef" xlink:href="nflx-20091231_pre.xml" xlink:title="Presentation Links, all"/>
    </appinfo>
  </annotation>
  <import namespace="http://www.xbrl.org/2003/instance" schemaLocation="http://www.xbrl.org/2003/xbrl-instance-2003-12-31.xsd"/>
  <import namespace="http://www.xbrl.org/2003/linkbase" schemaLocation="http://www.xbrl.org/2003/xbrl-linkbase-2003-12-31.xsd"/>
  <import namespace="http://xbrl.org/2005/xbrldt" schemaLocation="http://www.xbrl.org/2005/xbrldt-2005.xsd"/>
  <import namespace="http://xbrl.us/us-types/2009-01-31" schemaLocation="http://taxonomies.xbrl.us/us-gaap/2009/elts/us-types-2009-01-31.xsd"/>
  <import namespace="http://xbrl.us/us-gaap/2009-01-31" schemaLocation="http://taxonomies.xbrl.us/us-gaap/2009/elts/us-gaap-2009-01-31.xsd"/>
  <import namespace="http://xbrl.us/dei/2009-01-31" schemaLocation="http://taxonomies.xbrl.us/us-gaap/2009/non-gaap/dei-2009-01-31.xsd"/>
  <element name="AmortizationOfContentLibrary" id="nflx_AmortizationOfContentLibrary" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ContentLibraryNetCurrent" id="nflx_ContentLibraryNetCurrent" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ContentLibraryNetNoncurrent" id="nflx_ContentLibraryNetNoncurrent" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DetailsOfCertainBalanceSheetAccountsDisclosureTextBlock" id="nflx_DetailsOfCertainBalanceSheetAccountsDisclosureTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FulfillmentExpense" id="nflx_FulfillmentExpense" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="IncreaseDecreaseInContentLibrary" id="nflx_IncreaseDecreaseInContentLibrary" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="NotesToFinancialStatementsAbstract" id="nflx_NotesToFinancialStatementsAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="OtherIncomeExpenseAbstract" id="nflx_OtherIncomeExpenseAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="PaymentsForAcquisitionsOfContentLibrary" id="nflx_PaymentsForAcquisitionsOfContentLibrary" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="PaymentsForProceedsFromOtherAssets" id="nflx_PaymentsForProceedsFromOtherAssets" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="PrepaidRevenueSharingExpensesCurrent" id="nflx_PrepaidRevenueSharingExpensesCurrent" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="SharesOutstanding" id="nflx_SharesOutstanding" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShortTermInvestmentsTextBlock" id="nflx_ShortTermInvestmentsTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="StockBasedCompensationExpenseAssociatedWithFulfillmentExpense" id="nflx_StockBasedCompensationExpenseAssociatedWithFulfillmentExpense" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="StockBasedCompensationExpenseAssociatedWithGeneralAndAdministrativeExpense" id="nflx_StockBasedCompensationExpenseAssociatedWithGeneralAndAdministrativeExpense" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="StockBasedCompensationExpenseAssociatedWithMarketingExpense" id="nflx_StockBasedCompensationExpenseAssociatedWithMarketingExpense" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="StockBasedCompensationExpenseAssociatedWithTechnologyAndDevelopmentExpense" id="nflx_StockBasedCompensationExpenseAssociatedWithTechnologyAndDevelopmentExpense" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
</schema>