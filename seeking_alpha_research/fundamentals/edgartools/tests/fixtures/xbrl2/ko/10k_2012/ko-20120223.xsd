<?xml version="1.0" encoding="US-ASCII"?>
<!--XBRL Document Created with WebFilings-->
<xsd:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://www.thecocacolacompany.com/20111231" xmlns:ko="http://www.thecocacolacompany.com/20111231" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:nonnum="http://www.xbrl.org/dtr/type/non-numeric" xmlns:num="http://www.xbrl.org/dtr/type/numeric" xmlns:us-types="http://fasb.org/us-types/2011-01-31" xmlns:xbrldt="http://xbrl.org/2005/xbrldt" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import namespace="http://fasb.org/us-gaap/2011-01-31" schemaLocation="http://xbrl.fasb.org/us-gaap/2011/elts/us-gaap-2011-01-31.xsd" />
  <xsd:import namespace="http://fasb.org/us-roles/2011-01-31" schemaLocation="http://xbrl.fasb.org/us-gaap/2011/elts/us-roles-2011-01-31.xsd" />
  <xsd:import namespace="http://fasb.org/us-types/2011-01-31" schemaLocation="http://xbrl.fasb.org/us-gaap/2011/elts/us-types-2011-01-31.xsd" />
  <xsd:import namespace="http://www.xbrl.org/2003/instance" schemaLocation="http://www.xbrl.org/2003/xbrl-instance-2003-12-31.xsd" />
  <xsd:import namespace="http://www.xbrl.org/2003/linkbase" schemaLocation="http://www.xbrl.org/2003/xbrl-linkbase-2003-12-31.xsd" />
  <xsd:import namespace="http://www.xbrl.org/2009/arcrole/fact-explanatoryFact" schemaLocation="http://www.xbrl.org/lrr/arcrole/factExplanatory-2009-12-16.xsd" />
  <xsd:import namespace="http://www.xbrl.org/2009/role/negated" schemaLocation="http://www.xbrl.org/lrr/role/negated-2009-12-16.xsd" />
  <xsd:import namespace="http://www.xbrl.org/2009/role/net" schemaLocation="http://www.xbrl.org/lrr/role/net-2009-12-16.xsd" />
  <xsd:import namespace="http://www.xbrl.org/dtr/type/non-numeric" schemaLocation="http://www.xbrl.org/dtr/type/nonNumeric-2009-12-16.xsd" />
  <xsd:import namespace="http://www.xbrl.org/dtr/type/numeric" schemaLocation="http://www.xbrl.org/dtr/type/numeric-2009-12-16.xsd" />
  <xsd:import namespace="http://xbrl.org/2005/xbrldt" schemaLocation="http://www.xbrl.org/2005/xbrldt-2005.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/country/2011-01-31" schemaLocation="http://xbrl.sec.gov/country/2011/country-2011-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/currency/2011-01-31" schemaLocation="http://xbrl.sec.gov/currency/2011/currency-2011-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/dei/2011-01-31" schemaLocation="http://xbrl.sec.gov/dei/2011/dei-2011-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/exch/2011-01-31" schemaLocation="http://xbrl.sec.gov/exch/2011/exch-2011-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/invest/2011-01-31" schemaLocation="http://xbrl.sec.gov/invest/2011/invest-2011-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/naics/2011-01-31" schemaLocation="http://xbrl.sec.gov/naics/2011/naics-2011-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/sic/2011-01-31" schemaLocation="http://xbrl.sec.gov/sic/2011/sic-2011-01-31.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/stpr/2011-01-31" schemaLocation="http://xbrl.sec.gov/stpr/2011/stpr-2011-01-31.xsd" />
  <xsd:annotation>
    <xsd:appinfo>
      <link:linkbaseRef xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:href="ko-20111231_cal.xml" xlink:role="http://www.xbrl.org/2003/role/calculationLinkbaseRef" xlink:type="simple" />
      <link:linkbaseRef xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:href="ko-20111231_def.xml" xlink:role="http://www.xbrl.org/2003/role/definitionLinkbaseRef" xlink:type="simple" />
      <link:linkbaseRef xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:href="ko-20111231_lab.xml" xlink:role="http://www.xbrl.org/2003/role/labelLinkbaseRef" xlink:type="simple" />
      <link:linkbaseRef xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:href="ko-20111231_pre.xml" xlink:role="http://www.xbrl.org/2003/role/presentationLinkbaseRef" xlink:type="simple" />
      <link:roleType id="AccountsPayableAndAccruedExpenses" roleURI="http://www.thecocacolacompany.com/role/AccountsPayableAndAccruedExpenses">
        <link:definition>2109100 - Disclosure - ACCOUNTS PAYABLE AND ACCRUED EXPENSES</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AccountsPayableAndAccruedExpensesDetails" roleURI="http://www.thecocacolacompany.com/role/AccountsPayableAndAccruedExpensesDetails">
        <link:definition>2409402 - Disclosure - ACCOUNTS PAYABLE AND ACCRUED EXPENSES (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AccountsPayableAndAccruedExpensesTables" roleURI="http://www.thecocacolacompany.com/role/AccountsPayableAndAccruedExpensesTables">
        <link:definition>2309301 - Disclosure - ACCOUNTS PAYABLE AND ACCRUED EXPENSES (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AcquisitionsAndDivestitures" roleURI="http://www.thecocacolacompany.com/role/AcquisitionsAndDivestitures">
        <link:definition>2102100 - Disclosure - ACQUISITIONS AND DIVESTITURES</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AcquisitionsAndDivestituresDetails" roleURI="http://www.thecocacolacompany.com/role/AcquisitionsAndDivestituresDetails">
        <link:definition>2402402 - Disclosure - ACQUISITIONS AND DIVESTITURES (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AcquisitionsAndDivestituresDetails2" roleURI="http://www.thecocacolacompany.com/role/AcquisitionsAndDivestituresDetails2">
        <link:definition>2402403 - Disclosure - ACQUISITIONS AND DIVESTITURES (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AcquisitionsAndDivestituresDetails3" roleURI="http://www.thecocacolacompany.com/role/AcquisitionsAndDivestituresDetails3">
        <link:definition>2402404 - Disclosure - ACQUISITIONS AND DIVESTITURES (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AcquisitionsAndDivestituresDetails4" roleURI="http://www.thecocacolacompany.com/role/AcquisitionsAndDivestituresDetails4">
        <link:definition>2402405 - Disclosure - ACQUISITIONS AND DIVESTITURES (Details 4)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AcquisitionsAndDivestituresDetails5" roleURI="http://www.thecocacolacompany.com/role/AcquisitionsAndDivestituresDetails5">
        <link:definition>2402406 - Disclosure - ACQUISITIONS AND DIVESTITURES (Details 5)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="AcquisitionsAndDivestituresTables" roleURI="http://www.thecocacolacompany.com/role/AcquisitionsAndDivestituresTables">
        <link:definition>2302301 - Disclosure - ACQUISITIONS AND DIVESTITURES (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessAndSummaryOfSignificantAccountingPolicies" roleURI="http://www.thecocacolacompany.com/role/BusinessAndSummaryOfSignificantAccountingPolicies">
        <link:definition>2101100 - Disclosure - BUSINESS AND SUMMARY OF SIGNIFICANT ACCOUNTING POLICIES</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessAndSummaryOfSignificantAccountingPoliciesDetails" roleURI="http://www.thecocacolacompany.com/role/BusinessAndSummaryOfSignificantAccountingPoliciesDetails">
        <link:definition>2401403 - Disclosure - BUSINESS AND SUMMARY OF SIGNIFICANT ACCOUNTING POLICIES (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessAndSummaryOfSignificantAccountingPoliciesDetails2" roleURI="http://www.thecocacolacompany.com/role/BusinessAndSummaryOfSignificantAccountingPoliciesDetails2">
        <link:definition>2401404 - Disclosure - BUSINESS AND SUMMARY OF SIGNIFICANT ACCOUNTING POLICIES (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessAndSummaryOfSignificantAccountingPoliciesDetails3" roleURI="http://www.thecocacolacompany.com/role/BusinessAndSummaryOfSignificantAccountingPoliciesDetails3">
        <link:definition>2401405 - Disclosure - BUSINESS AND SUMMARY OF SIGNIFICANT ACCOUNTING POLICIES (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessAndSummaryOfSignificantAccountingPoliciesDetails4" roleURI="http://www.thecocacolacompany.com/role/BusinessAndSummaryOfSignificantAccountingPoliciesDetails4">
        <link:definition>2401406 - Disclosure - BUSINESS AND SUMMARY OF SIGNIFICANT ACCOUNTING POLICIES (Details 4)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessAndSummaryOfSignificantAccountingPoliciesPolicies" roleURI="http://www.thecocacolacompany.com/role/BusinessAndSummaryOfSignificantAccountingPoliciesPolicies">
        <link:definition>2201201 - Disclosure - BUSINESS AND SUMMARY OF SIGNIFICANT ACCOUNTING POLICIES (Policies)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="BusinessAndSummaryOfSignificantAccountingPoliciesTables" roleURI="http://www.thecocacolacompany.com/role/BusinessAndSummaryOfSignificantAccountingPoliciesTables">
        <link:definition>2301302 - Disclosure - BUSINESS AND SUMMARY OF SIGNIFICANT ACCOUNTING POLICIES (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommitmentsAndContingencies" roleURI="http://www.thecocacolacompany.com/role/CommitmentsAndContingencies">
        <link:definition>2111100 - Disclosure - COMMITMENTS AND CONTINGENCIES</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommitmentsAndContingenciesDetails" roleURI="http://www.thecocacolacompany.com/role/CommitmentsAndContingenciesDetails">
        <link:definition>2411402 - Disclosure - COMMITMENTS AND CONTINGENCIES (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommitmentsAndContingenciesDetails2" roleURI="http://www.thecocacolacompany.com/role/CommitmentsAndContingenciesDetails2">
        <link:definition>2411403 - Disclosure - COMMITMENTS AND CONTINGENCIES (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommitmentsAndContingenciesDetails3" roleURI="http://www.thecocacolacompany.com/role/CommitmentsAndContingenciesDetails3">
        <link:definition>2411404 - Disclosure - COMMITMENTS AND CONTINGENCIES (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="CommitmentsAndContingenciesTables" roleURI="http://www.thecocacolacompany.com/role/CommitmentsAndContingenciesTables">
        <link:definition>2311301 - Disclosure - COMMITMENTS AND CONTINGENCIES (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedBalanceSheetParentheticals" roleURI="http://www.thecocacolacompany.com/role/ConsolidatedBalanceSheetParentheticals">
        <link:definition>1002500 - Statement - CONSOLIDATED BALANCE SHEET (Parentheticals)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedBalanceSheets" roleURI="http://www.thecocacolacompany.com/role/ConsolidatedBalanceSheets">
        <link:definition>1002000 - Statement - CONSOLIDATED BALANCE SHEETS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedStatementsOfCashFlows" roleURI="http://www.thecocacolacompany.com/role/ConsolidatedStatementsOfCashFlows">
        <link:definition>1003000 - Statement - CONSOLIDATED STATEMENTS OF CASH FLOWS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedStatementsOfIncome" roleURI="http://www.thecocacolacompany.com/role/ConsolidatedStatementsOfIncome">
        <link:definition>1001000 - Statement - CONSOLIDATED STATEMENTS OF INCOME</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedStatementsOfShareownersEquity" roleURI="http://www.thecocacolacompany.com/role/ConsolidatedStatementsOfShareownersEquity">
        <link:definition>1004000 - Statement - CONSOLIDATED STATEMENTS OF SHAREOWNERS' EQUITY</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ConsolidatedStatementsOfShareownersEquityParentheticals" roleURI="http://www.thecocacolacompany.com/role/ConsolidatedStatementsOfShareownersEquityParentheticals">
        <link:definition>1004500 - Statement - CONSOLIDATED STATEMENTS OF SHAREOWNERS' EQUITY (Parentheticals)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DebtAndBorrowingArrangements" roleURI="http://www.thecocacolacompany.com/role/DebtAndBorrowingArrangements">
        <link:definition>2110100 - Disclosure - DEBT AND BORROWING ARRANGEMENTS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DebtAndBorrowingArrangementsDetails" roleURI="http://www.thecocacolacompany.com/role/DebtAndBorrowingArrangementsDetails">
        <link:definition>2410402 - Disclosure - DEBT AND BORROWING ARRANGEMENTS (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DebtAndBorrowingArrangementsTables" roleURI="http://www.thecocacolacompany.com/role/DebtAndBorrowingArrangementsTables">
        <link:definition>2310301 - Disclosure - DEBT AND BORROWING ARRANGEMENTS (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="DocumentAndEntityInformationDocument" roleURI="http://www.thecocacolacompany.com/role/DocumentAndEntityInformationDocument">
        <link:definition>0001000 - Document - Document and Entity Information Document</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EquityMethodInvestments" roleURI="http://www.thecocacolacompany.com/role/EquityMethodInvestments">
        <link:definition>2106100 - Disclosure - EQUITY METHOD INVESTMENTS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EquityMethodInvestmentsDetails" roleURI="http://www.thecocacolacompany.com/role/EquityMethodInvestmentsDetails">
        <link:definition>2406402 - Disclosure - EQUITY METHOD INVESTMENTS (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EquityMethodInvestmentsDetails2" roleURI="http://www.thecocacolacompany.com/role/EquityMethodInvestmentsDetails2">
        <link:definition>2406403 - Disclosure - EQUITY METHOD INVESTMENTS (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="EquityMethodInvestmentsTables" roleURI="http://www.thecocacolacompany.com/role/EquityMethodInvestmentsTables">
        <link:definition>2306301 - Disclosure - EQUITY METHOD INVESTMENTS (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurements" roleURI="http://www.thecocacolacompany.com/role/FairValueMeasurements">
        <link:definition>2116100 - Disclosure - FAIR VALUE MEASUREMENTS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementsDetails" roleURI="http://www.thecocacolacompany.com/role/FairValueMeasurementsDetails">
        <link:definition>2416402 - Disclosure - FAIR VALUE MEASUREMENTS (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementsDetails2" roleURI="http://www.thecocacolacompany.com/role/FairValueMeasurementsDetails2">
        <link:definition>2416403 - Disclosure - FAIR VALUE MEASUREMENTS (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementsDetails3" roleURI="http://www.thecocacolacompany.com/role/FairValueMeasurementsDetails3">
        <link:definition>2416404 - Disclosure - FAIR VALUE MEASUREMENTS (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="FairValueMeasurementsTables" roleURI="http://www.thecocacolacompany.com/role/FairValueMeasurementsTables">
        <link:definition>2316301 - Disclosure - FAIR VALUE MEASUREMENTS (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="HedgingTransactionsAndDerivativeFinancialInstruments" roleURI="http://www.thecocacolacompany.com/role/HedgingTransactionsAndDerivativeFinancialInstruments">
        <link:definition>2105100 - Disclosure - HEDGING TRANSACTIONS AND DERIVATIVE FINANCIAL INSTRUMENTS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="HedgingTransactionsAndDerivativeFinancialInstrumentsDetails" roleURI="http://www.thecocacolacompany.com/role/HedgingTransactionsAndDerivativeFinancialInstrumentsDetails">
        <link:definition>2405402 - Disclosure - HEDGING TRANSACTIONS AND DERIVATIVE FINANCIAL INSTRUMENTS (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="HedgingTransactionsAndDerivativeFinancialInstrumentsDetails2" roleURI="http://www.thecocacolacompany.com/role/HedgingTransactionsAndDerivativeFinancialInstrumentsDetails2">
        <link:definition>2405403 - Disclosure - HEDGING TRANSACTIONS AND DERIVATIVE FINANCIAL INSTRUMENTS (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="HedgingTransactionsAndDerivativeFinancialInstrumentsTables" roleURI="http://www.thecocacolacompany.com/role/HedgingTransactionsAndDerivativeFinancialInstrumentsTables">
        <link:definition>2305301 - Disclosure - HEDGING TRANSACTIONS AND DERIVATIVE FINANCIAL INSTRUMENTS (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxes" roleURI="http://www.thecocacolacompany.com/role/IncomeTaxes">
        <link:definition>2114100 - Disclosure - INCOME TAXES</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesDetails" roleURI="http://www.thecocacolacompany.com/role/IncomeTaxesDetails">
        <link:definition>2414402 - Disclosure - INCOME TAXES (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesDetails2" roleURI="http://www.thecocacolacompany.com/role/IncomeTaxesDetails2">
        <link:definition>2414403 - Disclosure - INCOME TAXES (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesDetails3" roleURI="http://www.thecocacolacompany.com/role/IncomeTaxesDetails3">
        <link:definition>2414404 - Disclosure - INCOME TAXES (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IncomeTaxesTables" roleURI="http://www.thecocacolacompany.com/role/IncomeTaxesTables">
        <link:definition>2314301 - Disclosure - INCOME TAXES (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IntangibleAssets" roleURI="http://www.thecocacolacompany.com/role/IntangibleAssets">
        <link:definition>2108100 - Disclosure - INTANGIBLE ASSETS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IntangibleAssetsDetails" roleURI="http://www.thecocacolacompany.com/role/IntangibleAssetsDetails">
        <link:definition>2408402 - Disclosure - INTANGIBLE ASSETS (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IntangibleAssetsDetails2" roleURI="http://www.thecocacolacompany.com/role/IntangibleAssetsDetails2">
        <link:definition>2408403 - Disclosure - INTANGIBLE ASSETS (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="IntangibleAssetsTables" roleURI="http://www.thecocacolacompany.com/role/IntangibleAssetsTables">
        <link:definition>2308301 - Disclosure - INTANGIBLE ASSETS (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="Inventories" roleURI="http://www.thecocacolacompany.com/role/Inventories">
        <link:definition>2104100 - Disclosure - INVENTORIES</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InventoriesDetails" roleURI="http://www.thecocacolacompany.com/role/InventoriesDetails">
        <link:definition>2404402 - Disclosure - INVENTORIES (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InventoriesTables" roleURI="http://www.thecocacolacompany.com/role/InventoriesTables">
        <link:definition>2304301 - Disclosure - INVENTORIES (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="Investments" roleURI="http://www.thecocacolacompany.com/role/Investments">
        <link:definition>2103100 - Disclosure - INVESTMENTS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InvestmentsDetails" roleURI="http://www.thecocacolacompany.com/role/InvestmentsDetails">
        <link:definition>2403402 - Disclosure - INVESTMENTS (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InvestmentsDetails2" roleURI="http://www.thecocacolacompany.com/role/InvestmentsDetails2">
        <link:definition>2403403 - Disclosure - INVESTMENTS (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InvestmentsDetails3" roleURI="http://www.thecocacolacompany.com/role/InvestmentsDetails3">
        <link:definition>2403404 - Disclosure - INVESTMENTS (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InvestmentsDetails4" roleURI="http://www.thecocacolacompany.com/role/InvestmentsDetails4">
        <link:definition>2403405 - Disclosure - INVESTMENTS (Details 4)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InvestmentsDetails5" roleURI="http://www.thecocacolacompany.com/role/InvestmentsDetails5">
        <link:definition>2403406 - Disclosure - INVESTMENTS (Details 5)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InvestmentsDetails6" roleURI="http://www.thecocacolacompany.com/role/InvestmentsDetails6">
        <link:definition>2403407 - Disclosure - INVESTMENTS (Details 6)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InvestmentsDetails7" roleURI="http://www.thecocacolacompany.com/role/InvestmentsDetails7">
        <link:definition>2403408 - Disclosure - INVESTMENTS (Details 7)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="InvestmentsTables" roleURI="http://www.thecocacolacompany.com/role/InvestmentsTables">
        <link:definition>2303301 - Disclosure - INVESTMENTS (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="NetChangeInOperatingAssetsAndLiabilities" roleURI="http://www.thecocacolacompany.com/role/NetChangeInOperatingAssetsAndLiabilities">
        <link:definition>2120100 - Disclosure - NET CHANGE IN OPERATING ASSETS AND LIABILITIES</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="NetChangeInOperatingAssetsAndLiabilitiesDetails" roleURI="http://www.thecocacolacompany.com/role/NetChangeInOperatingAssetsAndLiabilitiesDetails">
        <link:definition>2420402 - Disclosure - NET CHANGE IN OPERATING ASSETS AND LIABILITIES (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="NetChangeInOperatingAssetsAndLiabilitiesTables" roleURI="http://www.thecocacolacompany.com/role/NetChangeInOperatingAssetsAndLiabilitiesTables">
        <link:definition>2320301 - Disclosure - NET CHANGE IN OPERATING ASSETS AND LIABILITIES (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OperatingSegments" roleURI="http://www.thecocacolacompany.com/role/OperatingSegments">
        <link:definition>2119100 - Disclosure - OPERATING SEGMENTS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OperatingSegmentsDetails" roleURI="http://www.thecocacolacompany.com/role/OperatingSegmentsDetails">
        <link:definition>2419402 - Disclosure - OPERATING SEGMENTS (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OperatingSegmentsDetails2" roleURI="http://www.thecocacolacompany.com/role/OperatingSegmentsDetails2">
        <link:definition>2419403 - Disclosure - OPERATING SEGMENTS (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OperatingSegmentsTables" roleURI="http://www.thecocacolacompany.com/role/OperatingSegmentsTables">
        <link:definition>2319301 - Disclosure - OPERATING SEGMENTS (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OtherComprehensiveIncome" roleURI="http://www.thecocacolacompany.com/role/OtherComprehensiveIncome">
        <link:definition>2115100 - Disclosure - OTHER COMPREHENSIVE INCOME</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OtherComprehensiveIncomeCalc2Details" roleURI="http://www.thecocacolacompany.com/role/OtherComprehensiveIncomeCalc2Details">
        <link:definition>2415403 - Disclosure - OTHER COMPREHENSIVE INCOME Calc 2 (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OtherComprehensiveIncomeDetails" roleURI="http://www.thecocacolacompany.com/role/OtherComprehensiveIncomeDetails">
        <link:definition>2415402 - Disclosure - OTHER COMPREHENSIVE INCOME (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="OtherComprehensiveIncomeTables" roleURI="http://www.thecocacolacompany.com/role/OtherComprehensiveIncomeTables">
        <link:definition>2315301 - Disclosure - OTHER COMPREHENSIVE INCOME (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementBenefitPlans" roleURI="http://www.thecocacolacompany.com/role/PensionAndOtherPostretirementBenefitPlans">
        <link:definition>2113100 - Disclosure - PENSION AND OTHER POSTRETIREMENT BENEFIT PLANS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementBenefitPlansDetails" roleURI="http://www.thecocacolacompany.com/role/PensionAndOtherPostretirementBenefitPlansDetails">
        <link:definition>2413402 - Disclosure - PENSION AND OTHER POSTRETIREMENT BENEFIT PLANS (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementBenefitPlansDetails2" roleURI="http://www.thecocacolacompany.com/role/PensionAndOtherPostretirementBenefitPlansDetails2">
        <link:definition>2413403 - Disclosure - PENSION AND OTHER POSTRETIREMENT BENEFIT PLANS (Details 2)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementBenefitPlansDetails3" roleURI="http://www.thecocacolacompany.com/role/PensionAndOtherPostretirementBenefitPlansDetails3">
        <link:definition>2413404 - Disclosure - PENSION AND OTHER POSTRETIREMENT BENEFIT PLANS (Details 3)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementBenefitPlansDetails4" roleURI="http://www.thecocacolacompany.com/role/PensionAndOtherPostretirementBenefitPlansDetails4">
        <link:definition>2413405 - Disclosure - PENSION AND OTHER POSTRETIREMENT BENEFIT PLANS (Details 4)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementBenefitPlansDetails5" roleURI="http://www.thecocacolacompany.com/role/PensionAndOtherPostretirementBenefitPlansDetails5">
        <link:definition>2413406 - Disclosure - PENSION AND OTHER POSTRETIREMENT BENEFIT PLANS (Details 5)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementBenefitPlansDetails6" roleURI="http://www.thecocacolacompany.com/role/PensionAndOtherPostretirementBenefitPlansDetails6">
        <link:definition>2413407 - Disclosure - PENSION AND OTHER POSTRETIREMENT BENEFIT PLANS (Details 6)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PensionAndOtherPostretirementBenefitPlansTables" roleURI="http://www.thecocacolacompany.com/role/PensionAndOtherPostretirementBenefitPlansTables">
        <link:definition>2313301 - Disclosure - PENSION AND OTHER POSTRETIREMENT BENEFIT PLANS (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ProductivityIntegrationAndRestructuringInitiatives" roleURI="http://www.thecocacolacompany.com/role/ProductivityIntegrationAndRestructuringInitiatives">
        <link:definition>2118100 - Disclosure - PRODUCTIVITY, INTEGRATION AND RESTRUCTURING INITIATIVES</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ProductivityIntegrationAndRestructuringInitiativesDetails" roleURI="http://www.thecocacolacompany.com/role/ProductivityIntegrationAndRestructuringInitiativesDetails">
        <link:definition>2418402 - Disclosure - PRODUCTIVITY, INTEGRATION AND RESTRUCTURING INITIATIVES (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="ProductivityIntegrationAndRestructuringInitiativesTables" roleURI="http://www.thecocacolacompany.com/role/ProductivityIntegrationAndRestructuringInitiativesTables">
        <link:definition>2318301 - Disclosure - PRODUCTIVITY, INTEGRATION AND RESTRUCTURING INITIATIVES (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PropertyPlantAndEquipment" roleURI="http://www.thecocacolacompany.com/role/PropertyPlantAndEquipment">
        <link:definition>2107100 - Disclosure - PROPERTY, PLANT AND EQUIPMENT</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PropertyPlantAndEquipmentDetails" roleURI="http://www.thecocacolacompany.com/role/PropertyPlantAndEquipmentDetails">
        <link:definition>2407402 - Disclosure - PROPERTY, PLANT AND EQUIPMENT (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="PropertyPlantAndEquipmentTables" roleURI="http://www.thecocacolacompany.com/role/PropertyPlantAndEquipmentTables">
        <link:definition>2307301 - Disclosure - PROPERTY, PLANT AND EQUIPMENT (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SignificantOperatingAndNonoperatingItems" roleURI="http://www.thecocacolacompany.com/role/SignificantOperatingAndNonoperatingItems">
        <link:definition>2117100 - Disclosure - SIGNIFICANT OPERATING AND NONOPERATING ITEMS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="SignificantOperatingAndNonoperatingItemsDetails" roleURI="http://www.thecocacolacompany.com/role/SignificantOperatingAndNonoperatingItemsDetails">
        <link:definition>2417401 - Disclosure - SIGNIFICANT OPERATING AND NONOPERATING ITEMS (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="StockCompensationPlans" roleURI="http://www.thecocacolacompany.com/role/StockCompensationPlans">
        <link:definition>2112100 - Disclosure - STOCK COMPENSATION PLANS</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="StockCompensationPlansDetails" roleURI="http://www.thecocacolacompany.com/role/StockCompensationPlansDetails">
        <link:definition>2412402 - Disclosure - STOCK COMPENSATION PLANS (Details)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType id="StockCompensationPlansTables" roleURI="http://www.thecocacolacompany.com/role/StockCompensationPlansTables">
        <link:definition>2312301 - Disclosure - STOCK COMPENSATION PLANS (Tables)</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
    </xsd:appinfo>
  </xsd:annotation>
  <xsd:element id="ko_AcceleratedShareBasedCompensationExpense" name="AcceleratedShareBasedCompensationExpense" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_AccountsPayableAndAccruedExpensesAbstract" name="AccountsPayableAndAccruedExpensesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_AccountsPayableAndAccruedExpensesDisclosureAbstract" name="AccountsPayableAndAccruedExpensesDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_AccountsPayableAndAccruedExpensesHeldForSale" name="AccountsPayableAndAccruedExpensesHeldForSale" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_AccruedIncomeTaxesHeldForSale" name="AccruedIncomeTaxesHeldForSale" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_AcquisitionOfCocaColaEnterprisesNorthAmericanBusinessMember" name="AcquisitionOfCocaColaEnterprisesNorthAmericanBusinessMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_AcquisitionsAndDivestituresDisclosureAbstract" name="AcquisitionsAndDivestituresDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_AcquisitionsAndInvestmentsPrincipallyBeverageAndBottlingCompaniesAndTrademarks" name="AcquisitionsAndInvestmentsPrincipallyBeverageAndBottlingCompaniesAndTrademarks" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_AdjustmentsToAdditionalPaidInCapitalShareBasedCompensationAwardsIssuedInConnectionWithAcquisition" name="AdjustmentsToAdditionalPaidInCapitalShareBasedCompensationAwardsIssuedInConnectionWithAcquisition" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_AggregateSalesAllowancesGoods" name="AggregateSalesAllowancesGoods" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_AmortazibleCarryingValueOfInfrastructurePrograms" name="AmortazibleCarryingValueOfInfrastructurePrograms" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_AmortizationExpenseForInfrastructurePrograms" name="AmortizationExpenseForInfrastructurePrograms" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_AmortizationForLeaseholdImprovements" name="AmortizationForLeaseholdImprovements" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_AmountsRecognizedAsOfAcquisitionDateAsAdjustedMember" name="AmountsRecognizedAsOfAcquisitionDateAsAdjustedMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_AmountsRecognizedAsOfAcquisitionDateMember" name="AmountsRecognizedAsOfAcquisitionDateMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_AssetImpairmentChargesOperating" name="AssetImpairmentChargesOperating" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_AssetImpairmentsAssetsMeasuredAtFairValueOnNonrecurringBasis" name="AssetImpairmentsAssetsMeasuredAtFairValueOnNonrecurringBasis" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_AssetsHeldForSaleAbstract" name="AssetsHeldForSaleAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_AujanBottlingAndDistributionCompanyMember" name="AujanBottlingAndDistributionCompanyMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_AujanIndustriesTrademarkOnwerMember" name="AujanIndustriesTrademarkOnwerMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_AvailableForSaleSecuritiesAndCostMethodInvestments" name="AvailableForSaleSecuritiesAndCostMethodInvestments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_AvailableForSaleSecuritiesAndHeldToMaturitySecuritiesTextBlock" name="AvailableForSaleSecuritiesAndHeldToMaturitySecuritiesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_AvailableForSaleSecuritiesDonatedCostBasis" name="AvailableForSaleSecuritiesDonatedCostBasis" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_AvailableForSaleSecuritiesDonatedFairValue" name="AvailableForSaleSecuritiesDonatedFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_BalanceSheetParentheticalAbstract" name="BalanceSheetParentheticalAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BeverageServingsConsumedPerDay" name="BeverageServingsConsumedPerDay" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BeverageServingsConsumedPerDayOfBusinessOperations" name="BeverageServingsConsumedPerDayOfBusinessOperations" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_BottlersFranchiseRightsMember" name="BottlersFranchiseRightsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_BottlingInvestmentsAndCorporateMember" name="BottlingInvestmentsAndCorporateMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_BottlingInvestmentsMember" name="BottlingInvestmentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BrandsFromTopFiveBrandsOfWorldOwnedAndMarketed" name="BrandsFromTopFiveBrandsOfWorldOwnedAndMarketed" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BrandsOwnedOrLicensedAndMarketed" name="BrandsOwnedOrLicensedAndMarketed" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessAcquisitionAndDivestitureCostOfAcquiredEntityTransactionCosts" name="BusinessAcquisitionAndDivestitureCostOfAcquiredEntityTransactionCosts" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_BusinessAcquisitionAndInvestmentLineItems" name="BusinessAcquisitionAndInvestmentLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessAcquisitionCostOfAcquiredEntityPercentageOfIndirectOwnershipInterestTransfer" name="BusinessAcquisitionCostOfAcquiredEntityPercentageOfIndirectOwnershipInterestTransfer" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionCostOfAcquiredEntityPortionOfAmountOfCashPaidRelatedToDebtShortfall" name="BusinessAcquisitionCostOfAcquiredEntityPortionOfAmountOfCashPaidRelatedToDebtShortfall" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionCostOfAcquiredEntityShareBasedPaymentRelatedToServicePriorToAcquisitionDate" name="BusinessAcquisitionCostOfAcquiredEntityShareBasedPaymentRelatedToServicePriorToAcquisitionDate" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessAcquisitionCostOfAcquiredEntityShareBasedPaymentRelatedToServicePriorToAcquisitionDateFairValue" name="BusinessAcquisitionCostOfAcquiredEntityShareBasedPaymentRelatedToServicePriorToAcquisitionDateFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionCostOfAcquiredEntityShareBasedPaymentRelatedToServicePriorToAcquisitionDateNetOfTax" name="BusinessAcquisitionCostOfAcquiredEntityShareBasedPaymentRelatedToServicePriorToAcquisitionDateNetOfTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessAcquisitionOperationalSynergiesPhaseInPeriod" name="BusinessAcquisitionOperationalSynergiesPhaseInPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionPurchasePriceAllocationBadDebt" name="BusinessAcquisitionPurchasePriceAllocationBadDebt" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionPurchasePriceAllocationBenefitPlanAssets" name="BusinessAcquisitionPurchasePriceAllocationBenefitPlanAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionPurchasePriceAllocationCommercialPaper" name="BusinessAcquisitionPurchasePriceAllocationCommercialPaper" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionPurchasePriceAllocationCurrentAssetsReceivablesGross" name="BusinessAcquisitionPurchasePriceAllocationCurrentAssetsReceivablesGross" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionPurchasePriceAllocationCurrentLiabilitiesAccountsPayableAndAccruedExpenses" name="BusinessAcquisitionPurchasePriceAllocationCurrentLiabilitiesAccountsPayableAndAccruedExpenses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionPurchasePriceAllocationCurrentLiabilitiesLoansAndNotesPayable" name="BusinessAcquisitionPurchasePriceAllocationCurrentLiabilitiesLoansAndNotesPayable" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionPurchasePriceAllocationCurrentLiabilitiesShortTermDebt" name="BusinessAcquisitionPurchasePriceAllocationCurrentLiabilitiesShortTermDebt" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionPurchasePriceAllocationGoodwillAmountAfterDeductionOfNetLiabilitiesAssumed" name="BusinessAcquisitionPurchasePriceAllocationGoodwillAmountAfterDeductionOfNetLiabilitiesAssumed" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_BusinessAcquisitionPurchasePriceAllocationProjectedBenefitObligation" name="BusinessAcquisitionPurchasePriceAllocationProjectedBenefitObligation" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_BusinessAcquisitionShareBasedPaymentAxis" name="BusinessAcquisitionShareBasedPaymentAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_BusinessAcquisitionShareBasedPaymentDomain" name="BusinessAcquisitionShareBasedPaymentDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessAcquisitionsAndDivestituresProFormaNetIncomeLoss" name="BusinessAcquisitionsAndDivestituresProFormaNetIncomeLoss" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessAcquisitionsAndDivestituresProFormaRevenue" name="BusinessAcquisitionsAndDivestituresProFormaRevenue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessAndSummaryOfSignificantAccountPoliciesTextBlock" name="BusinessAndSummaryOfSignificantAccountPoliciesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_BusinessAxis" name="BusinessAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessCombinationDivestituresAndInvestmentsDisclosureTextBlock" name="BusinessCombinationDivestituresAndInvestmentsDisclosureTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessCombinationStepAcquisitionEquityInterestInAcquireeRemeasurementGainAndPreExistentRelationshipSettlement" name="BusinessCombinationStepAcquisitionEquityInterestInAcquireeRemeasurementGainAndPreExistentRelationshipSettlement" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_BusinessDomain" name="BusinessDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessReorganizationNumberOfBusinessUnits" name="BusinessReorganizationNumberOfBusinessUnits" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessTransactionOwnershipInterestByThirdPartiesAfterTransaction" name="BusinessTransactionOwnershipInterestByThirdPartiesAfterTransaction" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessTransactionValuation" name="BusinessTransactionValuation" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_BusinessTransactionValuationPeriod" name="BusinessTransactionValuationPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CarlsbergGroupBeverageMember" name="CarlsbergGroupBeverageMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CarryingValueBeforeImpairmentFairValueDisclosureMember" name="CarryingValueBeforeImpairmentFairValueDisclosureMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_Cce2007PerformanceGrantsMember" name="Cce2007PerformanceGrantsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CcesNorthAmericanBusinessDomain" name="CcesNorthAmericanBusinessDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ChangesInAllowanceForDoubtfulAccountsTableTextBlock" name="ChangesInAllowanceForDoubtfulAccountsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ChangesInIncomeTaxReconciliationDeductionsMedicarePrescriptionDrugBenefitSubsidyDueToNewLegislation" name="ChangesInIncomeTaxReconciliationDeductionsMedicarePrescriptionDrugBenefitSubsidyDueToNewLegislation" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_CharitableContributions" name="CharitableContributions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CocaColaAmatilMember" name="CocaColaAmatilMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CocaColaBeveragesPakistanLtdMember" name="CocaColaBeveragesPakistanLtdMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CocaColaEmbonorSaMember" name="CocaColaEmbonorSaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CocaColaEnterprisesAndNorwegianAndSwedishBottlingOperationsMember" name="CocaColaEnterprisesAndNorwegianAndSwedishBottlingOperationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CocaColaEnterprisesIncMember" name="CocaColaEnterprisesIncMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CocaColaEnterprisesIncsNorthAmericanBusinessMember" name="CocaColaEnterprisesIncsNorthAmericanBusinessMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CocaColaFemsaMember" name="CocaColaFemsaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CocaColaFreestyleMember" name="CocaColaFreestyleMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CocaColaHellenicMember" name="CocaColaHellenicMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ColdDrinkEquipmentMember" name="ColdDrinkEquipmentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_CollectiveBargainingAgreementsPeriodHighEndOfRange" name="CollectiveBargainingAgreementsPeriodHighEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_CollectiveBargainingAgreementsPeriodLowEndOfRange" name="CollectiveBargainingAgreementsPeriodLowEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CommitedBusinessTransactionsDomain" name="CommitedBusinessTransactionsDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CommittedBusinessTransactionCaptionsLineItems" name="CommittedBusinessTransactionCaptionsLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_CommodityContractsAssetFairValueDisclosure" name="CommodityContractsAssetFairValueDisclosure" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_CommodityContractsLiabilitiesFairValueDisclosure" name="CommodityContractsLiabilitiesFairValueDisclosure" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_ComponentOfOperatingAndNonoperatingOtherCostAndExpenseLineItems" name="ComponentOfOperatingAndNonoperatingOtherCostAndExpenseLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ComponentOfOtherOperatingAndNonoperatingCostAndExpenseTable" name="ComponentOfOtherOperatingAndNonoperatingCostAndExpenseTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ComprehensiveIncomeLineItems" name="ComprehensiveIncomeLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ContractualMaturitiesOfSecuritiesTextBlock" name="ContractualMaturitiesOfSecuritiesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ConversionOfPerformanceShareUnitTargetPercentage" name="ConversionOfPerformanceShareUnitTargetPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_CorporateMember" name="CorporateMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_CostMethodInvestmentsAbstract" name="CostMethodInvestmentsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_CostsRelatedToExchangeOfEquitySecuritiesOfInvestee" name="CostsRelatedToExchangeOfEquitySecuritiesOfInvestee" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_CountriesCoveredForBusinessOperations" name="CountriesCoveredForBusinessOperations" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DebtAndBorrowingArrangementsDisclosureAbstract" name="DebtAndBorrowingArrangementsDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DebtInstrumentInterestLongTermNoteStatedPercentageRate" name="DebtInstrumentInterestLongTermNoteStatedPercentageRate" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_DebtInstrumentLongTermNoteFaceAmount" name="DebtInstrumentLongTermNoteFaceAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_DebtIssuedInExchangeOfAssumedDebt" name="DebtIssuedInExchangeOfAssumedDebt" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_DeconsolidationOfEntitiesPercentageOfNetIncomeToShareownersNetIncome" name="DeconsolidationOfEntitiesPercentageOfNetIncomeToShareownersNetIncome" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DeductionsFromRevenuePolicyTextBlock" name="DeductionsFromRevenuePolicyTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DeferredIncomeTaxesHeldForSale" name="DeferredIncomeTaxesHeldForSale" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_DeferredRevenueAmortizationPeriod" name="DeferredRevenueAmortizationPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_DeferredTaxAssetsGoodwillAndTrademarksAndIntangibleAssets" name="DeferredTaxAssetsGoodwillAndTrademarksAndIntangibleAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_DeferredTaxAssetsOrLiabilitiesNetOutsideUnitedStates" name="DeferredTaxAssetsOrLiabilitiesNetOutsideUnitedStates" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_DeferredTaxAssetsPropertyPlantAndEquipment" name="DeferredTaxAssetsPropertyPlantAndEquipment" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_DeferredTaxesLiabilitiesNetChangeInUnrealizedGainOrLoss" name="DeferredTaxesLiabilitiesNetChangeInUnrealizedGainOrLoss" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_DefinedBenefitPlanAssumptionsUsedCalculatingNetPeriodicBenefitCostAnnualizedReturnOnAssetsSinceInception" name="DefinedBenefitPlanAssumptionsUsedCalculatingNetPeriodicBenefitCostAnnualizedReturnOnAssetsSinceInception" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanAssumptionsUsedCalculatingNetPeriodicBenefitCostFifteenYearAnnualizedReturnOnAssets" name="DefinedBenefitPlanAssumptionsUsedCalculatingNetPeriodicBenefitCostFifteenYearAnnualizedReturnOnAssets" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanAssumptionsUsedCalculatingNetPeriodicBenefitCostTenYearAnnualizedReturnOnAssets" name="DefinedBenefitPlanAssumptionsUsedCalculatingNetPeriodicBenefitCostTenYearAnnualizedReturnOnAssets" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanBenefitsPaidForUnfundedPlans" name="DefinedBenefitPlanBenefitsPaidForUnfundedPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanChangeInBenefitObligationInterestCost" name="DefinedBenefitPlanChangeInBenefitObligationInterestCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanChangeInBenefitObligationServiceCost" name="DefinedBenefitPlanChangeInBenefitObligationServiceCost" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanChangeInFairValueOfPlanAssetsBenefitsPaid" name="DefinedBenefitPlanChangeInFairValueOfPlanAssetsBenefitsPaid" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanContributionByEmployerToEmployeeBeneficiaryTrust" name="DefinedBenefitPlanContributionByEmployerToEmployeeBeneficiaryTrust" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanCostOfProvidingSpecialOrContractualTerminationBenefitsRecognizedDuringPeriodDuration" name="DefinedBenefitPlanCostOfProvidingSpecialOrContractualTerminationBenefitsRecognizedDuringPeriodDuration" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanInvestmentInEntityStock" name="DefinedBenefitPlanInvestmentInEntityStock" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanMaximumPortionOfPlanAssetsEntrustedWithInvestmentManager" name="DefinedBenefitPlanMaximumPortionOfPlanAssetsEntrustedWithInvestmentManager" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_DefinedBenefitPlanMutualAndCommingledFunds" name="DefinedBenefitPlanMutualAndCommingledFunds" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanNetPeriodicBenefitCostBeforeCurtailmentsAndSpecialTerminationBenefits" name="DefinedBenefitPlanNetPeriodicBenefitCostBeforeCurtailmentsAndSpecialTerminationBenefits" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanNetPeriodicBenefitCostExcludingSettlementCurtailmentAndSpecialTerminationBenefits" name="DefinedBenefitPlanNetPeriodicBenefitCostExcludingSettlementCurtailmentAndSpecialTerminationBenefits" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanOtherAdjustments" name="DefinedBenefitPlanOtherAdjustments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanOtherAdjustmentsPlanAssets" name="DefinedBenefitPlanOtherAdjustmentsPlanAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanPooledFixedIncomeSecurities" name="DefinedBenefitPlanPooledFixedIncomeSecurities" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedBenefitPlanPortionOfProjectedPensionBenefitObligationPercentage" name="DefinedBenefitPlanPortionOfProjectedPensionBenefitObligationPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_DefinedBenefitPlanPortionOfProjectedPensionPlanAssetsPercentage" name="DefinedBenefitPlanPortionOfProjectedPensionPlanAssetsPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_DefinedBenefitPlanTargetAllocationPercentageOfAssetsAlternativeInvestments" name="DefinedBenefitPlanTargetAllocationPercentageOfAssetsAlternativeInvestments" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DefinedContributionPlanAxis" name="DefinedContributionPlanAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DefinedContributionPlanDomain" name="DefinedContributionPlanDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedContributionPlanEmployerContributionPercentageMatchOfEmployeeContribution" name="DefinedContributionPlanEmployerContributionPercentageMatchOfEmployeeContribution" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DefinedContributionPlanEmployerMaximumContributionPercentageOfCompensationHighEndOfRange" name="DefinedContributionPlanEmployerMaximumContributionPercentageOfCompensationHighEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_DefinedContributionPlanEmployerMaximumContributionPercentageOfCompensationLowEndOfRange" name="DefinedContributionPlanEmployerMaximumContributionPercentageOfCompensationLowEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_DefinedContributionPlanLineItems" name="DefinedContributionPlanLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DefinedContributionPlanTable" name="DefinedContributionPlanTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DerivativeInstrumentsFairValueDesignatedAsHedgingInstrumentsTextBlock" name="DerivativeInstrumentsFairValueDesignatedAsHedgingInstrumentsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DerivativeInstrumentsFairValueNotDesignatedAsHedgingInstrumentsTextBlock" name="DerivativeInstrumentsFairValueNotDesignatedAsHedgingInstrumentsTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DisclosureOfAssetsAndLiabilitiesHeldForSaleTableTextBlock" name="DisclosureOfAssetsAndLiabilitiesHeldForSaleTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DisposalGroupIncludingDiscontinuedOperationIncreaseDecreaseInRevenue" name="DisposalGroupIncludingDiscontinuedOperationIncreaseDecreaseInRevenue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_DisposalGroupNotDiscontinuedOperationSalePrice" name="DisposalGroupNotDiscontinuedOperationSalePrice" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DisposalGroupNotDiscontinuedOperationsAxis" name="DisposalGroupNotDiscontinuedOperationsAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DisposalGroupNotDiscontinuedOperationsTable" name="DisposalGroupNotDiscontinuedOperationsTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DisposalOfNorwegianAndSwedishBottlingOperationsMember" name="DisposalOfNorwegianAndSwedishBottlingOperationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_DistributionCharges" name="DistributionCharges" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DivestituresAbstract" name="DivestituresAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DivestituresLineItems" name="DivestituresLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DocumentAndEntityInformationAbstract" name="DocumentAndEntityInformationAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DomesticLargeCapEquitySecuritiesMember" name="DomesticLargeCapEquitySecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DomesticMidCapEquitySecuritiesMember" name="DomesticMidCapEquitySecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DomesticSmallCapEquitySecuritiesMember" name="DomesticSmallCapEquitySecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_DrPepperSnappleGroupMember" name="DrPepperSnappleGroupMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EchangedDebtAssumed" name="EchangedDebtAssumed" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxRateReconciiliationPreexistingRelationshipCharges" name="EffectiveIncomeTaxRateReconciiliationPreexistingRelationshipCharges" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxRateReconciliationNondeductibleExpenseRestructuringAndImpairmentCharges" name="EffectiveIncomeTaxRateReconciliationNondeductibleExpenseRestructuringAndImpairmentCharges" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxRateReconciliationOnAcquisitionOfBusiness" name="EffectiveIncomeTaxRateReconciliationOnAcquisitionOfBusiness" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxRateReconciliationOnSaleOfBusinessOrDivestiture" name="EffectiveIncomeTaxRateReconciliationOnSaleOfBusinessOrDivestiture" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxRateReconciliationOtherGains" name="EffectiveIncomeTaxRateReconciliationOtherGains" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxRateReconciliationOtherThanTemporaryImpairmentCharge" name="EffectiveIncomeTaxRateReconciliationOtherThanTemporaryImpairmentCharge" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxRateReconciliationReversalOfDeferredTaxLiabilities" name="EffectiveIncomeTaxRateReconciliationReversalOfDeferredTaxLiabilities" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxRateReconciliationUnusualItems" name="EffectiveIncomeTaxRateReconciliationUnusualItems" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxRateReconciliationUnusualItemsRecordedByInvestees" name="EffectiveIncomeTaxRateReconciliationUnusualItemsRecordedByInvestees" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxRateReconsiliationGainOnSaleOfInvestment" name="EffectiveIncomeTaxRateReconsiliationGainOnSaleOfInvestment" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EffectiveIncomeTaxReconciliationNondeductibleExpenseProportionalShareInvesteeRestructuringChargesAndAssetImpairments" name="EffectiveIncomeTaxReconciliationNondeductibleExpenseProportionalShareInvesteeRestructuringChargesAndAssetImpairments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EmployeeStock2002OptionPlanMember" name="EmployeeStock2002OptionPlanMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EmployeeStockOption2008OptionPlanMember" name="EmployeeStockOption2008OptionPlanMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EmployeeStockOptionPlan1999Member" name="EmployeeStockOptionPlan1999Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EmployeeStockOptionsGrantedFrom1999ThroughJuly2003Member" name="EmployeeStockOptionsGrantedFrom1999ThroughJuly2003Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EmployeeStockOptionsGrantedInDecember2003AndThereafterMember" name="EmployeeStockOptionsGrantedInDecember2003AndThereafterMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EmployeeStockOptionsGrantedPriorTo1999AndInDecember2003Member" name="EmployeeStockOptionsGrantedPriorTo1999AndInDecember2003Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EnvironmentalContingenciesMember" name="EnvironmentalContingenciesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EquityIncomeLossNetAbstract" name="EquityIncomeLossNetAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EquityInvesteeImpairmentOfFranchiseRights" name="EquityInvesteeImpairmentOfFranchiseRights" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_EquityInvesteeImpairmentOfFranchiseRightsNetOfTax" name="EquityInvesteeImpairmentOfFranchiseRightsNetOfTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_EquityMethodInvestmentDifferenceBetweenQuotedMarketValueAndCarryingValue" name="EquityMethodInvestmentDifferenceBetweenQuotedMarketValueAndCarryingValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_EquityMethodInvestmentProportionateShareOfUnusualOrInfrequentItemsRecordedByInvestees" name="EquityMethodInvestmentProportionateShareOfUnusualOrInfrequentItemsRecordedByInvestees" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EquityMethodInvestmentSummarizedFinancialInformationFinancialPositionAbstract" name="EquityMethodInvestmentSummarizedFinancialInformationFinancialPositionAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EquityMethodInvestmentSummarizedFinancialInformationNetIncomeLossAttributableToNonControllingInterest" name="EquityMethodInvestmentSummarizedFinancialInformationNetIncomeLossAttributableToNonControllingInterest" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_EquityMethodInvestmentSummarizedFinancialInformationOperatingIncomeLoss" name="EquityMethodInvestmentSummarizedFinancialInformationOperatingIncomeLoss" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_EquityMethodInvestmentSummarizedFinancialInformationProfitLoss" name="EquityMethodInvestmentSummarizedFinancialInformationProfitLoss" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EquityMethodInvestmentsAbstract" name="EquityMethodInvestmentsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_EquityMethodInvestmentsImpairment" name="EquityMethodInvestmentsImpairment" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_EquityMethodInvestmentsOtherThanTemporaryImpairment" name="EquityMethodInvestmentsOtherThanTemporaryImpairment" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EquityParentheticalAbstract" name="EquityParentheticalAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EquitySecuritiesInternationalBasedCompaniesMember" name="EquitySecuritiesInternationalBasedCompaniesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EquitySecuritiesUsBasedCompaniesMember" name="EquitySecuritiesUsBasedCompaniesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EurasiaAndAfricaMember" name="EurasiaAndAfricaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EuropeMember" name="EuropeMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_EuropeanPensionsPlansMember" name="EuropeanPensionsPlansMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ExpectedOperationalSynergies" name="ExpectedOperationalSynergies" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_FairValueAdjustmentOnAssumedDebt" name="FairValueAdjustmentOnAssumedDebt" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_FairValueAdjustmentsWeightedAverageAmortizationPeriod" name="FairValueAdjustmentsWeightedAverageAmortizationPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisDisclosureItemsAxis" name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisDisclosureItemsAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisDisclosureItemsDomain" name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisDisclosureItemsDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisFinancialStatementCaptionsLineItems" name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisFinancialStatementCaptionsLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisTable" name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisTextBlock" name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FairValueMeasurementNettingAdjustmentMember" name="FairValueMeasurementNettingAdjustmentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_FairValueMeasurementWithUnobservableInputsPurchasedAnnuityContracts" name="FairValueMeasurementWithUnobservableInputsPurchasedAnnuityContracts" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetBusinessCombinationsAndDivestituresNet" name="FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetBusinessCombinationsAndDivestituresNet" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetExchangeRateDifferences" name="FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetExchangeRateDifferences" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetReturnOnPlanAssetsAbstract" name="FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetReturnOnPlanAssetsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetReturnOnPlanAssetsHeldOnClosingDate" name="FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetReturnOnPlanAssetsHeldOnClosingDate" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetReturnOnPlanAssetsSoldDuringPeriod" name="FairValueMeasurementWithUnobservableInputsReconciliationRecurringBasisAssetReturnOnPlanAssetsSoldDuringPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FairValueMeasurementsDisclosureAbstract" name="FairValueMeasurementsDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_FairValueMeasurementsDisclosuresTextBlock" name="FairValueMeasurementsDisclosuresTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FairValueMeasurementsNewCostBasisMember" name="FairValueMeasurementsNewCostBasisMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_FairValueOfTradingSecuritiesIncludedInMarketableSecuritiesCaptionOnBalanceSheet" name="FairValueOfTradingSecuritiesIncludedInMarketableSecuritiesCaptionOnBalanceSheet" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_FairValueOfTradingSecuritiesIncludedInOtherAssetsCaptionOnBalanceSheet" name="FairValueOfTradingSecuritiesIncludedInOtherAssetsCaptionOnBalanceSheet" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_FiniteLivedAndIndefiniteLivedIntangibleAssetsByMajorClassAxis" name="FiniteLivedAndIndefiniteLivedIntangibleAssetsByMajorClassAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FiniteLivedAndIndefiniteLivedIntangibleAssetsByMajorClassDomain" name="FiniteLivedAndIndefiniteLivedIntangibleAssetsByMajorClassDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FiniteLivedAndIndefiniteLivedIntangibleAssetsByMajorClassLineItems" name="FiniteLivedAndIndefiniteLivedIntangibleAssetsByMajorClassLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_FiniteLivedAndIndefiniteLivedIntangibleAssetsByMajorClassTable" name="FiniteLivedAndIndefiniteLivedIntangibleAssetsByMajorClassTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_FiniteLivedIntangibleAssetsWeightedAverageEstimatedUsefulLife" name="FiniteLivedIntangibleAssetsWeightedAverageEstimatedUsefulLife" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ForeignDefinedContributionPlanMember" name="ForeignDefinedContributionPlanMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_GainLossOnInterestRateDerivativeInstrumentsAcquiredNotDesignatedAsHedgingInstruments" name="GainLossOnInterestRateDerivativeInstrumentsAcquiredNotDesignatedAsHedgingInstruments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_GainLossOnSaleOfStockInSubsidiaryOrEquityMethodInvesteeIncomeRecognizedOperatingSegmentsCombined" name="GainLossOnSaleOfStockInSubsidiaryOrEquityMethodInvesteeIncomeRecognizedOperatingSegmentsCombined" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_GoodwillDivestitureAndDeconsolidation" name="GoodwillDivestitureAndDeconsolidation" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_GoodwillMember" name="GoodwillMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_GoodwillTrademarksAndOtherIntangibleAssetsAbstract" name="GoodwillTrademarksAndOtherIntangibleAssetsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_GreatPlainsCocaColaBottlingCompanyMember" name="GreatPlainsCocaColaBottlingCompanyMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_HedgeFundsAndLimitedPartnershipsMember" name="HedgeFundsAndLimitedPartnershipsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_HedgingTransactionsAndDerivativeFinancialInstrumentsDisclosuresAbstract" name="HedgingTransactionsAndDerivativeFinancialInstrumentsDisclosuresAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_HeldToMaturitySecuritiesUnrealizedGainsGross" name="HeldToMaturitySecuritiesUnrealizedGainsGross" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_HeldToMaturitySecuritiesUnrealizedLossesGross" name="HeldToMaturitySecuritiesUnrealizedLossesGross" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_HighEndOfRangeLittleOrNoDefenseOrIndemnityCostsThatWillNotBeCovered" name="HighEndOfRangeLittleOrNoDefenseOrIndemnityCostsThatWillNotBeCovered" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_HighYieldBondsMember" name="HighYieldBondsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ImpactOnEffectiveIncomeTaxRateReconciliationForDebtExtingusimentAndOtherCharges" name="ImpactOnEffectiveIncomeTaxRateReconciliationForDebtExtingusimentAndOtherCharges" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ImpairedAssetByTypeDomain" name="ImpairedAssetByTypeDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ImpairmentChargeByAssetAxis" name="ImpairmentChargeByAssetAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_IncomeStatementLocationAxis" name="IncomeStatementLocationAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_IncomeStatementLocationDomain" name="IncomeStatementLocationDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_IncomeTaxExpenseBenefitAmortizationOfFavorableSupplyContracts" name="IncomeTaxExpenseBenefitAmortizationOfFavorableSupplyContracts" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_IncomeTaxExpenseBenefitDebtRepurchasedExtinguishedAndOtherFinancialItems" name="IncomeTaxExpenseBenefitDebtRepurchasedExtinguishedAndOtherFinancialItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_IncomeTaxExpenseBenefitFinalizationOfWorkingCapitalAdjustments" name="IncomeTaxExpenseBenefitFinalizationOfWorkingCapitalAdjustments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_IncomeTaxExpenseBenefitReconciliationRemeasurementOfEquityMethodInvestmentToFairValueOnBusinessAcquisition" name="IncomeTaxExpenseBenefitReconciliationRemeasurementOfEquityMethodInvestmentToFairValueOnBusinessAcquisition" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_IncomeTaxExpenseBenefitUnusualOrInfrequentItemsProductivityIntegrationRestructuringTransactionCostsAndOtherActivities" name="IncomeTaxExpenseBenefitUnusualOrInfrequentItemsProductivityIntegrationRestructuringTransactionCostsAndOtherActivities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_IncomeTaxExpenseBenefitUnusualOrInfrequentItemsTransactionGainsLosses" name="IncomeTaxExpenseBenefitUnusualOrInfrequentItemsTransactionGainsLosses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_IncomeTaxExpenseBenefiteRelatedToPreexistingRelationship" name="IncomeTaxExpenseBenefiteRelatedToPreexistingRelationship" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_IncomeTaxesAbstract" name="IncomeTaxesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_IncreaseDecreaseInIncomeLossFromDiscontinuedOperationsNetOfTax" name="IncreaseDecreaseInIncomeLossFromDiscontinuedOperationsNetOfTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_IncreaseDecreaseInOperatingCapitalDisclosureTextBlock" name="IncreaseDecreaseInOperatingCapitalDisclosureTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_IncreaseDecreaseInValuationAllowanceResultingFromAcquisitionOfBusiness" name="IncreaseDecreaseInValuationAllowanceResultingFromAcquisitionOfBusiness" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_IncreaseInCarryingValueOfLongTermDebtInterestRateFairValueHedgeAdjustment" name="IncreaseInCarryingValueOfLongTermDebtInterestRateFairValueHedgeAdjustment" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_IndefiniteLivedIntangibleAssetsAbstract" name="IndefiniteLivedIntangibleAssetsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_IndefiniteLivedIntangibleAssetsIncludingGoodwill" name="IndefiniteLivedIntangibleAssetsIncludingGoodwill" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_IntangibleAssetsAbstract" name="IntangibleAssetsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_IntangibleAssetsAndAccountsReceivableCarryingValue" name="IntangibleAssetsAndAccountsReceivableCarryingValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_IntangibleAssetsHeldForSale" name="IntangibleAssetsHeldForSale" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_IntegrationActivitiesExpensesRecognitionPeriod" name="IntegrationActivitiesExpensesRecognitionPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_IntegrationOfAcquiredGermanBottlingAndDistributionOperationsMember" name="IntegrationOfAcquiredGermanBottlingAndDistributionOperationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_IntegrationOfCcesNorthAmericanOperationsMember" name="IntegrationOfCcesNorthAmericanOperationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_IntegrationOfCocaColaEnterprisesNorthAmericanOperationsMember" name="IntegrationOfCocaColaEnterprisesNorthAmericanOperationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_InternationalEquitySecuritiesMember" name="InternationalEquitySecuritiesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_InternationalMember" name="InternationalMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_InventoriesAbstract" name="InventoriesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_InvestmentInAujanIndustriesMember" name="InvestmentInAujanIndustriesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_InvestmentStrategyAllocationTargetsForInternationalPlans" name="InvestmentStrategyAllocationTargetsForInternationalPlans" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_InvestmentsByBalanceSheetGroupingTextBlock" name="InvestmentsByBalanceSheetGroupingTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_InvestmentsClassifiedAsCashEquivalentsMaximumMaturityPeriod" name="InvestmentsClassifiedAsCashEquivalentsMaximumMaturityPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_InvestmentsClassifiedAsShortTermInvestmentsMaturityPeriodHighEndOfRange" name="InvestmentsClassifiedAsShortTermInvestmentsMaturityPeriodHighEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_InvestmentsClassifiedAsShortTermInvestmentsMaturityPeriodLowEndOfRange" name="InvestmentsClassifiedAsShortTermInvestmentsMaturityPeriodLowEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_InvestmentsDisclosureAbstract" name="InvestmentsDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_InvestmentsDisclosureTextBlock" name="InvestmentsDisclosureTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_IssuanceOfLongTermDebt" name="IssuanceOfLongTermDebt" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_JapanRevenueToOperatingRevenueNetPercentage" name="JapanRevenueToOperatingRevenueNetPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_JapansEventsMember" name="JapansEventsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_LatinAmericaMember" name="LatinAmericaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_LeaoJuniorMember" name="LeaoJuniorMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_LeaoJuniorSaMember" name="LeaoJuniorSaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_LegalContingenciesAbstract" name="LegalContingenciesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_LiabilitiesHeldForSaleAbstract" name="LiabilitiesHeldForSaleAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_LiabilitiesHeldForSaleAtCarryingValue" name="LiabilitiesHeldForSaleAtCarryingValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_LineOfCreditFacilityForGeneralCorporatePurposeMaximumBorrowingCapacity" name="LineOfCreditFacilityForGeneralCorporatePurposeMaximumBorrowingCapacity" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_LineOfCreditFacilityForGeneralCorporatePurposeRemainingBorrowingCapacity" name="LineOfCreditFacilityForGeneralCorporatePurposeRemainingBorrowingCapacity" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element id="ko_LoansAndNotesPayable" name="LoansAndNotesPayable" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_LongDurationBondsMember" name="LongDurationBondsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_LongTermDebtAverageInterestRate" name="LongTermDebtAverageInterestRate" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_LongTermDebtAverageInterestRateAdjusted" name="LongTermDebtAverageInterestRateAdjusted" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_LongTermNotesMember" name="LongTermNotesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_LossContingenciesAutoInsurance" name="LossContingenciesAutoInsurance" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_LossContingenciesPropertyLossInsurance" name="LossContingenciesPropertyLossInsurance" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_LossContingenciesRiskInsuranceReserves" name="LossContingenciesRiskInsuranceReserves" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_LossContingenciesWorkersCompensation" name="LossContingenciesWorkersCompensation" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_LossContingencyCountersuitNumberOfPartiesDefendant" name="LossContingencyCountersuitNumberOfPartiesDefendant" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_LossContingencyCountersuitNumberOfPartiesPlaintiff" name="LossContingencyCountersuitNumberOfPartiesPlaintiff" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_LossContingencyDamagesSoughtAmount" name="LossContingencyDamagesSoughtAmount" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_LossContingencyInsurancePolicyPurchased" name="LossContingencyInsurancePolicyPurchased" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_LossContingencyInsuranceRecoverablePolicyLimitPercentCovered" name="LossContingencyInsuranceRecoverablePolicyLimitPercentCovered" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_LowEndOfRangeLittleOrNoDefenseOrIndemnityCostThatWillNotBeCovered" name="LowEndOfRangeLittleOrNoDefenseOrIndemnityCostThatWillNotBeCovered" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_MachineryEquipmentAndVehicleFleetMember" name="MachineryEquipmentAndVehicleFleetMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_MaximumPeriodForWhichDefenceOrIndemnityCostsAreNotCoveredUnderInsurance" name="MaximumPeriodForWhichDefenceOrIndemnityCostsAreNotCoveredUnderInsurance" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_MeasurementPeriodAdjustmentsMember" name="MeasurementPeriodAdjustmentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_MergerOfEmbotelladorasArcaSabDeCvAndGrupoContinentalSabMember" name="MergerOfEmbotelladorasArcaSabDeCvAndGrupoContinentalSabMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_MinimumPeriodForWhichDefenceOrIndemnityCostsAreNotCoveredUnderInsurance" name="MinimumPeriodForWhichDefenceOrIndemnityCostsAreNotCoveredUnderInsurance" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_MonetaryAssets" name="MonetaryAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_MostlyLocatedOutsideOfUsaExcludingCceMember" name="MostlyLocatedOutsideOfUsaExcludingCceMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_MultiemployerPlanWithdrawalObligationNumberOfPlans" name="MultiemployerPlanWithdrawalObligationNumberOfPlans" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_MultiemployerPlanWithdrawalPlanContribution" name="MultiemployerPlanWithdrawalPlanContribution" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_MutualPooledAndCommingledFundsMember" name="MutualPooledAndCommingledFundsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_NetChangeInOperatingAssetsAndLiabilitiesDisclosureAbstract" name="NetChangeInOperatingAssetsAndLiabilitiesDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_NetChargeOnExchangeRepaymentOrExtinguishmentOfLongTermDebt" name="NetChargeOnExchangeRepaymentOrExtinguishmentOfLongTermDebt" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_NetGainOnExchangeOfEquitySecurities" name="NetGainOnExchangeOfEquitySecurities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_NetGainsFromInvesteeTransactionsandEquityInvestmentSales" name="NetGainsFromInvesteeTransactionsandEquityInvestmentSales" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_NewEntityToBeFormedSubsequentToClosingOfCceTransactionAbstract" name="NewEntityToBeFormedSubsequentToClosingOfCceTransactionAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_NoncashOrPartNoncashAcquisitionDebtAssumedAtMergerAgreementDate" name="NoncashOrPartNoncashAcquisitionDebtAssumedAtMergerAgreementDate" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_NorthAmericaMember" name="NorthAmericaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_NotesToCondensedConsolidatedFinancialStatementsAbstract" name="NotesToCondensedConsolidatedFinancialStatementsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_NumberOfAssociates" name="NumberOfAssociates" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_NumberOfAssociatesCoveredByCollectiveBargainingAgreements" name="NumberOfAssociatesCoveredByCollectiveBargainingAgreements" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_NumberOfExchangeRatesAvailableForRemeasuringBolivarDenominatedTransactions" name="NumberOfExchangeRatesAvailableForRemeasuringBolivarDenominatedTransactions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_NumberOfOperationalUnitsInvolvedInIntegrationInitiatives" name="NumberOfOperationalUnitsInvolvedInIntegrationInitiatives" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_OaoNidanJuicesRussianJuiceCompanyMember" name="OaoNidanJuicesRussianJuiceCompanyMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_OfficialExchangeRateSetByGovernmentForEssentialGoods" name="OfficialExchangeRateSetByGovernmentForEssentialGoods" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_OfficialExchangeRateSetByGovernmentForNonEssentialGoods" name="OfficialExchangeRateSetByGovernmentForNonEssentialGoods" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_OperatingLossCarryforwardsSubjectToLimitationsOfUptoFiveYears" name="OperatingLossCarryforwardsSubjectToLimitationsOfUptoFiveYears" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_OperatingSegmentsDisclosureAbstract" name="OperatingSegmentsDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_OtherComprehensiveIncomeAbstract" name="OtherComprehensiveIncomeAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_OtherComprehensiveIncomeDefinedBenefitPlansDivestituresImpactDuringPeriodBeforeTax" name="OtherComprehensiveIncomeDefinedBenefitPlansDivestituresImpactDuringPeriodBeforeTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_OtherComprehensiveIncomeDefinedBenefitPlansNetTranslationGainLossArisingDuringPeriodBeforeTax" name="OtherComprehensiveIncomeDefinedBenefitPlansNetTranslationGainLossArisingDuringPeriodBeforeTax" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_OtherComprehensiveIncomeForeignCurrencyTransactionAndTranslationAdjustmentAndUnrealizedGainOrLossOnForeignExchangeHedgesNetOfTaxPeriodIncreaseDecrease" name="OtherComprehensiveIncomeForeignCurrencyTransactionAndTranslationAdjustmentAndUnrealizedGainOrLossOnForeignExchangeHedgesNetOfTaxPeriodIncreaseDecrease" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_OtherComprehensiveIncomeNetChangeInUnrealizedGainOnAvailableForSaleSecurities" name="OtherComprehensiveIncomeNetChangeInUnrealizedGainOnAvailableForSaleSecurities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_OtherCostAndExpenseOperatingProductivityInitiatives" name="OtherCostAndExpenseOperatingProductivityInitiatives" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_OtherCostAndExpenseOperatingUnusualOrInfrequentItems" name="OtherCostAndExpenseOperatingUnusualOrInfrequentItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_OtherDerivativesInstrumentsAssetFairValueDisclosure" name="OtherDerivativesInstrumentsAssetFairValueDisclosure" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_OtherDerivativesInstrumentsLiabilitiesFairValueDisclosure" name="OtherDerivativesInstrumentsLiabilitiesFairValueDisclosure" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_OtherDue2018Member" name="OtherDue2018Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_OtherDueThrough2018Member" name="OtherDueThrough2018Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_OtherEquityMethodInvesteesMember" name="OtherEquityMethodInvesteesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_OtherFiniteLivedAndIndefiniteLivedMember" name="OtherFiniteLivedAndIndefiniteLivedMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_OtherIncomeLossNetAbstract" name="OtherIncomeLossNetAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_OtherIndefiniteLivedAndFiniteLivedIntangibleAssets" name="OtherIndefiniteLivedAndFiniteLivedIntangibleAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_OtherInitiativesMember" name="OtherInitiativesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_OtherLongTermDebtMember" name="OtherLongTermDebtMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_OtherNonoperatingItemsAbstract" name="OtherNonoperatingItemsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_OtherOperatingChargesDisclosureAbstract" name="OtherOperatingChargesDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_OtherOperatingChargesMember" name="OtherOperatingChargesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_OtherThanTemporaryCharges" name="OtherThanTemporaryCharges" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_OtherThanTemporaryImpairmentLossesInvestmentsAndDonationsPortionRecognizedInEarningsNet" name="OtherThanTemporaryImpairmentLossesInvestmentsAndDonationsPortionRecognizedInEarningsNet" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_OwnershipInterestInInvestmentByThirdParties" name="OwnershipInterestInInvestmentByThirdParties" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_PacificMember" name="PacificMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ParentCompanyExcludingCcesNorthAmericaBusinessMember" name="ParentCompanyExcludingCcesNorthAmericaBusinessMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PendingDisclosureOfCocaColaEnterprisesNorthAmericaMember" name="PendingDisclosureOfCocaColaEnterprisesNorthAmericaMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PendingDisposalOfGermanBottlerOperationsMember" name="PendingDisposalOfGermanBottlerOperationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PendingDisposalOfNorwegianAndSwedishBottlingOperationsMember" name="PendingDisposalOfNorwegianAndSwedishBottlingOperationsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PendingTransactionCompletionPeriodOfTimeAfterDefinitiveAgreementHighEndOfRange" name="PendingTransactionCompletionPeriodOfTimeAfterDefinitiveAgreementHighEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PendingTransactionCompletionPeriodOfTimeAfterDefinitiveAgreementLowEndOfRange" name="PendingTransactionCompletionPeriodOfTimeAfterDefinitiveAgreementLowEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PensionAndOtherPostretirementBenefitPlansAbstract" name="PensionAndOtherPostretirementBenefitPlansAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PercentageOfCarryingValueOfInvestment" name="PercentageOfCarryingValueOfInvestment" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PercentageOfCashAndCashEquivalentsBalanceOfMonetaryAssets" name="PercentageOfCashAndCashEquivalentsBalanceOfMonetaryAssets" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PercentageOfDeclineOfCommonStockMarketPriceMinimum" name="PercentageOfDeclineOfCommonStockMarketPriceMinimum" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PercentageOfOwnerhipInSubsidiary" name="PercentageOfOwnerhipInSubsidiary" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PercentageOfSaleOfInvestmentsInSubsidiary" name="PercentageOfSaleOfInvestmentsInSubsidiary" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformanceBasedRestrictedStockUnitAwardsMember" name="PerformanceBasedRestrictedStockUnitAwardsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformanceGrants2004Member" name="PerformanceGrants2004Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformancePeriod2007To2009Member" name="PerformancePeriod2007To2009Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformancePeriod2008To2010And2009And2010Member" name="PerformancePeriod2008To2010And2009And2010Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformancePeriod2008To2010And2010Member" name="PerformancePeriod2008To2010And2010Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformancePeriod2008To2010Member" name="PerformancePeriod2008To2010Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformancePeriod2009Member" name="PerformancePeriod2009Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformancePeriod2010To2012Member" name="PerformancePeriod2010To2012Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformancePeriod2011To2013Member" name="PerformancePeriod2011To2013Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformancePeriodAxis" name="PerformancePeriodAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformancePeriodDomain" name="PerformancePeriodDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PerformanceShareUnitsMember" name="PerformanceShareUnitsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PeriodForWhichDefenseAndIndemnityCostsAreInSameRange" name="PeriodForWhichDefenseAndIndemnityCostsAreInSameRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PeriodOfAgreementWithCustomersHighEndOfRange" name="PeriodOfAgreementWithCustomersHighEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PeriodOfAgreementWithCustomersLowEndOfRange" name="PeriodOfAgreementWithCustomersLowEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PremiumOnExchangeOfLongTermDebt" name="PremiumOnExchangeOfLongTermDebt" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_PremiumOnRepaymentOfLongTermDebt" name="PremiumOnRepaymentOfLongTermDebt" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PrepaidExpensesAndOtherAssetsMember" name="PrepaidExpensesAndOtherAssetsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PrepaidExpensesAndOtherCurrentAssetsHeldForSale" name="PrepaidExpensesAndOtherCurrentAssetsHeldForSale" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_PrescriptionDrugSubsidyEffectOfChangeInLegislationOnDeferredTaxAssets" name="PrescriptionDrugSubsidyEffectOfChangeInLegislationOnDeferredTaxAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_PrescriptionDrugSubsidyEffectOfChangeInLegislationOnIncomeTaxExpense" name="PrescriptionDrugSubsidyEffectOfChangeInLegislationOnIncomeTaxExpense" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_PrescriptionDrugSubsidyReceiptsFirstFiveFiscalYears" name="PrescriptionDrugSubsidyReceiptsFirstFiveFiscalYears" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_PrimaryUnitedStatesPlanOfUsEntityDefinedBenefitMember" name="PrimaryUnitedStatesPlanOfUsEntityDefinedBenefitMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PrimaryUsPlanMember" name="PrimaryUsPlanMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PrincipalNotesDue2011VariabelInterestRateMember" name="PrincipalNotesDue2011VariabelInterestRateMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PrincipalNotesDueMarch152014Member" name="PrincipalNotesDueMarch152014Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PrincipalNotesDueNovember152015Member" name="PrincipalNotesDueNovember152015Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PrincipalNotesUSDollarDebenturesDue2012To2098Member" name="PrincipalNotesUSDollarDebenturesDue2012To2098Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ProceedsFromDisposalsOfBottlingCompaniesAndOtherInvestments" name="ProceedsFromDisposalsOfBottlingCompaniesAndOtherInvestments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_ProceedsFromDisposalsOfShortTermInvestments" name="ProceedsFromDisposalsOfShortTermInvestments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_ProceedsFromDivestitureActivities" name="ProceedsFromDivestitureActivities" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_ProceedsFromSpecialDividendsReceived" name="ProceedsFromSpecialDividendsReceived" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ProductivityAndStreamliningInitiativesByInitiativeDomain" name="ProductivityAndStreamliningInitiativesByInitiativeDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ProductivityInitiativesByInitiativeAxis" name="ProductivityInitiativesByInitiativeAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ProductivityInitiativesMember" name="ProductivityInitiativesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ProductivityIntegrationAndRestructuringInitiativesAbstract" name="ProductivityIntegrationAndRestructuringInitiativesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PropertyPlantAndEquipamentAbstract" name="PropertyPlantAndEquipamentAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PropertyPlantAndEquipmentNetPercentage" name="PropertyPlantAndEquipmentNetPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_PurchasePriceAllocationAxis" name="PurchasePriceAllocationAxis" nillable="true" substitutionGroup="xbrldt:dimensionItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_PurchasePriceByMajorClassOfAssetsAndLiabilitiesDomain" name="PurchasePriceByMajorClassOfAssetsAndLiabilitiesDomain" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_PurchasesOfShortTermInvestments" name="PurchasesOfShortTermInvestments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_RelatedPartyTransactionAbstract" name="RelatedPartyTransactionAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_RelatedPartyTransactionMarketingExpensesReimbursed" name="RelatedPartyTransactionMarketingExpensesReimbursed" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_RelatedPartyTransactionMarketingPaymentsDirectlyToRelatedParty" name="RelatedPartyTransactionMarketingPaymentsDirectlyToRelatedParty" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_RelatedPartyTransactionMarketingPaymentsToThirdParty" name="RelatedPartyTransactionMarketingPaymentsToThirdParty" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_RelatedPartyTransactionOtherPayments" name="RelatedPartyTransactionOtherPayments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_RelatedPartyTransactionRawMaterialPurchasesByRelatedParty" name="RelatedPartyTransactionRawMaterialPurchasesByRelatedParty" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_RelatedPartyTransactionRepairExpensesPaymentsDirectlyToRelatedParty" name="RelatedPartyTransactionRepairExpensesPaymentsDirectlyToRelatedParty" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_RemeasurementChargesOnSubsidiaryAssets" name="RemeasurementChargesOnSubsidiaryAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_RemilAndCocaColaBeveragesPakistanLtdMember" name="RemilAndCocaColaBeveragesPakistanLtdMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_RemilMember" name="RemilMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_RenewalPeriodOfLicenseAgreement" name="RenewalPeriodOfLicenseAgreement" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_RepaymentAssumedDebtUnamortizedFairValueAdjustments" name="RepaymentAssumedDebtUnamortizedFairValueAdjustments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_Restricted1983StockAwardPlanMember" name="Restricted1983StockAwardPlanMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_RestrictedShareUnitsMember" name="RestrictedShareUnitsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_RestrictedStock1989AwardPlanMember" name="RestrictedStock1989AwardPlanMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_RestrictedStockAwardPlanMember" name="RestrictedStockAwardPlanMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_RestructuringAndRelatedCostNumberOfFacilitiesAffected" name="RestructuringAndRelatedCostNumberOfFacilitiesAffected" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_RestructuringChargesOtherDirectCostsMember" name="RestructuringChargesOtherDirectCostsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_RestructuringChargesRelatedToEquityInvestmentsMember" name="RestructuringChargesRelatedToEquityInvestmentsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_RestructuringCostsIntegrationInitiativesTableTextBlock" name="RestructuringCostsIntegrationInitiativesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_RestructuringCostsProductivityInitiativesTextBlock" name="RestructuringCostsProductivityInitiativesTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_RestructuringProductivityInitiativesAndAssetImpairmentCharges" name="RestructuringProductivityInitiativesAndAssetImpairmentCharges" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_RestructuringReserveRefinementOfRestructuringAccruals" name="RestructuringReserveRefinementOfRestructuringAccruals" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_RiskManagementProgramsAbstract" name="RiskManagementProgramsAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_RiskManagementProgramsMember" name="RiskManagementProgramsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_SaleOfStockPercentageOfOwnershipSold" name="SaleOfStockPercentageOfOwnershipSold" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_SalesRevenueConcentrateOperationsNetPercentage" name="SalesRevenueConcentrateOperationsNetPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_SalesRevenueFinishedProductsOperationsNetPercentage" name="SalesRevenueFinishedProductsOperationsNetPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_SalesRevenueNetPercentage" name="SalesRevenueNetPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_ScheduleOfAccumulatedOtherComprehensiveIncomeDefinedBenefitPlanTableTextBlock" name="ScheduleOfAccumulatedOtherComprehensiveIncomeDefinedBenefitPlanTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfAccumulatedOtherComprehensiveIncomeTableTextBlock" name="ScheduleOfAccumulatedOtherComprehensiveIncomeTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfAllocationOfOtherPostretirementPlanAssetsTableTextBlock" name="ScheduleOfAllocationOfOtherPostretirementPlanAssetsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfAllocationOfPensionPlanAssetsTableTextBlock" name="ScheduleOfAllocationOfPensionPlanAssetsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfAmountsRecognizedInAccumulatedOtherComprehensiveIncomeLossTableTextBlock" name="ScheduleOfAmountsRecognizedInAccumulatedOtherComprehensiveIncomeLossTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ScheduleOfBusinessAcquisitionsAndInvestmentActivityTable" name="ScheduleOfBusinessAcquisitionsAndInvestmentActivityTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfCashFlowHedgesGainsLossesRecognizedInAociAndEarningsTableTextBlock" name="ScheduleOfCashFlowHedgesGainsLossesRecognizedInAociAndEarningsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfChangesInProjectedBenefitObligationsAndFairValueOfPlanAssetsTableTextBlock" name="ScheduleOfChangesInProjectedBenefitObligationsAndFairValueOfPlanAssetsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ScheduleOfCommittedBusinessTransactionByTransactionTable" name="ScheduleOfCommittedBusinessTransactionByTransactionTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfConversionOfPerformanceShareUnitsToRestrictedStockAndPromisesToGrantRestrictedStockTableTextBlock" name="ScheduleOfConversionOfPerformanceShareUnitsToRestrictedStockAndPromisesToGrantRestrictedStockTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfDeferredTaxAssetValuationAllowancesTableTextBlock" name="ScheduleOfDeferredTaxAssetValuationAllowancesTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfDerivativeInstrumentsNotDesignatedAsHedgingInstrumentsGainLossInStatementOfFinancialPerformanceTableTextBlock" name="ScheduleOfDerivativeInstrumentsNotDesignatedAsHedgingInstrumentsGainLossInStatementOfFinancialPerformanceTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfEquityMethodInvestmentSignificantTransactionsPriorToAcquisitionTableTextBlock" name="ScheduleOfEquityMethodInvestmentSignificantTransactionsPriorToAcquisitionTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfEquityMethodInvestmentSummarizedFinancialInformationPriorToAcquisitionTableTextBlock" name="ScheduleOfEquityMethodInvestmentSummarizedFinancialInformationPriorToAcquisitionTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfEquityMethodInvestmentsExcludingAcquireeSummarizedFinancialInformationTableTextBlock" name="ScheduleOfEquityMethodInvestmentsExcludingAcquireeSummarizedFinancialInformationTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfFairValueAllocationOfOtherPostretirementPlanAssetsTableTextBlock" name="ScheduleOfFairValueAllocationOfOtherPostretirementPlanAssetsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfFairValueAllocationOfPensionPlanAssetsTableTextBlock" name="ScheduleOfFairValueAllocationOfPensionPlanAssetsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfFairValueHedgesGainsLossesRecognizedInIncomeLocationTableTextBlock" name="ScheduleOfFairValueHedgesGainsLossesRecognizedInIncomeLocationTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ScheduleOfIncomeTaxLineItems" name="ScheduleOfIncomeTaxLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ScheduleOfIncomeTaxTable" name="ScheduleOfIncomeTaxTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfIndefiniteLivedIntangibleAssetsTableTextBlock" name="ScheduleOfIndefiniteLivedIntangibleAssetsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfOperationsInformationTableTextBlock" name="ScheduleOfOperationsInformationTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfOtherComprehensiveIncomeLossTableTextBlock" name="ScheduleOfOtherComprehensiveIncomeLossTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfProFormaInformationOfAcquisitionsAndDivestituresTableTextBlock" name="ScheduleOfProFormaInformationOfAcquisitionsAndDivestituresTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfProjectedBenefitObligationsInExcessOfFairValueOfPlanAssetsTableTextBlock" name="ScheduleOfProjectedBenefitObligationsInExcessOfFairValueOfPlanAssetsTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfShareBasedPaymentAwardsAsPartOfBusinessCombinationTableTextBlock" name="ScheduleOfShareBasedPaymentAwardsAsPartOfBusinessCombinationTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ScheduleOfTotalPuchasePricePaidByAcquireeTableTextBlock" name="ScheduleOfTotalPuchasePricePaidByAcquireeTableTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_SegmentReportingInformationInvestmentsIncludingEquityMethodInvestments" name="SegmentReportingInformationInvestmentsIncludingEquityMethodInvestments" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_SegmentReportingRestructuringContractTerminationProductivityInitiativesAndAssetImpairmentCharges" name="SegmentReportingRestructuringContractTerminationProductivityInitiativesAndAssetImpairmentCharges" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_SegmentReportingSegmentOperatingAssets" name="SegmentReportingSegmentOperatingAssets" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardExpirationPeriod" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardExpirationPeriod" nillable="true" substitutionGroup="xbrli:item" type="us-types:durationStringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardPerformancePeriod" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardPerformancePeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardPerformancePeriodHighEndOfRange" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardPerformancePeriodHighEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardPerformancePeriodLowEndOfRange" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardPerformancePeriodLowEndOfRange" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardRequisiteHoldingPeriodAwardsGranted2008AndBeyond" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardRequisiteHoldingPeriodAwardsGranted2008AndBeyond" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardRequisiteHoldingPeriodAwardsGrantedBefore2008" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardAwardRequisiteHoldingPeriodAwardsGrantedBefore2008" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardDisclosurePeriod" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardDisclosurePeriod" nillable="true" substitutionGroup="xbrli:item" type="us-types:durationStringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsAward2004Grants" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsAward2004Grants" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsAwardBasedOnBusinessUnitPerformance" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsAwardBasedOnBusinessUnitPerformance" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsConvertedInRestrictedStockInPeriodWeightedAverageGrantDateFairValue" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsConvertedInRestrictedStockInPeriodWeightedAverageGrantDateFairValue" nillable="true" substitutionGroup="xbrli:item" type="num:perShareItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsConvertedInRestrictedStockUnitsInPeriod" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsConvertedInRestrictedStockUnitsInPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsNonvestedNumberAtTarget" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsNonvestedNumberAtTarget" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsOutstandingAndNonvested" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsOutstandingAndNonvested" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsPaidInCashEquivalentInPeriod" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsPaidInCashEquivalentInPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsPaidInCashEquivalentInPeriodValue" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsPaidInCashEquivalentInPeriodValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsPaidInCashEquivalentInPeriodWeightedAverageGrantDateFairValue" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsPaidInCashEquivalentInPeriodWeightedAverageGrantDateFairValue" nillable="true" substitutionGroup="xbrli:item" type="num:perShareItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsPromisesToGrantInPeriod" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsPromisesToGrantInPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsPromisesToGrantInPeriodWeightedAverageGrantDateFairValue" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsPromisesToGrantInPeriodWeightedAverageGrantDateFairValue" nillable="true" substitutionGroup="xbrli:item" type="num:perShareItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsVestedAndReleaseInPeriodAtTarget" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsVestedAndReleaseInPeriodAtTarget" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsVestedInPeriodTotalIntrinsicValue" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsVestedInPeriodTotalIntrinsicValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardEstimatedFairValue" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEstimatedFairValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardIssuedAndEstimatedFairValueOfAwardAbstract" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardIssuedAndEstimatedFairValueOfAwardAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsExercisableAndIntrinsicValue" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsExercisableAndIntrinsicValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsOutstandingWeightedAverageRemainingContractualTerm2010" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsOutstandingWeightedAverageRemainingContractualTerm2010" nillable="true" substitutionGroup="xbrli:item" type="xbrli:decimalItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitActualPayoutPercentageAfterModification" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitActualPayoutPercentageAfterModification" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitAdditionalUnitsGrantedMaximumPercentage" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitAdditionalUnitsGrantedMaximumPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitMaximunAwardLevelNonvestedNumber" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitMaximunAwardLevelNonvestedNumber" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitProjectedPayoutPercentage" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitProjectedPayoutPercentage" nillable="true" substitutionGroup="xbrli:item" type="num:percentItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitThresholdAwardLevelNonvestedNumber" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitThresholdAwardLevelNonvestedNumber" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="instant" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitsPromisesToGrantInPeriod2007To2009PerformancePeriod" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardPerformanceShareUnitsPromisesToGrantInPeriod2007To2009PerformancePeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardSharesAwardedForPerformanceOverTarget" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardSharesAwardedForPerformanceOverTarget" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardSharesExpectedToBeIssuedInPeriod" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardSharesExpectedToBeIssuedInPeriod" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardSharesIssuedInPeriodAfterModification" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardSharesIssuedInPeriodAfterModification" nillable="true" substitutionGroup="xbrli:item" type="xbrli:sharesItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationArrangementByShareBasedPaymentAwardSharesIssuedInPeriodAfterModificationTotalIntrinsicValue" name="ShareBasedCompensationArrangementByShareBasedPaymentAwardSharesIssuedInPeriodAfterModificationTotalIntrinsicValue" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_ShareBasedCompensationOptionAndIncentivePlansPolicyTextBlock" name="ShareBasedCompensationOptionAndIncentivePlansPolicyTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ShortTermInvestmentsPolicyTextBlock" name="ShortTermInvestmentsPolicyTextBlock" nillable="true" substitutionGroup="xbrli:item" type="nonnum:textBlockItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_SignificantOperatingAndNonoperatingItemsDisclosureAbstract" name="SignificantOperatingAndNonoperatingItemsDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_StockAppreciationRightsMember" name="StockAppreciationRightsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_StockCompensationPlansAbstract" name="StockCompensationPlansAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_StreamliningInitiativesMember" name="StreamliningInitiativesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_SummaryOfSignificantAccountingPoliciesAbstract" name="SummaryOfSignificantAccountingPoliciesAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_TermOfAgreement" name="TermOfAgreement" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_TermOfLicenseAgreement" name="TermOfLicenseAgreement" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ThailandEventsMember" name="ThailandEventsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ThirdPartyNumberOfPendingActiveClaims" name="ThirdPartyNumberOfPendingActiveClaims" nillable="true" substitutionGroup="xbrli:item" type="xbrli:integerItemType" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_TimeBasedRestrictedStockUnitAwardsMember" name="TimeBasedRestrictedStockUnitAwardsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TotalPrincipalNotes079DueNovember152013Member" name="TotalPrincipalNotes079DueNovember152013Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TotalPrincipalNotesDueMarch152019AtFixedRateOf4875PercentMember" name="TotalPrincipalNotesDueMarch152019AtFixedRateOf4875PercentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TotalPrincipalNotesDueMay152012AtInterestRateOf3MonthLiborMember" name="TotalPrincipalNotesDueMay152012AtInterestRateOf3MonthLiborMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TotalPrincipalNotesDueNovember152020InterestRate315PercentMember" name="TotalPrincipalNotesDueNovember152020InterestRate315PercentMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TotalPrincipalNotesDueSeptember12016Member" name="TotalPrincipalNotesDueSeptember12016Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TotalPrincipalNotesDueSeptember12021Member" name="TotalPrincipalNotesDueSeptember12021Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TotalPrincipalUsDollar57NotesDue2011To2037Member" name="TotalPrincipalUsDollar57NotesDue2011To2037Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TotalUkPoundSterlingNotesDue2016To2021Member" name="TotalUkPoundSterlingNotesDue2016To2021Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TrademarksAndBrandsMember" name="TrademarksAndBrandsMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TradingSecuritiesDisclosureAbstract" name="TradingSecuritiesDisclosureAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_TradingSecuritiesUnrealizedHoldingGainsLosses" name="TradingSecuritiesUnrealizedHoldingGainsLosses" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_TranslationAndRemeasurementLineItems" name="TranslationAndRemeasurementLineItems" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_TranslationAndRemeasurementTable" name="TranslationAndRemeasurementTable" nillable="true" substitutionGroup="xbrldt:hypercubeItem" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_UndistributedForeignEarnings" name="UndistributedForeignEarnings" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="instant" />
  <xsd:element abstract="true" id="ko_UnitedStatesDefinedContributionPlanMember" name="UnitedStatesDefinedContributionPlanMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_UnitedStatesMember" name="UnitedStatesMember" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_UnrecognizedTaxBenefitsChangesResultingFromEffectsOfForeignCurrencyTranslations" name="UnrecognizedTaxBenefitsChangesResultingFromEffectsOfForeignCurrencyTranslations" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_UnrecognizedTaxBenefitsDifferentTaxJurisdictions" name="UnrecognizedTaxBenefitsDifferentTaxJurisdictions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="instant" />
  <xsd:element id="ko_UnrecognizedTaxBenefitsIncreasesResultingFromAcquisition" name="UnrecognizedTaxBenefitsIncreasesResultingFromAcquisition" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_UnusualOrInfrequentEventCharges" name="UnusualOrInfrequentEventCharges" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element id="ko_UnusualOrInfrequentItemOperating" name="UnusualOrInfrequentItemOperating" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_UsDollarNotesDue20102093Member" name="UsDollarNotesDue20102093Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
  <xsd:element id="ko_ValuationAllowanceAdditions" name="ValuationAllowanceAdditions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="credit" xbrli:periodType="duration" />
  <xsd:element id="ko_ValuationAllowanceDeductions" name="ValuationAllowanceDeductions" nillable="true" substitutionGroup="xbrli:item" type="xbrli:monetaryItemType" xbrli:balance="debit" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ValuationAllowanceRollForward" name="ValuationAllowanceRollForward" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_WorkforceAbstract" name="WorkforceAbstract" nillable="true" substitutionGroup="xbrli:item" type="xbrli:stringItemType" xbrli:periodType="duration" />
  <xsd:element abstract="true" id="ko_ZeroCouponNotesDueIn2020Member" name="ZeroCouponNotesDueIn2020Member" nillable="true" substitutionGroup="xbrli:item" type="nonnum:domainItemType" xbrli:periodType="duration" />
</xsd:schema>