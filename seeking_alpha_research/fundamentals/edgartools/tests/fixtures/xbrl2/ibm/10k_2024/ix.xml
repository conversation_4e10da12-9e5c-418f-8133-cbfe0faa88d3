<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico" />
    <title>XBRL Viewer</title>
    <script language="JavaScript">
    /* <![CDATA[ */
        /* Created by staff of the U.S. Securities and Exchange Commission.
        *  Data and content created by government employees within the scope of their employment
        *  are not subject to domestic copyright protection. 17 U.S.C. 105.
        *
        *  This script checks the accession number of the document requested to determine
        *  the year and sequence of filing.  If filed after the availability of ixviewer-plus
        *  this script redirects to that viewer, otherwise to a prior ixviewer in effect
        *  or deemed compatible with the filing.
        */

        function loadViewer(ixvUrl) {
            // load viewer in an iframe so the end user's browser URL is not changed
            var iFrame = document.getElementById("ixvFrame");
            iFrame.src = ixvUrl;
        }

        const url = location.href;
        // url doc pattern is /Archives/edgar/data/ciknum/{10digits}{YY}{sequence}/{more params}
        // uncomment one of the following urlPatterns depending on usage:
        // for SEC.GOV: const urlPattern = /(\?doc=(\/Archives\/edgar\/data\/[0-9]+\/[0-9]{10}([0-9]{8})\/).*$)/;
        // for Arelle GUI: const urlPattern = /(\?doc=(\/[0-9]+\/).*$)/;
        const urlPattern = /(\?doc=(\/Archives\/edgar\/data\/[0-9]+\/[0-9]{10}([0-9]{8})\/).*$)/;
        const ixvPlusNsPattern = /http:\/\/xbrl.sec.gov\/(ffd|sbs)\/.*$/;
        var ixvUrl = null;
        
        if (! urlPattern.test(url)) {
            alert("Not an EDGAR inline XBRL document viewing request, please check URL");
            // this hangs: loadViewer("/ixviewer/ix.html" + ixParams);
        } else {
            const match = url.match(urlPattern);
            const ixParams = match[1];
            const docPath = match[2];
            const acsnYrSeq = match[3];
            const acsnYr = Number(match[3].substring(0, 2));
            let xhr = new XMLHttpRequest();
            xhr.onreadystatechange = function() {
                if (this.readyState == 4) {
                    if (this.status == 200) {
                        const ml = JSON.parse(this.responseText);
                        if (!(typeof ml === 'object' && "instance" in ml)) {
                            console.log("Metalinks does not have instance objects");
                            // Unable to read MetaLinks.json, use 23.2 ixviewer
                            loadViewer("/ixviewer/ix.html" + ixParams);
                        } else {
                            const numInstances = Object.keys(ml["instance"]).length;
                            let maxDocsPerInst = 0;
                            let hasIxvPlusExhibit = false;
                            Object.keys(ml["instance"]).forEach(instanceNames => {
                                let docsInInstance = instanceNames.split(" ").length;
                                if (docsInInstance > maxDocsPerInst)
                                    maxDocsPerInst = docsInInstance;
                                if ("baseTaxonomies" in ml["instance"][instanceNames]) {
                                    Object.keys(ml["instance"][instanceNames]["baseTaxonomies"]).forEach(baseTaxonomyUrl => {
                                        if (ixvPlusNsPattern.test(baseTaxonomyUrl))
                                            hasIxvPlusExhibit = true;
                                    });
                                }
                            });

                            if (
                                (numInstances > 1 || maxDocsPerInst > 1 || hasIxvPlusExhibit || url.includes("&redline=true")) 
                                || acsnYr >= 25
                            )
                                loadViewer("/ixviewer-plus/ix.xhtml" + ixParams);
                            else
                                loadViewer("/ixviewer/ix.html" + ixParams);
                        }
                    } else {
                        console.log("Unable to read MetaLinks.json for this accession");
                        // Unable to read MetaLinks.json, use 23.2 ixviewer
                        loadViewer("/ixviewer/ix.html" + ixParams);
                    }
                }
            };
            xhr.ontimeout = function() {
                console.log("Timeout reading MetaLinks.json for this accession");
                // Unable to read MetaLinks.json, use 23.2 ixviewer
                loadViewer("/ixviewer/ix.html" + ixParams);
            };
            xhr.open("GET", docPath + 'MetaLinks.json', true);
            xhr.timeout = 2000; // milliseconds
            xhr.send();

            /* Arelle GUI usage: set title for tab in browser for when there are multiple tabs */
            const urlTitleParamPattern = /&title=([^&]+),*$/;
            if (urlTitleParamPattern.test(url)) {
                const match = url.match(urlTitleParamPattern);
                document.title = decodeURIComponent(match[1]);
            }

            /* alternative select viewer using accession number

            const acsnYrSeq = match[3];
            if (acsnYrSeq > "24905000") {
                loadViewer(docPath + "ixbrlviewer.xhtml");
            } else if (acsnYrSeq > "23905000") {
                loadViewer("/ixviewer-plus/ix.xhtml" + ixParams);
            } else {
                loadViewer("/ixviewer/ix.html" + ixParams);
            }

            */
        }
    /* ]]> */
    </script>
</head>

<body style="margin: 0">
    <noscript>
        Please enable JavaScript to use the EDGAR Inline XBRL Viewer.
    </noscript>
    <iframe id="ixvFrame"
        title="Inline XBRL Viewer"
        style="position: absolute; height: 100%; width: 100%; border: none"
    />
</body>
</html>
