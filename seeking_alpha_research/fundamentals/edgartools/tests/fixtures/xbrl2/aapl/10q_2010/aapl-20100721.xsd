<?xml version="1.0" encoding="US-ASCII"?>
<!--  EDGAR Online I-Metrix Xcelerate Taxonomy Schema, based on XBRL 2.1  http://www.edgar-online.com/  -->
<!--  Version: 6.6.4 -->
<!--  Round: 732b23ac-7150-4d84-88fd-1d6594d5325b -->
<!--  Creation date: 2010-07-21T12:11:05Z -->
<!--  Copyright (c) 2005-2010 EDGAR Online, Inc. All Rights Reserved. -->
<schema xmlns="http://www.w3.org/2001/XMLSchema"
  xmlns:xbrli="http://www.xbrl.org/2003/instance"
  xmlns:link="http://www.xbrl.org/2003/linkbase"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:aapl="http://www.apple.com/20100626"
  xmlns:xbrldt="http://xbrl.org/2005/xbrldt"
  xmlns:us-types="http://xbrl.us/us-types/2009-01-31"
  targetNamespace="http://www.apple.com/20100626"
  elementFormDefault="qualified" attributeFormDefault="unqualified">
  <annotation>
    <appinfo>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DocumentDocumentandEntityInformation" id="DocumentDocumentandEntityInformation">
        <link:definition>101 - Document - Document and Entity Information</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/StatementOfIncome" id="IMetrix_StatementOfIncome">
        <link:definition>103 - Statement - CONDENSED CONSOLIDATED STATEMENTS OF OPERATIONS</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/StatementOfFinancialPositionClassified" id="IMetrix_StatementOfFinancialPositionClassified">
        <link:definition>104 - Statement - CONDENSED CONSOLIDATED BALANCE SHEETS</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/StatementOfFinancialPositionClassifiedParenthetical" id="IMetrix_StatementOfFinancialPositionClassifiedParen">
        <link:definition>105 - Statement - CONDENSED CONSOLIDATED BALANCE SHEETS (Parenthetical)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/StatementOfCashFlowsIndirect" id="IMetrix_StatementOfCashFlowsIndirect">
        <link:definition>106 - Statement - CONDENSED CONSOLIDATED STATEMENTS OF CASH FLOWS</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsBasisOfPresentationAndSignificantAccountingPoliciesTextBlock" id="IMetrix_NotesToFinancialStatementsBasisOfPresentationAndSignificantAccountingPoliciesTextBlock">
        <link:definition>107 - Disclosure - Summary of Significant Accounting Policies</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsFinancialInstrumentsTextBlock" id="IMetrix_NotesToFinancialStatementsFinancialInstrumentsTextBlock">
        <link:definition>108 - Disclosure - Financial Instruments</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsFairValueMeasurementInputsDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsFairValueMeasurementInputsDisclosureTextBlock">
        <link:definition>109 - Disclosure - Fair Value Measurements</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsDetailsOfCertainBalanceSheetAccountsDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsDetailsOfCertainBalanceSheetAccountsDisclosureTextBlock">
        <link:definition>110 - Disclosure - Condensed Consolidated Financial Statement Details</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsIncomeTaxDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsIncomeTaxDisclosureTextBlock">
        <link:definition>111 - Disclosure - Income Taxes</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsStockholdersEquityAndStockBasedCompensationDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsStockholdersEquityAndStockBasedCompensationDisclosureTextBlock">
        <link:definition>112 - Disclosure - Shareholders' Equity and Stock-Based Compensation</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsCommitmentsAndContingenciesDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsCommitmentsAndContingenciesDisclosureTextBlock">
        <link:definition>113 - Disclosure - Commitments and Contingencies</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsSegmentReportingDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsSegmentReportingDisclosureTextBlock">
        <link:definition>114 - Disclosure - Segment Information and Geographic Data</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsRelatedPartyTransactionsDisclosureTextBlock" id="IMetrix_NotesToFinancialStatementsRelatedPartyTransactionsDisclosureTextBlock">
        <link:definition>115 - Disclosure - Related Party Transactions and Certain Other Transactions</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsBasisOfPresentationAndSignificantAccountingPoliciesTextBlockPolicies" id="IMetrix_NotesToFinancialStatementsBasisOfPresentationAndSignificantAccountingPoliciesTextBlockPolicies">
        <link:definition>116 - Disclosure - Summary of Significant Accounting Policies (Policies)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsBasisOfPresentationAndSignificantAccountingPoliciesTextBlockTables" id="IMetrix_NotesToFinancialStatementsBasisOfPresentationAndSignificantAccountingPoliciesTextBlockTables">
        <link:definition>117 - Disclosure - Summary of Significant Accounting Policies (Tables)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsFinancialInstrumentsTextBlockPolicies" id="IMetrix_NotesToFinancialStatementsFinancialInstrumentsTextBlockPolicies">
        <link:definition>118 - Disclosure - Financial Instruments (Policies)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsFinancialInstrumentsTextBlockTables" id="IMetrix_NotesToFinancialStatementsFinancialInstrumentsTextBlockTables">
        <link:definition>119 - Disclosure - Financial Instruments (Tables)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsFairValueMeasurementInputsDisclosureTextBlockTables" id="IMetrix_NotesToFinancialStatementsFairValueMeasurementInputsDisclosureTextBlockTables">
        <link:definition>120 - Disclosure - Fair Value Measurements (Tables)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsDetailsOfCertainBalanceSheetAccountsDisclosureTextBlockTables" id="IMetrix_NotesToFinancialStatementsDetailsOfCertainBalanceSheetAccountsDisclosureTextBlockTables">
        <link:definition>121 - Disclosure - Condensed Consolidated Financial Statement Details (Tables)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsStockholdersEquityAndStockBasedCompensationDisclosureTextBlockTables" id="IMetrix_NotesToFinancialStatementsStockholdersEquityAndStockBasedCompensationDisclosureTextBlockTables">
        <link:definition>122 - Disclosure - Shareholders' Equity and Stock-Based Compensation (Tables)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsCommitmentsAndContingenciesDisclosureTextBlockTables" id="IMetrix_NotesToFinancialStatementsCommitmentsAndContingenciesDisclosureTextBlockTables">
        <link:definition>123 - Disclosure - Commitments and Contingencies (Tables)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/NotesToFinancialStatementsSegmentReportingDisclosureTextBlockTables" id="IMetrix_NotesToFinancialStatementsSegmentReportingDisclosureTextBlockTables">
        <link:definition>124 - Disclosure - Segment Information and Geographic Data (Tables)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureSummaryOfSignificantAccountingPoliciesAdditionalInformation" id="DisclosureSummaryOfSignificantAccountingPoliciesAdditionalInformation">
        <link:definition>125 - Disclosure - Summary of Significant Accounting Policies - Additional Information (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureComputationOfBasicAndDilutedEarningsPerCommonShare" id="DisclosureComputationOfBasicAndDilutedEarningsPerCommonShare">
        <link:definition>126 - Disclosure - Computation of Basic and Diluted Earnings Per Common Share (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureCashAndAvailableforSaleSecuritiesHeldInItsMarketableSecuritiesInvestmentPortfolio" id="DisclosureCashAndAvailableforSaleSecuritiesHeldInItsMarketableSecuritiesInvestmentPortfolio">
        <link:definition>127 - Disclosure - Cash and Available-for-Sale Securities Held in its Marketable Securities Investment Portfolio (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureAvailableforSaleSecuritiesAdjustedCostGrossUnrealizedGainsGrossUnrealizedLossesAndFairValueBySignificantInvestmentCategory" id="DisclosureAvailableforSaleSecuritiesAdjustedCostGrossUnrealizedGainsGrossUnrealizedLossesAndFairValueBySignificantInvestmentCategory">
        <link:definition>128 - Disclosure - Available-for-Sale Securities' Adjusted Cost, Gross Unrealized Gains, Gross Unrealized Losses and Fair Value by Significant Investment Category (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureFinancialInstrumentsAdditionalInformation" id="DisclosureFinancialInstrumentsAdditionalInformation">
        <link:definition>129 - Disclosure - Financial Instruments - Additional Information (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureGrossUnrealizedLossesAndFairValueForInvestmentsInAnUnrealizedLossPosition" id="DisclosureGrossUnrealizedLossesAndFairValueForInvestmentsInAnUnrealizedLossPosition">
        <link:definition>130 - Disclosure - Gross Unrealized Losses and Fair Value for Investments in an Unrealized Loss Position (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureNotionalPrincipalAndCreditRiskAmountsOfDerivativeInstrumentsOutstanding" id="DisclosureNotionalPrincipalAndCreditRiskAmountsOfDerivativeInstrumentsOutstanding">
        <link:definition>131 - Disclosure - Notional Principal and Credit Risk Amounts of Derivative Instruments Outstanding (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureDerivativeInstrumentsMeasuredAtGrossFairValueAsReflectedInTheConsolidatedBalanceSheets" id="DisclosureDerivativeInstrumentsMeasuredAtGrossFairValueAsReflectedInTheConsolidatedBalanceSheets">
        <link:definition>132 - Disclosure - Derivative Instruments Measured at Gross Fair Value as Reflected in the Consolidated Balance Sheets (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosurePreTaxEffectOfDerivativeInstrumentsDesignatedAsCashFlowAndNetInvestmentHedges" id="DisclosurePreTaxEffectOfDerivativeInstrumentsDesignatedAsCashFlowAndNetInvestmentHedges">
        <link:definition>133 - Disclosure - Pre-Tax Effect of Derivative Instruments Designated as Cash Flow and Net Investment Hedges (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureAssetsAndLiabilitiesMeasuredAtFairValue" id="DisclosureAssetsAndLiabilitiesMeasuredAtFairValue">
        <link:definition>134 - Disclosure - Assets and Liabilities Measured at Fair Value (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureAssetsAndLiabilitiesMeasuredAtFairValueOnARecurringBasis" id="DisclosureAssetsAndLiabilitiesMeasuredAtFairValueOnARecurringBasis">
        <link:definition>135 - Disclosure - Assets and Liabilities Measured at Fair Value on a Recurring Basis (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureCondensedConsolidatedFinancialStatementDetails" id="DisclosureCondensedConsolidatedFinancialStatementDetails">
        <link:definition>136 - Disclosure - Condensed Consolidated Financial Statement Details (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureIncomeTaxesAdditionalInformation" id="DisclosureIncomeTaxesAdditionalInformation">
        <link:definition>137 - Disclosure - Income Taxes - Additional Information (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureShareholdersEquityAndStockBasedCompensationAdditionalInformation" id="DisclosureShareholdersEquityAndStockBasedCompensationAdditionalInformation">
        <link:definition>138 - Disclosure - Shareholders' Equity and Stock-Based Compensation - Additional Information (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureComponentsOfTotalComprehensiveIncome" id="DisclosureComponentsOfTotalComprehensiveIncome">
        <link:definition>139 - Disclosure - Components of Total Comprehensive Income (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureOtherComprehensiveIncomeRelatedToDerivatives" id="DisclosureOtherComprehensiveIncomeRelatedToDerivatives">
        <link:definition>140 - Disclosure - Other Comprehensive Income Related to Derivatives (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureComponentsOfAccumulatedOtherComprehensiveIncomeNetOfTaxes" id="DisclosureComponentsOfAccumulatedOtherComprehensiveIncomeNetOfTaxes">
        <link:definition>141 - Disclosure - Components of Accumulated Other Comprehensive Income, Net of Taxes (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureRestrictedStockActivity" id="DisclosureRestrictedStockActivity">
        <link:definition>142 - Disclosure - Restricted Stock Activity (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureStockOptionAndRSUActivityAndRelatedInformation" id="DisclosureStockOptionAndRSUActivityAndRelatedInformation">
        <link:definition>143 - Disclosure - Stock Option and RSU Activity and Related Information (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureWeightedAverageAssumptionsAndResultingEstimatesOfWeightedAverageFairValuePerShareOfStockOptionsGrantedAndStockPurchaseRights" id="DisclosureWeightedAverageAssumptionsAndResultingEstimatesOfWeightedAverageFairValuePerShareOfStockOptionsGrantedAndStockPurchaseRights">
        <link:definition>144 - Disclosure - Weighted-Average Assumptions and Resulting Estimates of Weighted-Average Fair Value Per Share of Stock Options Granted and Stock Purchase Rights (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureSummaryOfTheStockBasedCompensationExpense" id="DisclosureSummaryOfTheStockBasedCompensationExpense">
        <link:definition>145 - Disclosure - Summary of the Stock-Based Compensation Expense (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureCommitmentsAndContingenciesAdditionalInformation" id="DisclosureCommitmentsAndContingenciesAdditionalInformation">
        <link:definition>146 - Disclosure - Commitments and Contingencies - Additional Information (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureChangesInAccruedWarrantyAndRelatedCosts" id="DisclosureChangesInAccruedWarrantyAndRelatedCosts">
        <link:definition>147 - Disclosure - Changes in Accrued Warranty and Related Costs (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureSegmentInformationAndGeographicDataAdditionalInformation" id="DisclosureSegmentInformationAndGeographicDataAdditionalInformation">
        <link:definition>148 - Disclosure - Segment Information and Geographic Data - Additional Information (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureSummaryInformationByOperatingSegment" id="DisclosureSummaryInformationByOperatingSegment">
        <link:definition>149 - Disclosure - Summary Information by Operating Segment (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureReconciliationOfSegmentOperatingIncomeToTheCondensedConsolidatedFinancialStatements" id="DisclosureReconciliationOfSegmentOperatingIncomeToTheCondensedConsolidatedFinancialStatements">
        <link:definition>150 - Disclosure - Reconciliation of Segment Operating Income to the Condensed Consolidated Financial Statements (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:roleType roleURI="http://www.apple.com/taxonomy/role/DisclosureRelatedPartyTransactionsAndCertainOtherTransactionsAdditionalInformation" id="DisclosureRelatedPartyTransactionsAndCertainOtherTransactionsAdditionalInformation">
        <link:definition>151 - Disclosure - Related Party Transactions and Certain Other Transactions - Additional Information (Detail)</link:definition>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:linkbaseRef xlink:type="simple" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:role="http://www.xbrl.org/2003/role/calculationLinkbaseRef" xlink:href="aapl-20100626_cal.xml" xlink:title="Calculation Links, all"/>
      <link:linkbaseRef xlink:type="simple" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:role="http://www.xbrl.org/2003/role/definitionLinkbaseRef" xlink:href="aapl-20100626_def.xml" xlink:title="Definition Links, all"/>
      <link:linkbaseRef xlink:type="simple" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:role="http://www.xbrl.org/2003/role/labelLinkbaseRef" xlink:href="aapl-20100626_lab.xml" xlink:title="Label Links, all"/>
      <link:linkbaseRef xlink:type="simple" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:role="http://www.xbrl.org/2003/role/presentationLinkbaseRef" xlink:href="aapl-20100626_pre.xml" xlink:title="Presentation Links, all"/>
    </appinfo>
  </annotation>
  <import namespace="http://www.xbrl.org/2003/instance" schemaLocation="http://www.xbrl.org/2003/xbrl-instance-2003-12-31.xsd"/>
  <import namespace="http://www.xbrl.org/2003/linkbase" schemaLocation="http://www.xbrl.org/2003/xbrl-linkbase-2003-12-31.xsd"/>
  <import namespace="http://xbrl.org/2005/xbrldt" schemaLocation="http://www.xbrl.org/2005/xbrldt-2005.xsd"/>
  <import namespace="http://xbrl.us/us-types/2009-01-31" schemaLocation="http://taxonomies.xbrl.us/us-gaap/2009/elts/us-types-2009-01-31.xsd"/>
  <import namespace="http://xbrl.us/us-gaap/2009-01-31" schemaLocation="http://taxonomies.xbrl.us/us-gaap/2009/elts/us-gaap-2009-01-31.xsd"/>
  <import namespace="http://xbrl.us/dei/2009-01-31" schemaLocation="http://taxonomies.xbrl.us/us-gaap/2009/non-gaap/dei-2009-01-31.xsd"/>
  <element name="AccruedMarketingAndDistributionCurrent" id="aapl_AccruedMarketingAndDistributionCurrent" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AccruedWarrantiesAndRelatedCostsTableDisclosureTextBlock" id="aapl_AccruedWarrantiesAndRelatedCostsTableDisclosureTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AccumulatedDepreciationDepletionAndAmortizationPropertyPlantAndEquipmentAndCapitalizedSoftware" id="aapl_AccumulatedDepreciationDepletionAndAmortizationPropertyPlantAndEquipmentAndCapitalizedSoftware" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AmericasMember" id="aapl_AmericasMember" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AppleIpadMember" id="aapl_AppleIpadMember" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AppleIphoneMember" id="aapl_AppleIphoneMember" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AppleIpodTouchMember" id="aapl_AppleIpodTouchMember" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AppleTVMember" id="aapl_AppleTVMember" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AsiaPacificMember" id="aapl_AsiaPacificMember" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="Available-For-SaleSecuritiesDebtSecuritiesUnrealizedGainsLossesNet" id="aapl_Available-For-SaleSecuritiesDebtSecuritiesUnrealizedGainsLossesNet" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AvailableForSaleSecuritiesContinuousUnrealizedLossLessThanTwelveMonths" id="aapl_AvailableForSaleSecuritiesContinuousUnrealizedLossLessThanTwelveMonths" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AvailableForSaleSecuritiesContinuousUnrealizedLossTwelveMonthsOrLonger" id="aapl_AvailableForSaleSecuritiesContinuousUnrealizedLossTwelveMonthsOrLonger" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AvailableForSaleSecuritiesDebtSecuritiesUnrealizedGains" id="aapl_AvailableForSaleSecuritiesDebtSecuritiesUnrealizedGains" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AvailableForSaleSecuritiesDebtSecuritiesUnrealizedLosses" id="aapl_AvailableForSaleSecuritiesDebtSecuritiesUnrealizedLosses" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="AvailableForSaleSecuritiesUnrealizedLosses" id="aapl_AvailableForSaleSecuritiesUnrealizedLosses" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="BasisOfPresentationAndSignificantAccountingPoliciesTextBlock" id="aapl_BasisOfPresentationAndSignificantAccountingPoliciesTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="BusinessAcquisitionPurchasePriceAllocationIntangibleAssets" id="aapl_BusinessAcquisitionPurchasePriceAllocationIntangibleAssets" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="CashAndCashEquivalentsAndMarketableSecuritiesDisclosureTextBlock" id="aapl_CashAndCashEquivalentsAndMarketableSecuritiesDisclosureTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="CashCashEquivalentsAndMarketableSecurities" id="aapl_CashCashEquivalentsAndMarketableSecurities" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ComponentsOfAccumulatedOtherComprehensiveIncomeTextBlock" id="aapl_ComponentsOfAccumulatedOtherComprehensiveIncomeTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ComprehensiveIncomeTableTextBlock" id="aapl_ComprehensiveIncomeTableTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="CreditRiskAmountOfForeignCurrencyDerivativeInstrumentsDesignatedAsHedgingInstruments" id="aapl_CreditRiskAmountOfForeignCurrencyDerivativeInstrumentsDesignatedAsHedgingInstruments" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="CreditRiskAmountOfForeignCurrencyDerivativeInstrumentsNotDesignatedAsHedgingInstruments" id="aapl_CreditRiskAmountOfForeignCurrencyDerivativeInstrumentsNotDesignatedAsHedgingInstruments" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DerivativesByDerivativeInstrumentContractAxis" id="aapl_DerivativesByDerivativeInstrumentContractAxis" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrldt:dimensionItem"/>
  <element name="DerivativesByDerivativeInstrumentContractDomain" id="aapl_DerivativesByDerivativeInstrumentContractDomain" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DetailsOfCertainBalanceSheetAccountsDisclosureTextBlock" id="aapl_DetailsOfCertainBalanceSheetAccountsDisclosureTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureAssetsAndLiabilitiesMeasuredAtFairValueAbstract" id="aapl_DisclosureAssetsAndLiabilitiesMeasuredAtFairValueAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureAssetsAndLiabilitiesMeasuredAtFairValueOnARecurringBasisAbstract" id="aapl_DisclosureAssetsAndLiabilitiesMeasuredAtFairValueOnARecurringBasisAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureAvailableforSaleSecuritiesAdjustedCostGrossUnrealizedGainsGrossUnrealizedLossesAndFairValueBySignificantInvestmentCategoryAbstract" id="aapl_DisclosureAvailableforSaleSecuritiesAdjustedCostGrossUnrealizedGainsGrossUnrealizedLossesAndFairValueBySignificantInvestmentCategoryAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureCashAndAvailableforSaleSecuritiesHeldInItsMarketableSecuritiesInvestmentPortfolioAbstract" id="aapl_DisclosureCashAndAvailableforSaleSecuritiesHeldInItsMarketableSecuritiesInvestmentPortfolioAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureChangesInAccruedWarrantyAndRelatedCostsAbstract" id="aapl_DisclosureChangesInAccruedWarrantyAndRelatedCostsAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureCommitmentsAndContingenciesAdditionalInformationAbstract" id="aapl_DisclosureCommitmentsAndContingenciesAdditionalInformationAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureComponentsOfAccumulatedOtherComprehensiveIncomeNetOfTaxesAbstract" id="aapl_DisclosureComponentsOfAccumulatedOtherComprehensiveIncomeNetOfTaxesAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureComponentsOfTotalComprehensiveIncomeAbstract" id="aapl_DisclosureComponentsOfTotalComprehensiveIncomeAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureComputationOfBasicAndDilutedEarningsPerCommonShareAbstract" id="aapl_DisclosureComputationOfBasicAndDilutedEarningsPerCommonShareAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureCondensedConsolidatedFinancialStatementDetailsAbstract" id="aapl_DisclosureCondensedConsolidatedFinancialStatementDetailsAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureDerivativeInstrumentsMeasuredAtGrossFairValueAsReflectedInTheConsolidatedBalanceSheetsAbstract" id="aapl_DisclosureDerivativeInstrumentsMeasuredAtGrossFairValueAsReflectedInTheConsolidatedBalanceSheetsAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureFinancialInstrumentsAdditionalInformationAbstract" id="aapl_DisclosureFinancialInstrumentsAdditionalInformationAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureGrossUnrealizedLossesAndFairValueForInvestmentsInAnUnrealizedLossPositionAbstract" id="aapl_DisclosureGrossUnrealizedLossesAndFairValueForInvestmentsInAnUnrealizedLossPositionAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureIncomeTaxesAdditionalInformationAbstract" id="aapl_DisclosureIncomeTaxesAdditionalInformationAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureNotionalPrincipalAndCreditRiskAmountsOfDerivativeInstrumentsOutstandingAbstract" id="aapl_DisclosureNotionalPrincipalAndCreditRiskAmountsOfDerivativeInstrumentsOutstandingAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureOtherComprehensiveIncomeRelatedToDerivativesAbstract" id="aapl_DisclosureOtherComprehensiveIncomeRelatedToDerivativesAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosurePreTaxEffectOfDerivativeInstrumentsDesignatedAsCashFlowAndNetInvestmentHedgesAbstract" id="aapl_DisclosurePreTaxEffectOfDerivativeInstrumentsDesignatedAsCashFlowAndNetInvestmentHedgesAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureReconciliationOfSegmentOperatingIncomeToTheCondensedConsolidatedFinancialStatementsAbstract" id="aapl_DisclosureReconciliationOfSegmentOperatingIncomeToTheCondensedConsolidatedFinancialStatementsAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureRelatedPartyTransactionsAndCertainOtherTransactionsAdditionalInformationAbstract" id="aapl_DisclosureRelatedPartyTransactionsAndCertainOtherTransactionsAdditionalInformationAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureRestrictedStockActivityAbstract" id="aapl_DisclosureRestrictedStockActivityAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureSegmentInformationAndGeographicDataAdditionalInformationAbstract" id="aapl_DisclosureSegmentInformationAndGeographicDataAdditionalInformationAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureShareholdersEquityAndStockBasedCompensationAdditionalInformationAbstract" id="aapl_DisclosureShareholdersEquityAndStockBasedCompensationAdditionalInformationAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureStockOptionAndRSUActivityAndRelatedInformationAbstract" id="aapl_DisclosureStockOptionAndRSUActivityAndRelatedInformationAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureSummaryInformationByOperatingSegmentAbstract" id="aapl_DisclosureSummaryInformationByOperatingSegmentAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureSummaryOfSignificantAccountingPoliciesAdditionalInformationAbstract" id="aapl_DisclosureSummaryOfSignificantAccountingPoliciesAdditionalInformationAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureSummaryOfTheStockBasedCompensationExpenseAbstract" id="aapl_DisclosureSummaryOfTheStockBasedCompensationExpenseAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DisclosureWeightedAverageAssumptionsAndResultingEstimatesOfWeightedAverageFairValuePerShareOfStockOptionsGrantedAndStockPurchaseRightsAbstract" id="aapl_DisclosureWeightedAverageAssumptionsAndResultingEstimatesOfWeightedAverageFairValuePerShareOfStockOptionsGrantedAndStockPurchaseRightsAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="DocumentDocumentandEntityInformationAbstract" id="aapl_DocumentDocumentandEntityInformationAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="EarningsPerShareBasicAndDilutedDenominatorAbstract" id="aapl_EarningsPerShareBasicAndDilutedDenominatorAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="EarningsPerShareBasicAndDilutedNumeratorAbstract" id="aapl_EarningsPerShareBasicAndDilutedNumeratorAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="EstimatedLifeOfRelatedHardwareDevice" id="aapl_EstimatedLifeOfRelatedHardwareDevice" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="EstimateOfSellingPriceForSoftwareUpgradeRight" id="aapl_EstimateOfSellingPriceForSoftwareUpgradeRight" type="us-types:perUnitItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="EuropeMember" id="aapl_EuropeMember" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueAssetsAndLiabilitiesComponentDomain" id="aapl_FairValueAssetsAndLiabilitiesComponentDomain" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueAssetsAndLiabilitiesComponentsAxis" id="aapl_FairValueAssetsAndLiabilitiesComponentsAxis" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrldt:dimensionItem"/>
  <element name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisTextBlock" id="aapl_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisUnobservableInputReconciliationByMajorTypeAxis" id="aapl_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisUnobservableInputReconciliationByMajorTypeAxis" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrldt:dimensionItem"/>
  <element name="FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisUnobservableInputReconciliationByMajorTypesDomain" id="aapl_FairValueAssetsAndLiabilitiesMeasuredOnRecurringBasisUnobservableInputReconciliationByMajorTypesDomain" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueAssetsMeasuredOnRecurringBasis" id="aapl_FairValueAssetsMeasuredOnRecurringBasis" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueAssetsMeasuredOnRecurringBasisAbstract" id="aapl_FairValueAssetsMeasuredOnRecurringBasisAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueAssetsMeasuredOnRecurringBasisMarketableSecuritiesCurrent" id="aapl_FairValueAssetsMeasuredOnRecurringBasisMarketableSecuritiesCurrent" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueAssetsMeasuredOnRecurringBasisMarketableSecuritiesNonCurrent" id="aapl_FairValueAssetsMeasuredOnRecurringBasisMarketableSecuritiesNonCurrent" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueAssetsMeasuredOnRecurringBasisOtherAssets" id="aapl_FairValueAssetsMeasuredOnRecurringBasisOtherAssets" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueDisclosuresPolicyTextBlock" id="aapl_FairValueDisclosuresPolicyTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueLiabilitiesMeasuredOnRecurringBasis" id="aapl_FairValueLiabilitiesMeasuredOnRecurringBasis" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FairValueLiabilitiesMeasuredOnRecurringBasisAbstract" id="aapl_FairValueLiabilitiesMeasuredOnRecurringBasisAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="FinancialInstrumentsTextBlock" id="aapl_FinancialInstrumentsTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ForeignCurrencyDerivativeInstrumentsDesignatedAsHedgingInstrumentsAssetAtFairValue" id="aapl_ForeignCurrencyDerivativeInstrumentsDesignatedAsHedgingInstrumentsAssetAtFairValue" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ForeignCurrencyDerivativeInstrumentsDesignatedAsHedgingInstrumentsLiabilityAtFairValue" id="aapl_ForeignCurrencyDerivativeInstrumentsDesignatedAsHedgingInstrumentsLiabilityAtFairValue" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="IncreaseDecreaseOtherCurrentAssets" id="aapl_IncreaseDecreaseOtherCurrentAssets" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="JapanMember" id="aapl_JapanMember" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="LandAndBuildingsGross" id="aapl_LandAndBuildingsGross" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="MachineryEquipmentAndInternalUseSoftwareGross" id="aapl_MachineryEquipmentAndInternalUseSoftwareGross" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="MajorFacilityLeaseTermsMaximumPeriodInYears" id="aapl_MajorFacilityLeaseTermsMaximumPeriodInYears" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="MajorFacilityLeaseTermsMinimumPeriodInYears" id="aapl_MajorFacilityLeaseTermsMinimumPeriodInYears" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="NotesToFinancialStatementsAbstract" id="aapl_NotesToFinancialStatementsAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="NotionalAmountOfForeignCurrencyDerivativeInstrumentsDesignatedAsHedgingInstruments" id="aapl_NotionalAmountOfForeignCurrencyDerivativeInstrumentsDesignatedAsHedgingInstruments" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="NotionalAndCreditRiskAmountsOfOutstandingDerivativePositionsDisclosureTextBlock" id="aapl_NotionalAndCreditRiskAmountsOfOutstandingDerivativePositionsDisclosureTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="NumberOfStores" id="aapl_NumberOfStores" type="xbrli:integerItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="OperatingLeasesFutureMinimumPaymentsDueRetailSpace" id="aapl_OperatingLeasesFutureMinimumPaymentsDueRetailSpace" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="OperatingLeasesIncreaseInFutureMinimumPaymentsDueRetailSpace" id="aapl_OperatingLeasesIncreaseInFutureMinimumPaymentsDueRetailSpace" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="credit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="OtherIncomeAndExpenseMember" id="aapl_OtherIncomeAndExpenseMember" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="PotentialDilutiveSecuritiesThatCouldBeIncludedInComputationOfEarningsPerShareAmount" id="aapl_PotentialDilutiveSecuritiesThatCouldBeIncludedInComputationOfEarningsPerShareAmount" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="PropertyPlantAndEquipmentAndCapitalizedSoftwareGross" id="aapl_PropertyPlantAndEquipmentAndCapitalizedSoftwareGross" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="PropertyPlantAndEquipmentAndCapitalizedSoftwareNet" id="aapl_PropertyPlantAndEquipmentAndCapitalizedSoftwareNet" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="PurchaseCommitmentMaximumPeriodRequired" id="aapl_PurchaseCommitmentMaximumPeriodRequired" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="PurchaseCommitmentMinimumPeriodRequired" id="aapl_PurchaseCommitmentMinimumPeriodRequired" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ReconciliationOfOperatingIncomeFromSegmentsToConsolidatedTextBlock" id="aapl_ReconciliationOfOperatingIncomeFromSegmentsToConsolidatedTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="RenewalOptionTermsMaximumAdditionalPeriodInYears" id="aapl_RenewalOptionTermsMaximumAdditionalPeriodInYears" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="RenewalOptionTermsMinimumAdditionalPeriodInYears" id="aapl_RenewalOptionTermsMinimumAdditionalPeriodInYears" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="RestrictedShareActivityDisclosureTextBlock" id="aapl_RestrictedShareActivityDisclosureTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="RetailLeaseTermsMajorityPeriodInYears" id="aapl_RetailLeaseTermsMajorityPeriodInYears" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="RetailLeaseTermsMaximumPeriodInYears" id="aapl_RetailLeaseTermsMaximumPeriodInYears" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="RetailLeaseTermsMinimumPeriodInYears" id="aapl_RetailLeaseTermsMinimumPeriodInYears" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="RetailMember" id="aapl_RetailMember" type="us-types:domainItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ScheduleOfDerivativeInstrumentsEffectOnIncomeAndOtherComprehensiveIncome" id="aapl_ScheduleOfDerivativeInstrumentsEffectOnIncomeAndOtherComprehensiveIncome" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ScheduleOfDerivativeInstrumentsRecognizedInOtherComprehensiveIncomeTextBlock" id="aapl_ScheduleOfDerivativeInstrumentsRecognizedInOtherComprehensiveIncomeTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ScheduleOfShareBasedCompensationOptionsActivityTextBlock" id="aapl_ScheduleOfShareBasedCompensationOptionsActivityTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="SegmentReportingInformationCorporateExpenses" id="aapl_SegmentReportingInformationCorporateExpenses" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="Share-BasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantAdditionalSharesAuthorizedInPeriod" id="aapl_Share-BasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantAdditionalSharesAuthorizedInPeriod" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementBySharBasedPaymentAwardOptionsRestrictedStockUnitsCancelledWeightedAverageExercisePrice" id="aapl_ShareBasedCompensationArrangementBySharBasedPaymentAwardOptionsRestrictedStockUnitsCancelledWeightedAverageExercisePrice" type="us-types:perShareItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementBySharBasedPaymentAwardOptionsRestrictedStockUnitsGrantedWeightedAverageExercisePrice" id="aapl_ShareBasedCompensationArrangementBySharBasedPaymentAwardOptionsRestrictedStockUnitsGrantedWeightedAverageExercisePrice" type="us-types:perShareItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardAvailableForGrantRestrictedStockUnitsCancelledInPeriod" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardAvailableForGrantRestrictedStockUnitsCancelledInPeriod" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardAvailableForGrantRestrictedStockUnitsGrantedInPeriod" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardAvailableForGrantRestrictedStockUnitsGrantedInPeriod" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsAggregateIntrinsicValueAbstract" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsAggregateIntrinsicValueAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsWeightedAverageGrantDateFairValueAbstract" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardEquityInstrumentsOtherThanOptionsWeightedAverageGrantDateFairValueAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardIncreaseNumberOfSharesAvailableForGrant" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardIncreaseNumberOfSharesAvailableForGrant" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAdditionalSharesAuthorizedWeightedAverageExercisePrice" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAdditionalSharesAuthorizedWeightedAverageExercisePrice" type="us-types:perShareItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAggregateIntrinsicValueAbstract" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAggregateIntrinsicValueAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAssumedInPeriod" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAssumedInPeriod" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrant" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrant" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantAbstract" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantGrantsInPeriod" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantGrantsInPeriod" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantOptionsAssumedInPeriod" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantOptionsAssumedInPeriod" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantOptionsCancelledInPeriod" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantOptionsCancelledInPeriod" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantOptionsExercisedInPeriod" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsAvailableForGrantOptionsExercisedInPeriod" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsExpectedToVestIntrinsicValue" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsExpectedToVestIntrinsicValue" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsExpectedToVestWeightedAverageRemainingContractualTerm" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsExpectedToVestWeightedAverageRemainingContractualTerm" type="xbrli:decimalItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsWeightedAverageExercisePriceAbstract" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsWeightedAverageExercisePriceAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsWeightedAverageRemainingContractualTermAbstract" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardOptionsWeightedAverageRemainingContractualTermAbstract" type="xbrli:stringItemType" abstract="true" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardRestrictedStockUnitsCancelledInPeriod" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardRestrictedStockUnitsCancelledInPeriod" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardRestrictedStockUnitsGrantedInPeriod" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardRestrictedStockUnitsGrantedInPeriod" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBasedPaymentAwardStockPurchaseRightsGrantsInPeriodWeightedAverageGrantDateFairValue" id="aapl_ShareBasedCompensationArrangementByShareBasedPaymentAwardStockPurchaseRightsGrantsInPeriodWeightedAverageGrantDateFairValue" type="us-types:perShareItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBbasedPaymentAwardOptionsAssumedWeightedAverageExercisePrice" id="aapl_ShareBasedCompensationArrangementByShareBbasedPaymentAwardOptionsAssumedWeightedAverageExercisePrice" type="us-types:perShareItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementByShareBbasedPaymentAwardOptionsExpectedToVestWeightedAverageExercisePrice" id="aapl_ShareBasedCompensationArrangementByShareBbasedPaymentAwardOptionsExpectedToVestWeightedAverageExercisePrice" type="us-types:perShareItemType" abstract="false" xbrli:periodType="instant" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationArrangementShareBasedPaymentAwardAdditionalSharesAuthorized" id="aapl_ShareBasedCompensationArrangementShareBasedPaymentAwardAdditionalSharesAuthorized" type="xbrli:sharesItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="ShareBasedCompensationOptionsExercisableIntrinsicValue" id="aapl_ShareBasedCompensationOptionsExercisableIntrinsicValue" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="instant" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="StockBasedCompensationExpenseAssociatedToCostOfSales" id="aapl_StockBasedCompensationExpenseAssociatedToCostOfSales" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="StockBasedCompensationExpenseAssociatedToResearchAndDevelopmentExpense" id="aapl_StockBasedCompensationExpenseAssociatedToResearchAndDevelopmentExpense" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="StockBasedCompensationExpenseAssociatedToSellingGeneralAndAdministrativeExpense" id="aapl_StockBasedCompensationExpenseAssociatedToSellingGeneralAndAdministrativeExpense" type="xbrli:monetaryItemType" abstract="false" xbrli:periodType="duration" xbrli:balance="debit" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="StockholdersEquityAndStockBasedCompensationDisclosureTextBlock" id="aapl_StockholdersEquityAndStockBasedCompensationDisclosureTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="UnrealizedGainsAndLossesOnInvestmentsTextBlock" id="aapl_UnrealizedGainsAndLossesOnInvestmentsTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="WeightedAverageAssumptionsAndResultingEstimatesOfWeightedAverageFairValueTextBlock" id="aapl_WeightedAverageAssumptionsAndResultingEstimatesOfWeightedAverageFairValueTextBlock" type="us-types:textBlockItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
  <element name="WeightedAverageFairValueOfStockOptionsAssumed" id="aapl_WeightedAverageFairValueOfStockOptionsAssumed" type="us-types:perShareItemType" abstract="false" xbrli:periodType="duration" nillable="true" substitutionGroup="xbrli:item"/>
</schema>