#!/usr/bin/env python3
"""
Check the quality and correctness of IB data being downloaded.
"""

import sys
import os
from datetime import datetime, timedelta
import pandas as pd

# Add project to path
sys.path.insert(0, os.path.dirname(__file__))

from core.data_service import DataService
from core.logger import get_logger

logger = get_logger(__name__)


def check_data_quality():
    """Check the quality of IB data."""
    print("🔍 Checking IB Data Quality...")
    print("=" * 40)
    
    ds = DataService()
    if not ds.ib_connector:
        print("❌ No IB connection available")
        return
    
    symbol = "AAPL"
    
    # Test 1: Recent minute data
    print(f"\n📊 Recent Minute Data ({symbol}):")
    try:
        recent_bars = ds.get_minute_bars(symbol, "2025-07-10", "2025-07-11")
        print(f"Records: {len(recent_bars)}")
        if not recent_bars.empty:
            print(f"Date range: {recent_bars.index[0]} to {recent_bars.index[-1]}")
            print(f"Columns: {list(recent_bars.columns)}")
            
            print("\nFirst 3 bars:")
            print(recent_bars.head(3)[['open', 'high', 'low', 'close', 'volume']])
            
            print("\nLast 3 bars:")
            print(recent_bars.tail(3)[['open', 'high', 'low', 'close', 'volume']])
            
            # Check for data quality issues
            print(f"\nData Quality:")
            print(f"- Null values: {recent_bars.isnull().any().any()}")
            print(f"- Zero volumes: {(recent_bars['volume'] == 0).sum()}")
            print(f"- Negative prices: {(recent_bars[['open', 'high', 'low', 'close']] < 0).any().any()}")
            
            # Check date consistency
            today = datetime.now().date()
            future_dates = recent_bars.index > pd.Timestamp.now()
            print(f"- Future dates: {future_dates.sum()}")
            print(f"- Today: {today}")
            print(f"- Latest bar: {recent_bars.index[-1].date()}")
            
    except Exception as e:
        print(f"❌ Error getting recent minute data: {e}")
    
    # Test 2: Daily data
    print(f"\n📈 Daily Data ({symbol} Jan 2024):")
    try:
        daily_bars = ds.get_daily_bars(symbol, "2024-01-01", "2024-01-31")
        print(f"Records: {len(daily_bars)}")
        if not daily_bars.empty:
            print(f"Date range: {daily_bars.index[0]} to {daily_bars.index[-1]}")
            
            print("\nSample daily bars:")
            print(daily_bars.head(5)[['open', 'high', 'low', 'close', 'volume']])
            
            # Check for data quality issues
            print(f"\nData Quality:")
            print(f"- Null values: {daily_bars.isnull().any().any()}")
            print(f"- Zero volumes: {(daily_bars['volume'] == 0).sum()}")
            print(f"- Negative prices: {(daily_bars[['open', 'high', 'low', 'close']] < 0).any().any()}")
            
            # Check OHLC logic
            ohlc_issues = (
                (daily_bars['high'] < daily_bars['low']) |
                (daily_bars['high'] < daily_bars['open']) |
                (daily_bars['high'] < daily_bars['close']) |
                (daily_bars['low'] > daily_bars['open']) |
                (daily_bars['low'] > daily_bars['close'])
            ).sum()
            print(f"- OHLC logic issues: {ohlc_issues}")
            
    except Exception as e:
        print(f"❌ Error getting daily data: {e}")
    
    # Test 3: Historical minute data (should trigger our date logic)
    print(f"\n📅 Historical Minute Data ({symbol} 60 days ago):")
    try:
        hist_date = datetime.now().date() - timedelta(days=60)
        start_date = hist_date - timedelta(days=2)
        
        hist_bars = ds.get_minute_bars(
            symbol, 
            start_date.strftime("%Y-%m-%d"), 
            hist_date.strftime("%Y-%m-%d")
        )
        print(f"Records: {len(hist_bars)}")
        if not hist_bars.empty:
            print(f"Date range: {hist_bars.index[0]} to {hist_bars.index[-1]}")
            print(f"Requested end date: {hist_date}")
            print(f"Actual end date: {hist_bars.index[-1].date()}")
            
            # Check if we got the right date range
            if hist_bars.index[-1].date() > hist_date + timedelta(days=5):
                print("⚠️ WARNING: Got much newer data than requested (fallback to recent data)")
            else:
                print("✅ Date range looks correct")
                
    except Exception as e:
        print(f"❌ Error getting historical minute data: {e}")
    
    print("\n✅ Data quality check complete!")


if __name__ == "__main__":
    check_data_quality()
