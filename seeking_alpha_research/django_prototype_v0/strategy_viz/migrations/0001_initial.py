# Generated by Django 5.2.4 on 2025-07-11 16:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="BacktestRun",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "initial_capital",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "final_capital",
                    models.DecimalField(decimal_places=2, max_digits=12, null=True),
                ),
                ("total_return", models.FloatField(null=True)),
                ("sharpe_ratio", models.FloatField(null=True)),
                ("max_drawdown", models.FloatField(null=True)),
                ("win_rate", models.FloatField(null=True)),
                ("total_trades", models.IntegerField(default=0)),
                ("winning_trades", models.IntegerField(default=0)),
                ("losing_trades", models.IntegerField(default=0)),
                ("delistings", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("config", models.JSONField(default=dict)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="StockAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("symbol", models.CharField(max_length=10)),
                ("analysis_date", models.DateField()),
                ("filings_analyzed", models.IntegerField()),
                ("cash_burn_months", models.FloatField()),
                ("atm_probability", models.FloatField()),
                ("has_active_atm", models.BooleanField(default=False)),
                ("llm_analysis", models.TextField()),
                ("filing_details", models.JSONField(default=dict)),
                ("predicted_gap_start", models.DateField(null=True)),
                ("predicted_gap_end", models.DateField(null=True)),
                (
                    "predicted_dilution_amount",
                    models.DecimalField(decimal_places=2, max_digits=12, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "backtest_run",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="stock_analyses",
                        to="strategy_viz.backtestrun",
                    ),
                ),
            ],
            options={
                "ordering": ["-atm_probability", "cash_burn_months"],
                "unique_together": {("backtest_run", "symbol", "analysis_date")},
            },
        ),
        migrations.CreateModel(
            name="InsiderPattern",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("detection_date", models.DateField()),
                ("pattern_type", models.CharField(max_length=100)),
                ("confidence_score", models.FloatField()),
                ("volume_ratio", models.FloatField()),
                ("price_stability", models.FloatField()),
                ("tick_data_summary", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "stock_analysis",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="insider_patterns",
                        to="strategy_viz.stockanalysis",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Trade",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("symbol", models.CharField(max_length=10)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ENTERED", "Entered"),
                            ("EXITED", "Exited"),
                            ("DELISTED", "Delisted"),
                        ],
                        max_length=20,
                    ),
                ),
                ("entry_date", models.DateField()),
                ("entry_price", models.DecimalField(decimal_places=4, max_digits=10)),
                ("shares", models.IntegerField()),
                (
                    "position_value",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                ("entry_reason", models.TextField()),
                ("exit_date", models.DateField(null=True)),
                (
                    "exit_price",
                    models.DecimalField(decimal_places=4, max_digits=10, null=True),
                ),
                ("exit_reason", models.TextField(null=True)),
                ("exit_time", models.TimeField(null=True)),
                (
                    "pnl",
                    models.DecimalField(decimal_places=2, max_digits=12, null=True),
                ),
                ("pnl_percent", models.FloatField(null=True)),
                ("gap_percentage", models.FloatField(null=True)),
                ("news_catalyst", models.TextField(null=True)),
                (
                    "actual_dilution_amount",
                    models.DecimalField(decimal_places=2, max_digits=12, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "backtest_run",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trades",
                        to="strategy_viz.backtestrun",
                    ),
                ),
                (
                    "stock_analysis",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="strategy_viz.stockanalysis",
                    ),
                ),
            ],
            options={
                "ordering": ["entry_date"],
            },
        ),
        migrations.CreateModel(
            name="ValidationMetric",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("alpha_confidence", models.FloatField()),
                ("information_ratio", models.FloatField()),
                ("sortino_ratio", models.FloatField()),
                ("calmar_ratio", models.FloatField()),
                ("gap_prediction_accuracy", models.FloatField()),
                ("timing_accuracy", models.FloatField()),
                ("dilution_prediction_accuracy", models.FloatField()),
                ("delisting_impact", models.FloatField()),
                ("survivorship_adjusted_return", models.FloatField()),
                ("random_selection_return", models.FloatField()),
                ("buy_and_hold_return", models.FloatField()),
                ("validation_report", models.TextField()),
                ("statistical_tests", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "backtest_run",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="validation_metrics",
                        to="strategy_viz.backtestrun",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DailyPortfolio",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField()),
                ("total_value", models.DecimalField(decimal_places=2, max_digits=12)),
                ("cash", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "positions_value",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                ("num_positions", models.IntegerField()),
                ("num_watchlist", models.IntegerField()),
                ("daily_return", models.FloatField(null=True)),
                ("positions", models.JSONField(default=dict)),
                ("watchlist", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "backtest_run",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="daily_snapshots",
                        to="strategy_viz.backtestrun",
                    ),
                ),
            ],
            options={
                "ordering": ["date"],
                "unique_together": {("backtest_run", "date")},
            },
        ),
    ]
