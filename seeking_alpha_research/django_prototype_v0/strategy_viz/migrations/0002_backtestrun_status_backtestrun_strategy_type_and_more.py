# Generated by Django 5.2.4 on 2025-07-13 05:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("strategy_viz", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="backtestrun",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("running", "Running"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="backtestrun",
            name="strategy_type",
            field=models.CharField(
                choices=[
                    ("gap_up_atm", "Gap Up ATM"),
                    ("earnings_gap", "Earnings Gap"),
                    ("news_catalyst", "News Catalyst"),
                ],
                default="gap_up_atm",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="stockanalysis",
            name="cash_burn_monthly",
            field=models.DecimalField(decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name="stockanalysis",
            name="filing_analysis",
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name="stockanalysis",
            name="gap_percentage",
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name="stockanalysis",
            name="has_news_catalyst",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="stockanalysis",
            name="months_runway",
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name="stockanalysis",
            name="volume_surge_ratio",
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name="stockanalysis",
            name="atm_probability",
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name="stockanalysis",
            name="cash_burn_months",
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name="stockanalysis",
            name="filings_analyzed",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="stockanalysis",
            name="llm_analysis",
            field=models.TextField(null=True),
        ),
    ]
