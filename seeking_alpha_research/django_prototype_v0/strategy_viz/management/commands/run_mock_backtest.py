#!/usr/bin/env python3
"""
Django management command to create MOCK backtest data for TESTING ONLY.

This is separate from the production run_backtest.py command.
Use this ONLY for testing Django visualization with fake data.

Usage:
    python manage.py run_mock_backtest --name "Test Visualization"
"""

import os
import sys
from datetime import datetime, timedelta
from decimal import Decimal
import json
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone

from strategy_viz.models import (
    BacktestRun, StockAnalysis, InsiderPattern, Trade, 
    DailyPortfolio, ValidationMetric
)


class Command(BaseCommand):
    help = 'Create MOCK backtest data for TESTING Django visualization ONLY'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            default='Mock Test Run',
            help='Name for this mock backtest'
        )
        parser.add_argument(
            '--initial-capital',
            type=float,
            default=100000,
            help='Initial capital (default: 100000)'
        )

    def handle(self, *args, **options):
        """Create mock backtest data for testing visualization."""
        name = f"[MOCK] {options['name']}"
        initial_capital = options['initial_capital']
        
        self.stdout.write(self.style.WARNING("=" * 60))
        self.stdout.write(self.style.WARNING("WARNING: CREATING MOCK DATA FOR TESTING ONLY"))
        self.stdout.write(self.style.WARNING("THIS IS NOT REAL BACKTEST DATA"))
        self.stdout.write(self.style.WARNING("DO NOT USE FOR INVESTMENT DECISIONS"))
        self.stdout.write(self.style.WARNING("=" * 60))
        
        try:
            # Create mock backtest run record
            backtest_run = BacktestRun.objects.create(
                name=name,
                start_date='2024-01-01',
                end_date='2024-01-31',
                initial_capital=initial_capital,
                config={
                    'MOCK_DATA': True,
                    'WARNING': 'This is mock data for testing only',
                    'gap_threshold': 0.30,
                    'volume_threshold': 1.5,
                    'cash_burn_months_threshold': 6,
                    'atm_probability_threshold': 0.7
                }
            )
            
            self.stdout.write(f"Creating mock backtest: {name}")
            
            self._create_mock_backtest(backtest_run, initial_capital)
            
            self.stdout.write(self.style.SUCCESS("Mock backtest created successfully!"))
            self.stdout.write(self.style.WARNING("REMEMBER: This is MOCK data for testing only"))
            self.stdout.write(f"View at: http://localhost:8000/backtest/{backtest_run.id}/")
            
        except Exception as e:
            raise CommandError(f"Failed to create mock backtest: {e}")

    def _create_mock_backtest(self, backtest_run, initial_capital):
        """Create mock backtest data for testing visualization."""
        self.stdout.write("Creating mock data components...")
        
        with transaction.atomic():
            # Mock stock analysis
            stock_analysis = StockAnalysis.objects.create(
                backtest_run=backtest_run,
                symbol='SAVA',
                analysis_date='2024-01-05',
                filings_analyzed=8,
                cash_burn_months=4.2,
                atm_probability=0.85,
                has_active_atm=True,
                llm_analysis="[MOCK] Analysis shows high burn rate with active ATM shelf. High probability of dilution within 3-6 months.",
                filing_details={
                    'MOCK_DATA': True,
                    'latest_10k': '2023-12-31',
                    'latest_10q': '2023-09-30',
                    'atm_shelf_date': '2023-08-15'
                },
                predicted_gap_start='2024-01-08',
                predicted_gap_end='2024-01-15',
                predicted_dilution_amount=15000000
            )
            
            # Mock insider pattern
            InsiderPattern.objects.create(
                stock_analysis=stock_analysis,
                detection_date='2024-01-05',
                pattern_type='[MOCK] Unusual Volume Accumulation',
                confidence_score=0.78,
                volume_ratio=2.3,
                price_stability=0.92,
                tick_data_summary={
                    'MOCK_DATA': True,
                    'avg_trade_size': 1250,
                    'large_block_frequency': 0.15,
                    'price_impact': 0.02
                }
            )
            
            # Mock trade
            Trade.objects.create(
                backtest_run=backtest_run,
                stock_analysis=stock_analysis,
                symbol='SAVA',
                status='EXITED',
                entry_date='2024-01-05',
                entry_price=20.50,
                shares=500,
                position_value=10250,
                entry_reason='[MOCK] Insider accumulation detected + high ATM probability',
                exit_date='2024-01-08',
                exit_price=27.65,
                exit_reason='[MOCK] Gap up 35% - premarket exit at 7:30 AM',
                pnl=3575,
                pnl_percent=34.88,
                gap_percentage=35.0,
                news_catalyst='[MOCK] FDA breakthrough therapy designation',
                actual_dilution_amount=12500000
            )
            
            # Mock daily snapshots
            for i in range(5):
                date_str = f'2024-01-0{i+1}'
                portfolio_value = initial_capital + (i * 715)  # Gradual increase
                
                DailyPortfolio.objects.create(
                    backtest_run=backtest_run,
                    date=date_str,
                    total_value=portfolio_value,
                    cash=90000 if i == 0 else 87425,
                    positions_value=0 if i == 0 else 10250,
                    num_positions=0 if i == 0 else 1,
                    num_watchlist=3,
                    daily_return=0.715 if i > 0 else 0,
                    positions={'SAVA': {'shares': 500, 'avg_price': 20.50, 'MOCK': True}} if i > 0 else {},
                    watchlist={'RDHL': {'MOCK': True}, 'CLOV': {'MOCK': True}, 'BBIG': {'MOCK': True}}
                )
            
            # Update final values
            backtest_run.final_capital = 103575
            backtest_run.total_return = 3.575
            backtest_run.sharpe_ratio = 1.2
            backtest_run.max_drawdown = -2.5
            backtest_run.win_rate = 100.0
            backtest_run.total_trades = 1
            backtest_run.winning_trades = 1
            backtest_run.losing_trades = 0
            backtest_run.delistings = 0
            backtest_run.save()
            
            # Mock validation metrics
            ValidationMetric.objects.create(
                backtest_run=backtest_run,
                alpha_confidence=0.95,
                information_ratio=1.8,
                sortino_ratio=2.1,
                calmar_ratio=1.4,
                gap_prediction_accuracy=1.0,
                timing_accuracy=0.85,
                dilution_prediction_accuracy=0.9,
                delisting_impact=0.0,
                survivorship_adjusted_return=3.575,
                random_selection_return=-2.3,
                buy_and_hold_return=8.2,
                validation_report="[MOCK] Mock validation shows strategy outperformed random selection by 5.9%. THIS IS MOCK DATA FOR TESTING ONLY.",
                statistical_tests={'t_test_p_value': 0.032, 'shapiro_p_value': 0.78, 'MOCK_DATA': True}
            )