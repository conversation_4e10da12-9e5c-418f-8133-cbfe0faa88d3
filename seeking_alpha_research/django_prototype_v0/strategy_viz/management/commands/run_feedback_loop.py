"""
Django Management Command for Feedback Loop

This command can be used in multiple ways:
1. CLI: python manage.py run_feedback_loop --symbol AAPL --date 2024-01-15
2. Django: call_command('run_feedback_loop', symbol='AAPL', date='2024-01-15')
3. Future API: Easily convertible to DRF ViewSet with minimal changes

All data structures are JSON serializable for API compatibility.
"""

import sys
import os
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from decimal import Decimal

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.utils import timezone
from django.db import transaction

# Add project root to path for imports
sys.path.append('/Users/<USER>/PycharmProjects/stk_v5/seeking_alpha_research')

# Import our feedback loop system
from core.feedback_loop import FeedbackLoop, FeedbackLoopResult
from scanners.professional_gap_detector import ProfessionalGapDetector
from analysis.professional_news_validator import ProfessionalNewsValidator
from validation.professional_alpha_validator import ProfessionalAlphaValidator

class Command(BaseCommand):
    """
    Professional Feedback Loop Management Command
    
    Designed for:
    - CLI execution
    - Django integration  
    - Future API compatibility (DRF-ready)
    
    All responses are JSON serializable for API use.
    """
    
    help = 'Run professional feedback loop analysis for individual stocks'
    
    def add_arguments(self, parser):
        """Define command arguments (CLI + API compatible)"""
        
        # Required arguments
        parser.add_argument(
            '--symbol',
            type=str,
            required=True,
            help='Stock symbol to analyze (e.g., AAPL, TSLA)'
        )
        
        parser.add_argument(
            '--date',
            type=str,
            help='Analysis date in YYYY-MM-DD format (default: yesterday)',
            default=None
        )
        
        # Optional mode arguments
        parser.add_argument(
            '--mode',
            type=str,
            choices=['full', 'quick', 'gap_scan', 'news_only', 'alpha_test'],
            default='full',
            help='Analysis mode to run'
        )
        
        # Analysis options
        parser.add_argument(
            '--lookback-days',
            type=int,
            default=14,
            help='Days to look back for analysis (default: 14)'
        )
        
        parser.add_argument(
            '--min-gap-threshold',
            type=float,
            default=30.0,
            help='Minimum gap percentage threshold (default: 30.0)'
        )
        
        # Output options
        parser.add_argument(
            '--output-format',
            type=str,
            choices=['json', 'summary', 'detailed'],
            default='summary',
            help='Output format'
        )
        
        parser.add_argument(
            '--save-to-file',
            type=str,
            help='Save results to JSON file',
            default=None
        )
        
        # API-ready options
        parser.add_argument(
            '--return-json',
            action='store_true',
            help='Return JSON response (for API compatibility)'
        )
        
        parser.add_argument(
            '--async-mode',
            action='store_true',
            help='Run in async mode (for API endpoints)'
        )
    
    def handle(self, *args, **options):
        """Main command handler (API-compatible)"""
        
        try:
            # Parse and validate arguments
            parsed_options = self._parse_and_validate_options(options)
            
            # Initialize feedback loop system
            feedback_system = self._initialize_feedback_system()
            
            # Execute analysis based on mode
            if options['async_mode']:
                # For future API endpoints
                result = asyncio.run(self._run_async_analysis(parsed_options, feedback_system))
            else:
                # For CLI and synchronous Django calls
                result = asyncio.run(self._run_analysis(parsed_options, feedback_system))
            
            # Format and return response
            response = self._format_response(result, options)
            
            # Return for Django/API use
            if options['return_json']:
                return response
            
            # Output results (only for CLI usage)
            self._output_results(response, options)
            
        except Exception as e:
            error_response = self._create_error_response(str(e), options)
            
            if options['return_json']:
                return error_response
            else:
                raise CommandError(f"Feedback loop failed: {e}")
    
    def _parse_and_validate_options(self, options: Dict) -> Dict:
        """Parse and validate command options (API-ready)"""
        
        # Parse date
        if options['date']:
            try:
                analysis_date = datetime.strptime(options['date'], '%Y-%m-%d')
            except ValueError:
                raise CommandError("Date must be in YYYY-MM-DD format")
        else:
            # Default to yesterday
            analysis_date = datetime.now() - timedelta(days=1)
        
        # Validate symbol
        symbol = options['symbol'].upper().strip()
        if not symbol or len(symbol) > 10:
            raise CommandError("Invalid symbol format")
        
        # Validate numeric parameters
        lookback_days = max(1, min(options['lookback_days'], 30))  # 1-30 days
        min_gap_threshold = max(5.0, min(options['min_gap_threshold'], 500.0))  # 5-500%
        
        return {
            'symbol': symbol,
            'analysis_date': analysis_date,
            'mode': options['mode'],
            'lookback_days': lookback_days,
            'min_gap_threshold': min_gap_threshold,
            'output_format': options['output_format'],
            'save_to_file': options['save_to_file']
        }
    
    def _initialize_feedback_system(self) -> Dict:
        """Initialize feedback loop system components"""
        
        # Database path (use Django settings for production)
        db_path = getattr(settings, 'FEEDBACK_LOOP_DB_PATH', 
                         '/Users/<USER>/PycharmProjects/stk_v5/seeking_alpha_research/feedback_loop.db')
        
        # Configuration
        config = {
            'database_path': db_path,
            'lookback_days': 14,
            'min_tick_data_points': 100,
            'min_data_quality_score': 0.5
        }
        
        # Initialize components
        feedback_loop = FeedbackLoop(config)
        
        return {
            'feedback_loop': feedback_loop,
            'config': config
        }
    
    async def _run_analysis(self, options: Dict, feedback_system: Dict) -> Dict:
        """Run main analysis (async for API compatibility)"""
        
        symbol = options['symbol']
        analysis_date = options['analysis_date']
        mode = options['mode']
        
        self.stdout.write(f"Starting {mode} analysis for {symbol} on {analysis_date.date()}")
        
        feedback_loop = feedback_system['feedback_loop']
        
        if mode == 'full':
            # Full feedback loop analysis
            result = await feedback_loop.run_single_stock_analysis(symbol, analysis_date)
            return self._serialize_feedback_result(result)
            
        elif mode == 'quick':
            # Quick analysis - skip some components
            result = await self._run_quick_analysis(symbol, analysis_date, feedback_loop)
            return result
            
        elif mode == 'gap_scan':
            # Gap detection only
            result = await self._run_gap_scan(symbol, analysis_date, feedback_system)
            return result
            
        elif mode == 'news_only':
            # News analysis only
            result = await self._run_news_analysis(symbol, analysis_date, feedback_system)
            return result
            
        elif mode == 'alpha_test':
            # Alpha validation only
            result = await self._run_alpha_validation(symbol, analysis_date, feedback_system)
            return result
        
        else:
            raise ValueError(f"Unknown mode: {mode}")
    
    async def _run_async_analysis(self, options: Dict, feedback_system: Dict) -> Dict:
        """Run analysis in async mode (for API endpoints)"""
        
        # Same as _run_analysis but with additional async optimizations
        # This is where we'd add async database operations, parallel processing, etc.
        
        return await self._run_analysis(options, feedback_system)
    
    async def _run_quick_analysis(self, symbol: str, analysis_date: datetime, 
                                feedback_loop: FeedbackLoop) -> Dict:
        """Run quick analysis mode"""
        
        # Simplified analysis with key components only
        try:
            # Mock data for testing (avoid IB connection issues)
            import random
            
            # Mock price data
            base_price = 100.0 + random.uniform(-10, 10)
            gap_change = random.uniform(-5, 40)  # -5% to +40% gap
            current_price = base_price * (1 + gap_change / 100)
            previous_close = base_price
            
            gap_percentage = ((current_price - previous_close) / previous_close) * 100
            
            # Mock news check
            has_news = random.choice([True, False])
            
            # Mock scoring
            quick_score = 0.8 if gap_percentage >= 30.0 and has_news else 0.3
            
            result = {
                'mode': 'quick',
                'symbol': symbol,
                'analysis_date': analysis_date.isoformat(),
                'gap_percentage': round(gap_percentage, 2),
                'gap_detected': gap_percentage >= 30.0,
                'news_present': has_news,
                'quick_score': round(quick_score, 2),
                'recommendation': 'strong_buy' if gap_percentage >= 30.0 and has_news else 
                               'monitor' if gap_percentage >= 20.0 else 'skip',
                'execution_time_seconds': 1.5,
                'data_source': 'mock_for_testing'
            }
            
            return result
            
        except Exception as e:
            return {'error': str(e), 'mode': 'quick'}
    
    async def _run_gap_scan(self, symbol: str, analysis_date: datetime, 
                          feedback_system: Dict) -> Dict:
        """Run gap detection analysis"""
        
        try:
            # Initialize gap detector
            from core.ib_connector import IBConnector
            
            ib_connector = IBConnector()
            gap_detector = ProfessionalGapDetector(
                ib_connector, 
                feedback_system['config']['database_path']
            )
            
            # Scan for gaps
            scan_result = gap_detector.scan_for_gaps([symbol], analysis_date)
            
            # Convert to serializable format
            result = {
                'mode': 'gap_scan',
                'symbol': symbol,
                'analysis_date': analysis_date.isoformat(),
                'gaps_detected': scan_result.gaps_detected,
                'qualified_gaps': scan_result.qualified_gaps,
                'scan_duration_seconds': scan_result.scan_duration_seconds,
                'gap_events': [
                    {
                        'symbol': event.symbol,
                        'gap_percentage': float(event.gap_percentage),
                        'premarket_price': float(event.premarket_price),
                        'previous_close': float(event.previous_close),
                        'gap_quality_score': float(event.gap_quality_score),
                        'risk_score': float(event.risk_score),
                        'confidence_level': event.confidence_level,
                        'news_catalyst': event.news_catalyst,
                        'market_cap': float(event.market_cap) if event.market_cap else None
                    }
                    for event in scan_result.gap_events
                ]
            }
            
            return result
            
        except Exception as e:
            return {'error': str(e), 'mode': 'gap_scan'}
    
    async def _run_news_analysis(self, symbol: str, analysis_date: datetime,
                                feedback_system: Dict) -> Dict:
        """Run news catalyst analysis"""
        
        try:
            # Initialize news validator
            news_validator = ProfessionalNewsValidator(
                feedback_system['config']['database_path']
            )
            
            # Analyze news catalyst for a hypothetical gap
            gap_percentage = 35.0  # Example gap to validate
            validation = news_validator.validate_gap_catalyst(
                symbol, gap_percentage, analysis_date
            )
            
            # Convert to serializable format
            result = {
                'mode': 'news_analysis',
                'symbol': symbol,
                'analysis_date': analysis_date.isoformat(),
                'gap_percentage': gap_percentage,
                'catalyst_strength': float(validation.catalyst_strength),
                'gap_justification': float(validation.gap_justification),
                'credibility_assessment': float(validation.credibility_assessment),
                'gap_validated': validation.gap_validated,
                'confidence_level': validation.confidence_level,
                'news_events_count': len(validation.news_events),
                'primary_catalyst': {
                    'headline': validation.primary_catalyst.headline,
                    'source': validation.primary_catalyst.source,
                    'relevance_score': float(validation.primary_catalyst.relevance_score),
                    'impact_score': float(validation.primary_catalyst.impact_score),
                    'sentiment_score': float(validation.primary_catalyst.sentiment_score)
                } if validation.primary_catalyst else None,
                'validation_reasons': validation.validation_reasons or []
            }
            
            return result
            
        except Exception as e:
            return {'error': str(e), 'mode': 'news_analysis'}
    
    async def _run_alpha_validation(self, symbol: str, analysis_date: datetime,
                                  feedback_system: Dict) -> Dict:
        """Run alpha validation analysis"""
        
        try:
            # Initialize alpha validator
            alpha_validator = ProfessionalAlphaValidator(
                feedback_system['config']['database_path']
            )
            
            # Run validation for past period
            start_date = analysis_date - timedelta(days=90)  # 3 months
            validation = alpha_validator.validate_strategy_alpha(
                'gap_up_strategy', start_date, analysis_date
            )
            
            # Convert to serializable format
            result = {
                'mode': 'alpha_validation',
                'symbol': symbol,
                'analysis_period': {
                    'start': validation.analysis_period[0].isoformat(),
                    'end': validation.analysis_period[1].isoformat()
                },
                'total_trades': validation.total_trades,
                'performance_metrics': {
                    'total_return': float(validation.performance_metrics.total_return),
                    'annualized_return': float(validation.performance_metrics.annualized_return),
                    'sharpe_ratio': float(validation.performance_metrics.sharpe_ratio),
                    'max_drawdown': float(validation.performance_metrics.max_drawdown),
                    'win_rate': float(validation.performance_metrics.win_rate),
                    'statistical_significance': validation.performance_metrics.statistical_significance
                },
                'alpha_genuine': validation.alpha_genuine,
                'alpha_confidence_level': float(validation.alpha_confidence_level),
                'professional_validation': validation.professional_validation,
                'validation_confidence': validation.validation_confidence,
                'validation_reasons': validation.validation_reasons,
                'benchmark_comparisons': [
                    {
                        'benchmark_name': bc.benchmark_name,
                        'excess_return': float(bc.excess_return),
                        'alpha': float(bc.alpha),
                        'beta': float(bc.beta),
                        'significantly_outperforms': bc.significantly_outperforms
                    }
                    for bc in validation.benchmark_comparisons
                ]
            }
            
            return result
            
        except Exception as e:
            return {'error': str(e), 'mode': 'alpha_validation'}
    
    def _serialize_feedback_result(self, result: FeedbackLoopResult) -> Dict:
        """Convert FeedbackLoopResult to JSON-serializable dict"""
        
        return {
            'mode': 'full',
            'symbol': result.symbol,
            'analysis_date': result.analysis_date.isoformat(),
            'execution_time_seconds': float(result.execution_time_seconds),
            
            # Data collection metrics
            'data_collection': {
                'tick_data_points': result.tick_data_points,
                'data_quality_score': float(result.data_quality_score),
                'data_completeness_pct': float(result.data_completeness_pct)
            },
            
            # Analysis results
            'insider_signal': {
                'signal_strength': float(result.insider_signal.signal_strength),
                'confidence_level': result.insider_signal.confidence_level,
                'supporting_evidence': result.insider_signal.supporting_evidence
            } if result.insider_signal else None,
            
            'entry_recommendation': result.entry_recommendation,
            
            # Trading actions
            'trading_actions': {
                'position_entered': result.position_entered,
                'position_exited': result.position_exited,
                'current_position_status': result.current_position_status
            },
            
            # Performance
            'performance': {
                'daily_pnl': float(result.daily_pnl),
                'cumulative_pnl': float(result.cumulative_pnl),
                'risk_metrics': result.risk_metrics
            },
            
            # Audit trail
            'audit_trail': {
                'database_records_created': result.database_records_created,
                'analysis_steps_completed': len(result.analysis_chain),
                'step_timings': [
                    {
                        'step': step['step_name'],
                        'duration_seconds': float(step['duration_seconds']),
                        'success': step['success']
                    }
                    for step in result.analysis_chain
                ]
            }
        }
    
    def _format_response(self, result: Dict, options: Dict) -> Dict:
        """Format response for output (API-ready)"""
        
        output_format = options['output_format']
        
        if output_format == 'json':
            return {
                'status': 'success' if 'error' not in result else 'error',
                'timestamp': timezone.now().isoformat(),
                'data': result,
                'meta': {
                    'command': 'run_feedback_loop',
                    'version': '1.0',
                    'format': 'json'
                }
            }
        
        elif output_format == 'summary':
            return self._create_summary_response(result)
        
        elif output_format == 'detailed':
            return {
                'status': 'success' if 'error' not in result else 'error',
                'timestamp': timezone.now().isoformat(),
                'summary': self._create_summary_response(result),
                'detailed_data': result,
                'meta': {
                    'command': 'run_feedback_loop',
                    'version': '1.0',
                    'format': 'detailed'
                }
            }
        
        return result
    
    def _create_summary_response(self, result: Dict) -> Dict:
        """Create summary response"""
        
        if 'error' in result:
            return {
                'status': 'error',
                'error': result['error'],
                'mode': result.get('mode', 'unknown')
            }
        
        mode = result.get('mode', 'unknown')
        symbol = result.get('symbol', 'N/A')
        
        summary = {
            'status': 'success',
            'mode': mode,
            'symbol': symbol,
            'analysis_date': result.get('analysis_date', 'N/A')
        }
        
        if mode == 'full':
            summary.update({
                'tick_data_points': result.get('data_collection', {}).get('tick_data_points', 0),
                'data_quality': f"{result.get('data_collection', {}).get('data_quality_score', 0):.2f}",
                'insider_signal': result.get('insider_signal', {}).get('confidence_level', 'none') if result.get('insider_signal') else 'none',
                'position_entered': result.get('trading_actions', {}).get('position_entered', False),
                'daily_pnl': f"${result.get('performance', {}).get('daily_pnl', 0):.2f}",
                'execution_time': f"{result.get('execution_time_seconds', 0):.1f}s"
            })
        
        elif mode == 'gap_scan':
            summary.update({
                'gaps_detected': result.get('gaps_detected', 0),
                'qualified_gaps': result.get('qualified_gaps', 0),
                'highest_quality_gap': max([event.get('gap_quality_score', 0) for event in result.get('gap_events', [])], default=0)
            })
        
        elif mode == 'news_analysis':
            summary.update({
                'gap_validated': result.get('gap_validated', False),
                'catalyst_strength': f"{result.get('catalyst_strength', 0):.2f}",
                'confidence_level': result.get('confidence_level', 'low'),
                'news_events_count': result.get('news_events_count', 0)
            })
        
        elif mode == 'alpha_validation':
            summary.update({
                'alpha_genuine': result.get('alpha_genuine', False),
                'total_trades': result.get('total_trades', 0),
                'sharpe_ratio': f"{result.get('performance_metrics', {}).get('sharpe_ratio', 0):.2f}",
                'validation_confidence': result.get('validation_confidence', 'low')
            })
        
        return summary
    
    def _create_error_response(self, error_msg: str, options: Dict) -> Dict:
        """Create error response (API-ready)"""
        
        return {
            'status': 'error',
            'timestamp': timezone.now().isoformat(),
            'error': {
                'message': error_msg,
                'type': 'FeedbackLoopError'
            },
            'meta': {
                'command': 'run_feedback_loop',
                'version': '1.0',
                'options': {
                    'symbol': options.get('symbol', 'N/A'),
                    'mode': options.get('mode', 'N/A')
                }
            }
        }
    
    def _output_results(self, response: Dict, options: Dict):
        """Output results to console/file"""
        
        # Console output
        if options['output_format'] == 'json':
            self.stdout.write(json.dumps(response, indent=2, default=str))
        else:
            # Human-readable output
            status = response.get('status', 'unknown')
            
            if status == 'success':
                self.stdout.write(
                    self.style.SUCCESS(f"✓ Analysis completed successfully")
                )
                
                # Print summary
                if 'summary' in response:
                    summary = response['summary']
                    for key, value in summary.items():
                        if key != 'status':
                            self.stdout.write(f"  {key}: {value}")
                
            else:
                error_msg = response.get('error', {}).get('message', 'Unknown error')
                self.stdout.write(
                    self.style.ERROR(f"✗ Analysis failed: {error_msg}")
                )
        
        # Save to file
        if options['save_to_file']:
            try:
                with open(options['save_to_file'], 'w') as f:
                    json.dump(response, f, indent=2, default=str)
                self.stdout.write(f"Results saved to: {options['save_to_file']}")
            except Exception as e:
                self.stdout.write(self.style.WARNING(f"Failed to save to file: {e}"))