"""
Models for Gap-Up ATM Strategy Visualization

Per specs lines 156-188:
"So, when it comes to the UI/UX for confirming that the strategy works.
At a per stock level for the backtesting...
django, no auth needed, but it needs to give confidence that the strategy works."
"""

from django.db import models
import json


class BacktestRun(models.Model):
    """Represents a complete backtest run."""
    
    STRATEGY_TYPES = [
        ('gap_up_atm', 'Gap Up ATM'),
        ('earnings_gap', 'Earnings Gap'),
        ('news_catalyst', 'News Catalyst'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    name = models.CharField(max_length=200)
    strategy_type = models.CharField(max_length=50, choices=STRATEGY_TYPES, default='gap_up_atm')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    start_date = models.DateField()
    end_date = models.DateField()
    initial_capital = models.DecimalField(max_digits=12, decimal_places=2)
    final_capital = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    total_return = models.FloatField(null=True)
    sharpe_ratio = models.FloatField(null=True)
    max_drawdown = models.FloatField(null=True)
    win_rate = models.FloatField(null=True)
    total_trades = models.IntegerField(default=0)
    winning_trades = models.IntegerField(default=0)
    losing_trades = models.IntegerField(default=0)
    delistings = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Store configuration
    config = models.JSONField(default=dict)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.start_date} to {self.end_date})"


class StockAnalysis(models.Model):
    """
    Per-stock analysis showing why we think it will gap.
    
    Per specs line 163:
    "detailed calculations ReAct agents data giving the time range of when 
    the gappup might happen and how much money they need to raise"
    """
    
    backtest_run = models.ForeignKey(BacktestRun, on_delete=models.CASCADE, related_name='stock_analyses')
    symbol = models.CharField(max_length=10)
    analysis_date = models.DateField()
    
    # Gap Detection Metrics
    gap_percentage = models.FloatField(null=True)
    volume_surge_ratio = models.FloatField(null=True)
    has_news_catalyst = models.BooleanField(default=False)
    
    # SEC Filing Analysis
    filings_analyzed = models.IntegerField(default=0)
    cash_burn_months = models.FloatField(null=True)
    cash_burn_monthly = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    months_runway = models.FloatField(null=True)
    atm_probability = models.FloatField(null=True)
    has_active_atm = models.BooleanField(default=False)
    
    # Detailed LLM analysis
    llm_analysis = models.TextField(null=True)
    filing_details = models.JSONField(default=dict)  # Store filing URLs, dates, types
    filing_analysis = models.JSONField(default=dict)  # Structured filing analysis
    
    # Predicted gap window
    predicted_gap_start = models.DateField(null=True)
    predicted_gap_end = models.DateField(null=True)
    predicted_dilution_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-atm_probability', 'cash_burn_months']
        unique_together = ['backtest_run', 'symbol', 'analysis_date']


class InsiderPattern(models.Model):
    """
    Insider accumulation patterns detected.
    
    Per specs line 166:
    "1 week prior to the gap up, there was strange volume tick data analysis"
    """
    
    stock_analysis = models.ForeignKey(StockAnalysis, on_delete=models.CASCADE, related_name='insider_patterns')
    detection_date = models.DateField()
    pattern_type = models.CharField(max_length=100)
    confidence_score = models.FloatField()
    volume_ratio = models.FloatField()
    price_stability = models.FloatField()
    tick_data_summary = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)


class Trade(models.Model):
    """Individual trade record."""
    
    TRADE_STATUS = [
        ('ENTERED', 'Entered'),
        ('EXITED', 'Exited'),
        ('DELISTED', 'Delisted'),
    ]
    
    backtest_run = models.ForeignKey(BacktestRun, on_delete=models.CASCADE, related_name='trades')
    stock_analysis = models.ForeignKey(StockAnalysis, on_delete=models.SET_NULL, null=True)
    
    symbol = models.CharField(max_length=10)
    status = models.CharField(max_length=20, choices=TRADE_STATUS, default='ENTERED')
    
    # Entry
    entry_date = models.DateField()
    entry_price = models.DecimalField(max_digits=10, decimal_places=4)
    shares = models.IntegerField()
    position_value = models.DecimalField(max_digits=12, decimal_places=2, null=True)  # Auto-calculated
    entry_reason = models.TextField(null=True)  # Optional
    
    # Exit
    exit_date = models.DateField(null=True)
    exit_price = models.DecimalField(max_digits=10, decimal_places=4, null=True)
    exit_reason = models.TextField(null=True)
    exit_time = models.TimeField(null=True)  # For premarket exits
    
    # P&L
    pnl = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    pnl_percent = models.FloatField(null=True)
    
    # Gap metrics
    gap_percentage = models.FloatField(null=True)
    news_catalyst = models.TextField(null=True)
    actual_dilution_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def save(self, *args, **kwargs):
        """Auto-calculate position_value, P&L, and status."""
        # Calculate position value
        if self.entry_price and self.shares:
            self.position_value = self.entry_price * self.shares
        
        # Calculate P&L and set status if exit price is available
        if self.exit_price and self.position_value:
            exit_value = self.exit_price * self.shares
            self.pnl = exit_value - self.position_value
            self.pnl_percent = float(self.pnl / self.position_value * 100) if self.position_value else 0
            self.status = 'EXITED'
        
        super().save(*args, **kwargs)
    
    class Meta:
        ordering = ['entry_date']


class DailyPortfolio(models.Model):
    """Daily portfolio snapshot."""
    
    backtest_run = models.ForeignKey(BacktestRun, on_delete=models.CASCADE, related_name='daily_snapshots')
    date = models.DateField()
    total_value = models.DecimalField(max_digits=12, decimal_places=2)
    cash = models.DecimalField(max_digits=12, decimal_places=2)
    positions_value = models.DecimalField(max_digits=12, decimal_places=2)
    num_positions = models.IntegerField()
    num_watchlist = models.IntegerField()
    daily_return = models.FloatField(null=True)
    
    # Store position details
    positions = models.JSONField(default=dict)
    watchlist = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['date']
        unique_together = ['backtest_run', 'date']


class ValidationMetric(models.Model):
    """
    Metrics to prove the strategy works and isn't random.
    
    Per specs line 187:
    "a sophsticated live report with proof at every angle... 
    that we have thought of everything and what we are seeing isnt pure randomness!!"
    """
    
    backtest_run = models.ForeignKey(BacktestRun, on_delete=models.CASCADE, related_name='validation_metrics')
    
    # Statistical validation
    alpha_confidence = models.FloatField()  # Statistical significance of returns
    information_ratio = models.FloatField()
    sortino_ratio = models.FloatField()
    calmar_ratio = models.FloatField()
    
    # Pattern validation
    gap_prediction_accuracy = models.FloatField()  # How often our predictions were correct
    timing_accuracy = models.FloatField()  # How close we were to actual gap dates
    dilution_prediction_accuracy = models.FloatField()
    
    # Survivorship bias impact
    delisting_impact = models.FloatField()  # Return difference with/without delistings
    survivorship_adjusted_return = models.FloatField()
    
    # Control group comparison
    random_selection_return = models.FloatField()  # Return from random stock selection
    buy_and_hold_return = models.FloatField()  # Market return for comparison
    
    # Detailed report
    validation_report = models.TextField()  # LLM-generated comprehensive report
    statistical_tests = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)


class CorporateAction(models.Model):
    """
    Corporate actions detected by the advanced monitoring system.
    
    Represents real-time detected splits, dividends, spinoffs, mergers, delistings.
    """
    
    ACTION_TYPES = [
        ('dividend', 'Dividend'),
        ('split', 'Stock Split'),
        ('spinoff', 'Spinoff'),
        ('merger', 'Merger'),
        ('delisting', 'Delisting'),
    ]
    
    MONITORING_STATUS = [
        ('active', 'Active Monitoring'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    symbol = models.CharField(max_length=10, db_index=True)
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    effective_date = models.DateField()
    detection_time = models.DateTimeField()
    
    # Action-specific fields
    dividend_amount = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)
    ex_date = models.DateField(null=True, blank=True)
    split_ratio = models.FloatField(null=True, blank=True)
    spinoff_ratio = models.FloatField(null=True, blank=True)
    
    # Monitoring and impact data
    monitoring_status = models.CharField(max_length=20, choices=MONITORING_STATUS, default='active')
    data_adjustment_triggered = models.BooleanField(default=False)
    impact_magnitude = models.FloatField(null=True, blank=True)
    strategy_relevance = models.CharField(max_length=20, null=True, blank=True)
    
    # Raw and adjusted gap calculations
    raw_gap_percentage = models.FloatField(null=True, blank=True)
    adjusted_gap_percentage = models.FloatField(null=True, blank=True)
    adjustment_quality = models.FloatField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-detection_time']
        unique_together = ['symbol', 'action_type', 'effective_date']
    
    def __str__(self):
        return f"{self.symbol}: {self.action_type} on {self.effective_date}"


class MonitoringAlert(models.Model):
    """
    Alerts generated by the corporate actions monitoring system.
    """
    
    ALERT_TYPES = [
        ('split_detected', 'Split Detected'),
        ('dividend_announced', 'Dividend Announced'),
        ('merger_activity', 'Merger Activity'),
        ('delisting_risk', 'Delisting Risk'),
    ]
    
    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    DELIVERY_STATUS = [
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('pending', 'Pending'),
    ]
    
    corporate_action = models.ForeignKey(CorporateAction, on_delete=models.CASCADE, related_name='alerts')
    alert_type = models.CharField(max_length=30, choices=ALERT_TYPES)
    priority = models.CharField(max_length=20, choices=PRIORITY_LEVELS)
    alert_id = models.CharField(max_length=100, unique=True)
    
    # Alert delivery information
    channels_notified = models.JSONField(default=list)
    delivery_status = models.JSONField(default=dict)
    escalation_triggered = models.BooleanField(default=False)
    
    # Alert content
    message = models.TextField()
    details = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.alert_type} - {self.corporate_action.symbol} ({self.priority})"


class SystemResilience(models.Model):
    """
    System resilience test results for monitoring system.
    """
    
    SYSTEM_STATUS = [
        ('operational', 'Operational'),
        ('degraded', 'Degraded'),
        ('failed', 'Failed'),
        ('recovering', 'Recovering'),
    ]
    
    TEST_SCENARIOS = [
        ('data_feed_loss', 'Data Feed Interruption'),
        ('volume_spike', 'High Volume Corporate Actions'),
        ('db_connection_loss', 'Database Connection Loss'),
        ('memory_pressure', 'Memory Pressure'),
    ]
    
    scenario_name = models.CharField(max_length=100)
    test_type = models.CharField(max_length=30, choices=TEST_SCENARIOS)
    system_status = models.CharField(max_length=20, choices=SYSTEM_STATUS)
    
    # Performance metrics
    recovery_time = models.FloatField()  # seconds
    data_loss = models.FloatField()  # percentage
    alert_generation = models.BooleanField()
    failover_success = models.BooleanField()
    
    # Calculated resilience score
    resilience_score = models.FloatField(null=True, blank=True)
    
    test_date = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-test_date']
    
    def __str__(self):
        return f"{self.scenario_name} - {self.system_status} ({self.test_date.date()})"


class DataAdjustment(models.Model):
    """
    Records of automatic data adjustments triggered by corporate actions.
    """
    
    ADJUSTMENT_TYPES = [
        ('price_volume_split', 'Price/Volume Split Adjustment'),
        ('price_dividend_adjust', 'Price Dividend Adjustment'),
        ('complex_spinoff_adjust', 'Complex Spinoff Adjustment'),
    ]
    
    corporate_action = models.ForeignKey(CorporateAction, on_delete=models.CASCADE, related_name='adjustments')
    adjustment_type = models.CharField(max_length=30, choices=ADJUSTMENT_TYPES)
    
    # Adjustment details
    affected_periods = models.IntegerField()  # Number of trading periods adjusted
    adjustment_triggered = models.BooleanField()
    rollback_capability = models.BooleanField()
    
    # Data consistency checks
    consistency_check_passed = models.BooleanField()
    checks_performed = models.JSONField(default=list)
    issues_found = models.IntegerField(default=0)
    overall_integrity = models.FloatField(null=True, blank=True)
    
    adjustment_timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-adjustment_timestamp']
    
    def __str__(self):
        return f"{self.corporate_action.symbol} - {self.adjustment_type}"
