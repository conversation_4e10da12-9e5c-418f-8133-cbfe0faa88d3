"""
URL configuration for strategy visualization app.
"""

from django.urls import path
from . import views

app_name = 'strategy_viz'

urlpatterns = [
    # List all backtests
    path('', views.BacktestListView.as_view(), name='backtest_list'),
    
    # Backtest detail dashboard
    path('backtest/<int:pk>/', views.BacktestDetailView.as_view(), name='backtest_detail'),
    
    # API endpoints for charts
    path('backtest/<int:pk>/portfolio-chart/', views.portfolio_chart_data, name='portfolio_chart'),
    path('backtest/<int:pk>/timeline/', views.trade_timeline, name='trade_timeline'),
    
    # Detailed views
    path('backtest/<int:pk>/stock/<str:symbol>/', views.stock_analysis_detail, name='stock_analysis'),
    path('backtest/<int:pk>/validation/', views.validation_report, name='validation_report'),
    path('backtest/<int:pk>/patterns/', views.pattern_analysis, name='pattern_analysis'),
    
    # =============================================================================
    # FEEDBACK LOOP ENDPOINTS (CLI + Django + Future API)
    # =============================================================================
    
    # Traditional Django trigger view (for web interface)
    path('trigger-feedback-loop/', views.trigger_feedback_loop_view, name='trigger_feedback_loop'),
    
    # API-ready endpoints (JSON responses)
    path('api/feedback-loop/', views.FeedbackLoopAPIView.as_view(), name='feedback_loop_api'),
    path('api/feedback-loop/endpoint/', views.feedback_loop_endpoint, name='feedback_loop_endpoint'),
    
    # Specialized analysis endpoints
    path('api/gap-scan/', views.gap_scan_endpoint, name='gap_scan_api'),
    path('api/news-analysis/', views.news_analysis_endpoint, name='news_analysis_api'),
    path('api/alpha-validation/', views.alpha_validation_endpoint, name='alpha_validation_api'),
    
    # =============================================================================
    # ADVANCED CORPORATE ACTIONS ENDPOINTS
    # =============================================================================
    
    # Corporate Actions views
    path('corp-actions/', views.AdvancedCorpActionsListView.as_view(), name='corp_actions_list'),
    path('corp-actions/<int:pk>/', views.AdvancedCorpActionsDetailView.as_view(), name='corp_actions_detail'),
    
    # Corporate Actions dashboard and reporting
    path('corp-actions-dashboard/', views.corp_actions_monitoring_dashboard, name='corp_actions_dashboard'),
    path('system-resilience/', views.system_resilience_report, name='system_resilience_report'),
    
    # API endpoints for charts and data
    path('api/corp-actions/<int:pk>/impact/', views.corp_actions_impact_analysis, name='corp_actions_impact_api'),
    path('api/corp-actions/alerts/', views.corp_actions_alerts_api, name='corp_actions_alerts_api'),
]

# =============================================================================
# FUTURE DRF ROUTES (Ready for Implementation)
# =============================================================================
"""
When ready to implement Django REST Framework API:

1. pip install djangorestframework
2. Add 'rest_framework' to INSTALLED_APPS in settings.py
3. Uncomment the DRF ViewSet in views.py
4. Add these routes:

from rest_framework.routers import DefaultRouter

# DRF Router for API endpoints
router = DefaultRouter()
router.register(r'feedback-loop', views.FeedbackLoopViewSet, basename='feedback-loop')

# API URLs will be:
# GET  /api/feedback-loop/analyze/?symbol=AAPL&mode=quick
# POST /api/feedback-loop/analyze/ (with JSON body)
# GET  /api/feedback-loop/gap-scan/?symbol=AAPL
# GET  /api/feedback-loop/news-analysis/?symbol=AAPL
# GET  /api/feedback-loop/alpha-validation/?symbol=AAPL

# Add to main urls.py:
# path('api/', include(router.urls)),
"""