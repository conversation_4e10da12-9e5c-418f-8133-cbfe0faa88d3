{% extends 'strategy_viz/base.html' %}
{% load static %}

{% block title %}{{ corp_action.symbol }} {{ corp_action.get_action_type_display }} - Corporate Action Detail{% endblock %}

{% block extra_css %}
<style>
    .impact-chart {
        height: 300px;
    }
    .timeline-event {
        border-left: 3px solid #007bff;
        padding-left: 15px;
        margin-bottom: 20px;
        position: relative;
    }
    .timeline-event::before {
        content: '';
        position: absolute;
        left: -6px;
        top: 8px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007bff;
    }
    .timeline-event.detection::before { background: #28a745; }
    .timeline-event.adjustment::before { background: #ffc107; }
    .timeline-event.alert::before { background: #dc3545; }
    .metric-card {
        border-left: 4px solid #007bff;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    .strategy-relevance-high { color: #28a745; font-weight: bold; }
    .strategy-relevance-medium { color: #ffc107; font-weight: bold; }
    .strategy-relevance-low { color: #6c757d; }
    .strategy-relevance-excluded { color: #dc3545; font-weight: bold; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'strategy_viz:corp_actions_list' %}" data-test-id="breadcrumb-list">Corporate Actions</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        {{ corp_action.symbol }} {{ corp_action.get_action_type_display }}
                    </li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1" data-test-id="corp-action-title">
                        {{ corp_action.symbol }} - {{ corp_action.get_action_type_display }}
                    </h1>
                    <p class="text-muted mb-0">
                        Detected {{ corp_action.detection_time|timesince }} ago | 
                        Effective {{ corp_action.effective_date|date:"M j, Y" }}
                    </p>
                </div>
                <div>
                    <span class="badge badge-{{ corp_action.monitoring_status }} badge-lg" data-test-id="monitoring-status">
                        {{ corp_action.get_monitoring_status_display }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Details -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Corporate Action Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Symbol:</strong></td>
                                    <td data-test-id="symbol">{{ corp_action.symbol }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Action Type:</strong></td>
                                    <td data-test-id="action-type">{{ corp_action.get_action_type_display }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Effective Date:</strong></td>
                                    <td data-test-id="effective-date">{{ corp_action.effective_date|date:"M j, Y" }}</td>
                                </tr>
                                {% if corp_action.ex_date %}
                                <tr>
                                    <td><strong>Ex-Date:</strong></td>
                                    <td data-test-id="ex-date">{{ corp_action.ex_date|date:"M j, Y" }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                {% if corp_action.action_type == 'dividend' and corp_action.dividend_amount %}
                                <tr>
                                    <td><strong>Dividend Amount:</strong></td>
                                    <td data-test-id="dividend-amount">${{ corp_action.dividend_amount }}</td>
                                </tr>
                                {% elif corp_action.action_type == 'split' and corp_action.split_ratio %}
                                <tr>
                                    <td><strong>Split Ratio:</strong></td>
                                    <td data-test-id="split-ratio">{{ corp_action.split_ratio }}:1</td>
                                </tr>
                                {% elif corp_action.action_type == 'spinoff' and corp_action.spinoff_ratio %}
                                <tr>
                                    <td><strong>Spinoff Ratio:</strong></td>
                                    <td data-test-id="spinoff-ratio">{{ corp_action.spinoff_ratio }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>Data Adjustment:</strong></td>
                                    <td data-test-id="data-adjustment">
                                        {% if corp_action.data_adjustment_triggered %}
                                            <span class="badge badge-success">
                                                <i class="fas fa-check"></i> Triggered
                                            </span>
                                        {% else %}
                                            <span class="badge badge-secondary">
                                                <i class="fas fa-clock"></i> Pending
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Detection Time:</strong></td>
                                    <td data-test-id="detection-time">{{ corp_action.detection_time|date:"M j, Y g:i A" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Impact Metrics -->
        <div class="col-md-4">
            <div class="card metric-card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> Strategy Impact
                    </h6>
                </div>
                <div class="card-body">
                    {% if impact_data %}
                        <div class="mb-3">
                            <label class="font-weight-bold">Gap Impact:</label>
                            <div class="d-flex justify-content-between">
                                <span>Raw Gap:</span>
                                <span data-test-id="raw-gap">{{ impact_data.raw_gap|floatformat:1 }}%</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Adjusted Gap:</span>
                                <span data-test-id="adjusted-gap">{{ impact_data.adjusted_gap|floatformat:1 }}%</span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="font-weight-bold">Impact Magnitude:</label>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" 
                                     style="width: {{ impact_data.impact_magnitude|floatformat:0 }}%"
                                     data-test-id="impact-magnitude">
                                </div>
                            </div>
                            <small class="text-muted">{{ impact_data.impact_magnitude|floatformat:1 }}%</small>
                        </div>
                        
                        {% if corp_action.strategy_relevance %}
                        <div class="mb-3">
                            <label class="font-weight-bold">Strategy Relevance:</label>
                            <br>
                            <span class="strategy-relevance-{{ corp_action.strategy_relevance }}" 
                                  data-test-id="strategy-relevance">
                                {{ corp_action.strategy_relevance|title }}
                            </span>
                        </div>
                        {% endif %}
                        
                        {% if impact_data.adjustment_quality %}
                        <div class="mb-3">
                            <label class="font-weight-bold">Adjustment Quality:</label>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" 
                                     style="width: {{ impact_data.adjustment_quality|floatformat:0 }}%"
                                     data-test-id="adjustment-quality">
                                </div>
                            </div>
                            <small class="text-muted">{{ impact_data.adjustment_quality|floatformat:1 }}%</small>
                        </div>
                        {% endif %}
                    {% else %}
                        <p class="text-muted">Impact analysis not yet available</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Impact Visualization -->
    {% if impact_data %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line"></i> Gap Impact Analysis
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="gapComparisonChart" class="impact-chart"></canvas>
                        </div>
                        <div class="col-md-6">
                            <canvas id="impactMetricsChart" class="impact-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Timeline and Alerts -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock"></i> Event Timeline
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline" data-test-id="event-timeline">
                        <!-- Timeline will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell"></i> Related Alerts
                    </h5>
                </div>
                <div class="card-body">
                    {% if alerts %}
                        {% for alert in alerts %}
                        <div class="alert alert-{{ alert.priority }} alert-dismissible" 
                             data-test-id="alert-{{ alert.id }}">
                            <strong>{{ alert.get_alert_type_display }}</strong>
                            <span class="badge badge-{{ alert.priority }} ml-2">{{ alert.get_priority_display }}</span>
                            <p class="mb-1">{{ alert.message }}</p>
                            <small class="text-muted">{{ alert.created_at|timesince }} ago</small>
                            
                            {% if alert.escalation_triggered %}
                            <div class="mt-1">
                                <span class="badge badge-danger">
                                    <i class="fas fa-exclamation-triangle"></i> Escalated
                                </span>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No alerts generated for this corporate action.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Data Adjustments -->
    {% if adjustments %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs"></i> Data Adjustments
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" data-test-id="adjustments-table">
                            <thead>
                                <tr>
                                    <th>Adjustment Type</th>
                                    <th>Affected Periods</th>
                                    <th>Consistency Check</th>
                                    <th>Issues Found</th>
                                    <th>Integrity Score</th>
                                    <th>Timestamp</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for adjustment in adjustments %}
                                <tr data-test-id="adjustment-{{ adjustment.id }}">
                                    <td>{{ adjustment.get_adjustment_type_display }}</td>
                                    <td>{{ adjustment.affected_periods }}</td>
                                    <td>
                                        {% if adjustment.consistency_check_passed %}
                                            <span class="badge badge-success">Passed</span>
                                        {% else %}
                                            <span class="badge badge-danger">Failed</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ adjustment.issues_found }}</td>
                                    <td>
                                        {% if adjustment.overall_integrity %}
                                            {{ adjustment.overall_integrity|floatformat:1 }}%
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </td>
                                    <td>{{ adjustment.adjustment_timestamp|date:"M j, Y g:i A" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <a href="{% url 'strategy_viz:corp_actions_list' %}" 
                   class="btn btn-secondary" 
                   data-test-id="back-to-list">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
                
                <div>
                    <button type="button" 
                            class="btn btn-info" 
                            onclick="refreshImpactData()"
                            data-test-id="refresh-data">
                        <i class="fas fa-sync-alt"></i> Refresh Data
                    </button>
                    
                    <a href="{% url 'strategy_viz:corp_actions_dashboard' %}" 
                       class="btn btn-primary" 
                       data-test-id="dashboard-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Timeline data from Django context
const timelineEvents = {{ timeline_events|safe }};
const impactData = {{ impact_data|default:"{}" }};

// Render timeline
function renderTimeline() {
    const timelineContainer = document.querySelector('[data-test-id="event-timeline"]');
    
    if (timelineEvents.length === 0) {
        timelineContainer.innerHTML = '<p class="text-muted">No events recorded yet.</p>';
        return;
    }
    
    let timelineHtml = '';
    timelineEvents.forEach(event => {
        const eventDate = new Date(event.date);
        timelineHtml += `
            <div class="timeline-event ${event.type}">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">${event.title}</h6>
                        <p class="mb-1 text-muted">${event.description}</p>
                        <small class="text-muted">${eventDate.toLocaleString()}</small>
                    </div>
                    <span class="badge badge-secondary">${event.type}</span>
                </div>
            </div>
        `;
    });
    
    timelineContainer.innerHTML = timelineHtml;
}

// Chart configurations
function createGapComparisonChart() {
    const ctx = document.getElementById('gapComparisonChart');
    if (!ctx || !impactData.raw_gap) return;
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Raw Gap', 'Adjusted Gap'],
            datasets: [{
                label: 'Gap Percentage',
                data: [impactData.raw_gap, impactData.adjusted_gap],
                backgroundColor: ['#dc3545', '#28a745'],
                borderColor: ['#c82333', '#1e7e34'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Gap Comparison: Before vs After Adjustment'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Percentage (%)'
                    }
                }
            }
        }
    });
}

function createImpactMetricsChart() {
    const ctx = document.getElementById('impactMetricsChart');
    if (!ctx || !impactData.impact_magnitude) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Impact Magnitude', 'Adjustment Quality'],
            datasets: [{
                data: [impactData.impact_magnitude, impactData.adjustment_quality],
                backgroundColor: ['#ffc107', '#17a2b8'],
                borderColor: ['#e0a800', '#138496'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Impact Metrics'
                }
            }
        }
    });
}

// Refresh data functionality
function refreshImpactData() {
    const btn = document.querySelector('[data-test-id="refresh-data"]');
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    btn.disabled = true;
    
    // Fetch updated impact data
    fetch(`/api/corp-actions/{{ corp_action.pk }}/impact/`)
        .then(response => response.json())
        .then(data => {
            console.log('Updated impact data:', data);
            
            // Update UI with new data (would require page refresh for full update)
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show';
                alert.innerHTML = `
                    <strong>Data Refreshed!</strong> Impact analysis updated.
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                `;
                
                document.querySelector('.container-fluid').insertBefore(
                    alert, 
                    document.querySelector('.row')
                );
                
                // Auto-dismiss after 3 seconds
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 3000);
            }, 1000);
        })
        .catch(error => {
            console.error('Error refreshing data:', error);
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    renderTimeline();
    
    // Initialize charts if we have impact data
    if (Object.keys(impactData).length > 0) {
        createGapComparisonChart();
        createImpactMetricsChart();
    }
});
</script>
{% endblock %}