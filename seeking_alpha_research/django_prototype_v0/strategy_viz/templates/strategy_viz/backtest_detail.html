{% extends 'strategy_viz/base.html' %}

{% block title %}{{ backtest.name }} - Backtest Results{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">{{ backtest.name }}</h1>
        <p class="text-muted">
            {{ backtest.start_date|date:"Y-m-d" }} to {{ backtest.end_date|date:"Y-m-d" }}
        </p>
    </div>
</div>

<!-- Key Metrics -->
<div class="row">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-label">Total Return</div>
            <div class="metric-value {% if backtest.total_return > 0 %}positive{% else %}negative{% endif %}">
                {{ backtest.total_return|floatformat:2 }}%
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-label">Win Rate</div>
            <div class="metric-value">{{ backtest.win_rate|floatformat:1 }}%</div>
            <small>{{ trades_summary.winners }}/{{ trades_summary.total }} trades</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-label">Sharpe Ratio</div>
            <div class="metric-value">{{ backtest.sharpe_ratio|floatformat:2 }}</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-label">Max Drawdown</div>
            <div class="metric-value negative">{{ backtest.max_drawdown|floatformat:1 }}%</div>
        </div>
    </div>
</div>

<!-- Strategy Validation -->
{% if validation %}
<div class="row mt-4">
    <div class="col-12">
        <h3>Strategy Validation</h3>
        <div class="alert alert-info">
            <strong>Is this strategy real or random?</strong>
            <ul class="mb-0 mt-2">
                <li>Alpha Confidence: 
                    <span class="validation-badge {% if validation.alpha_confidence > 0.95 %}badge-high{% elif validation.alpha_confidence > 0.8 %}badge-medium{% else %}badge-low{% endif %}">
                        {{ validation.alpha_confidence|floatformat:0 }}%
                    </span>
                </li>
                <li>Gap Prediction Accuracy: {{ validation.gap_prediction_accuracy|floatformat:1 }}%</li>
                <li>Survivorship Bias Impact: {{ validation.delisting_impact|floatformat:1 }}%</li>
            </ul>
            <a href="{% url 'strategy_viz:validation_report' backtest.pk %}" class="btn btn-primary mt-3">
                View Full Validation Report
            </a>
        </div>
    </div>
</div>
{% endif %}

<!-- Portfolio Chart -->
<div class="row mt-4">
    <div class="col-12">
        <h3>Portfolio Value Over Time</h3>
        <canvas id="portfolioChart" height="100"></canvas>
    </div>
</div>

<!-- Recent Trades -->
<div class="row mt-4">
    <div class="col-md-6">
        <h3>Top Winning Trades</h3>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Symbol</th>
                        <th>Entry</th>
                        <th>Exit</th>
                        <th>Return</th>
                    </tr>
                </thead>
                <tbody>
                    {% for trade in top_trades %}
                    <tr>
                        <td>{{ trade.symbol }}</td>
                        <td>{{ trade.entry_date|date:"m/d" }}</td>
                        <td>{{ trade.exit_date|date:"m/d" }}</td>
                        <td class="positive">+{{ trade.pnl_percent|floatformat:1 }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="col-md-6">
        <h3>Worst Losing Trades</h3>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Symbol</th>
                        <th>Entry</th>
                        <th>Exit</th>
                        <th>Return</th>
                    </tr>
                </thead>
                <tbody>
                    {% for trade in worst_trades %}
                    <tr>
                        <td>{{ trade.symbol }}</td>
                        <td>{{ trade.entry_date|date:"m/d" }}</td>
                        <td>{{ trade.exit_date|date:"m/d" }}</td>
                        <td class="negative">{{ trade.pnl_percent|floatformat:1 }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Recent Stock Analyses -->
<div class="row mt-4">
    <div class="col-12">
        <h3>Recent Stock Analyses</h3>
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Symbol</th>
                        <th>Analysis Date</th>
                        <th>ATM Probability</th>
                        <th>Cash Runway</th>
                        <th>Filings Analyzed</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for analysis in recent_analyses %}
                    <tr>
                        <td>{{ analysis.symbol }}</td>
                        <td>{{ analysis.analysis_date|date:"Y-m-d" }}</td>
                        <td>
                            <span class="validation-badge {% if analysis.atm_probability > 0.7 %}badge-high{% elif analysis.atm_probability > 0.5 %}badge-medium{% else %}badge-low{% endif %}">
                                {{ analysis.atm_probability|floatformat:0 }}%
                            </span>
                        </td>
                        <td>{{ analysis.cash_burn_months|floatformat:0 }} months</td>
                        <td>{{ analysis.filings_analyzed }}</td>
                        <td>
                            <a href="{% url 'strategy_viz:stock_analysis' backtest.pk analysis.symbol %}" 
                               class="btn btn-sm btn-outline-primary">View Details</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Additional Actions -->
<div class="row mt-4">
    <div class="col-12">
        <a href="{% url 'strategy_viz:trade_timeline' backtest.pk %}" class="btn btn-secondary">View Trade Timeline</a>
        <a href="{% url 'strategy_viz:pattern_analysis' backtest.pk %}" class="btn btn-secondary">Insider Pattern Analysis</a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Fetch and display portfolio chart
fetch("{% url 'strategy_viz:portfolio_chart' backtest.pk %}")
    .then(response => response.json())
    .then(data => {
        const ctx = document.getElementById('portfolioChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.dates,
                datasets: [{
                    label: 'Portfolio Value',
                    data: data.values,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }, {
                    label: 'Cash',
                    data: data.cash,
                    borderColor: 'rgb(255, 159, 64)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}