{% extends 'strategy_viz/base.html' %}

{% block title %}{{ analysis.symbol }} - Detailed Analysis{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">{{ analysis.symbol }} - Detailed Analysis</h1>
        <p class="text-muted">Analysis Date: {{ analysis.analysis_date|date:"Y-m-d" }}</p>
    </div>
</div>

<!-- Key Metrics Row -->
<div class="row">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-label">ATM Probability</div>
            <div class="metric-value {% if analysis.atm_probability > 0.7 %}positive{% else %}warning{% endif %}">
                {{ analysis.atm_probability|floatformat:0 }}%
            </div>
            <small>{{ analysis.confidence_level|title }} confidence</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-label">Cash Runway</div>
            <div class="metric-value {% if analysis.cash_burn_months < 6 %}negative{% elif analysis.cash_burn_months < 12 %}warning{% else %}positive{% endif %}">
                {{ analysis.cash_burn_months|floatformat:1 }} months
            </div>
            <small>Until cash depletion</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-label">Trigger Date</div>
            <div class="metric-value">
                {% if analysis.trigger_date %}
                    {{ analysis.trigger_date }}
                {% else %}
                    TBD
                {% endif %}
            </div>
            <small>Expected ATM timing</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-label">Current Cash</div>
            <div class="metric-value">
                ${{ analysis.current_cash|floatformat:1 }}M
            </div>
            <small>Burn: ${{ analysis.burn_rate_monthly|floatformat:1 }}M/mo</small>
        </div>
    </div>
</div>

<!-- ReAct Agent Analysis Section -->
<div class="row mt-4">
    <div class="col-12">
        <h3>🧠 ReAct Agent Analysis</h3>
        <div class="alert alert-info">
            <h5>Detailed Financial Analysis</h5>
            <p>{{ analysis.summary }}</p>
            
            {% if analysis.most_likely_gap_period %}
            <div class="mt-3">
                <strong>Gap Timing Prediction:</strong> {{ analysis.most_likely_gap_period }}<br>
                <strong>Primary Catalyst:</strong> {{ analysis.primary_catalyst|default:"Market conditions" }}<br>
                <strong>Has Active ATM:</strong> 
                <span class="badge {% if analysis.has_active_atm %}bg-warning{% else %}bg-secondary{% endif %}">
                    {{ analysis.has_active_atm|yesno:"Yes,No" }}
                </span>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Filing Timeline -->
<div class="row mt-4">
    <div class="col-12">
        <h3>📄 SEC Filing Timeline</h3>
        <div class="timeline-container">
            {% for filing in recent_filings %}
            <div class="timeline-item">
                <div class="timeline-date">{{ filing.filing_date|date:"M d, Y" }}</div>
                <div class="timeline-content">
                    <h6>{{ filing.form_type }}</h6>
                    <p>{{ filing.description|truncatewords:20 }}</p>
                    {% if filing.key_metrics %}
                    <small class="text-muted">Key: {{ filing.key_metrics|truncatewords:10 }}</small>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Price Action Chart -->
<div class="row mt-4">
    <div class="col-12">
        <h3>📈 Candlestick Chart with Gap Analysis</h3>
        <div class="alert alert-info mb-3">
            <strong>Chart Legend:</strong>
            🔴 Gap Events • 🟢 Entry Points • 🔵 Exit Points • 📰 News Catalysts
        </div>
        <canvas id="candlestickChart" height="80"></canvas>
        <canvas id="volumeChart" height="30" class="mt-2"></canvas>
        <div class="mt-3">
            <h5>Insider Accumulation Signals</h5>
            <div class="row">
                {% for pattern in insider_patterns %}
                <div class="col-md-4">
                    <div class="metric-card">
                        <div class="metric-label">{{ pattern.pattern_type|title }}</div>
                        <div class="metric-value">{{ pattern.confidence_score|floatformat:0 }}%</div>
                        <small>{{ pattern.date_detected|date:"M d" }} - Vol: {{ pattern.volume_ratio|floatformat:1 }}x</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Trade Results -->
{% if trades %}
<div class="row mt-4">
    <div class="col-12">
        <h3>💰 Trade Results</h3>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Entry Date</th>
                        <th>Entry Price</th>
                        <th>Exit Date</th>
                        <th>Exit Price</th>
                        <th>Shares</th>
                        <th>P&L</th>
                        <th>Return %</th>
                        <th>Gap %</th>
                    </tr>
                </thead>
                <tbody>
                    {% for trade in trades %}
                    <tr>
                        <td>{{ trade.entry_date|date:"M d, Y" }}</td>
                        <td>${{ trade.entry_price|floatformat:2 }}</td>
                        <td>{% if trade.exit_date %}{{ trade.exit_date|date:"M d, Y" }}{% else %}Open{% endif %}</td>
                        <td>{% if trade.exit_price %}\${{ trade.exit_price|floatformat:2 }}{% else %}-{% endif %}</td>
                        <td>{{ trade.shares|floatformat:0 }}</td>
                        <td class="{% if trade.pnl > 0 %}positive{% else %}negative{% endif %}">
                            \${{ trade.pnl|floatformat:0 }}
                        </td>
                        <td class="{% if trade.pnl_percent > 0 %}positive{% else %}negative{% endif %}">
                            {{ trade.pnl_percent|floatformat:1 }}%
                        </td>
                        <td>{{ trade.gap_percentage|floatformat:1 }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- Manual Adjustment Panel -->
<div class="row mt-4">
    <div class="col-12">
        <h3>🔧 Manual Review & Adjustments</h3>
        <div class="alert alert-warning">
            <h5>Feedback Loop - Parameter Adjustment</h5>
            <p><strong>Current Strategy Performance:</strong></p>
            <ul>
                <li>ATM Prediction Accuracy: {{ validation_metrics.atm_accuracy|default:75 }}%</li>
                <li>Timing Accuracy: {{ validation_metrics.timing_accuracy|default:68 }}%</li>
                <li>False Positives: {{ validation_metrics.false_positive_rate|default:15 }}%</li>
            </ul>
            
            <div class="mt-3">
                <button class="btn btn-primary" onclick="adjustParameters()">Adjust Parameters</button>
                <button class="btn btn-secondary" onclick="reanalyzeStock()">Re-analyze with Current Settings</button>
                <button class="btn btn-warning" onclick="flagForReview()">Flag for Review</button>
            </div>
        </div>
    </div>
</div>

<!-- Detailed JSON Analysis (Collapsible) -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#detailedAnalysis">
                        🔍 Detailed Analysis JSON (Click to Expand)
                    </button>
                </h5>
            </div>
            <div id="detailedAnalysis" class="collapse">
                <div class="card-body">
                    <pre><code>{{ analysis.detailed_analysis|pprint }}</code></pre>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.timeline-container {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
    padding-left: 20px;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: -15px;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #007bff;
}

.timeline-container:before {
    content: '';
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-date {
    font-weight: bold;
    color: #007bff;
    font-size: 0.9rem;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.warning {
    color: #fd7e14;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Get chart data from Django context
const priceData = {{ price_data|safe }};
const gapEvents = {{ gap_events|safe }};
const tradeEvents = {{ trade_events|safe }};
const newsEvents = {{ news_events|safe }};

// Candlestick Chart
const candlestickCtx = document.getElementById('candlestickChart').getContext('2d');

// Transform price data for candlestick format
const candlestickData = priceData.dates.map((date, i) => ({
    x: date,
    o: priceData.open[i],
    h: priceData.high[i], 
    l: priceData.low[i],
    c: priceData.close[i]
}));

// Create annotations for events
const annotations = [];

// Add gap events
if (gapEvents) {
    gapEvents.forEach(event => {
        annotations.push({
            type: 'point',
            xValue: event.date,
            yValue: event.price,
            backgroundColor: 'rgba(255, 99, 132, 0.8)',
            borderColor: 'rgb(255, 99, 132)',
            borderWidth: 2,
            radius: 8,
            label: {
                content: `Gap: ${event.gap_percent}%`,
                enabled: true,
                position: 'top'
            }
        });
    });
}

// Add entry/exit points
if (tradeEvents) {
    tradeEvents.forEach(event => {
        annotations.push({
            type: 'point',
            xValue: event.date,
            yValue: event.price,
            backgroundColor: event.type === 'entry' ? 'rgba(75, 192, 192, 0.8)' : 'rgba(54, 162, 235, 0.8)',
            borderColor: event.type === 'entry' ? 'rgb(75, 192, 192)' : 'rgb(54, 162, 235)',
            borderWidth: 2,
            radius: 6,
            label: {
                content: event.type === 'entry' ? 'Entry' : 'Exit',
                enabled: true,
                position: 'top'
            }
        });
    });
}

// Add news events
if (newsEvents) {
    newsEvents.forEach(event => {
        annotations.push({
            type: 'line',
            xMin: event.date,
            xMax: event.date,
            borderColor: 'rgba(255, 206, 86, 0.8)',
            borderWidth: 2,
            borderDash: [5, 5],
            label: {
                content: 'News',
                enabled: true,
                position: 'top'
            }
        });
    });
}

new Chart(candlestickCtx, {
    type: 'candlestick',
    data: {
        datasets: [{
            label: '{{ analysis.symbol }} Price',
            data: candlestickData,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            x: {
                type: 'time',
                time: {
                    unit: 'day'
                }
            },
            y: {
                title: {
                    display: true,
                    text: 'Price ($)'
                }
            }
        },
        plugins: {
            legend: {
                display: true
            },
            title: {
                display: true,
                text: '{{ analysis.symbol }} - Gap-Up ATM Analysis'
            },
            annotation: {
                annotations: annotations
            }
        }
    }
});

// Volume Chart
const volumeCtx = document.getElementById('volumeChart').getContext('2d');

new Chart(volumeCtx, {
    type: 'bar',
    data: {
        labels: priceData.dates,
        datasets: [{
            label: 'Volume',
            data: priceData.volumes,
            backgroundColor: priceData.volumes.map((vol, i) => {
                // Color volume bars based on price movement
                const prevClose = i > 0 ? priceData.close[i-1] : priceData.close[i];
                const currentClose = priceData.close[i];
                return currentClose > prevClose ? 'rgba(75, 192, 192, 0.6)' : 'rgba(255, 99, 132, 0.6)';
            }),
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            x: {
                display: false
            },
            y: {
                title: {
                    display: true,
                    text: 'Volume'
                },
                ticks: {
                    callback: function(value) {
                        if (value >= 1000000) {
                            return (value / 1000000).toFixed(1) + 'M';
                        } else if (value >= 1000) {
                            return (value / 1000).toFixed(0) + 'K';
                        }
                        return value;
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Adjustment functions
function adjustParameters() {
    alert('Parameter adjustment panel would open here - allowing manual tuning of ATM thresholds, timing windows, etc.');
}

function reanalyzeStock() {
    if (confirm('Re-analyze {{ analysis.symbol }} with current settings?')) {
        // Would trigger re-analysis
        window.location.reload();
    }
}

function flagForReview() {
    alert('{{ analysis.symbol }} flagged for manual review. This helps calibrate the system.');
}
</script>
{% endblock %}