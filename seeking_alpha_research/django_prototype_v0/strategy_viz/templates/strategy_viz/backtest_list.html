{% extends 'strategy_viz/base.html' %}
{% load humanize %}

{% block title %}Backtest Runs - Gap-Up ATM Strategy{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">Backtest Runs</h1>
        
        <div class="warning-box">
            <strong>⚠️ Critical Strategy Parameters:</strong>
            <ul class="mb-0">
                <li>Market Cap: &lt; $100M (small caps only)</li>
                <li>Gap Threshold: 30%+ with news catalyst</li>
                <li>SEC Analysis: 2 YEARS of filings (not just latest)</li>
                <li>Exit: Premarket ~7:30 AM when volume arrives</li>
                <li>Includes delisted stocks for survivorship bias</li>
            </ul>
        </div>
        
        {% if backtests %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Period</th>
                        <th>Initial Capital</th>
                        <th>Final Capital</th>
                        <th>Return</th>
                        <th>Win Rate</th>
                        <th>Trades</th>
                        <th>Delistings</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for backtest in backtests %}
                    <tr>
                        <td>{{ backtest.name }}</td>
                        <td>{{ backtest.start_date|date:"Y-m-d" }} to {{ backtest.end_date|date:"Y-m-d" }}</td>
                        <td>${{ backtest.initial_capital|floatformat:0|intcomma }}</td>
                        <td>${{ backtest.final_capital|floatformat:0|intcomma }}</td>
                        <td class="{% if backtest.total_return > 0 %}positive{% else %}negative{% endif %}">
                            {{ backtest.total_return|floatformat:2 }}%
                        </td>
                        <td>{{ backtest.win_rate|floatformat:1 }}%</td>
                        <td>{{ backtest.total_trades }}</td>
                        <td>{{ backtest.delistings }}</td>
                        <td>
                            <a href="{% url 'strategy_viz:backtest_detail' backtest.pk %}" 
                               class="btn btn-sm btn-primary">View Details</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            No backtest runs yet. Run: <code>python manage.py run_backtest --start 2024-01-01 --end 2024-01-31</code>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}