{% extends 'strategy_viz/base.html' %}
{% load static %}

{% block title %}Advanced Corporate Actions Monitoring{% endblock %}

{% block extra_css %}
<style>
    .corp-action-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .corp-action-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .status-badge {
        font-size: 0.8em;
        padding: 0.3em 0.6em;
    }
    .action-type-dividend { border-left-color: #28a745; }
    .action-type-split { border-left-color: #ffc107; }
    .action-type-spinoff { border-left-color: #17a2b8; }
    .action-type-merger { border-left-color: #6f42c1; }
    .action-type-delisting { border-left-color: #dc3545; }
    .monitoring-stats {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Advanced Corporate Actions Monitoring</h1>
                    <p class="text-muted">Real-time detection and automatic data adjustment system</p>
                </div>
                <div>
                    <a href="{% url 'strategy_viz:corp_actions_dashboard' %}" class="btn btn-primary" data-test-id="dashboard-btn">
                        <i class="fas fa-chart-line"></i> Dashboard
                    </a>
                    <a href="{% url 'strategy_viz:system_resilience_report' %}" class="btn btn-outline-info" data-test-id="resilience-btn">
                        <i class="fas fa-shield-alt"></i> System Health
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Monitoring Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card monitoring-stats h-100">
                <div class="card-body text-center">
                    <i class="fas fa-search fa-2x mb-2"></i>
                    <h4 class="card-title" data-test-id="total-actions">{{ total_actions }}</h4>
                    <p class="card-text">Total Actions Detected</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-eye fa-2x mb-2"></i>
                    <h4 class="card-title" data-test-id="active-monitoring">{{ active_monitoring }}</h4>
                    <p class="card-text">Active Monitoring</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-cogs fa-2x mb-2"></i>
                    <h4 class="card-title" data-test-id="adjustments-triggered">{{ adjustments_triggered }}</h4>
                    <p class="card-text">Data Adjustments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-heartbeat fa-2x mb-2"></i>
                    <h4 class="card-title text-capitalize" data-test-id="system-health">{{ system_health }}</h4>
                    <p class="card-text">System Status</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Alerts Section -->
    {% if recent_alerts %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell text-warning"></i> Recent Alerts
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for alert in recent_alerts %}
                        <div class="list-group-item d-flex justify-content-between align-items-center" data-test-id="alert-{{ alert.id }}">
                            <div>
                                <strong>{{ alert.corporate_action.symbol }}</strong>
                                <span class="badge badge-{{ alert.priority }} ml-2">{{ alert.get_priority_display }}</span>
                                <p class="mb-0 text-muted small">{{ alert.message }}</p>
                            </div>
                            <small class="text-muted">{{ alert.created_at|timesince }} ago</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Corporate Actions List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Corporate Actions Detected</h5>
                </div>
                <div class="card-body">
                    {% if corp_actions %}
                        <div class="row">
                            {% for action in corp_actions %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card corp-action-card action-type-{{ action.action_type }}" data-test-id="corp-action-{{ action.id }}">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">
                                                <strong>{{ action.symbol }}</strong>
                                            </h6>
                                            <span class="badge badge-{{ action.monitoring_status }} status-badge">
                                                {{ action.get_monitoring_status_display }}
                                            </span>
                                        </div>
                                        
                                        <p class="card-text">
                                            <strong>{{ action.get_action_type_display }}</strong>
                                            {% if action.action_type == 'dividend' and action.dividend_amount %}
                                                <br><small class="text-muted">${{ action.dividend_amount }}</small>
                                            {% elif action.action_type == 'split' and action.split_ratio %}
                                                <br><small class="text-muted">{{ action.split_ratio }}:1 ratio</small>
                                            {% elif action.action_type == 'spinoff' and action.spinoff_ratio %}
                                                <br><small class="text-muted">{{ action.spinoff_ratio }} ratio</small>
                                            {% endif %}
                                        </p>
                                        
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar"></i> 
                                                Effective: {{ action.effective_date|date:"M j, Y" }}
                                            </small>
                                            {% if action.ex_date %}
                                            <br><small class="text-muted">
                                                <i class="fas fa-calendar-alt"></i> 
                                                Ex-Date: {{ action.ex_date|date:"M j, Y" }}
                                            </small>
                                            {% endif %}
                                        </div>

                                        {% if action.data_adjustment_triggered %}
                                        <div class="mb-2">
                                            <span class="badge badge-success">
                                                <i class="fas fa-check"></i> Data Adjusted
                                            </span>
                                        </div>
                                        {% endif %}

                                        {% if action.impact_magnitude %}
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar" 
                                                 role="progressbar" 
                                                 style="width: {{ action.impact_magnitude|floatformat:0 }}%"
                                                 title="Impact Magnitude: {{ action.impact_magnitude|floatformat:1 }}%">
                                            </div>
                                        </div>
                                        {% endif %}

                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                Detected {{ action.detection_time|timesince }} ago
                                            </small>
                                            <a href="{% url 'strategy_viz:corp_actions_detail' action.pk %}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               data-test-id="view-detail-{{ action.id }}">
                                                View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="Corporate actions pagination">
                            <ul class="pagination justify-content-center mt-4">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1" data-test-id="first-page">&laquo; First</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}" data-test-id="prev-page">Previous</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link" data-test-id="current-page">
                                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}" data-test-id="next-page">Next</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" data-test-id="last-page">Last &raquo;</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Corporate Actions Detected</h5>
                            <p class="text-muted">The monitoring system is running and will detect corporate actions automatically.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh page every 30 seconds for real-time monitoring
let autoRefreshInterval;

function startAutoRefresh() {
    autoRefreshInterval = setInterval(() => {
        // Only refresh if user hasn't interacted recently
        if (Date.now() - lastUserActivity > 30000) {
            location.reload();
        }
    }, 30000);
}

let lastUserActivity = Date.now();

// Track user activity
document.addEventListener('mousemove', () => {
    lastUserActivity = Date.now();
});

document.addEventListener('keypress', () => {
    lastUserActivity = Date.now();
});

// Start auto-refresh on page load
document.addEventListener('DOMContentLoaded', () => {
    startAutoRefresh();
});

// Clean up interval on page unload
window.addEventListener('beforeunload', () => {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
</script>
{% endblock %}