# Generated by Django 5.2.4 on 2025-07-11 12:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="BacktestRun",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("run_date", models.DateTimeField(auto_now_add=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "initial_capital",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "position_size_pct",
                    models.DecimalField(decimal_places=2, max_digits=5),
                ),
                ("max_positions", models.IntegerField()),
                ("gap_threshold", models.DecimalField(decimal_places=2, max_digits=5)),
                ("stop_loss_pct", models.DecimalField(decimal_places=2, max_digits=5)),
                (
                    "take_profit_pct",
                    models.DecimalField(decimal_places=2, max_digits=5),
                ),
                ("total_return", models.DecimalField(decimal_places=2, max_digits=10)),
                ("sharpe_ratio", models.DecimalField(decimal_places=3, max_digits=6)),
                ("max_drawdown", models.DecimalField(decimal_places=2, max_digits=10)),
                ("win_rate", models.DecimalField(decimal_places=2, max_digits=5)),
                ("total_trades", models.IntegerField()),
                (
                    "gaps_with_news_win_rate",
                    models.DecimalField(decimal_places=2, max_digits=5),
                ),
                (
                    "gaps_without_news_win_rate",
                    models.DecimalField(decimal_places=2, max_digits=5),
                ),
                (
                    "dilution_confirmation_rate",
                    models.DecimalField(decimal_places=2, max_digits=5),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("running", "Running"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="running",
                        max_length=20,
                    ),
                ),
                ("error_message", models.TextField(blank=True)),
            ],
            options={
                "ordering": ["-run_date"],
            },
        ),
        migrations.CreateModel(
            name="StockAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("symbol", models.CharField(db_index=True, max_length=10)),
                ("analysis_date", models.DateField()),
                ("gap_date", models.DateField(blank=True, null=True)),
                (
                    "gap_percentage",
                    models.DecimalField(decimal_places=2, max_digits=10, null=True),
                ),
                ("had_news", models.BooleanField(default=False)),
                ("news_headline", models.TextField(blank=True)),
                (
                    "cash_burn_rate",
                    models.DecimalField(decimal_places=2, max_digits=12, null=True),
                ),
                (
                    "months_until_cash_out",
                    models.DecimalField(decimal_places=1, max_digits=5, null=True),
                ),
                ("has_active_atm", models.BooleanField(default=False)),
                (
                    "atm_amount",
                    models.DecimalField(decimal_places=2, max_digits=12, null=True),
                ),
                (
                    "filing_risk_score",
                    models.DecimalField(decimal_places=2, max_digits=3, null=True),
                ),
                ("llm_analysis", models.TextField(blank=True)),
                ("predicted_gap_date_start", models.DateField(blank=True, null=True)),
                ("predicted_gap_date_end", models.DateField(blank=True, null=True)),
                ("unusual_volume_days", models.IntegerField(default=0)),
                ("volume_analysis", models.JSONField(default=dict)),
                ("dilution_confirmed", models.BooleanField(default=False)),
                ("dilution_date", models.DateField(blank=True, null=True)),
                ("dilution_form_type", models.CharField(blank=True, max_length=20)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-analysis_date", "symbol"],
                "unique_together": {("symbol", "analysis_date")},
            },
        ),
        migrations.CreateModel(
            name="TradePosition",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("symbol", models.CharField(max_length=10)),
                ("entry_date", models.DateField()),
                ("entry_price", models.DecimalField(decimal_places=4, max_digits=10)),
                ("shares", models.IntegerField()),
                ("exit_date", models.DateField(blank=True, null=True)),
                (
                    "exit_price",
                    models.DecimalField(decimal_places=4, max_digits=10, null=True),
                ),
                ("exit_reason", models.CharField(blank=True, max_length=50)),
                (
                    "pnl",
                    models.DecimalField(decimal_places=2, max_digits=12, null=True),
                ),
                (
                    "pnl_percentage",
                    models.DecimalField(decimal_places=2, max_digits=10, null=True),
                ),
                ("held_days", models.IntegerField(null=True)),
                (
                    "max_gain_pct",
                    models.DecimalField(decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "max_loss_pct",
                    models.DecimalField(decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "backtest_run",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="positions",
                        to="core.backtestrun",
                    ),
                ),
                (
                    "stock_analysis",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="core.stockanalysis",
                    ),
                ),
            ],
            options={
                "ordering": ["-entry_date"],
            },
        ),
        migrations.CreateModel(
            name="DailyPortfolioValue",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField()),
                (
                    "portfolio_value",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                ("cash", models.DecimalField(decimal_places=2, max_digits=12)),
                ("positions_count", models.IntegerField()),
                (
                    "backtest_run",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="daily_values",
                        to="core.backtestrun",
                    ),
                ),
            ],
            options={
                "ordering": ["date"],
                "unique_together": {("backtest_run", "date")},
            },
        ),
    ]
