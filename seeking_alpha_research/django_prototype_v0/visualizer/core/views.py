from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q, Avg, Count
import json
from datetime import datetime, timedelta
import pandas as pd
import sys
import os

# Add parent directories to path for imports
sys.path.append(
    os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
)

# Import our analysis modules
from comprehensive_backtester import ComprehensiveBacktester
from premarket_scanner import PremarketScanner
from dilution_confirmation import DilutionConfirmation
from llm_agent import analyze_filing_for_atm_risk, calculate_cash_burn
from data_service import DataService

from .models import BacktestRun, StockAnalysis, TradePosition, DailyPortfolioValue


def dashboard(request):
    """Main dashboard showing strategy overview and validation."""

    # Get latest backtest runs
    recent_backtests = BacktestRun.objects.filter(status="completed")[:5]

    # Calculate overall strategy metrics
    if recent_backtests:
        avg_return = sum(b.total_return for b in recent_backtests) / len(
            recent_backtests
        )
        avg_win_rate = sum(b.win_rate for b in recent_backtests) / len(recent_backtests)
        avg_sharpe = sum(b.sharpe_ratio for b in recent_backtests) / len(
            recent_backtests
        )
        avg_dilution_rate = sum(
            b.dilution_confirmation_rate for b in recent_backtests
        ) / len(recent_backtests)
    else:
        avg_return = avg_win_rate = avg_sharpe = avg_dilution_rate = 0

    # Get recent gap events with high confidence
    recent_gaps = StockAnalysis.objects.filter(
        gap_percentage__gte=20, filing_risk_score__gte=0.7
    ).order_by("-gap_date")[:10]

    context = {
        "recent_backtests": recent_backtests,
        "avg_return": avg_return,
        "avg_win_rate": avg_win_rate,
        "avg_sharpe": avg_sharpe,
        "avg_dilution_rate": avg_dilution_rate,
        "recent_gaps": recent_gaps,
    }

    return render(request, "core/dashboard.html", context)


def stock_analysis(request, symbol):
    """Detailed analysis view for a specific stock."""

    # Get all analyses for this stock
    analyses = StockAnalysis.objects.filter(symbol=symbol).order_by("-analysis_date")

    # Get latest analysis
    latest = analyses.first() if analyses else None

    # Get all trades for this stock
    trades = TradePosition.objects.filter(symbol=symbol).order_by("-entry_date")

    # Calculate stock-specific metrics
    if trades:
        stock_win_rate = (
            len([t for t in trades if t.pnl_percentage > 0]) / len(trades) * 100
        )
        avg_return = sum(t.pnl_percentage for t in trades) / len(trades)
    else:
        stock_win_rate = avg_return = 0

    context = {
        "symbol": symbol,
        "latest_analysis": latest,
        "analyses": analyses,
        "trades": trades,
        "stock_win_rate": stock_win_rate,
        "avg_return": avg_return,
    }

    return render(request, "core/stock_analysis.html", context)


def backtest_detail(request, backtest_id):
    """Detailed view of a specific backtest run."""

    backtest = get_object_or_404(BacktestRun, id=backtest_id)

    # Get all positions for this backtest
    positions = backtest.positions.all().select_related("stock_analysis")

    # Get daily values for equity curve
    daily_values = backtest.daily_values.all().order_by("date")

    # Calculate position statistics
    winning_positions = [p for p in positions if p.pnl_percentage > 0]
    losing_positions = [p for p in positions if p.pnl_percentage <= 0]

    # Group positions by exit reason
    exit_reasons = {}
    for pos in positions:
        reason = pos.exit_reason or "Open"
        if reason not in exit_reasons:
            exit_reasons[reason] = {"count": 0, "total_pnl": 0}
        exit_reasons[reason]["count"] += 1
        exit_reasons[reason]["total_pnl"] += float(pos.pnl or 0)

    context = {
        "backtest": backtest,
        "positions": positions,
        "daily_values": daily_values,
        "winning_positions": winning_positions,
        "losing_positions": losing_positions,
        "exit_reasons": exit_reasons,
    }

    return render(request, "core/backtest_detail.html", context)


@require_http_methods(["POST"])
def run_backtest(request):
    """Run a new backtest with given parameters."""

    # Get parameters from request
    data = json.loads(request.body)

    start_date = data.get("start_date")
    end_date = data.get("end_date")
    initial_capital = float(data.get("initial_capital", 100000))
    gap_threshold = float(data.get("gap_threshold", 20))
    require_news = data.get("require_news", False)

    # Create backtest run record
    backtest_run = BacktestRun.objects.create(
        start_date=start_date,
        end_date=end_date,
        initial_capital=initial_capital,
        position_size_pct=2.0,
        max_positions=10,
        gap_threshold=gap_threshold,
        stop_loss_pct=-10.0,
        take_profit_pct=50.0,
        total_return=0,
        sharpe_ratio=0,
        max_drawdown=0,
        win_rate=0,
        total_trades=0,
        gaps_with_news_win_rate=0,
        gaps_without_news_win_rate=0,
        dilution_confirmation_rate=0,
        status="running",
    )

    try:
        # Initialize backtester
        backtester = ComprehensiveBacktester(
            initial_capital=initial_capital, position_size_pct=0.02, max_positions=10
        )

        # Run backtest
        results = backtester.run_backtest(
            start_date=start_date,
            end_date=end_date,
            gap_threshold=gap_threshold,
            require_news=require_news,
            require_filing_analysis=False,  # Disable for speed in demo
        )

        # Update backtest run with results
        backtest_run.total_return = results.total_return
        backtest_run.sharpe_ratio = results.sharpe_ratio
        backtest_run.max_drawdown = results.max_drawdown
        backtest_run.win_rate = results.win_rate
        backtest_run.total_trades = results.total_trades
        backtest_run.gaps_with_news_win_rate = results.gaps_with_news_win_rate
        backtest_run.gaps_without_news_win_rate = results.gaps_without_news_win_rate
        backtest_run.status = "completed"
        backtest_run.save()

        # Save positions
        for pos in results.positions:
            TradePosition.objects.create(
                backtest_run=backtest_run,
                symbol=pos.symbol,
                entry_date=pos.entry_date,
                entry_price=pos.entry_price,
                shares=pos.shares,
                exit_date=pos.exit_date,
                exit_price=pos.exit_price,
                exit_reason=pos.exit_reason,
                pnl=pos.pnl,
                pnl_percentage=pos.pnl_percentage,
                held_days=pos.held_days,
                max_gain_pct=pos.max_gain_pct,
                max_loss_pct=pos.max_loss_pct,
            )

        # Save daily values
        if (
            hasattr(results, "daily_portfolio_value")
            and not results.daily_portfolio_value.empty
        ):
            for date, value in results.daily_portfolio_value.items():
                DailyPortfolioValue.objects.create(
                    backtest_run=backtest_run,
                    date=date,
                    portfolio_value=value,
                    cash=0,  # Would need to track this separately
                    positions_count=0,
                )

        return JsonResponse(
            {
                "status": "success",
                "backtest_id": backtest_run.id,
                "total_return": float(results.total_return),
                "win_rate": float(results.win_rate),
            }
        )

    except Exception as e:
        backtest_run.status = "failed"
        backtest_run.error_message = str(e)
        backtest_run.save()

        return JsonResponse({"status": "error", "message": str(e)}, status=500)


@require_http_methods(["POST"])
def analyze_stock(request):
    """Run comprehensive analysis on a specific stock."""

    data = json.loads(request.body)
    symbol = data.get("symbol")
    analysis_date = data.get("date", datetime.now().strftime("%Y-%m-%d"))

    try:
        # Initialize services
        data_service = DataService()
        scanner = PremarketScanner(data_service)
        dilution_checker = DilutionConfirmation(data_service)

        # Check for recent gaps
        end_date = pd.to_datetime(analysis_date)
        start_date = end_date - timedelta(days=30)

        gaps = scanner.scan_premarket_gaps(
            analysis_date, min_gap_pct=20.0, min_volume_ratio=2.0
        )

        gap_found = False
        gap_data = {}

        if not gaps.empty and symbol in gaps["symbol"].values:
            gap_info = gaps[gaps["symbol"] == symbol].iloc[0]
            gap_found = True
            gap_data = {
                "gap_date": analysis_date,
                "gap_percentage": float(gap_info["gap_pct"]),
                "had_news": bool(gap_info["has_news"]),
                "unusual_volume_days": int(gap_info["unusual_volume_days"]),
            }

        # Get filing analysis (simplified for demo)
        filing_analysis = {
            "cash_burn_rate": 1000000,  # $1M/month placeholder
            "months_until_cash_out": 6,
            "has_active_atm": True,
            "atm_amount": 50000000,
            "filing_risk_score": 0.8,
        }

        # Check for dilution if gap found
        dilution_data = {}
        if gap_found:
            dilution_result = dilution_checker.check_for_dilution(
                symbol, gap_data["gap_date"]
            )
            dilution_data = {
                "dilution_confirmed": dilution_result["dilution_confirmed"],
                "dilution_date": dilution_result.get("filed_at"),
                "dilution_form_type": dilution_result.get("form_type", ""),
            }

        # Create or update analysis
        analysis, created = StockAnalysis.objects.update_or_create(
            symbol=symbol,
            analysis_date=analysis_date,
            defaults={
                **gap_data,
                **filing_analysis,
                **dilution_data,
                "llm_analysis": "Analysis would be performed by LLM here.",
            },
        )

        return JsonResponse(
            {
                "status": "success",
                "analysis_id": analysis.id,
                "gap_found": gap_found,
                "filing_risk_score": filing_analysis["filing_risk_score"],
            }
        )

    except Exception as e:
        return JsonResponse({"status": "error", "message": str(e)}, status=500)


def strategy_validation(request):
    """View showing comprehensive strategy validation metrics."""

    # Get all completed backtests
    backtests = BacktestRun.objects.filter(status="completed")

    if not backtests:
        context = {"has_data": False}
        return render(request, "core/strategy_validation.html", context)

    # Calculate validation metrics
    total_backtests = len(backtests)
    profitable_backtests = len([b for b in backtests if b.total_return > 0])

    # Dilution confirmation analysis
    analyses_with_gaps = StockAnalysis.objects.filter(gap_percentage__gte=20)
    confirmed_dilutions = analyses_with_gaps.filter(dilution_confirmed=True)

    dilution_accuracy = 0
    if analyses_with_gaps:
        dilution_accuracy = (
            confirmed_dilutions.count() / analyses_with_gaps.count()
        ) * 100

    # News correlation
    trades_with_news = TradePosition.objects.filter(
        stock_analysis__had_news=True
    ).aggregate(avg_return=Avg("pnl_percentage"), count=Count("id"))

    trades_without_news = TradePosition.objects.filter(
        stock_analysis__had_news=False
    ).aggregate(avg_return=Avg("pnl_percentage"), count=Count("id"))

    # Filing risk correlation
    high_risk_trades = TradePosition.objects.filter(
        stock_analysis__filing_risk_score__gte=0.7
    ).aggregate(avg_return=Avg("pnl_percentage"), count=Count("id"))

    low_risk_trades = TradePosition.objects.filter(
        stock_analysis__filing_risk_score__lt=0.7
    ).aggregate(avg_return=Avg("pnl_percentage"), count=Count("id"))

    context = {
        "has_data": True,
        "total_backtests": total_backtests,
        "profitable_backtests": profitable_backtests,
        "success_rate": (
            (profitable_backtests / total_backtests * 100) if total_backtests else 0
        ),
        "dilution_accuracy": dilution_accuracy,
        "trades_with_news": trades_with_news,
        "trades_without_news": trades_without_news,
        "high_risk_trades": high_risk_trades,
        "low_risk_trades": low_risk_trades,
        "backtests": backtests,
    }

    return render(request, "core/strategy_validation.html", context)
