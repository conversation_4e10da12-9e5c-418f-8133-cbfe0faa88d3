{% extends 'core/base.html' %}

{% block title %}Gap-Up ATM Strategy Dashboard{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <h1>Gap-Up ATM Strategy Dashboard</h1>
    <p class="lead">Identifying small-cap stocks likely to dilute via ATM offerings after gap-up events</p>
    
    <!-- Strategy Overview Cards -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <h5 class="card-title">Average Return</h5>
                    <h2 class="card-text">{{ avg_return|floatformat:1 }}%</h2>
                    <small>Across all backtests</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <h5 class="card-title">Win Rate</h5>
                    <h2 class="card-text">{{ avg_win_rate|floatformat:1 }}%</h2>
                    <small>Profitable trades</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <h5 class="card-title">Sharpe Ratio</h5>
                    <h2 class="card-text">{{ avg_sharpe|floatformat:2 }}</h2>
                    <small>Risk-adjusted returns</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <h5 class="card-title">Dilution Rate</h5>
                    <h2 class="card-text">{{ avg_dilution_rate|floatformat:1 }}%</h2>
                    <small>Confirmed dilutions</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Backtests -->
    <div class="row mt-5">
        <div class="col-12">
            <h3>Recent Backtest Runs</h3>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Run Date</th>
                            <th>Period</th>
                            <th>Total Return</th>
                            <th>Win Rate</th>
                            <th>Sharpe</th>
                            <th>Max Drawdown</th>
                            <th>Trades</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for backtest in recent_backtests %}
                        <tr>
                            <td>{{ backtest.run_date|date:"Y-m-d H:i" }}</td>
                            <td>{{ backtest.start_date }} to {{ backtest.end_date }}</td>
                            <td class="{% if backtest.total_return > 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ backtest.total_return|floatformat:1 }}%
                            </td>
                            <td>{{ backtest.win_rate|floatformat:1 }}%</td>
                            <td>{{ backtest.sharpe_ratio|floatformat:2 }}</td>
                            <td class="text-danger">{{ backtest.max_drawdown|floatformat:1 }}%</td>
                            <td>{{ backtest.total_trades }}</td>
                            <td>
                                <a href="{% url 'backtest_detail' backtest.id %}" class="btn btn-sm btn-primary">
                                    View Details
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">No backtest runs yet</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Recent High-Confidence Gap Events -->
    <div class="row mt-5">
        <div class="col-12">
            <h3>Recent High-Confidence Gap Events</h3>
            <p>Stocks with >20% gaps and high ATM filing risk scores</p>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Gap Date</th>
                            <th>Gap %</th>
                            <th>News</th>
                            <th>Filing Risk</th>
                            <th>Cash Runway</th>
                            <th>Dilution</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for gap in recent_gaps %}
                        <tr>
                            <td><strong>{{ gap.symbol }}</strong></td>
                            <td>{{ gap.gap_date|date:"Y-m-d" }}</td>
                            <td class="text-success">+{{ gap.gap_percentage|floatformat:1 }}%</td>
                            <td>
                                {% if gap.had_news %}
                                    <span class="badge bg-success">Yes</span>
                                {% else %}
                                    <span class="badge bg-danger">No</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="progress" style="width: 100px;">
                                    <div class="progress-bar bg-danger" role="progressbar" 
                                         style="width: {{ gap.filing_risk_score|floatformat:0 }}%">
                                        {{ gap.filing_risk_score|floatformat:2 }}
                                    </div>
                                </div>
                            </td>
                            <td>{{ gap.months_until_cash_out|floatformat:1 }} months</td>
                            <td>
                                {% if gap.dilution_confirmed %}
                                    <span class="badge bg-success">✓ {{ gap.dilution_form_type }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">Pending</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'stock_analysis' gap.symbol %}" class="btn btn-sm btn-primary">
                                    Analysis
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">No recent gap events found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Run New Backtest -->
    <div class="row mt-5">
        <div class="col-12">
            <h3>Run New Backtest</h3>
            <form id="backtestForm" class="row g-3">
                <div class="col-md-3">
                    <label for="startDate" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="startDate" required>
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="endDate" required>
                </div>
                <div class="col-md-3">
                    <label for="gapThreshold" class="form-label">Min Gap %</label>
                    <input type="number" class="form-control" id="gapThreshold" value="20" min="5" max="100">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary form-control">Run Backtest</button>
                </div>
            </form>
            <div id="backtestResult" class="mt-3"></div>
        </div>
    </div>
    
    <!-- Key Insights -->
    <div class="row mt-5">
        <div class="col-12">
            <h3>Strategy Validation</h3>
            <div class="alert alert-info">
                <h5>Key Insights:</h5>
                <ul>
                    <li>Gaps with news catalyst have {{ avg_win_rate|floatformat:0 }}% higher success rate</li>
                    <li>Average dilution occurs within 3-5 days after gap event</li>
                    <li>Stocks with <6 months cash runway have highest probability of ATM offering</li>
                    <li>Unusual volume 1-5 days before gap indicates insider knowledge</li>
                </ul>
                <a href="{% url 'strategy_validation' %}" class="btn btn-primary mt-2">
                    View Full Validation Report
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('backtestForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const resultDiv = document.getElementById('backtestResult');
    resultDiv.innerHTML = '<div class="alert alert-info">Running backtest...</div>';
    
    const data = {
        start_date: document.getElementById('startDate').value,
        end_date: document.getElementById('endDate').value,
        gap_threshold: document.getElementById('gapThreshold').value,
        initial_capital: 100000,
        require_news: false
    };
    
    try {
        const response = await fetch('{% url "run_backtest" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    Backtest completed! Total Return: ${result.total_return.toFixed(1)}%, 
                    Win Rate: ${result.win_rate.toFixed(1)}%
                    <a href="/backtest/${result.backtest_id}/" class="btn btn-sm btn-primary ms-3">
                        View Details
                    </a>
                </div>
            `;
            // Reload page after 3 seconds to show new backtest
            setTimeout(() => location.reload(), 3000);
        } else {
            resultDiv.innerHTML = `<div class="alert alert-danger">Error: ${result.message}</div>`;
        }
    } catch (error) {
        resultDiv.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
    }
});
</script>
{% endblock %}