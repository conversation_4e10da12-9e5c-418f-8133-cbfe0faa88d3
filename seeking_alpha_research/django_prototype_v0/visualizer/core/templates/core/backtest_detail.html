{% extends 'core/base.html' %}

{% block title %}Backtest Details - {{ backtest.start_date }} to {{ backtest.end_date }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <h1>Backtest Details</h1>
    <p class="lead">{{ backtest.start_date }} to {{ backtest.end_date }} - Status: {{ backtest.status|title }}</p>
    
    <!-- Performance Summary -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-white {% if backtest.total_return > 0 %}bg-success{% else %}bg-danger{% endif %}">
                <div class="card-body">
                    <h5 class="card-title">Total Return</h5>
                    <h2 class="card-text">{{ backtest.total_return|floatformat:1 }}%</h2>
                    <small>Starting: ${{ backtest.initial_capital|floatformat:0 }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Win Rate</h5>
                    <h2 class="card-text">{{ backtest.win_rate|floatformat:1 }}%</h2>
                    <small>{{ winning_positions|length }} wins / {{ backtest.total_trades }} trades</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Sharpe Ratio</h5>
                    <h2 class="card-text">{{ backtest.sharpe_ratio|floatformat:2 }}</h2>
                    <small>Risk-adjusted returns</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-danger">
                <div class="card-body">
                    <h5 class="card-title">Max Drawdown</h5>
                    <h2 class="card-text">{{ backtest.max_drawdown|floatformat:1 }}%</h2>
                    <small>Largest peak-to-trough decline</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Strategy Parameters -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h4 class="mb-0">Strategy Parameters</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Gap Threshold:</strong> {{ backtest.gap_threshold }}%<br>
                            <strong>Position Size:</strong> {{ backtest.position_size_pct }}%<br>
                            <strong>Max Positions:</strong> {{ backtest.max_positions }}
                        </div>
                        <div class="col-md-4">
                            <strong>Stop Loss:</strong> {{ backtest.stop_loss_pct }}%<br>
                            <strong>Take Profit:</strong> {{ backtest.take_profit_pct }}%<br>
                            <strong>Max Hold Days:</strong> 30
                        </div>
                        <div class="col-md-4">
                            <strong>News Filter:</strong> {{ backtest.require_news|yesno:"Required,Not Required" }}<br>
                            <strong>Dilution Rate:</strong> {{ backtest.dilution_confirmation_rate|floatformat:1 }}%<br>
                            <strong>Runtime:</strong> {{ backtest.run_date|timesince }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Equity Curve -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Equity Curve</h4>
                </div>
                <div class="card-body">
                    <canvas id="equityChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Exit Reason Analysis -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">Exit Reasons</h4>
                </div>
                <div class="card-body">
                    <canvas id="exitReasonChart"></canvas>
                    <div class="mt-3">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Exit Reason</th>
                                    <th>Count</th>
                                    <th>Total P&L</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reason, stats in exit_reasons.items %}
                                <tr>
                                    <td>{{ reason }}</td>
                                    <td>{{ stats.count }}</td>
                                    <td class="{% if stats.total_pnl > 0 %}text-success{% else %}text-danger{% endif %}">
                                        ${{ stats.total_pnl|floatformat:0 }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- News vs No News Performance -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">News Impact</h4>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h3 class="text-success">{{ backtest.gaps_with_news_win_rate|floatformat:1 }}%</h3>
                            <p>With News Catalyst</p>
                        </div>
                        <div class="col-6">
                            <h3 class="text-danger">{{ backtest.gaps_without_news_win_rate|floatformat:1 }}%</h3>
                            <p>Without News</p>
                        </div>
                    </div>
                    <hr>
                    <p class="text-center mb-0">
                        <strong>News Advantage:</strong> 
                        <span class="badge bg-success">
                            +{{ backtest.gaps_with_news_win_rate|add:"-"|add:backtest.gaps_without_news_win_rate|floatformat:1 }}%
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- All Positions -->
    <div class="row mt-4">
        <div class="col-12">
            <h3>All Positions ({{ positions|length }})</h3>
            <div class="table-responsive">
                <table class="table table-striped table-sm">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Entry Date</th>
                            <th>Entry Price</th>
                            <th>Exit Date</th>
                            <th>Exit Price</th>
                            <th>Exit Reason</th>
                            <th>Days Held</th>
                            <th>P&L</th>
                            <th>P&L %</th>
                            <th>Max Gain</th>
                            <th>Max Loss</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for pos in positions %}
                        <tr>
                            <td>
                                <a href="{% url 'stock_analysis' pos.symbol %}">
                                    <strong>{{ pos.symbol }}</strong>
                                </a>
                            </td>
                            <td>{{ pos.entry_date|date:"Y-m-d" }}</td>
                            <td>${{ pos.entry_price|floatformat:2 }}</td>
                            <td>{{ pos.exit_date|date:"Y-m-d" }}</td>
                            <td>${{ pos.exit_price|floatformat:2 }}</td>
                            <td>
                                <span class="badge bg-secondary">{{ pos.exit_reason }}</span>
                            </td>
                            <td>{{ pos.held_days }}</td>
                            <td class="{% if pos.pnl > 0 %}text-success{% else %}text-danger{% endif %}">
                                ${{ pos.pnl|floatformat:0 }}
                            </td>
                            <td class="{% if pos.pnl_percentage > 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ pos.pnl_percentage|floatformat:1 }}%
                            </td>
                            <td class="text-success">{{ pos.max_gain_pct|floatformat:1 }}%</td>
                            <td class="text-danger">{{ pos.max_loss_pct|floatformat:1 }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Performance by Month -->
    <div class="row mt-4">
        <div class="col-12">
            <h3>Monthly Performance</h3>
            <div class="card">
                <div class="card-body">
                    <canvas id="monthlyChart" height="60"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Prepare data for charts
const dailyValues = [
    {% for dv in daily_values %}
    {
        date: '{{ dv.date|date:"Y-m-d" }}',
        value: {{ dv.portfolio_value }}
    },
    {% endfor %}
];

// Equity Curve Chart
const equityCtx = document.getElementById('equityChart').getContext('2d');
new Chart(equityCtx, {
    type: 'line',
    data: {
        labels: dailyValues.map(d => d.date),
        datasets: [{
            label: 'Portfolio Value',
            data: dailyValues.map(d => d.value),
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1,
            fill: false
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: false,
                title: {
                    display: true,
                    text: 'Portfolio Value ($)'
                }
            }
        }
    }
});

// Exit Reason Chart
const exitReasonCtx = document.getElementById('exitReasonChart').getContext('2d');
const exitReasonData = {
    {% for reason, stats in exit_reasons.items %}
    '{{ reason }}': {{ stats.count }},
    {% endfor %}
};

new Chart(exitReasonCtx, {
    type: 'doughnut',
    data: {
        labels: Object.keys(exitReasonData),
        datasets: [{
            data: Object.values(exitReasonData),
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 206, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)'
            ]
        }]
    }
});

// Monthly Performance Chart
const monthlyData = {};
{% for pos in positions %}
const exitMonth = '{{ pos.exit_date|date:"Y-m" }}';
if (!monthlyData[exitMonth]) {
    monthlyData[exitMonth] = { count: 0, pnl: 0 };
}
monthlyData[exitMonth].count += 1;
monthlyData[exitMonth].pnl += {{ pos.pnl|default:0 }};
{% endfor %}

const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
const months = Object.keys(monthlyData).sort();
new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: months,
        datasets: [{
            label: 'Monthly P&L',
            data: months.map(m => monthlyData[m].pnl),
            backgroundColor: months.map(m => monthlyData[m].pnl > 0 ? 'rgba(75, 192, 192, 0.8)' : 'rgba(255, 99, 132, 0.8)')
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'P&L ($)'
                }
            }
        }
    }
});
</script>
{% endblock %}