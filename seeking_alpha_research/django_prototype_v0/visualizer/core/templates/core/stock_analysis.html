{% extends 'core/base.html' %}

{% block title %}{{ symbol }} Analysis - Gap-Up ATM Strategy{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <h1>{{ symbol }} - Stock Analysis</h1>
    
    {% if latest_analysis %}
    <div class="row mt-4">
        <!-- Latest Analysis Summary -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Latest Analysis - {{ latest_analysis.analysis_date|date:"Y-m-d" }}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Gap Event Details</h5>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Gap Percentage
                                    <span class="badge bg-success rounded-pill">+{{ latest_analysis.gap_percentage|floatformat:1 }}%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Gap Date
                                    <span>{{ latest_analysis.gap_date|date:"Y-m-d" }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    News Catalyst
                                    {% if latest_analysis.had_news %}
                                        <span class="badge bg-success">Yes</span>
                                    {% else %}
                                        <span class="badge bg-danger">No</span>
                                    {% endif %}
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Unusual Volume Days
                                    <span class="badge bg-info rounded-pill">{{ latest_analysis.unusual_volume_days }}</span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>ATM Filing Risk Analysis</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Filing Risk Score</span>
                                        <span class="text-danger">{{ latest_analysis.filing_risk_score|floatformat:2 }}</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-danger" role="progressbar" 
                                             style="width: {{ latest_analysis.filing_risk_score|floatformat:0 }}%">
                                        </div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Cash Runway
                                    <span class="badge bg-warning text-dark rounded-pill">
                                        {{ latest_analysis.months_until_cash_out|floatformat:1 }} months
                                    </span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Active ATM
                                    {% if latest_analysis.has_active_atm %}
                                        <span class="badge bg-danger">Yes - ${{ latest_analysis.atm_amount|floatformat:0 }}</span>
                                    {% else %}
                                        <span class="badge bg-success">No</span>
                                    {% endif %}
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Monthly Burn Rate
                                    <span class="text-danger">${{ latest_analysis.cash_burn_rate|floatformat:0 }}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Dilution Status -->
                    <div class="mt-4">
                        <h5>Dilution Confirmation</h5>
                        {% if latest_analysis.dilution_confirmed %}
                        <div class="alert alert-success">
                            <strong>✓ DILUTION CONFIRMED</strong> - {{ latest_analysis.dilution_form_type }} filed on {{ latest_analysis.dilution_date|date:"Y-m-d" }}
                        </div>
                        {% else %}
                        <div class="alert alert-warning">
                            <strong>⏳ DILUTION PENDING</strong> - No dilution filing detected yet (monitoring for 424B5, EFFECT, 8-K)
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- LLM Analysis -->
                    {% if latest_analysis.llm_analysis %}
                    <div class="mt-4">
                        <h5>AI Analysis Summary</h5>
                        <div class="card bg-light">
                            <div class="card-body">
                                <p>{{ latest_analysis.llm_analysis|linebreaks }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Trading Statistics -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">Trading Statistics</h4>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <h2 class="{% if avg_return > 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ avg_return|floatformat:1 }}%
                        </h2>
                        <p class="text-muted">Average Return</p>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h4>{{ stock_win_rate|floatformat:0 }}%</h4>
                            <p class="text-muted">Win Rate</p>
                        </div>
                        <div class="col-6">
                            <h4>{{ trades.count }}</h4>
                            <p class="text-muted">Total Trades</p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h5>Recent Trades</h5>
                    <div class="list-group">
                        {% for trade in trades|slice:":5" %}
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <small>{{ trade.entry_date|date:"Y-m-d" }}</small>
                                <span class="badge {% if trade.pnl_percentage > 0 %}bg-success{% else %}bg-danger{% endif %}">
                                    {{ trade.pnl_percentage|floatformat:1 }}%
                                </span>
                            </div>
                            <small class="text-muted">
                                ${{ trade.entry_price|floatformat:2 }} → ${{ trade.exit_price|floatformat:2 }}
                                ({{ trade.held_days }} days)
                            </small>
                        </a>
                        {% empty %}
                        <p class="text-muted">No trades yet</p>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">Quick Actions</h4>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary w-100 mb-2" onclick="refreshAnalysis()">
                        Refresh Analysis
                    </button>
                    <button class="btn btn-warning w-100 mb-2" onclick="checkDilution()">
                        Check for Dilution
                    </button>
                    <a href="https://www.sec.gov/cgi-bin/browse-edgar?CIK={{ symbol }}" 
                       target="_blank" class="btn btn-secondary w-100">
                        View SEC Filings
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Historical Analyses -->
    <div class="row mt-5">
        <div class="col-12">
            <h3>Historical Gap Events</h3>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Gap %</th>
                            <th>News</th>
                            <th>Volume Signal</th>
                            <th>Risk Score</th>
                            <th>Cash Runway</th>
                            <th>Dilution</th>
                            <th>Trade Result</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for analysis in analyses %}
                        <tr>
                            <td>{{ analysis.analysis_date|date:"Y-m-d" }}</td>
                            <td class="text-success">+{{ analysis.gap_percentage|floatformat:1 }}%</td>
                            <td>
                                {% if analysis.had_news %}
                                    <span class="badge bg-success">Yes</span>
                                {% else %}
                                    <span class="badge bg-danger">No</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if analysis.unusual_volume_days > 0 %}
                                    <span class="badge bg-warning text-dark">{{ analysis.unusual_volume_days }}d</span>
                                {% else %}
                                    <span class="badge bg-secondary">None</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="progress" style="width: 60px; height: 20px;">
                                    <div class="progress-bar bg-danger" role="progressbar" 
                                         style="width: {{ analysis.filing_risk_score|floatformat:0 }}%">
                                        <small>{{ analysis.filing_risk_score|floatformat:2 }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ analysis.months_until_cash_out|floatformat:1 }}m</td>
                            <td>
                                {% if analysis.dilution_confirmed %}
                                    <span class="badge bg-success">✓ {{ analysis.dilution_form_type }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% with analysis.positions.first as trade %}
                                    {% if trade %}
                                        <span class="badge {% if trade.pnl_percentage > 0 %}bg-success{% else %}bg-danger{% endif %}">
                                            {{ trade.pnl_percentage|floatformat:1 }}%
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">No trade</span>
                                    {% endif %}
                                {% endwith %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Price Chart Placeholder -->
    <div class="row mt-5">
        <div class="col-12">
            <h3>Price Action</h3>
            <div class="card">
                <div class="card-body">
                    <div id="priceChart" style="height: 400px;">
                        <!-- Plotly chart would go here -->
                        <p class="text-muted text-center mt-5">Price chart visualization coming soon...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {% else %}
    <div class="alert alert-info mt-4">
        <h4>No Analysis Available</h4>
        <p>No analysis has been performed for {{ symbol }} yet.</p>
        <button class="btn btn-primary" onclick="runAnalysis()">Run Analysis Now</button>
    </div>
    {% endif %}
</div>

<script>
async function refreshAnalysis() {
    const btn = event.target;
    btn.disabled = true;
    btn.textContent = 'Analyzing...';
    
    try {
        const response = await fetch('{% url "analyze_stock" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                symbol: '{{ symbol }}',
                date: new Date().toISOString().split('T')[0]
            })
        });
        
        const result = await response.json();
        if (result.status === 'success') {
            location.reload();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('Error: ' + error.message);
    } finally {
        btn.disabled = false;
        btn.textContent = 'Refresh Analysis';
    }
}

async function checkDilution() {
    alert('Checking for recent dilution filings...');
    // This would trigger a dilution check
    await refreshAnalysis();
}

async function runAnalysis() {
    await refreshAnalysis();
}
</script>
{% endblock %}