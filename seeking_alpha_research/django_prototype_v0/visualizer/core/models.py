from django.db import models
import json


class BacktestRun(models.Model):
    """Stores a complete backtest run with all parameters and results."""

    # Run metadata
    run_date = models.DateTimeField(auto_now_add=True)
    start_date = models.DateField()
    end_date = models.DateField()

    # Parameters
    initial_capital = models.DecimalField(max_digits=12, decimal_places=2)
    position_size_pct = models.DecimalField(max_digits=5, decimal_places=2)
    max_positions = models.IntegerField()
    gap_threshold = models.DecimalField(max_digits=5, decimal_places=2)
    stop_loss_pct = models.DecimalField(max_digits=5, decimal_places=2)
    take_profit_pct = models.DecimalField(max_digits=5, decimal_places=2)

    # Overall results
    total_return = models.DecimalField(max_digits=10, decimal_places=2)
    sharpe_ratio = models.DecimalField(max_digits=6, decimal_places=3)
    max_drawdown = models.DecimalField(max_digits=10, decimal_places=2)
    win_rate = models.DecimalField(max_digits=5, decimal_places=2)
    total_trades = models.IntegerField()

    # Strategy-specific metrics
    gaps_with_news_win_rate = models.DecimalField(max_digits=5, decimal_places=2)
    gaps_without_news_win_rate = models.DecimalField(max_digits=5, decimal_places=2)
    dilution_confirmation_rate = models.DecimalField(max_digits=5, decimal_places=2)

    # Status
    STATUS_CHOICES = [
        ("running", "Running"),
        ("completed", "Completed"),
        ("failed", "Failed"),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="running")
    error_message = models.TextField(blank=True)

    class Meta:
        ordering = ["-run_date"]

    def __str__(self):
        return f"Backtest {self.run_date.strftime('%Y-%m-%d %H:%M')} - {self.status}"


class StockAnalysis(models.Model):
    """Analysis for individual stocks showing gap events and predictions."""

    symbol = models.CharField(max_length=10, db_index=True)
    analysis_date = models.DateField()

    # Gap event data
    gap_date = models.DateField(null=True, blank=True)
    gap_percentage = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    had_news = models.BooleanField(default=False)
    news_headline = models.TextField(blank=True)

    # Filing analysis
    cash_burn_rate = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    months_until_cash_out = models.DecimalField(
        max_digits=5, decimal_places=1, null=True
    )
    has_active_atm = models.BooleanField(default=False)
    atm_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    filing_risk_score = models.DecimalField(max_digits=3, decimal_places=2, null=True)

    # LLM analysis
    llm_analysis = models.TextField(blank=True)
    predicted_gap_date_start = models.DateField(null=True, blank=True)
    predicted_gap_date_end = models.DateField(null=True, blank=True)

    # Volume analysis
    unusual_volume_days = models.IntegerField(default=0)
    volume_analysis = models.JSONField(default=dict)

    # Dilution confirmation
    dilution_confirmed = models.BooleanField(default=False)
    dilution_date = models.DateField(null=True, blank=True)
    dilution_form_type = models.CharField(max_length=20, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ["symbol", "analysis_date"]
        ordering = ["-analysis_date", "symbol"]

    def __str__(self):
        return f"{self.symbol} - {self.analysis_date}"


class TradePosition(models.Model):
    """Individual trade positions for detailed analysis."""

    backtest_run = models.ForeignKey(
        BacktestRun, on_delete=models.CASCADE, related_name="positions"
    )
    stock_analysis = models.ForeignKey(
        StockAnalysis, on_delete=models.SET_NULL, null=True
    )

    symbol = models.CharField(max_length=10)
    entry_date = models.DateField()
    entry_price = models.DecimalField(max_digits=10, decimal_places=4)
    shares = models.IntegerField()

    exit_date = models.DateField(null=True, blank=True)
    exit_price = models.DecimalField(max_digits=10, decimal_places=4, null=True)
    exit_reason = models.CharField(max_length=50, blank=True)

    pnl = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    pnl_percentage = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    held_days = models.IntegerField(null=True)

    max_gain_pct = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    max_loss_pct = models.DecimalField(max_digits=10, decimal_places=2, null=True)

    class Meta:
        ordering = ["-entry_date"]

    def __str__(self):
        return f"{self.symbol} - {self.entry_date} ({self.pnl_percentage}%)"


class DailyPortfolioValue(models.Model):
    """Daily portfolio values for equity curve visualization."""

    backtest_run = models.ForeignKey(
        BacktestRun, on_delete=models.CASCADE, related_name="daily_values"
    )
    date = models.DateField()
    portfolio_value = models.DecimalField(max_digits=12, decimal_places=2)
    cash = models.DecimalField(max_digits=12, decimal_places=2)
    positions_count = models.IntegerField()

    class Meta:
        unique_together = ["backtest_run", "date"]
        ordering = ["date"]
