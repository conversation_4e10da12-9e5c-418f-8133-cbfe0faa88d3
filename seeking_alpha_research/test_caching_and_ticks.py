#!/usr/bin/env python3
"""
Test caching system and tick data functionality.
"""

import sys
import os
from datetime import datetime, timedelta
import time

# Add project to path
sys.path.insert(0, os.path.dirname(__file__))

from core.data_service import DataService
from core.ib_data_service import get_ib_data_service
from core.logger import get_logger

logger = get_logger(__name__)


def test_caching_system():
    """Test if the caching system is working properly."""
    print("💾 Testing Caching System...")
    print("=" * 50)
    
    ds = DataService()
    if not ds.ib_connector:
        print("❌ No IB connection available")
        return False
    
    symbol = "AAPL"
    
    # Test 1: Daily bars caching
    print(f"\n📊 Testing Daily Bars Caching for {symbol}:")
    
    # First request - should fetch from IB and cache
    print("   First request (should fetch from IB)...")
    start_time = time.time()
    daily_bars1 = ds.get_daily_bars(symbol, "2025-07-10", "2025-07-10")
    first_duration = time.time() - start_time
    
    print(f"   Records: {len(daily_bars1)}")
    print(f"   Duration: {first_duration:.2f}s")
    
    # Second request - should use cache
    print("\n   Second request (should use cache)...")
    start_time = time.time()
    daily_bars2 = ds.get_daily_bars(symbol, "2025-07-10", "2025-07-10")
    second_duration = time.time() - start_time
    
    print(f"   Records: {len(daily_bars2)}")
    print(f"   Duration: {second_duration:.2f}s")
    
    # Check if caching worked
    if second_duration < first_duration * 0.5:  # Should be much faster
        print("   ✅ Caching appears to be working (second request much faster)")
    else:
        print("   ⚠️ Caching might not be working (similar durations)")
    
    # Check data consistency
    if daily_bars1.equals(daily_bars2):
        print("   ✅ Data consistency maintained")
    else:
        print("   ❌ Data inconsistency between requests")
    
    # Test 2: Minute bars caching
    print(f"\n📈 Testing Minute Bars Caching for {symbol}:")
    
    # First request
    print("   First request (should fetch from IB)...")
    start_time = time.time()
    minute_bars1 = ds.get_minute_bars(symbol, "2025-07-10", "2025-07-10")
    first_duration = time.time() - start_time
    
    print(f"   Records: {len(minute_bars1)}")
    print(f"   Duration: {first_duration:.2f}s")
    
    # Second request
    print("\n   Second request (should use cache)...")
    start_time = time.time()
    minute_bars2 = ds.get_minute_bars(symbol, "2025-07-10", "2025-07-10")
    second_duration = time.time() - start_time
    
    print(f"   Records: {len(minute_bars2)}")
    print(f"   Duration: {second_duration:.2f}s")
    
    # Check caching
    if second_duration < first_duration * 0.5:
        print("   ✅ Minute bars caching working")
    else:
        print("   ⚠️ Minute bars caching might not be working")
    
    # Test 3: Check database directly
    print(f"\n🗄️ Checking Database Storage:")
    try:
        # Check if data is actually in the database
        from core.database import stock_bars_daily, stock_bars_minute
        from sqlalchemy import select
        
        # Check daily bars in DB
        daily_query = select(stock_bars_daily).where(
            stock_bars_daily.c.symbol == symbol
        ).limit(5)
        daily_result = ds.db_conn.execute(daily_query).fetchall()
        print(f"   Daily bars in DB: {len(daily_result)} records")
        
        # Check minute bars in DB
        minute_query = select(stock_bars_minute).where(
            stock_bars_minute.c.symbol == symbol
        ).limit(5)
        minute_result = ds.db_conn.execute(minute_query).fetchall()
        print(f"   Minute bars in DB: {len(minute_result)} records")
        
        if len(daily_result) > 0 and len(minute_result) > 0:
            print("   ✅ Data is being saved to database")
        else:
            print("   ⚠️ Data might not be saved to database")
            
    except Exception as e:
        print(f"   ❌ Error checking database: {e}")
    
    return True


def test_tick_data():
    """Test tick data functionality during market hours."""
    print("\n🎯 Testing Tick Data...")
    print("=" * 50)
    
    ds = DataService()
    if not ds.ib_connector:
        print("❌ No IB connection available")
        return False
    
    symbol = "AAPL"
    
    # Test 1: Real-time tick data (short duration)
    print(f"\n⚡ Testing Real-time Ticks for {symbol}:")
    try:
        print("   Collecting real-time ticks for 5 seconds...")
        start_time = time.time()
        
        realtime_ticks = ds.get_tick_data(
            symbol, 
            start_time=None,  # Real-time
            duration_seconds=5
        )
        
        duration = time.time() - start_time
        
        if not realtime_ticks.empty:
            print(f"   ✅ Got {len(realtime_ticks)} real-time ticks in {duration:.1f}s")
            print(f"   Columns: {list(realtime_ticks.columns)}")
            print("\n   Sample ticks:")
            print(realtime_ticks.head(3))
            
            # Check tick data quality
            if 'price' in realtime_ticks.columns:
                price_range = realtime_ticks['price'].max() - realtime_ticks['price'].min()
                print(f"   Price range: ${price_range:.2f}")
                
            if 'size' in realtime_ticks.columns:
                total_volume = realtime_ticks['size'].sum()
                print(f"   Total volume: {total_volume:,}")
                
        else:
            print("   ⚠️ No real-time ticks received (might be outside market hours)")
            
    except Exception as e:
        print(f"   ❌ Real-time tick test failed: {e}")
    
    # Test 2: Historical tick data (recent)
    print(f"\n📜 Testing Historical Ticks for {symbol}:")
    try:
        # Try to get ticks from a few hours ago
        now = datetime.now()
        
        # Test different time ranges to find market hours
        test_times = [
            (now - timedelta(hours=1), "1 hour ago"),
            (now - timedelta(hours=6), "6 hours ago"),
            (now - timedelta(days=1, hours=2), "yesterday 2 PM"),
        ]
        
        for test_time, description in test_times:
            print(f"\n   Testing {description}...")
            start_time_str = test_time.strftime("%Y%m%d %H:%M:%S")
            
            try:
                hist_ticks = ds.get_tick_data(symbol, start_time=start_time_str)
                
                if not hist_ticks.empty:
                    print(f"   ✅ Got {len(hist_ticks)} historical ticks from {description}")
                    print(f"   Time range: {hist_ticks.index[0]} to {hist_ticks.index[-1]}")
                    
                    # Show sample
                    print("   Sample historical ticks:")
                    print(hist_ticks.head(2))
                    break
                else:
                    print(f"   ⚠️ No ticks from {description}")
                    
            except Exception as e:
                if "2106" in str(e):
                    print(f"   ⚠️ Error 2106 from {description} (outside market hours)")
                else:
                    print(f"   ❌ Error from {description}: {e}")
        
    except Exception as e:
        print(f"   ❌ Historical tick test failed: {e}")
    
    # Test 3: AllLast ticks (if available)
    print(f"\n🔍 Testing AllLast Ticks for {symbol}:")
    try:
        # Try recent AllLast ticks
        recent_time = (datetime.now() - timedelta(hours=2)).strftime("%Y%m%d %H:%M:%S")
        
        alllast_ticks = ds.get_alllast_ticks(symbol, recent_time)
        
        if not alllast_ticks.empty:
            print(f"   ✅ Got {len(alllast_ticks)} AllLast ticks")
            print("   Sample AllLast ticks:")
            print(alllast_ticks.head(2))
        else:
            print("   ⚠️ No AllLast ticks (might be outside market hours)")
            
    except Exception as e:
        if "2106" in str(e):
            print("   ⚠️ AllLast ticks: Error 2106 (outside market hours)")
        else:
            print(f"   ❌ AllLast tick test failed: {e}")
    
    return True


def main():
    """Run all caching and tick tests."""
    print("🧪 Testing Caching System and Tick Data")
    print("=" * 60)
    
    # Test caching
    caching_ok = test_caching_system()
    
    # Test tick data
    tick_ok = test_tick_data()
    
    # Summary
    print("\n📋 Test Summary")
    print("=" * 30)
    print(f"Caching System: {'✅ WORKING' if caching_ok else '❌ ISSUES'}")
    print(f"Tick Data: {'✅ WORKING' if tick_ok else '❌ ISSUES'}")
    
    print("\n💡 Notes:")
    print("- Tick data errors outside market hours are normal")
    print("- Caching should show faster second requests")
    print("- Database should contain saved bars")


if __name__ == "__main__":
    main()
