#!/usr/bin/env python3
"""
IB-ONLY DATA SERVICE
Per user request: "for bar and tick only use ib .. dont use alpaca for that kind of data"

This replaces Alpaca bar/tick data with IB exclusively.
Alpaca only used for news and corporate actions.
"""

import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, List
import logging

from .ib_connector import IBConnector
from .data_service import DataService
from .logger import get_logger

logger = get_logger(__name__)


class IBDataService(DataService):
    """
    Enhanced data service that uses IB exclusively for price data.
    Inherits from DataService but overrides bar/tick methods.
    """

    def __init__(self, ib_port: int = 4001):
        super().__init__()
        self.ib_port = ib_port
        self._ib_connector = None

    @property
    def ib_connector(self) -> IBConnector:
        """Get shared IB connector to prevent connection conflicts."""
        if self._ib_connector is None:
            # Use shared connection manager instead of creating new connection
            from .ib_connection_manager import get_ib_connection
            self._ib_connector = get_ib_connection()
            if not self._ib_connector or not self._ib_connector.is_connected():
                raise ValueError(
                    "CRITICAL: Cannot connect to IB Gateway. Bar/tick data unavailable!"
                )
        return self._ib_connector

    def get_daily_bars(self, symbol: str, start: str, end: str) -> pd.DataFrame:
        """
        Override to use IB exclusively for daily bars.

        Per specs: IB has better data quality than Alpaca.
        """
        logger.info(f"Getting daily bars for {symbol} from IB ONLY")

        try:
            # First check cache
            cached_bars = self._get_cached_bars(symbol, start, end, "daily")
            if not cached_bars.empty:
                logger.info(f"Using cached IB data for {symbol}")
                return cached_bars

            # Calculate days for IB API
            start_dt = datetime.strptime(start, "%Y-%m-%d")
            end_dt = datetime.strptime(end, "%Y-%m-%d")
            days = (end_dt - start_dt).days + 1

            # Get from IB (using days parameter)
            bars = self.ib_connector.get_daily_bars(symbol, days=days)

            if bars.empty:
                # FAIL LOUDLY - no fallback to Alpaca
                raise ValueError(
                    f"CRITICAL: No IB data for {symbol} from {start} to {end}"
                )

            # Filter to requested date range
            bars = bars.loc[start:end]

            # Cache the data
            self._cache_bars(bars, symbol, "daily")

            return bars

        except Exception as e:
            logger.error(f"Failed to get IB daily bars for {symbol}: {e}")
            # NO FALLBACK - fail loudly as required
            raise ValueError(f"CRITICAL: Cannot get bar data from IB: {e}")

    def get_minute_bars(self, symbol: str, start: str, end: str) -> pd.DataFrame:
        """
        Override to use IB exclusively for minute bars.

        Alpaca free tier doesn't provide minute data anyway.
        """
        logger.info(f"Getting minute bars for {symbol} from IB ONLY")

        try:
            # Check cache first
            cached_bars = self._get_cached_bars(symbol, start, end, "minute")
            if not cached_bars.empty:
                logger.info(f"Using cached IB minute data for {symbol}")
                return cached_bars

            # Calculate days for IB API
            start_dt = datetime.strptime(start, "%Y-%m-%d")
            end_dt = datetime.strptime(end, "%Y-%m-%d")
            days = (end_dt - start_dt).days + 1

            # IB's get_minute_bars expects days parameter
            bars = self.ib_connector.get_minute_bars(symbol, days=days)

            if bars.empty:
                raise ValueError(f"CRITICAL: No IB minute data for {symbol}")

            # Filter to requested date range
            bars = bars.loc[start:end]

            # Cache the data
            self._cache_bars(bars, symbol, "minute")

            return bars

        except Exception as e:
            logger.error(f"Failed to get IB minute bars for {symbol}: {e}")
            raise ValueError(f"CRITICAL: Cannot get minute data from IB: {e}")

    def get_tick_data(
        self,
        symbol: str,
        date: str,
        start_time: str = "09:30:00",
        end_time: str = "16:00:00",
    ) -> pd.DataFrame:
        """
        Get tick data from IB ONLY.

        This is critical for insider detection patterns.
        """
        logger.info(f"Getting tick data for {symbol} on {date} from IB")

        try:
            # Format datetime for IB
            start_datetime = f"{date.replace('-', '')} {start_time}"

            # Get historical ticks (IB method only takes start_time, not end_time)
            ticks = self.ib_connector.get_historical_ticks(
                symbol=symbol, start_time=start_datetime
            )

            if ticks.empty:
                raise ValueError(
                    f"CRITICAL: No tick data from IB for {symbol} on {date}"
                )

            return ticks

        except Exception as e:
            logger.error(f"Failed to get IB tick data: {e}")
            raise ValueError(f"CRITICAL: Cannot get tick data from IB: {e}")

    def get_realtime_ticks(
        self, symbol: str, duration_seconds: int = 60
    ) -> pd.DataFrame:
        """
        Get real-time tick data from IB.

        For live trading and immediate insider detection.
        """
        logger.info(f"Getting real-time ticks for {symbol}")

        try:
            ticks = self.ib_connector.get_realtime_ticks(symbol, duration_seconds)

            if ticks.empty:
                raise ValueError(f"CRITICAL: No real-time ticks from IB for {symbol}")

            return ticks

        except Exception as e:
            logger.error(f"Failed to get real-time ticks: {e}")
            raise ValueError(f"CRITICAL: Cannot get real-time ticks: {e}")

    def _cache_bars(self, bars: pd.DataFrame, symbol: str, timeframe: str):
        """Cache IB data to avoid hitting API limits."""
        try:
            # TODO: Implement proper caching to database
            # For now, just log
            logger.info(f"TODO: Cache {len(bars)} {timeframe} bars for {symbol}")
        except Exception as e:
            logger.warning(f"Failed to cache bars: {e}")

    def _get_cached_bars(
        self, symbol: str, start: str, end: str, timeframe: str
    ) -> pd.DataFrame:
        """Get cached bars if available."""
        # TODO: Implement cache retrieval
        # For now, return empty to force IB fetch
        return pd.DataFrame()

    def close(self):
        """Close connections."""
        if self._ib_connector:
            self._ib_connector.disconnect()
        super().close()


# Singleton instance for easy access
_ib_data_service = None


def get_ib_data_service(port: int = 4001) -> IBDataService:
    """Get singleton IB data service."""
    global _ib_data_service
    if _ib_data_service is None:
        _ib_data_service = IBDataService(port)
    return _ib_data_service


if __name__ == "__main__":
    # Test IB-only data service
    service = get_ib_data_service()

    try:
        # Test daily bars
        print("Testing IB daily bars...")
        bars = service.get_daily_bars("AAPL", "2024-01-01", "2024-01-31")
        print(f"Got {len(bars)} daily bars from IB")

        # Test minute bars
        print("\nTesting IB minute bars...")
        minute_bars = service.get_minute_bars("AAPL", "2024-01-15", "2024-01-15")
        print(f"Got {len(minute_bars)} minute bars from IB")

        # Test tick data
        print("\nTesting IB tick data...")
        ticks = service.get_tick_data("AAPL", "2024-01-15")
        print(f"Got {len(ticks)} ticks from IB")

    except Exception as e:
        print(f"Error: {e}")
    finally:
        service.close()
