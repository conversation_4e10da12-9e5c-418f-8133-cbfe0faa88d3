"""
Enhanced market data schema for ib_async integration with improved caching and performance.
Supports both tick and bar data with proper indexing and deduplication.
"""

import sqlalchemy
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Index, UniqueConstraint, JSON, BigInteger
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()


class MarketDataBarMinute(Base):
    """Enhanced minute bar data with ib_async compatibility and better caching"""
    __tablename__ = 'market_data_bar_minute'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # OHLCV data
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(BigInteger, nullable=False)
    
    # Additional IB-specific fields
    wap = Column(Float)  # Weighted Average Price
    bar_count = Column(Integer)  # Number of trades in bar
    
    # Source tracking
    source = Column(String(20), default='ib_async')  # 'ib_async', 'alpaca', etc.
    connection_id = Column(Integer)  # Track which client connection retrieved this
    
    # Cache management
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Constraints and indexes
    __table_args__ = (
        UniqueConstraint('symbol', 'timestamp', name='uq_minute_bar_symbol_timestamp'),
        Index('idx_minute_bar_symbol_date', 'symbol', sqlalchemy.cast(timestamp, sqlalchemy.Date)),
        Index('idx_minute_bar_created', 'created_at'),
    )


class MarketDataBarDaily(Base):
    """Enhanced daily bar data with ib_async compatibility"""
    __tablename__ = 'market_data_bar_daily'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # OHLCV data
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(BigInteger, nullable=False)
    
    # Additional IB-specific fields
    wap = Column(Float)
    bar_count = Column(Integer)
    
    # Source tracking
    source = Column(String(20), default='ib_async')
    connection_id = Column(Integer)
    
    # Cache management
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        UniqueConstraint('symbol', 'timestamp', name='uq_daily_bar_symbol_timestamp'),
        Index('idx_daily_bar_created', 'created_at'),
    )


class MarketDataTick(Base):
    """Enhanced tick data storage with better performance and deduplication"""
    __tablename__ = 'market_data_tick'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # Price and size
    price = Column(Float, nullable=False)
    size = Column(BigInteger, nullable=False)
    
    # Tick attributes
    tick_type = Column(String(20))  # 'TRADE', 'BID', 'ASK', 'MIDPOINT', 'ALLLAST'
    exchange = Column(String(20))
    conditions = Column(String(100))
    
    # Bid/Ask data (if applicable)
    bid_price = Column(Float)
    ask_price = Column(Float)
    bid_size = Column(BigInteger)
    ask_size = Column(BigInteger)
    
    # Source tracking
    source = Column(String(20), default='ib_async')
    connection_id = Column(Integer)
    
    # Performance optimization
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    __table_args__ = (
        # Prevent exact duplicates
        UniqueConstraint('symbol', 'timestamp', 'price', 'size', 'exchange', 
                        name='uq_tick_unique'),
        # Efficient querying
        Index('idx_tick_symbol_timestamp', 'symbol', 'timestamp'),
        Index('idx_tick_symbol_date', 'symbol', sqlalchemy.cast(timestamp, sqlalchemy.Date)),
        Index('idx_tick_type', 'tick_type'),
        Index('idx_tick_created', 'created_at'),
    )


class MarketDataCache(Base):
    """Cache tracking for efficient data retrieval"""
    __tablename__ = 'market_data_cache'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    data_type = Column(String(20), nullable=False)  # 'minute_bar', 'daily_bar', 'tick'
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    row_count = Column(Integer, nullable=False)
    source = Column(String(20), nullable=False)
    connection_id = Column(Integer)
    
    # Cache metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_accessed = Column(DateTime, default=datetime.utcnow, nullable=False)
    access_count = Column(Integer, default=1)
    
    # Data quality metrics
    completeness_score = Column(Float)  # 0.0 to 1.0
    has_gaps = Column(Boolean, default=False)
    gap_details = Column(JSON)  # Details about any gaps in data
    
    __table_args__ = (
        UniqueConstraint('symbol', 'data_type', 'start_date', 'end_date', 'source',
                        name='uq_cache_entry'),
        Index('idx_cache_symbol_type', 'symbol', 'data_type'),
        Index('idx_cache_dates', 'start_date', 'end_date'),
        Index('idx_cache_accessed', 'last_accessed'),
    )


class MarketDataConnection(Base):
    """Track IB connections for parallel data fetching"""
    __tablename__ = 'market_data_connection'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    client_id = Column(Integer, unique=True, nullable=False)
    host = Column(String(50), default='127.0.0.1')
    port = Column(Integer, default=4001)
    
    # Connection status
    is_active = Column(Boolean, default=True)
    last_heartbeat = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    disconnected_at = Column(DateTime)
    
    # Usage tracking
    requests_count = Column(Integer, default=0)
    errors_count = Column(Integer, default=0)
    last_error = Column(String(500))
    
    # Connection metadata
    purpose = Column(String(50))  # 'tick_data', 'bar_data', 'mixed'
    assigned_symbols = Column(JSON)  # List of symbols this connection handles
    
    __table_args__ = (
        Index('idx_connection_active', 'is_active'),
        Index('idx_connection_heartbeat', 'last_heartbeat'),
    )


# Migration helpers for existing data
def create_migration_script():
    """Generate SQL to migrate from old schema to new schema"""
    return """
    -- Migrate minute bars
    INSERT INTO market_data_bar_minute (symbol, timestamp, open, high, low, close, volume, 
                                       wap, bar_count, source, created_at)
    SELECT symbol, timestamp, open, high, low, close, volume, 
           vwap as wap, trade_count as bar_count, 'legacy' as source, NOW() as created_at
    FROM stock_bars_minute
    ON CONFLICT (symbol, timestamp) DO NOTHING;
    
    -- Migrate daily bars
    INSERT INTO market_data_bar_daily (symbol, timestamp, open, high, low, close, volume,
                                      wap, bar_count, source, created_at)
    SELECT symbol, timestamp, open, high, low, close, volume,
           vwap as wap, trade_count as bar_count, 'legacy' as source, NOW() as created_at
    FROM stock_bars_daily
    ON CONFLICT (symbol, timestamp) DO NOTHING;
    
    -- Migrate tick data
    INSERT INTO market_data_tick (symbol, timestamp, price, size, exchange, conditions,
                                 tick_type, source, created_at)
    SELECT symbol, timestamp, price, size, exchange, conditions,
           'TRADE' as tick_type, 'legacy' as source, COALESCE(created_at, NOW())
    FROM stock_ticks
    ON CONFLICT (symbol, timestamp, price, size, exchange) DO NOTHING;
    
    -- Migrate AllLast ticks
    INSERT INTO market_data_tick (symbol, timestamp, price, size, exchange, conditions,
                                 tick_type, source, created_at)
    SELECT symbol, timestamp, price, size, exchange, conditions,
           tick_type, 'legacy' as source, COALESCE(created_at, NOW())
    FROM stock_ticks_alllast
    ON CONFLICT (symbol, timestamp, price, size, exchange) DO NOTHING;
    """