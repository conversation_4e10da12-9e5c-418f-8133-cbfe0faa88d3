"""
IB Connection Manager - Singleton pattern for shared IB Gateway connection

Ensures only one IB connection is created and shared across all DataService instances.
This prevents connection conflicts when running parallel operations.

NO FAKES, NO MOCKS - Real IB Gateway connection management.
"""

import threading
import os
from typing import Optional
from .ib_connector import IBConnector
from .logger import get_logger

logger = get_logger(__name__)


class IBConnectionManager:
    """
    Singleton manager for IB Gateway connections.
    
    Ensures only one connection exists across the entire application,
    preventing conflicts during parallel operations.
    """
    
    _instance = None
    _lock = threading.Lock()
    _ib_connector: Optional[IBConnector] = None
    _connection_attempted = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def get_connection(self) -> Optional[IBConnector]:
        """
        Get the shared IB connection.
        
        Returns:
            IBConnector instance if connected, None if connection failed
        """
        with self._lock:
            # Only attempt connection once
            if not self._connection_attempted:
                self._connection_attempted = True
                try:
                    logger.info("Creating shared IB Gateway connection...")
                    self._ib_connector = IBConnector(
                        host=os.getenv("IB_HOST", "127.0.0.1"),
                        port=int(os.getenv("IB_PORT", 4001)),
                        client_id=int(os.getenv("IB_CLIENT_ID", 1))  # Single client ID
                    )
                    logger.info("Successfully created shared IB Gateway connection")
                except Exception as e:
                    logger.error(f"Failed to create IB connection: {e}")
                    self._ib_connector = None
            
            return self._ib_connector
    
    def close_connection(self):
        """Close the shared IB connection."""
        with self._lock:
            if self._ib_connector:
                try:
                    self._ib_connector.disconnect()
                    logger.info("Closed shared IB Gateway connection")
                except Exception as e:
                    logger.error(f"Error closing IB connection: {e}")
                finally:
                    self._ib_connector = None
                    self._connection_attempted = False
    
    def is_connected(self) -> bool:
        """Check if IB Gateway is connected."""
        with self._lock:
            return self._ib_connector is not None and self._ib_connector.is_connected()
    
    @classmethod
    def reset(cls):
        """Reset the singleton instance (useful for testing)."""
        with cls._lock:
            if cls._instance and cls._instance._ib_connector:
                cls._instance.close_connection()
            cls._instance = None


# Global instance getter
def get_ib_connection() -> Optional[IBConnector]:
    """
    Get the shared IB Gateway connection.
    
    This is the primary interface for getting IB connections throughout the application.
    """
    manager = IBConnectionManager()
    return manager.get_connection()