"""
Individual Stock Daily Feedback Loop System

This is the core feedback loop that processes one stock for one day:
1. Collect comprehensive tick data from IB
2. Analyze insider accumulation patterns 1-2 weeks prior
3. Determine entry signals based on sophisticated price action
4. Monitor exit conditions with multiple triggers
5. Generate complete JSON output and database storage
6. Provide maximum data retention for analysis

This system creates the foundation for testing and validation of the strategy.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import json
import sqlite3
import logging
from dataclasses import dataclass, asdict
import asyncio
from concurrent.futures import ThreadPoolExecutor

from analysis.tick_analyzer import TickA<PERSON>yzer, InsiderSignal
from strategy.exit_manager import ExitManager, Position, Trade, ExitSignal
from core.ib_connector import IBConnector
from core.data_service import DataService


@dataclass
class FeedbackLoopResult:
    """Complete result of single stock daily analysis"""

    symbol: str
    analysis_date: datetime
    execution_time_seconds: float

    # Data collection metrics
    tick_data_points: int
    data_quality_score: float
    data_completeness_pct: float

    # Analysis results
    insider_signal: Optional[InsiderSignal]
    entry_recommendation: Dict
    price_action_analysis: Dict
    volume_spread_analysis: Dict

    # Trading actions
    position_entered: bool
    position_exited: bool
    current_position_status: Dict
    exit_signals: List[ExitSignal]

    # Performance tracking
    daily_pnl: float
    cumulative_pnl: float
    risk_metrics: Dict

    # Complete audit trail
    raw_data_summary: Dict
    analysis_chain: List[Dict]
    database_records_created: int

    # JSON export
    complete_json: Dict


class FeedbackLoop:
    """
    Main feedback loop orchestrator for individual stock daily analysis

    This class coordinates all components to provide comprehensive analysis
    and maintains complete audit trails for strategy validation.
    """

    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize components
        self.ib_connector = IBConnector()
        self.data_service = DataService()
        self.tick_analyzer = TickAnalyzer(self.ib_connector, config["database_path"])
        self.exit_manager = ExitManager(self.ib_connector, config["database_path"])

        # Database setup
        self.db_path = config["database_path"]
        self._init_database()

        # Analysis parameters
        self.LOOKBACK_DAYS = config.get("lookback_days", 14)
        self.MIN_TICK_DATA_POINTS = config.get("min_tick_data_points", 1000)
        self.MIN_DATA_QUALITY_SCORE = config.get("min_data_quality_score", 0.8)

    def _init_database(self):
        """Initialize feedback loop database tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Main feedback loop results
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS feedback_loop_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                analysis_date DATE NOT NULL,
                execution_time_seconds REAL,
                tick_data_points INTEGER,
                data_quality_score REAL,
                data_completeness_pct REAL,
                insider_signal_strength REAL,
                entry_recommended BOOLEAN,
                position_entered BOOLEAN,
                position_exited BOOLEAN,
                daily_pnl REAL,
                cumulative_pnl REAL,
                risk_score REAL,
                database_records_created INTEGER,
                complete_json TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, analysis_date)
            )
        """
        )

        # Analysis chain tracking
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS analysis_chain (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                feedback_loop_id INTEGER,
                step_name TEXT NOT NULL,
                step_order INTEGER NOT NULL,
                start_time DATETIME,
                end_time DATETIME,
                duration_seconds REAL,
                success BOOLEAN,
                error_message TEXT,
                input_data_json TEXT,
                output_data_json TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (feedback_loop_id) REFERENCES feedback_loop_results (id)
            )
        """
        )

        # Data quality metrics
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS data_quality_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                analysis_date DATE NOT NULL,
                data_source TEXT NOT NULL,
                total_records INTEGER,
                missing_records INTEGER,
                invalid_records INTEGER,
                duplicate_records INTEGER,
                quality_score REAL,
                completeness_pct REAL,
                issues_json TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        # Performance tracking
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS daily_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                date DATE NOT NULL,
                position_size REAL,
                entry_price REAL,
                current_price REAL,
                daily_pnl REAL,
                daily_pnl_pct REAL,
                cumulative_pnl REAL,
                max_favorable REAL,
                max_adverse REAL,
                volume INTEGER,
                volatility REAL,
                beta REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, date)
            )
        """
        )

        conn.commit()
        conn.close()

    async def run_single_stock_analysis(
        self, symbol: str, target_date: datetime
    ) -> FeedbackLoopResult:
        """
        Execute complete analysis for single stock on single day

        This is the main feedback loop function that coordinates all analysis
        components and generates comprehensive output.
        """
        analysis_start = datetime.now()
        analysis_chain = []

        self.logger.info(f"Starting feedback loop for {symbol} on {target_date.date()}")

        try:
            # Step 1: Data Collection
            step_result = await self._execute_step(
                "data_collection",
                self._collect_comprehensive_data,
                {"symbol": symbol, "target_date": target_date},
            )
            analysis_chain.append(step_result)
            data_collection = step_result["output_data"]

            # Step 2: Data Quality Assessment
            step_result = await self._execute_step(
                "data_quality_assessment",
                self._assess_data_quality,
                {"data_collection": data_collection},
            )
            analysis_chain.append(step_result)
            data_quality = step_result["output_data"]

            # Step 3: Insider Accumulation Analysis
            step_result = await self._execute_step(
                "insider_analysis",
                self._analyze_insider_patterns,
                {"symbol": symbol, "target_date": target_date},
            )
            analysis_chain.append(step_result)
            insider_analysis = step_result["output_data"]

            # Step 4: Price Action Analysis
            step_result = await self._execute_step(
                "price_action_analysis",
                self._analyze_price_action,
                {
                    "symbol": symbol,
                    "target_date": target_date,
                    "data_collection": data_collection,
                },
            )
            analysis_chain.append(step_result)
            price_action = step_result["output_data"]

            # Step 5: Volume and Spread Analysis
            step_result = await self._execute_step(
                "volume_spread_analysis",
                self._analyze_volume_spread,
                {
                    "symbol": symbol,
                    "target_date": target_date,
                    "data_collection": data_collection,
                },
            )
            analysis_chain.append(step_result)
            volume_spread = step_result["output_data"]

            # Step 6: Entry Signal Generation
            step_result = await self._execute_step(
                "entry_signal_generation",
                self._generate_entry_signals,
                {
                    "symbol": symbol,
                    "insider_analysis": insider_analysis,
                    "price_action": price_action,
                    "volume_spread": volume_spread,
                },
            )
            analysis_chain.append(step_result)
            entry_signals = step_result["output_data"]

            # Step 7: Position Management
            step_result = await self._execute_step(
                "position_management",
                self._manage_positions,
                {
                    "symbol": symbol,
                    "target_date": target_date,
                    "entry_signals": entry_signals,
                },
            )
            analysis_chain.append(step_result)
            position_management = step_result["output_data"]

            # Step 8: Exit Signal Analysis
            step_result = await self._execute_step(
                "exit_signal_analysis",
                self._analyze_exit_signals,
                {"symbol": symbol, "target_date": target_date},
            )
            analysis_chain.append(step_result)
            exit_analysis = step_result["output_data"]

            # Step 9: Performance Calculation
            step_result = await self._execute_step(
                "performance_calculation",
                self._calculate_performance,
                {
                    "symbol": symbol,
                    "target_date": target_date,
                    "position_management": position_management,
                },
            )
            analysis_chain.append(step_result)
            performance = step_result["output_data"]

            # Step 10: Database Storage
            step_result = await self._execute_step(
                "database_storage",
                self._store_complete_analysis,
                {
                    "symbol": symbol,
                    "target_date": target_date,
                    "all_analysis_data": {
                        "data_collection": data_collection,
                        "data_quality": data_quality,
                        "insider_analysis": insider_analysis,
                        "price_action": price_action,
                        "volume_spread": volume_spread,
                        "entry_signals": entry_signals,
                        "position_management": position_management,
                        "exit_analysis": exit_analysis,
                        "performance": performance,
                    },
                },
            )
            analysis_chain.append(step_result)
            storage_result = step_result["output_data"]

            # Calculate total execution time
            execution_time = (datetime.now() - analysis_start).total_seconds()

            # Create complete result object
            result = FeedbackLoopResult(
                symbol=symbol,
                analysis_date=target_date,
                execution_time_seconds=execution_time,
                tick_data_points=data_collection.get("total_tick_points", 0),
                data_quality_score=data_quality.get("overall_score", 0),
                data_completeness_pct=data_quality.get("completeness_pct", 0),
                insider_signal=insider_analysis.get("insider_signal"),
                entry_recommendation=entry_signals,
                price_action_analysis=price_action,
                volume_spread_analysis=volume_spread,
                position_entered=position_management.get("position_entered", False),
                position_exited=position_management.get("position_exited", False),
                current_position_status=position_management.get("current_status", {}),
                exit_signals=exit_analysis.get("exit_signals", []),
                daily_pnl=performance.get("daily_pnl", 0),
                cumulative_pnl=performance.get("cumulative_pnl", 0),
                risk_metrics=performance.get("risk_metrics", {}),
                raw_data_summary=data_collection.get("summary", {}),
                analysis_chain=analysis_chain,
                database_records_created=storage_result.get("records_created", 0),
                complete_json={},  # Will be populated below
            )

            # Generate complete JSON output
            result.complete_json = self._generate_complete_json(result)

            # Store final result
            self._store_feedback_loop_result(result)

            self.logger.info(
                f"Feedback loop completed for {symbol}: "
                f"{execution_time:.2f}s, {result.tick_data_points} ticks, "
                f"quality: {result.data_quality_score:.2f}"
            )

            return result

        except Exception as e:
            self.logger.error(f"Feedback loop failed for {symbol}: {e}")
            # Create error result
            execution_time = (datetime.now() - analysis_start).total_seconds()

            error_result = FeedbackLoopResult(
                symbol=symbol,
                analysis_date=target_date,
                execution_time_seconds=execution_time,
                tick_data_points=0,
                data_quality_score=0,
                data_completeness_pct=0,
                insider_signal=None,
                entry_recommendation={"error": str(e)},
                price_action_analysis={"error": str(e)},
                volume_spread_analysis={"error": str(e)},
                position_entered=False,
                position_exited=False,
                current_position_status={"error": str(e)},
                exit_signals=[],
                daily_pnl=0,
                cumulative_pnl=0,
                risk_metrics={"error": str(e)},
                raw_data_summary={"error": str(e)},
                analysis_chain=analysis_chain,
                database_records_created=0,
                complete_json={"error": str(e)},
            )

            return error_result

    async def _execute_step(
        self, step_name: str, step_function, input_data: Dict
    ) -> Dict:
        """Execute individual analysis step with timing and error handling"""
        step_start = datetime.now()

        try:
            self.logger.debug(f"Executing step: {step_name}")

            # Execute step function
            if asyncio.iscoroutinefunction(step_function):
                output_data = await step_function(input_data)
            else:
                output_data = step_function(input_data)

            step_end = datetime.now()
            duration = (step_end - step_start).total_seconds()

            step_result = {
                "step_name": step_name,
                "start_time": step_start,
                "end_time": step_end,
                "duration_seconds": duration,
                "success": True,
                "error_message": None,
                "input_data": input_data,
                "output_data": output_data,
            }

            self.logger.debug(f"Step {step_name} completed in {duration:.2f}s")
            return step_result

        except Exception as e:
            step_end = datetime.now()
            duration = (step_end - step_start).total_seconds()

            self.logger.error(f"Step {step_name} failed: {e}")

            return {
                "step_name": step_name,
                "start_time": step_start,
                "end_time": step_end,
                "duration_seconds": duration,
                "success": False,
                "error_message": str(e),
                "input_data": input_data,
                "output_data": {},
            }

    def _collect_comprehensive_data(self, params: Dict) -> Dict:
        """Step 1: Collect all available tick data from IB"""
        symbol = params["symbol"]
        target_date = params["target_date"]

        # Calculate lookback period
        lookback_start = target_date - timedelta(days=self.LOOKBACK_DAYS)

        # Collect tick data using TickAnalyzer
        tick_data = self.tick_analyzer.collect_comprehensive_tick_data(
            symbol, lookback_start, target_date
        )

        # Get daily bars for context
        daily_bars = self.data_service.get_historical_data(
            symbol,
            lookback_start.strftime("%Y-%m-%d"),
            target_date.strftime("%Y-%m-%d"),
            "1Day",
        )

        # Get minute bars for detailed analysis
        minute_bars = self.data_service.get_historical_data(
            symbol,
            (target_date - timedelta(days=5)).strftime("%Y-%m-%d"),
            target_date.strftime("%Y-%m-%d"),
            "1Min",
        )

        return {
            "symbol": symbol,
            "target_date": target_date.isoformat(),
            "lookback_start": lookback_start.isoformat(),
            "tick_data": tick_data,
            "daily_bars": daily_bars.to_dict("records") if not daily_bars.empty else [],
            "minute_bars": (
                minute_bars.to_dict("records") if not minute_bars.empty else []
            ),
            "total_tick_points": len(tick_data),
            "date_range_days": self.LOOKBACK_DAYS,
            "summary": {
                "tick_count": len(tick_data),
                "daily_bars_count": len(daily_bars) if not daily_bars.empty else 0,
                "minute_bars_count": len(minute_bars) if not minute_bars.empty else 0,
                "data_sources": ["IB_ticks", "IB_daily_bars", "IB_minute_bars"],
            },
        }

    def _assess_data_quality(self, params: Dict) -> Dict:
        """Step 2: Assess quality and completeness of collected data"""
        data_collection = params["data_collection"]

        tick_data = data_collection["tick_data"]
        daily_bars = data_collection["daily_bars"]
        minute_bars = data_collection["minute_bars"]

        # Assess tick data quality
        tick_quality = self._assess_tick_data_quality(tick_data)

        # Assess daily bars quality
        daily_quality = self._assess_daily_bars_quality(daily_bars)

        # Assess minute bars quality
        minute_quality = self._assess_minute_bars_quality(minute_bars)

        # Calculate overall quality score
        quality_scores = [
            tick_quality["score"],
            daily_quality["score"],
            minute_quality["score"],
        ]
        overall_score = np.mean([s for s in quality_scores if s > 0])

        # Calculate completeness
        expected_trading_days = self.LOOKBACK_DAYS * 5 / 7  # Rough estimate
        actual_trading_days = len(daily_bars)
        completeness_pct = (
            min(actual_trading_days / expected_trading_days * 100, 100)
            if expected_trading_days > 0
            else 0
        )

        quality_assessment = {
            "overall_score": overall_score,
            "completeness_pct": completeness_pct,
            "tick_data_quality": tick_quality,
            "daily_bars_quality": daily_quality,
            "minute_bars_quality": minute_quality,
            "data_sufficient": (
                tick_quality["score"] >= self.MIN_DATA_QUALITY_SCORE
                and len(tick_data) >= self.MIN_TICK_DATA_POINTS
            ),
            "quality_issues": [],
        }

        # Identify quality issues
        if overall_score < self.MIN_DATA_QUALITY_SCORE:
            quality_assessment["quality_issues"].append(
                f"Overall quality score {overall_score:.2f} below threshold {self.MIN_DATA_QUALITY_SCORE}"
            )

        if len(tick_data) < self.MIN_TICK_DATA_POINTS:
            quality_assessment["quality_issues"].append(
                f"Tick data points {len(tick_data)} below minimum {self.MIN_TICK_DATA_POINTS}"
            )

        if completeness_pct < 80:
            quality_assessment["quality_issues"].append(
                f"Data completeness {completeness_pct:.1f}% below 80%"
            )

        return quality_assessment

    def _assess_tick_data_quality(self, tick_data: List) -> Dict:
        """Assess quality of tick data"""
        if not tick_data:
            return {"score": 0, "issues": ["No tick data available"]}

        issues = []

        # Check for missing timestamps
        timestamps = [tick.timestamp for tick in tick_data]
        if len(set(timestamps)) != len(timestamps):
            issues.append("Duplicate timestamps detected")

        # Check for reasonable price ranges
        prices = [tick.price for tick in tick_data]
        if prices:
            price_range = max(prices) - min(prices)
            avg_price = np.mean(prices)
            if price_range / avg_price > 0.5:  # 50% range seems excessive
                issues.append("Unusually large price range")

        # Check for zero sizes
        zero_sizes = sum(1 for tick in tick_data if tick.size == 0)
        if zero_sizes > len(tick_data) * 0.1:  # >10% zero sizes
            issues.append("High percentage of zero-size trades")

        # Calculate quality score
        score = max(0, 1.0 - len(issues) * 0.2)  # Reduce score for each issue

        return {
            "score": score,
            "total_ticks": len(tick_data),
            "issues": issues,
            "price_range": max(prices) - min(prices) if prices else 0,
            "avg_trade_size": np.mean([tick.size for tick in tick_data]),
        }

    def _assess_daily_bars_quality(self, daily_bars: List) -> Dict:
        """Assess quality of daily bars"""
        if not daily_bars:
            return {"score": 0, "issues": ["No daily bars available"]}

        issues = []

        # Check for OHLC consistency
        for bar in daily_bars:
            if bar["high"] < bar["low"]:
                issues.append("High < Low inconsistency")
            if not (bar["low"] <= bar["open"] <= bar["high"]):
                issues.append("Open price outside high/low range")
            if not (bar["low"] <= bar["close"] <= bar["high"]):
                issues.append("Close price outside high/low range")

        # Check for missing data
        volumes = [bar["volume"] for bar in daily_bars]
        zero_volume_days = sum(1 for v in volumes if v == 0)
        if zero_volume_days > 0:
            issues.append(f"{zero_volume_days} days with zero volume")

        score = max(0, 1.0 - len(issues) * 0.15)

        return {
            "score": score,
            "total_bars": len(daily_bars),
            "issues": issues,
            "zero_volume_days": zero_volume_days,
        }

    def _assess_minute_bars_quality(self, minute_bars: List) -> Dict:
        """Assess quality of minute bars"""
        if not minute_bars:
            return {
                "score": 0.5,
                "issues": ["No minute bars available"],
            }  # Not critical

        issues = []

        # Check for gaps in minute data
        if len(minute_bars) < 390 * 5:  # 390 minutes per day * 5 days
            issues.append("Insufficient minute bar coverage")

        score = max(0, 1.0 - len(issues) * 0.2)

        return {"score": score, "total_bars": len(minute_bars), "issues": issues}

    def _analyze_insider_patterns(self, params: Dict) -> Dict:
        """Step 3: Analyze insider accumulation patterns"""
        symbol = params["symbol"]
        target_date = params["target_date"]

        # Use TickAnalyzer to detect insider signals
        insider_signal = self.tick_analyzer.detect_insider_accumulation(
            symbol, target_date
        )

        # Get volume profiles for the lookback period
        volume_profiles = []
        current_date = target_date - timedelta(days=self.LOOKBACK_DAYS)

        while current_date <= target_date:
            if current_date.weekday() < 5:  # Trading days only
                profile = self.tick_analyzer.analyze_volume_profile(
                    symbol, current_date
                )
                volume_profiles.append(
                    {"date": current_date.isoformat(), "profile": asdict(profile)}
                )
            current_date += timedelta(days=1)

        return {
            "symbol": symbol,
            "analysis_date": target_date.isoformat(),
            "insider_signal": asdict(insider_signal) if insider_signal else None,
            "volume_profiles": volume_profiles,
            "pattern_strength": insider_signal.signal_strength if insider_signal else 0,
            "confidence_level": (
                insider_signal.confidence_level if insider_signal else "none"
            ),
            "supporting_evidence": (
                insider_signal.supporting_evidence if insider_signal else []
            ),
        }

    def _analyze_price_action(self, params: Dict) -> Dict:
        """Step 4: Analyze sophisticated price action patterns"""
        symbol = params["symbol"]
        target_date = params["target_date"]
        data_collection = params["data_collection"]

        minute_bars = pd.DataFrame(data_collection["minute_bars"])

        if minute_bars.empty:
            return {"error": "No minute bars available for price action analysis"}

        # Convert timestamp column to datetime
        minute_bars["timestamp"] = pd.to_datetime(minute_bars["timestamp"])
        minute_bars = minute_bars.sort_values("timestamp")

        # Price action analysis
        price_action = {
            "symbol": symbol,
            "analysis_date": target_date.isoformat(),
            "price_trends": self._analyze_price_trends(minute_bars),
            "support_resistance": self._find_support_resistance(minute_bars),
            "momentum_indicators": self._calculate_momentum_indicators(minute_bars),
            "volatility_analysis": self._analyze_volatility_patterns(minute_bars),
            "order_flow_signals": self._analyze_order_flow(minute_bars),
            "entry_zones": self._identify_entry_zones(minute_bars),
        }

        return price_action

    def _analyze_price_trends(self, minute_bars: pd.DataFrame) -> Dict:
        """Analyze price trends over multiple timeframes"""

        # Calculate moving averages
        minute_bars["ma_20"] = minute_bars["close"].rolling(20).mean()
        minute_bars["ma_50"] = minute_bars["close"].rolling(50).mean()
        minute_bars["ma_100"] = minute_bars["close"].rolling(100).mean()

        # Trend direction
        current_price = minute_bars["close"].iloc[-1]
        ma_20 = minute_bars["ma_20"].iloc[-1]
        ma_50 = minute_bars["ma_50"].iloc[-1]

        trend_strength = 0
        if current_price > ma_20 > ma_50:
            trend_strength = 1  # Strong uptrend
        elif current_price > ma_20:
            trend_strength = 0.5  # Weak uptrend
        elif current_price < ma_20 < ma_50:
            trend_strength = -1  # Strong downtrend
        else:
            trend_strength = -0.5  # Weak downtrend

        return {
            "trend_strength": trend_strength,
            "current_price": current_price,
            "ma_20": ma_20,
            "ma_50": ma_50,
            "price_above_ma20": current_price > ma_20,
            "ma20_above_ma50": ma_20 > ma_50,
        }

    def _find_support_resistance(self, minute_bars: pd.DataFrame) -> Dict:
        """Identify key support and resistance levels"""

        # Find pivot points
        highs = minute_bars["high"].rolling(21, center=True).max()
        lows = minute_bars["low"].rolling(21, center=True).min()

        # Resistance levels (pivot highs)
        resistance_levels = minute_bars[minute_bars["high"] == highs]["high"].unique()
        resistance_levels = sorted(resistance_levels[-5:])  # Top 5 recent levels

        # Support levels (pivot lows)
        support_levels = minute_bars[minute_bars["low"] == lows]["low"].unique()
        support_levels = sorted(support_levels[-5:])  # Bottom 5 recent levels

        current_price = minute_bars["close"].iloc[-1]

        return {
            "resistance_levels": list(resistance_levels),
            "support_levels": list(support_levels),
            "nearest_resistance": min(
                [r for r in resistance_levels if r > current_price], default=None
            ),
            "nearest_support": max(
                [s for s in support_levels if s < current_price], default=None
            ),
            "current_price": current_price,
        }

    def _calculate_momentum_indicators(self, minute_bars: pd.DataFrame) -> Dict:
        """Calculate momentum indicators"""

        # RSI calculation
        delta = minute_bars["close"].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # MACD calculation
        ema_12 = minute_bars["close"].ewm(span=12).mean()
        ema_26 = minute_bars["close"].ewm(span=26).mean()
        macd = ema_12 - ema_26
        signal = macd.ewm(span=9).mean()
        histogram = macd - signal

        return {
            "rsi": rsi.iloc[-1] if not rsi.empty else 50,
            "macd": macd.iloc[-1] if not macd.empty else 0,
            "macd_signal": signal.iloc[-1] if not signal.empty else 0,
            "macd_histogram": histogram.iloc[-1] if not histogram.empty else 0,
            "momentum_bullish": (
                rsi.iloc[-1] > 50 and macd.iloc[-1] > signal.iloc[-1]
                if not rsi.empty and not macd.empty
                else False
            ),
        }

    def _analyze_volatility_patterns(self, minute_bars: pd.DataFrame) -> Dict:
        """Analyze volatility patterns"""

        # Calculate True Range and ATR
        minute_bars["high_low"] = minute_bars["high"] - minute_bars["low"]
        minute_bars["high_close_prev"] = abs(
            minute_bars["high"] - minute_bars["close"].shift(1)
        )
        minute_bars["low_close_prev"] = abs(
            minute_bars["low"] - minute_bars["close"].shift(1)
        )

        minute_bars["true_range"] = minute_bars[
            ["high_low", "high_close_prev", "low_close_prev"]
        ].max(axis=1)
        minute_bars["atr"] = minute_bars["true_range"].rolling(14).mean()

        # Volatility metrics
        current_atr = minute_bars["atr"].iloc[-1] if not minute_bars["atr"].empty else 0
        avg_atr = minute_bars["atr"].mean() if not minute_bars["atr"].empty else 0
        volatility_ratio = current_atr / avg_atr if avg_atr > 0 else 1

        return {
            "current_atr": current_atr,
            "average_atr": avg_atr,
            "volatility_ratio": volatility_ratio,
            "high_volatility": volatility_ratio > 1.5,
            "low_volatility": volatility_ratio < 0.5,
        }

    def _analyze_order_flow(self, minute_bars: pd.DataFrame) -> Dict:
        """Analyze order flow and buying/selling pressure"""

        # Volume-weighted analysis
        minute_bars["vwap"] = (
            minute_bars["close"] * minute_bars["volume"]
        ).cumsum() / minute_bars["volume"].cumsum()

        # Buying vs selling pressure
        minute_bars["buying_pressure"] = np.where(
            minute_bars["close"] > minute_bars["open"], minute_bars["volume"], 0
        )
        minute_bars["selling_pressure"] = np.where(
            minute_bars["close"] < minute_bars["open"], minute_bars["volume"], 0
        )

        total_buying = minute_bars["buying_pressure"].sum()
        total_selling = minute_bars["selling_pressure"].sum()
        total_volume = minute_bars["volume"].sum()

        buy_sell_ratio = (
            total_buying / total_selling if total_selling > 0 else float("inf")
        )

        return {
            "vwap": (
                minute_bars["vwap"].iloc[-1] if not minute_bars["vwap"].empty else 0
            ),
            "price_vs_vwap": (
                minute_bars["close"].iloc[-1] - minute_bars["vwap"].iloc[-1]
                if not minute_bars["vwap"].empty
                else 0
            ),
            "buy_sell_ratio": buy_sell_ratio,
            "buying_pressure_pct": (
                total_buying / total_volume * 100 if total_volume > 0 else 0
            ),
            "selling_pressure_pct": (
                total_selling / total_volume * 100 if total_volume > 0 else 0
            ),
            "net_buying_pressure": total_buying - total_selling,
        }

    def _identify_entry_zones(self, minute_bars: pd.DataFrame) -> Dict:
        """Identify optimal entry zones based on price action"""

        current_price = minute_bars["close"].iloc[-1]

        # Entry criteria based on price action
        entry_signals = []

        # Volume breakout
        recent_volume = minute_bars["volume"].tail(20).mean()
        avg_volume = minute_bars["volume"].mean()
        if recent_volume > avg_volume * 1.5:
            entry_signals.append("Volume breakout detected")

        # Price breakout above resistance
        highs = minute_bars["high"].rolling(50).max()
        if current_price > highs.shift(1).iloc[-1]:
            entry_signals.append("Price breakout above resistance")

        # Momentum alignment
        ma_20 = minute_bars["close"].rolling(20).mean().iloc[-1]
        if current_price > ma_20:
            entry_signals.append("Price above 20-period moving average")

        return {
            "current_price": current_price,
            "entry_signals": entry_signals,
            "entry_recommended": len(entry_signals) >= 2,
            "signal_count": len(entry_signals),
            "confidence_score": min(
                len(entry_signals) / 3, 1.0
            ),  # Max confidence at 3+ signals
        }

    def _analyze_volume_spread(self, params: Dict) -> Dict:
        """Step 5: Analyze volume and spread conditions for entry"""
        symbol = params["symbol"]
        target_date = params["target_date"]
        data_collection = params["data_collection"]

        # Use TickAnalyzer for detailed volume and spread analysis
        volume_profile = self.tick_analyzer.analyze_volume_profile(symbol, target_date)
        spread_analysis = self.tick_analyzer.analyze_spread_behavior(
            symbol, target_date
        )

        # Additional volume analysis
        tick_data = data_collection["tick_data"]

        # Calculate volume distribution
        volume_distribution = self._calculate_volume_distribution(tick_data)

        # Assess execution conditions
        execution_quality = {
            "volume_adequate": volume_profile.total_volume > 100000,  # Min 100k shares
            "spread_acceptable": spread_analysis.avg_spread_pct < 0.5,  # <0.5% spread
            "market_depth_good": spread_analysis.depth_analysis.get(
                "total_bid_volume", 0
            )
            > 50000,
            "large_trade_presence": volume_profile.large_trades_count > 0,
            "institutional_activity": volume_profile.institutional_volume_pct > 20,
        }

        # Overall execution score
        execution_score = sum(execution_quality.values()) / len(execution_quality)

        return {
            "symbol": symbol,
            "analysis_date": target_date.isoformat(),
            "volume_profile": asdict(volume_profile),
            "spread_analysis": asdict(spread_analysis),
            "volume_distribution": volume_distribution,
            "execution_quality": execution_quality,
            "execution_score": execution_score,
            "entry_feasible": execution_score >= 0.6,  # 60% threshold
        }

    def _calculate_volume_distribution(self, tick_data: List) -> Dict:
        """Calculate volume distribution across different trade sizes"""
        if not tick_data:
            return {}

        sizes = [tick.size for tick in tick_data]

        # Categorize trades
        small_trades = [s for s in sizes if s < 1000]
        medium_trades = [s for s in sizes if 1000 <= s < 10000]
        large_trades = [s for s in sizes if s >= 10000]

        total_volume = sum(sizes)

        return {
            "small_trades_count": len(small_trades),
            "medium_trades_count": len(medium_trades),
            "large_trades_count": len(large_trades),
            "small_trades_volume": sum(small_trades),
            "medium_trades_volume": sum(medium_trades),
            "large_trades_volume": sum(large_trades),
            "small_trades_pct": (
                sum(small_trades) / total_volume * 100 if total_volume > 0 else 0
            ),
            "medium_trades_pct": (
                sum(medium_trades) / total_volume * 100 if total_volume > 0 else 0
            ),
            "large_trades_pct": (
                sum(large_trades) / total_volume * 100 if total_volume > 0 else 0
            ),
            "avg_trade_size": np.mean(sizes),
            "median_trade_size": np.median(sizes),
            "max_trade_size": max(sizes),
            "total_volume": total_volume,
        }

    def _generate_entry_signals(self, params: Dict) -> Dict:
        """Step 6: Generate entry signals based on all analysis"""
        symbol = params["symbol"]
        insider_analysis = params["insider_analysis"]
        price_action = params["price_action"]
        volume_spread = params["volume_spread"]

        # Collect all positive signals
        entry_factors = []

        # Insider signal strength
        insider_strength = insider_analysis.get("pattern_strength", 0)
        if insider_strength > 0.5:
            entry_factors.append(f"Strong insider signal ({insider_strength:.2f})")
        elif insider_strength > 0.3:
            entry_factors.append(f"Moderate insider signal ({insider_strength:.2f})")

        # Price action signals
        if price_action.get("entry_zones", {}).get("entry_recommended", False):
            entry_factors.append("Price action entry signals present")

        # Volume and spread conditions
        if volume_spread.get("entry_feasible", False):
            entry_factors.append("Volume and spread conditions suitable")

        # Momentum alignment
        momentum = price_action.get("momentum_indicators", {})
        if momentum.get("momentum_bullish", False):
            entry_factors.append("Momentum indicators bullish")

        # Calculate overall entry score
        entry_score = 0

        # Weight different factors
        entry_score += insider_strength * 0.4  # 40% weight on insider signal
        entry_score += (
            price_action.get("entry_zones", {}).get("confidence_score", 0)
        ) * 0.3  # 30% on price action
        entry_score += volume_spread.get("execution_score", 0) * 0.2  # 20% on execution
        entry_score += (
            1 if momentum.get("momentum_bullish", False) else 0
        ) * 0.1  # 10% on momentum

        # Determine recommendation
        if entry_score >= 0.7:
            recommendation = "strong_buy"
            position_size = 1.0  # Full position
        elif entry_score >= 0.5:
            recommendation = "buy"
            position_size = 0.7  # Reduced position
        elif entry_score >= 0.3:
            recommendation = "weak_buy"
            position_size = 0.5  # Half position
        else:
            recommendation = "hold"
            position_size = 0.0  # No position

        # Risk factors
        risk_factors = []
        if insider_strength < 0.3:
            risk_factors.append("Weak insider accumulation signal")
        if not volume_spread.get("entry_feasible", False):
            risk_factors.append("Poor volume/spread conditions")
        if price_action.get("volatility_analysis", {}).get("high_volatility", False):
            risk_factors.append("High volatility environment")

        return {
            "symbol": symbol,
            "entry_score": entry_score,
            "recommendation": recommendation,
            "position_size": position_size,
            "entry_factors": entry_factors,
            "risk_factors": risk_factors,
            "confidence_level": (
                "high"
                if entry_score >= 0.7
                else "medium" if entry_score >= 0.5 else "low"
            ),
            "entry_price_estimate": price_action.get("price_trends", {}).get(
                "current_price", 0
            ),
            "stop_loss_suggestion": price_action.get("support_resistance", {}).get(
                "nearest_support", 0
            ),
            "profit_target_suggestion": price_action.get("support_resistance", {}).get(
                "nearest_resistance", 0
            ),
        }

    def _manage_positions(self, params: Dict) -> Dict:
        """Step 7: Handle position entry/exit management"""
        symbol = params["symbol"]
        target_date = params["target_date"]
        entry_signals = params["entry_signals"]

        # Check for existing position
        existing_position = self.exit_manager.get_position_status(symbol)

        position_actions = {
            "symbol": symbol,
            "date": target_date.isoformat(),
            "existing_position": existing_position.get("status") == "open",
            "position_entered": False,
            "position_exited": False,
            "current_status": existing_position,
            "actions_taken": [],
        }

        # Handle position entry
        if not existing_position.get("status") == "open" and entry_signals.get(
            "recommendation"
        ) in ["strong_buy", "buy", "weak_buy"]:
            # Calculate position size
            entry_price = entry_signals.get("entry_price_estimate", 0)
            position_size = entry_signals.get("position_size", 0)

            if entry_price > 0 and position_size > 0:
                # Calculate shares based on available capital (placeholder logic)
                available_capital = 10000  # $10k per position
                shares = int(available_capital * position_size / entry_price)

                if shares > 0:
                    # Add position
                    position_id = self.exit_manager.add_position(
                        symbol=symbol,
                        entry_date=target_date,
                        entry_price=entry_price,
                        quantity=shares,
                        entry_reason=f"Insider signal: {entry_signals['entry_score']:.2f}",
                        insider_signal_strength=entry_signals["entry_score"],
                    )

                    position_actions["position_entered"] = True
                    position_actions["actions_taken"].append(
                        f"Entered {shares} shares at ${entry_price:.2f}"
                    )
                    position_actions["position_id"] = position_id

        # Handle position monitoring/exit
        if existing_position.get("status") == "open":
            # Update position with current prices
            current_price = entry_signals.get(
                "entry_price_estimate", 0
            )  # Use current price
            self.exit_manager.update_position_prices(
                symbol, current_price, 100000, target_date
            )

            # Check exit conditions
            exit_signals = self.exit_manager.check_exit_conditions(symbol, target_date)

            # Execute immediate exit signals
            immediate_exits = [s for s in exit_signals if s.urgency == "immediate"]
            if immediate_exits:
                # Execute first immediate exit
                exit_signal = immediate_exits[0]
                trade = self.exit_manager.execute_exit(
                    symbol, exit_signal, current_price, target_date
                )

                position_actions["position_exited"] = True
                position_actions["actions_taken"].append(
                    f"Exited position: {exit_signal.exit_reason.value}"
                )
                position_actions["completed_trade"] = asdict(trade)

        return position_actions

    def _analyze_exit_signals(self, params: Dict) -> Dict:
        """Step 8: Analyze potential exit signals"""
        symbol = params["symbol"]
        target_date = params["target_date"]

        # Get exit signals from ExitManager
        exit_signals = self.exit_manager.check_exit_conditions(symbol, target_date)

        # Analyze signal quality and timing
        signal_analysis = {
            "symbol": symbol,
            "analysis_date": target_date.isoformat(),
            "exit_signals": [asdict(signal) for signal in exit_signals],
            "immediate_signals": [
                asdict(s) for s in exit_signals if s.urgency == "immediate"
            ],
            "high_priority_signals": [
                asdict(s) for s in exit_signals if s.urgency == "high"
            ],
            "signal_count": len(exit_signals),
            "highest_urgency": max([s.urgency for s in exit_signals], default="none"),
            "exit_recommended": any(
                s.urgency in ["immediate", "high"] for s in exit_signals
            ),
        }

        return signal_analysis

    def _calculate_performance(self, params: Dict) -> Dict:
        """Step 9: Calculate performance metrics"""
        symbol = params["symbol"]
        target_date = params["target_date"]
        position_management = params["position_management"]

        # Get current position status
        position_status = self.exit_manager.get_position_status(symbol)

        performance = {
            "symbol": symbol,
            "date": target_date.isoformat(),
            "daily_pnl": 0,
            "daily_pnl_pct": 0,
            "cumulative_pnl": 0,
            "position_value": 0,
            "risk_metrics": {},
        }

        if position_status.get("status") == "open":
            position = position_status.get("position", {})

            daily_pnl = position.get("current_pnl", 0)
            daily_pnl_pct = position.get("current_pnl_pct", 0)
            position_value = position.get("quantity", 0) * position.get(
                "current_price", 0
            )

            performance.update(
                {
                    "daily_pnl": daily_pnl,
                    "daily_pnl_pct": daily_pnl_pct,
                    "position_value": position_value,
                    "max_favorable": position.get("max_favorable", 0),
                    "max_adverse": position.get("max_adverse", 0),
                    "hold_days": position.get("hold_days", 0),
                }
            )

            # Risk metrics
            entry_value = position.get("quantity", 0) * position.get("entry_price", 0)
            risk_metrics = {
                "position_size_pct": 10.0,  # Placeholder
                "risk_per_share": abs(position.get("max_adverse", 0))
                / position.get("quantity", 1),
                "unrealized_risk": daily_pnl if daily_pnl < 0 else 0,
                "days_held": position.get("hold_days", 0),
                "volatility_risk": "medium",  # Placeholder
            }

            performance["risk_metrics"] = risk_metrics

        # Get cumulative P&L from completed trades
        cumulative_pnl = self._get_cumulative_pnl(symbol)
        performance["cumulative_pnl"] = cumulative_pnl

        return performance

    def _get_cumulative_pnl(self, symbol: str) -> float:
        """Get cumulative P&L from completed trades"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            SELECT COALESCE(SUM(pnl), 0) as total_pnl
            FROM completed_trades 
            WHERE symbol = ?
        """,
            (symbol,),
        )

        result = cursor.fetchone()
        conn.close()

        return result[0] if result else 0

    def _store_complete_analysis(self, params: Dict) -> Dict:
        """Step 10: Store all analysis data in database"""
        symbol = params["symbol"]
        target_date = params["target_date"]
        all_data = params["all_analysis_data"]

        records_created = 0

        try:
            # Store data quality metrics
            data_quality = all_data.get("data_quality", {})
            self._store_data_quality_metrics(symbol, target_date, data_quality)
            records_created += 1

            # Store daily performance
            performance = all_data.get("performance", {})
            self._store_daily_performance(symbol, target_date, performance)
            records_created += 1

            # Additional storage operations would go here

        except Exception as e:
            self.logger.error(f"Failed to store analysis data: {e}")

        return {
            "symbol": symbol,
            "storage_date": datetime.now().isoformat(),
            "records_created": records_created,
            "storage_success": records_created > 0,
        }

    def _store_data_quality_metrics(
        self, symbol: str, target_date: datetime, data_quality: Dict
    ):
        """Store data quality metrics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            INSERT OR REPLACE INTO data_quality_metrics
            (symbol, analysis_date, data_source, total_records, quality_score,
             completeness_pct, issues_json)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """,
            (
                symbol,
                target_date.date(),
                "IB_comprehensive",
                data_quality.get("tick_data_quality", {}).get("total_ticks", 0),
                data_quality.get("overall_score", 0),
                data_quality.get("completeness_pct", 0),
                json.dumps(data_quality.get("quality_issues", [])),
            ),
        )

        conn.commit()
        conn.close()

    def _store_daily_performance(
        self, symbol: str, target_date: datetime, performance: Dict
    ):
        """Store daily performance metrics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            INSERT OR REPLACE INTO daily_performance
            (symbol, date, daily_pnl, daily_pnl_pct, cumulative_pnl)
            VALUES (?, ?, ?, ?, ?)
        """,
            (
                symbol,
                target_date.date(),
                performance.get("daily_pnl", 0),
                performance.get("daily_pnl_pct", 0),
                performance.get("cumulative_pnl", 0),
            ),
        )

        conn.commit()
        conn.close()

    def _generate_complete_json(self, result: FeedbackLoopResult) -> Dict:
        """Generate comprehensive JSON output for the feedback loop"""

        complete_json = {
            "metadata": {
                "symbol": result.symbol,
                "analysis_date": result.analysis_date.isoformat(),
                "execution_time_seconds": result.execution_time_seconds,
                "analysis_version": "1.0",
                "data_source": "Interactive Brokers",
                "generated_at": datetime.now().isoformat(),
            },
            "data_collection": {
                "tick_data_points": result.tick_data_points,
                "data_quality_score": result.data_quality_score,
                "data_completeness_pct": result.data_completeness_pct,
                "raw_data_summary": result.raw_data_summary,
            },
            "analysis_results": {
                "insider_signal": (
                    asdict(result.insider_signal) if result.insider_signal else None
                ),
                "entry_recommendation": result.entry_recommendation,
                "price_action_analysis": result.price_action_analysis,
                "volume_spread_analysis": result.volume_spread_analysis,
            },
            "trading_actions": {
                "position_entered": result.position_entered,
                "position_exited": result.position_exited,
                "current_position_status": result.current_position_status,
                "exit_signals": [asdict(signal) for signal in result.exit_signals],
            },
            "performance": {
                "daily_pnl": result.daily_pnl,
                "cumulative_pnl": result.cumulative_pnl,
                "risk_metrics": result.risk_metrics,
            },
            "audit_trail": {
                "analysis_chain": result.analysis_chain,
                "database_records_created": result.database_records_created,
                "step_timings": [
                    {
                        "step": step["step_name"],
                        "duration_seconds": step["duration_seconds"],
                        "success": step["success"],
                    }
                    for step in result.analysis_chain
                ],
            },
        }

        return complete_json

    def _store_feedback_loop_result(self, result: FeedbackLoopResult):
        """Store complete feedback loop result"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            INSERT OR REPLACE INTO feedback_loop_results
            (symbol, analysis_date, execution_time_seconds, tick_data_points,
             data_quality_score, data_completeness_pct, insider_signal_strength,
             entry_recommended, position_entered, position_exited, daily_pnl,
             cumulative_pnl, risk_score, database_records_created, complete_json)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                result.symbol,
                result.analysis_date.date(),
                result.execution_time_seconds,
                result.tick_data_points,
                result.data_quality_score,
                result.data_completeness_pct,
                result.insider_signal.signal_strength if result.insider_signal else 0,
                result.entry_recommendation.get("recommendation")
                in ["strong_buy", "buy", "weak_buy"],
                result.position_entered,
                result.position_exited,
                result.daily_pnl,
                result.cumulative_pnl,
                result.risk_metrics.get("overall_risk", 0),
                result.database_records_created,
                json.dumps(result.complete_json, indent=2),
            ),
        )

        # Store analysis chain
        feedback_loop_id = cursor.lastrowid

        for i, step in enumerate(result.analysis_chain):
            cursor.execute(
                """
                INSERT INTO analysis_chain
                (feedback_loop_id, step_name, step_order, start_time, end_time,
                 duration_seconds, success, error_message, input_data_json, output_data_json)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    feedback_loop_id,
                    step["step_name"],
                    i,
                    step["start_time"],
                    step["end_time"],
                    step["duration_seconds"],
                    step["success"],
                    step.get("error_message"),
                    json.dumps(step.get("input_data", {})),
                    json.dumps(step.get("output_data", {})),
                ),
            )

        conn.commit()
        conn.close()

        self.logger.info(
            f"Stored complete feedback loop result for {result.symbol} (ID: {feedback_loop_id})"
        )

    async def run_multiple_stocks(
        self, symbols: List[str], target_date: datetime
    ) -> List[FeedbackLoopResult]:
        """Run feedback loop for multiple stocks SEQUENTIALLY (no parallel due to data feed limits)"""

        self.logger.info(
            f"Running sequential analysis for {len(symbols)} stocks (no parallel processing)"
        )

        results = []

        for i, symbol in enumerate(symbols):
            try:
                self.logger.info(f"Processing stock {i+1}/{len(symbols)}: {symbol}")

                # Sequential processing - one stock at a time
                result = await self.run_single_stock_analysis(symbol, target_date)
                results.append(result)

                # Small delay between stocks to avoid overwhelming data feed
                if i < len(symbols) - 1:  # Don't delay after last stock
                    await asyncio.sleep(1.0)  # 1 second between stocks

            except Exception as e:
                self.logger.error(f"Failed to analyze {symbol}: {e}")
                # Continue with next stock even if one fails
                continue

        self.logger.info(
            f"Sequential analysis completed: {len(results)} successful out of {len(symbols)} stocks"
        )
        return results
