"""
Modern IB connector using ib_async library for better reliability and connection management.
"""

import asyncio
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, List
import nest_asyncio
from ib_async import IB, Stock, util
from .logger import get_logger, LogContext

# Enable nested event loops for Jupyter/async environments
nest_asyncio.apply()

logger = get_logger(__name__)


class IBAsyncConnector:
    """
    Modern IB connector using ib_async library for better reliability.
    Handles connection management, reconnection, and data fetching automatically.
    """
    
    def __init__(self, host="127.0.0.1", port=4001, client_id=1):
        """
        Initialize IB async connector.
        
        Args:
            host: IB Gateway host (default localhost)
            port: IB Gateway port (4001 for live, 4002 for paper)
            client_id: Unique client ID
        """
        self.host = host
        self.port = port
        self.client_id = client_id
        self.ib = IB()
        self._connected = False
        
        # Set up error handling
        self.ib.errorEvent += self._on_error
        self.ib.disconnectedEvent += self._on_disconnect
        
    def _on_error(self, reqId, errorCode, errorString, contract):
        """Handle IB errors"""
        logger.warning(f"IB Error {errorCode}: {errorString}")
        
    def _on_disconnect(self):
        """Handle disconnection"""
        logger.warning("IB connection lost")
        self._connected = False
        
    async def connect(self) -> bool:
        """
        Connect to IB Gateway with automatic retry.
        
        Returns:
            bool: True if connected successfully
        """
        if self._connected and self.ib.isConnected():
            return True
            
        try:
            logger.info(f"Connecting to IB Gateway at {self.host}:{self.port}")
            await self.ib.connectAsync(self.host, self.port, clientId=self.client_id, timeout=20)
            
            # Wait a moment for connection to stabilize
            await asyncio.sleep(1)
            
            if self.ib.isConnected():
                self._connected = True
                logger.info(f"✅ Connected to IB Gateway (server version: {self.ib.client.serverVersion()})")
                return True
            else:
                logger.error("❌ Failed to connect to IB Gateway")
                return False
                
        except Exception as e:
            logger.error(f"❌ Connection error: {e}")
            return False
    
    def connect_sync(self) -> bool:
        """Synchronous wrapper for connect()"""
        return asyncio.run(self.connect())
        
    async def disconnect(self):
        """Disconnect from IB Gateway"""
        if self.ib.isConnected():
            self.ib.disconnect()
            self._connected = False
            logger.info("Disconnected from IB Gateway")
    
    def disconnect_sync(self):
        """Synchronous wrapper for disconnect()"""
        asyncio.run(self.disconnect())
        
    def is_connected(self) -> bool:
        """Check if connected to IB Gateway"""
        return self._connected and self.ib.isConnected()
        
    async def _ensure_connected(self) -> bool:
        """Ensure connection is active, reconnect if needed"""
        if not self.is_connected():
            logger.info("Connection lost, attempting to reconnect...")
            return await self.connect()
        return True
        
    async def get_historical_bars_async(
        self, 
        symbol: str, 
        duration: str, 
        bar_size: str,
        end_date: Optional[datetime] = None,
        use_rth: bool = False  # False = include extended hours
    ) -> pd.DataFrame:
        """
        Get historical bars using ib_async.
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            duration: Duration string (e.g., '1 D', '30 D', '1 Y')
            bar_size: Bar size (e.g., '1 min', '1 day')
            end_date: End date for historical data (None = current time)
            use_rth: Use regular trading hours only (False = extended hours)
            
        Returns:
            DataFrame with OHLCV data
        """
        if not await self._ensure_connected():
            raise ConnectionError("Cannot connect to IB Gateway")
            
        try:
            # Create contract
            contract = Stock(symbol, 'SMART', 'USD')
            
            # Qualify the contract to get full details
            await self.ib.qualifyContractsAsync(contract)
            
            # Request historical data
            logger.info(f"Requesting {duration} of {bar_size} bars for {symbol}")
            
            bars = await self.ib.reqHistoricalDataAsync(
                contract=contract,
                endDateTime=end_date or '',
                durationStr=duration,
                barSizeSetting=bar_size,
                whatToShow='TRADES',
                useRTH=use_rth,
                formatDate=1,
                keepUpToDate=False,
                timeout=30  # 30 second timeout (reduced from 60)
            )
            
            if not bars:
                logger.warning(f"No historical data received for {symbol}")
                return pd.DataFrame()
                
            # Convert to DataFrame
            df = util.df(bars)
            
            if df.empty:
                logger.warning(f"Empty DataFrame for {symbol}")
                return df
                
            # Set datetime index
            df.set_index('date', inplace=True)
            df.index = pd.to_datetime(df.index)
            
            # Add symbol column
            df['symbol'] = symbol
            
            logger.info(f"✅ Retrieved {len(df)} bars for {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"❌ Error getting historical data for {symbol}: {e}")
            raise
            
    def get_historical_bars(
        self, 
        symbol: str, 
        duration: str, 
        bar_size: str,
        end_date: Optional[datetime] = None,
        use_rth: bool = False
    ) -> pd.DataFrame:
        """Synchronous wrapper for get_historical_bars_async"""
        return asyncio.run(self.get_historical_bars_async(
            symbol, duration, bar_size, end_date, use_rth
        ))
        
    def get_daily_bars(self, symbol: str, days: int = 30) -> pd.DataFrame:
        """
        Get daily bars for a symbol.
        
        Args:
            symbol: Stock symbol
            days: Number of days of data
            
        Returns:
            DataFrame with daily OHLCV data
        """
        duration = f"{days} D"
        return self.get_historical_bars(symbol, duration, "1 day", use_rth=False)
        
    def get_minute_bars(
        self, 
        symbol: str, 
        days: int = 1,
        end_date: str = ""
    ) -> pd.DataFrame:
        """
        Get minute bars for a symbol.
        
        Args:
            symbol: Stock symbol
            days: Number of days of data (max 30 for minute data)
            end_date: End date string (empty = current time)
            
        Returns:
            DataFrame with minute OHLCV data
        """
        # IB limits minute data to 30 days
        days = min(days, 30)
        duration = f"{days} D"
        
        end_datetime = None
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, "%Y%m%d %H:%M:%S")
            except ValueError:
                logger.warning(f"Invalid end_date format: {end_date}, using current time")
                
        return self.get_historical_bars(
            symbol, duration, "1 min", end_datetime, use_rth=False
        )

    def convert_to_alpaca_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Convert IB format DataFrame to Alpaca-compatible format.

        Args:
            df: DataFrame from IB with columns [open, high, low, close, volume, ...]

        Returns:
            DataFrame in Alpaca format with proper column names and structure
        """
        if df.empty:
            return df

        # Create a copy to avoid modifying original
        alpaca_df = df.copy()

        # Ensure we have the required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in alpaca_df.columns]

        if missing_cols:
            logger.warning(f"Missing columns in IB data: {missing_cols}")
            # Fill missing columns with NaN or appropriate defaults
            for col in missing_cols:
                if col == 'volume':
                    alpaca_df[col] = 0
                else:
                    alpaca_df[col] = float('nan')

        # Ensure datetime index
        if not isinstance(alpaca_df.index, pd.DatetimeIndex):
            if 'date' in alpaca_df.columns:
                alpaca_df.set_index('date', inplace=True)
                alpaca_df.index = pd.to_datetime(alpaca_df.index)
            else:
                logger.warning("No datetime index or 'date' column found")

        # Keep only the essential columns in the right order
        essential_cols = [col for col in ['open', 'high', 'low', 'close', 'volume', 'symbol']
                         if col in alpaca_df.columns]
        alpaca_df = alpaca_df[essential_cols]

        # Ensure proper data types
        numeric_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_cols:
            if col in alpaca_df.columns:
                alpaca_df[col] = pd.to_numeric(alpaca_df[col], errors='coerce')

        logger.info(f"Converted {len(alpaca_df)} bars to Alpaca format")
        return alpaca_df


# Global instance for shared connection
_global_ib_async_connector = None

def get_ib_async_connection() -> Optional[IBAsyncConnector]:
    """
    Get shared IB async connection instance.
    
    Returns:
        IBAsyncConnector instance or None if connection fails
    """
    global _global_ib_async_connector
    
    if _global_ib_async_connector is None:
        logger.info("Creating shared IB async connection...")
        _global_ib_async_connector = IBAsyncConnector()
        
        if not _global_ib_async_connector.connect_sync():
            logger.error("Failed to create IB async connection")
            _global_ib_async_connector = None
            return None
            
        logger.info("✅ Successfully created shared IB async connection")
        
    return _global_ib_async_connector


def cleanup_ib_async_connection():
    """Clean up global IB async connection"""
    global _global_ib_async_connector
    if _global_ib_async_connector:
        _global_ib_async_connector.disconnect_sync()
        _global_ib_async_connector = None
        logger.info("Cleaned up IB async connection")
