"""
Interactive Brokers connection manager and data fetcher.
IMPORTANT: This is READ-ONLY - no trading operations allowed!
"""

import threading
import time
from datetime import datetime, timedelta
import pandas as pd
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.common import BarData
from pathlib import Path
import os
import csv

from .logger import get_logger, LogContext
from .database import stock_bars_minute, stock_bars_daily

logger = get_logger(__name__)


# Helper to load IB error messages from CSV
_IB_ERROR_MAP = None

def get_ib_error_message(code):
    global _IB_ERROR_MAP
    if _IB_ERROR_MAP is None:
        _IB_ERROR_MAP = {}
        csv_path = os.path.join(os.path.dirname(__file__), '../data/reference/ib_error_messages.csv')
        try:
            with open(csv_path, newline='') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    _IB_ERROR_MAP[row['Code']] = row['TWS message']
        except Exception:
            pass
    return _IB_ERROR_MAP.get(str(code), None)


def load_ib_error_messages():
    """Load IB error messages from CSV file"""
    try:
        # Get the path to the error messages CSV
        current_dir = Path(__file__).parent
        csv_path = current_dir.parent / "data" / "reference" / "ib_error_messages.csv"
        
        if csv_path.exists():
            # Use quotechar to handle commas within fields
            df = pd.read_csv(csv_path, quotechar='"', on_bad_lines='skip')
            # Create a dictionary mapping error codes to messages and notes
            error_dict = {}
            for _, row in df.iterrows():
                code = str(row['Code']).strip()
                message = str(row['TWS message']).strip() if pd.notna(row['TWS message']) else ''
                notes = str(row['Additional notes']).strip() if pd.notna(row['Additional notes']) else ''
                error_dict[code] = {
                    'message': message,
                    'notes': notes
                }
            logger.info(f"Loaded {len(error_dict)} IB error codes from reference file")
            return error_dict
        else:
            logger.warning(f"IB error messages file not found: {csv_path}")
            return {}
    except Exception as e:
        logger.warning(f"Failed to load IB error messages: {e}")
        return {}


def get_ib_error_description(error_code: str) -> str:
    """Get detailed error description for IB error code"""
    if not hasattr(get_ib_error_description, '_error_dict'):
        get_ib_error_description._error_dict = load_ib_error_messages()
    
    error_dict = get_ib_error_description._error_dict
    code_str = str(error_code).strip()
    
    if code_str in error_dict:
        info = error_dict[code_str]
        description = f"IB Error {code_str}: {info['message']}"
        if info['notes'] and info['notes'] != 'nan':
            description += f"\nNote: {info['notes']}"
        return description
    else:
        return f"IB Error {code_str}: Unknown error code"


class IBWrapper(EWrapper):
    """IB API wrapper to handle responses"""

    def __init__(self):
        super().__init__()
        self.data = []
        self.next_order_id = None
        self.connection_ready = False
        self.historical_data_end = False
        self.last_error = None
        self.last_error_code = None

    def error(self, reqId, errorCode, errorString, *args):
        """Handle errors from IB"""
        # The new IB API can pass additional arguments like advancedOrderRejectJson
        # Handle info messages that come as "errors"
        info_codes = [
            2104,
            2106,
            2107,
            2108,
            2158,
            2174,  # No market data permissions for specific data (warning, not blocking)
        ]  # Market data farm connection and Sec-def data farm connection messages

        # Convert errorString to string if it's not already
        error_msg = str(errorString)

        # Check if the actual error code is in the errorString (common IB pattern)
        try:
            actual_error_code = int(error_msg.strip())
            if actual_error_code in info_codes:
                # This is an info message, not an error
                logger.info(f"IB connection status: Code {actual_error_code} - OK")
                return  # Don't set last_error for info messages
        except ValueError:
            actual_error_code = errorCode

        if errorCode in info_codes or actual_error_code in info_codes:
            logger.info(f"IB connection status: {error_msg}")
        elif errorCode == 0 and isinstance(actual_error_code, int) and actual_error_code not in info_codes:
            # Real error with code 0, actual code in errorString
            detailed_error = get_ib_error_description(str(actual_error_code))
            logger.error(
                f"IB Error - ReqId: {reqId}, Code: {actual_error_code}, Msg: {error_msg}"
            )
            logger.error(f"Details: {detailed_error}")
            self.last_error = f"Code {actual_error_code}: {error_msg}"
            self.last_error_code = str(actual_error_code)
        elif errorCode == 504:  # Not Connected
            detailed_error = get_ib_error_description(errorCode)
            logger.error(
                f"IB Not Connected - ReqId: {reqId}, Code: {errorCode}, Msg: {error_msg}"
            )
            logger.error(f"Details: {detailed_error}")
            self.last_error = f"Not Connected: {error_msg}"
            self.last_error_code = errorCode
        else:
            detailed_error = get_ib_error_description(errorCode)
            logger.error(
                f"IB Error - ReqId: {reqId}, Code: {errorCode}, Msg: {error_msg}"
            )
            logger.error(f"Details: {detailed_error}")
            self.last_error = f"Code {errorCode}: {error_msg}"
            self.last_error_code = errorCode

    def nextValidId(self, orderId):
        """Receive next valid order ID"""
        self.next_order_id = orderId
        self.connection_ready = True
        logger.info(f"IB connection ready. Next order ID: {orderId}")

    def historicalData(self, reqId, bar: BarData):
        """Receive historical bar data"""
        self.data.append(
            {
                "timestamp": bar.date,
                "open": bar.open,
                "high": bar.high,
                "low": bar.low,
                "close": bar.close,
                "volume": bar.volume,
                "wap": bar.wap,
                "count": bar.barCount,
            }
        )

    def historicalDataEnd(self, reqId, start, end):
        """Signal end of historical data"""
        self.historical_data_end = True
        logger.info(f"Historical data complete for request {reqId}")

    def tickPrice(self, reqId, tickType, price, attrib):
        """Handle real-time tick price data"""
        if not hasattr(self, "tick_data"):
            self.tick_data = {}

        if reqId not in self.tick_data:
            self.tick_data[reqId] = {}

        tick_names = {
            1: "bid",
            2: "ask",
            4: "last",
            6: "high",
            7: "low",
            9: "close",
            14: "open",
        }

        if tickType in tick_names:
            self.tick_data[reqId][tick_names[tickType]] = {
                "price": price,
                "timestamp": datetime.now(),
            }

            # Call tick handler if registered
            if hasattr(self, "tick_handlers") and reqId in self.tick_handlers:
                tick_data = {
                    "type": tick_names[tickType],
                    "price": price,
                    "time": datetime.now(),
                }
                self.tick_handlers[reqId](tick_data)

    def tickSize(self, reqId, tickType, size):
        """Handle real-time tick size data"""
        if not hasattr(self, "tick_data"):
            self.tick_data = {}

        if reqId not in self.tick_data:
            self.tick_data[reqId] = {}

        size_names = {
            0: "bid_size",
            3: "ask_size",
            5: "last_size",
            8: "volume",
            21: "avg_volume",
        }

        if tickType in size_names:
            self.tick_data[reqId][size_names[tickType]] = {
                "size": size,
                "timestamp": datetime.now(),
            }

    def tickGeneric(self, reqId, tickType, value):
        """Handle generic tick data"""
        # Tick type 48 is halted status (0 = not halted, 1 = halted)
        if tickType == 48:
            if not hasattr(self, "tick_data"):
                self.tick_data = {}
            if reqId not in self.tick_data:
                self.tick_data[reqId] = {}
            self.tick_data[reqId]["halted"] = value == 1

    def historicalTicks(self, reqId, ticks, done):
        """Handle historical tick data (MIDPOINT)"""
        if not hasattr(self, "historical_ticks"):
            self.historical_ticks = []

        for tick in ticks:
            self.historical_ticks.append(
                {
                    "timestamp": datetime.fromtimestamp(tick.time),
                    "price": tick.price,
                    "size": tick.size,
                    "exchange": getattr(tick, "exchange", ""),
                    "conditions": getattr(tick, "specialConditions", ""),
                }
            )

        if done:
            self.historical_ticks_done = True
            logger.info(
                f"Historical ticks (MIDPOINT) complete for request {reqId}: {len(self.historical_ticks)} ticks"
            )

    def historicalTicksLast(self, reqId, ticks, done):
        """Handle historical tick data (TRADES/LAST)"""
        if not hasattr(self, "historical_ticks"):
            self.historical_ticks = []

        for tick in ticks:
            self.historical_ticks.append(
                {
                    "timestamp": datetime.fromtimestamp(tick.time),
                    "price": tick.price,
                    "size": tick.size,
                    "exchange": getattr(tick, "exchange", ""),
                    "conditions": getattr(tick, "specialConditions", ""),
                }
            )

        if done:
            self.historical_ticks_done = True
            logger.info(
                f"Historical ticks (TRADES) complete for request {reqId}: {len(self.historical_ticks)} ticks"
            )

    def historicalTicksBidAsk(self, reqId, ticks, done):
        """Handle historical tick data (BID_ASK)"""
        if not hasattr(self, "historical_ticks"):
            self.historical_ticks = []

        for tick in ticks:
            self.historical_ticks.append(
                {
                    "timestamp": datetime.fromtimestamp(tick.time),
                    "bid_price": tick.priceBid,
                    "ask_price": tick.priceAsk,
                    "bid_size": tick.sizeBid,
                    "ask_size": tick.sizeAsk,
                    "exchange": getattr(tick, "exchange", ""),
                    "conditions": getattr(tick, "specialConditions", ""),
                }
            )

        if done:
            self.historical_ticks_done = True
            logger.info(
                f"Historical ticks (BID_ASK) complete for request {reqId}: {len(self.historical_ticks)} ticks"
            )

    def fundamentalData(self, reqId, data):
        """Handle fundamental data response from IB"""
        if not hasattr(self, "fundamental_data"):
            self.fundamental_data = {}
        
        self.fundamental_data[reqId] = data
        self.fundamental_data_received = True
        
        logger.info(f"Received fundamental data for request {reqId}: {len(data)} characters")

    def historicalTicksAllLast(self, reqId, ticks, done):
        """Handle historical tick data (ALLLAST) - includes off-exchange trades"""
        if not hasattr(self, "historical_ticks"):
            self.historical_ticks = []

        for tick in ticks:
            self.historical_ticks.append(
                {
                    "timestamp": datetime.fromtimestamp(tick.time),
                    "price": tick.price,
                    "size": tick.size,
                    "exchange": getattr(tick, "exchange", ""),
                    "conditions": getattr(tick, "specialConditions", ""),
                    "tick_type": "ALLLAST"  # Mark as AllLast for dark pool analysis
                }
            )

        if done:
            self.historical_ticks_done = True
            logger.info(
                f"Historical ticks (ALLLAST) complete for request {reqId}: {len(self.historical_ticks)} ticks"
            )

    def fundamentalData(self, reqId, data):
        """Handle fundamental data"""
        self.fundamental_data = data
        logger.info(
            f"Fundamental data received for request {reqId}: {len(data)} characters"
        )

    def newsProviders(self, newsProviders):
        """Handle news providers list"""
        self.news_providers = []
        for provider in newsProviders:
            self.news_providers.append({"code": provider.code, "name": provider.name})
        logger.info(f"Received {len(self.news_providers)} news providers")

    def historicalNews(self, reqId, time, providerCode, articleId, headline):
        """Handle historical news"""
        if not hasattr(self, "historical_news"):
            self.historical_news = []

        self.historical_news.append(
            {
                "time": time,
                "provider": providerCode,
                "articleId": articleId,
                "headline": headline,
            }
        )

    def historicalNewsEnd(self, reqId, hasMore):
        """Signal end of historical news"""
        self.historical_news_end = True
        logger.info(f"Historical news complete for request {reqId}, hasMore: {hasMore}")

    def contractDetails(self, reqId, contractDetails):
        """Handle contract details"""
        if not hasattr(self, "contract_details"):
            self.contract_details = []
        self.contract_details.append(contractDetails)

    def contractDetailsEnd(self, reqId):
        """Signal end of contract details"""
        logger.info(f"Contract details complete for request {reqId}")


class IBClient(EClient):
    """IB API client"""

    def __init__(self, wrapper):
        super().__init__(wrapper)


class IBConnector:
    """
    Manages IB Gateway connection for data fetching.
    READ-ONLY operations only!
    """

    def __init__(self, host="127.0.0.1", port=4001, client_id=1):
        """
        Initialize IB connection.

        Args:
            host: IB Gateway host (default localhost)
            port: IB Gateway port (4001 for live, 4002 for paper)
            client_id: Unique client ID
        """
        self.host = host
        self.port = port
        self.client_id = client_id

        # Create wrapper and client
        self.wrapper = IBWrapper()
        self.client = IBClient(self.wrapper)
        self.client.connect(host, port, client_id)

        # Start message processing thread
        self.thread = threading.Thread(target=self._run_client, daemon=True)
        self.thread.start()

        # Wait for connection
        timeout = 10
        start_time = time.time()
        while not self.wrapper.connection_ready:
            if time.time() - start_time > timeout:
                raise ConnectionError("Failed to connect to IB Gateway")
            time.sleep(0.1)

        # Extra delay to ensure connection is fully established
        time.sleep(1)
        
        # Set market data type to delayed (free for everyone)
        # 1=live, 2=frozen, 3=delayed, 4=delayed frozen
        self.client.reqMarketDataType(3)  # Use delayed data
        time.sleep(0.5)  # Give time for market data type to be set
        
        logger.info(f"Connected to IB Gateway at {host}:{port} with delayed market data")

    def _run_client(self):
        """Run the client message processing loop"""
        self.client.run()

    def disconnect(self):
        """Disconnect from IB Gateway"""
        self.client.disconnect()
        logger.info("Disconnected from IB Gateway")
    
    def is_connected(self) -> bool:
        """Check if connected to IB Gateway"""
        return self.client.isConnected() if self.client else False

    def get_historical_data(
        self, symbol: str, duration: str, bar_size: str, data_type: str = "TRADES", end_date: str = "", max_retries: int = 2
    ) -> pd.DataFrame:
        """
        Fetch historical bar data (OHLCV) from Interactive Brokers for a given symbol and time range.

        Args:
            symbol (str): Stock symbol (e.g., 'AAPL').
            duration (str): Duration string (e.g., '1 D', '2 D', '1 W', '1 M').
                - For minute bars: Max '1 M' (1 month).
                - For daily bars: Can use '1 Y', '2 Y', etc.
            bar_size (str): Bar size (e.g., '1 day', '1 hour', '5 mins', '1 min').
            data_type (str): Type of data ('TRADES', 'BID', 'ASK', 'MIDPOINT').
            end_date (str): End date/time for historical data. Empty string for current time.
            max_retries (int): Number of retry attempts on failure.

        Returns:
            pd.DataFrame: DataFrame with OHLCV data indexed by timestamp.

        Raises:
            ConnectionError: If not connected to IB Gateway.
            TimeoutError: If the request times out.
            ValueError: If no data is received or an IB error occurs.

        Notes:
            - Uses delayed data by default (free for all accounts).
            - For minute data, use end_date="" for current data only (no subscription required).
            - Handles IB's quirks with timeouts, retries, and error codes.
        """
        with LogContext(logger, f"IB historical data fetch for {symbol}"):
            import time
            
            # Create contract
            contract = Contract()
            contract.symbol = symbol
            contract.secType = "STK"
            contract.exchange = "SMART"
            contract.currency = "USD"

            # Retry logic with exponential backoff
            for attempt in range(max_retries + 1):
                try:
                    # Clear previous data
                    self.wrapper.data = []
                    self.wrapper.historical_data_end = False
                    self.wrapper.last_error = None
                    self.wrapper.last_error_code = None

                    # Request ID
                    req_id = self._get_next_req_id()
                    
                    # CRITICAL: Add small delay to ensure connection is stable
                    if attempt > 0:
                        time.sleep(1)  # Extra delay on retries
                    
                    # CRITICAL: Request delayed market data to avoid Error 162 (no market data permissions)
                    # This is especially important for paper accounts or accounts without subscriptions
                    if attempt == 0:
                        logger.info("Setting market data type to DELAYED to avoid permission errors")
                        self.client.reqMarketDataType(3)  # 3 = Delayed data (15 min delayed)

                    # Request historical data - use specific end_date if provided
                    # CRITICAL: IB expects empty string for current time, not None
                    if end_date is None:
                        end_time = ""  # Empty string means current time
                    else:
                        end_time = end_date
                    use_rth = 0  # 0 = all data, 1 = regular trading hours only
                    format_date = 1  # 1 = yyyyMMdd HH:mm:ss, 2 = system time format
                    keep_up_to_date = False
                    chart_options = []

                    self.client.reqHistoricalData(
                        req_id,
                        contract,
                        end_time,
                        duration,
                        bar_size,
                        data_type,
                        use_rth,
                        format_date,
                        keep_up_to_date,
                        chart_options,
                    )

                    # CRITICAL FIX: Smart timeout - check for data instead of just waiting for flag
                    timeout = 30  # Increased timeout for slow/illiquid symbols
                    start_time = time.time()
                    last_data_count = 0
                    no_new_data_counter = 0
                    
                    while True:
                        # Safety check: ensure data is not None
                        if self.wrapper.data is None:
                            self.wrapper.data = []
                        current_data_count = len(self.wrapper.data)
                        elapsed = time.time() - start_time
                        
                        # Check if we got the end flag
                        if self.wrapper.historical_data_end:
                            logger.info(f"Historical data end flag received for {symbol} after {elapsed:.1f}s")
                            break
                        
                        # Check if we have data and it stopped coming
                        if current_data_count > 0:
                            if current_data_count == last_data_count:
                                no_new_data_counter += 1
                                # If no new data for 2 seconds, assume complete
                                if no_new_data_counter >= 20:  # 20 * 0.1s = 2s
                                    logger.info(f"No new data for 2s, assuming complete. Got {current_data_count} bars for {symbol}")
                                    break
                            else:
                                no_new_data_counter = 0  # Reset counter
                                last_data_count = current_data_count
                        
                        # Hard timeout
                        if elapsed > timeout:
                            if current_data_count > 0:
                                # We have some data, use it
                                logger.warning(f"Timeout but have {current_data_count} bars for {symbol}, using partial data")
                                break
                            else:
                                # No data at all - this is an error
                                error_details = ""
                                if self.wrapper.last_error:
                                    error_details = f" - Last error: {self.wrapper.last_error}"
                                logger.error(f"Timeout with no data for {symbol}{error_details} (attempt {attempt + 1}/{max_retries + 1})")
                                # Cancel the request on timeout
                                self.client.cancelHistoricalData(req_id)
                                raise TimeoutError(f"API timeout for {symbol} - no data received")
                        
                        time.sleep(0.1)
                    
                    # If we reach here, data request completed successfully
                    break
                    
                except (TimeoutError, Exception) as e:
                    if attempt < max_retries:
                        # Exponential backoff: wait 1, 2, 4 seconds
                        wait_time = 2 ** attempt
                        logger.warning(f"Attempt {attempt + 1} failed for {symbol}: {e}. Retrying in {wait_time}s...")
                        time.sleep(wait_time)
                        continue
                    else:
                        # Final attempt failed
                        logger.error(f"All {max_retries + 1} attempts failed for {symbol}: {e}")
                        raise

            # Convert to DataFrame
            if self.wrapper.data:
                df = pd.DataFrame(self.wrapper.data)

                # Parse IB date format
                # Can be either "20250711 04:00:00 US/Eastern" for intraday or "20250529" for daily
                # Remove timezone suffix if present
                df["timestamp"] = df["timestamp"].str.split(" US/").str[0]

                # Try to parse with time first, then fall back to date only
                try:
                    df["timestamp"] = pd.to_datetime(
                        df["timestamp"], format="%Y%m%d %H:%M:%S"
                    )
                except ValueError:
                    # Daily bars only have date
                    df["timestamp"] = pd.to_datetime(df["timestamp"], format="%Y%m%d")

                df.set_index("timestamp", inplace=True)
                df["symbol"] = symbol

                logger.info(f"Received {len(df)} bars for {symbol}")
                return df
            else:
                error_msg = f"No data received for {symbol}"
                if self.wrapper.last_error:
                    error_msg += f" - IB Error: {self.wrapper.last_error}"
                    # Add detailed error description if we have an error code
                    if self.wrapper.last_error_code:
                        detailed_desc = get_ib_error_description(self.wrapper.last_error_code)
                        error_msg += f"\n{detailed_desc}"
                logger.warning(error_msg)
                raise ValueError(f"CRITICAL: {error_msg}")

    def get_minute_bars(self, symbol: str, days: int = 1, end_date: str = "") -> pd.DataFrame:
        """
        Retrieve minute bar OHLCV data for a given stock symbol from Interactive Brokers.

        Args:
            symbol (str): Stock symbol (e.g., 'AAPL').
            days (int): Number of days of data to retrieve (max ~5 days for minute bars).
            end_date (str): End date in 'YYYYMMDD' format or empty for current time.

        Returns:
            pd.DataFrame: DataFrame with minute OHLCV data indexed by timestamp.

        Raises:
            ConnectionError: If not connected to IB Gateway.
            ValueError: If no data is received or an IB error occurs.

        Notes:
            - Always requests at least 2 days to avoid IB's single-day timestamp bug.
            - Filters to the requested number of days after retrieval.
            - Uses delayed data by default.
        """
        
        # CRITICAL FIX: IB single-day requests corrupt timestamps
        # Force minimum 2 days to get proper date ranges, then filter to requested days
        original_days = days
        effective_days = max(days, 2)  # Always request at least 2 days
        
        if original_days == 1 and effective_days == 2:
            logger.info(f"TIMESTAMP FIX: Requesting {effective_days} days instead of {original_days} to avoid IB timestamp corruption")
        
        # CRITICAL: IB has specific duration limits for minute bars
        # For minute data: Max 1 month (30 days) BUT must use specific duration strings
        # When days > 7, use "X W" (weeks) format instead of "X D" (days)
        if effective_days > 7:
            # Convert to weeks for IB API (more reliable for minute data)
            weeks = min(effective_days // 7, 4)  # Max 4 weeks for minute data
            duration = f"{weeks} W"
            logger.info(f"Using {weeks} weeks duration for minute data request")
        else:
            duration = f"{effective_days} D"
        
        bars = self.get_historical_data(symbol, duration, "1 min", end_date=end_date)
        
        # If we requested extra days for the timestamp fix, filter back to original request
        if original_days == 1 and not bars.empty and len(bars) > 0:
            # Get the most recent trading day only
            unique_dates = bars.index.date
            if len(set(unique_dates)) > 1:
                # Success! We got multiple dates - the fix worked
                latest_date = max(unique_dates)
                bars_filtered = bars[bars.index.date == latest_date]
                logger.info(f"TIMESTAMP FIX SUCCESS: Filtered {len(bars)} bars to {len(bars_filtered)} bars for latest date {latest_date}")
                return bars_filtered
            else:
                # Still corrupted - return what we have but log warning
                logger.warning(f"TIMESTAMP CORRUPTION DETECTED: All {len(bars)} bars have same date {unique_dates[0]} despite requesting {effective_days} days")
        
        return bars

    def get_daily_bars(self, symbol: str, days: int = 30) -> pd.DataFrame:
        """
        Retrieve daily bar OHLCV data for a given stock symbol from Interactive Brokers.

        Args:
            symbol (str): Stock symbol (e.g., 'AAPL').
            days (int): Number of days of data to retrieve.

        Returns:
            pd.DataFrame: DataFrame with daily OHLCV data indexed by timestamp.

        Raises:
            ConnectionError: If not connected to IB Gateway.
            ValueError: If no data is received or an IB error occurs.

        Notes:
            - Uses delayed data by default.
            - Handles IB's quirks with timeouts and error codes.
        """
        # IB API rule: For periods > 365 days, use years instead of days
        if days > 365:
            years = int(days / 365.25)  # Round to whole years for IB API
            duration = f"{years} Y"
            logger.info(f"Requesting {days} days ({duration}) of historical data for {symbol}")
        else:
            duration = f"{days} D"
            logger.info(f"Requesting {duration} of historical data for {symbol}")
            
        return self.get_historical_data(symbol, duration, "1 day")

    def convert_to_alpaca_format(self, ib_df: pd.DataFrame) -> pd.DataFrame:
        """
        Convert IB data format to match Alpaca format.

        Args:
            ib_df: DataFrame from IB

        Returns:
            DataFrame in Alpaca format
        """
        if ib_df.empty:
            return ib_df

        # Rename columns to match Alpaca
        alpaca_df = ib_df.rename(columns={"wap": "vwap", "count": "trade_count"})

        # CRITICAL FIX: IB volume scaling for US stocks
        # Per IB documentation: "Volume for US Stocks are quoted in lots.
        # The actual number of shares in volume can be calculated by multiplying 100."
        if "volume" in alpaca_df.columns:
            # Multiply by 100 for US stocks (IB reports in lots, not shares)
            alpaca_df["volume"] = (alpaca_df["volume"] * 100).astype(int)
            logger.debug("Applied IB volume scaling (x100) for US stocks")

        return alpaca_df

    def get_tick_data(self, symbol: str, duration_seconds: int = 30) -> pd.DataFrame:
        """
        Get real-time tick data for a symbol.

        Args:
            symbol: Stock symbol
            duration_seconds: How long to collect ticks

        Returns:
            DataFrame with tick data
        """
        with LogContext(logger, f"get_tick_data for {symbol}"):
            if not self.is_connected():
                raise ConnectionError("Not connected to IB Gateway")

            # Create contract
            contract = Contract()
            contract.symbol = symbol
            contract.secType = "STK"
            contract.exchange = "SMART"
            contract.currency = "USD"

            # Request market data
            req_id = self._get_next_req_id()

            # Clear any existing tick data
            if hasattr(self.wrapper, "tick_data"):
                self.wrapper.tick_data.pop(req_id, None)

            # Request streaming ticks
            self.client.reqMktData(req_id, contract, "", False, False, [])

            # Collect ticks for specified duration
            logger.info(
                f"Collecting tick data for {symbol} for {duration_seconds} seconds..."
            )
            start_time = time.time()
            tick_snapshots = []

            while time.time() - start_time < duration_seconds:
                if (
                    hasattr(self.wrapper, "tick_data")
                    and req_id in self.wrapper.tick_data
                ):
                    # Take a snapshot of current tick data
                    snapshot = self.wrapper.tick_data[req_id].copy()
                    snapshot["snapshot_time"] = datetime.now()
                    tick_snapshots.append(snapshot)

                time.sleep(0.1)  # Collect snapshots every 100ms

            # Cancel market data
            self.client.cancelMktData(req_id)

            # Convert to DataFrame
            if tick_snapshots:
                df = pd.DataFrame(tick_snapshots)
                logger.info(f"Collected {len(df)} tick snapshots for {symbol}")
                return df
            else:
                logger.warning(f"No tick data received for {symbol}")
                raise ValueError(
                    f"CRITICAL: No tick data available for {symbol} - cannot proceed with incomplete analysis"
                )

    def get_realtime_ticks(
        self, symbol: str, duration_seconds: int = 30
    ) -> pd.DataFrame:
        """
        Alias for get_tick_data for consistency.

        Args:
            symbol: Stock symbol
            duration_seconds: How long to collect ticks

        Returns:
            DataFrame with real-time tick data
        """
        return self.get_tick_data(symbol, duration_seconds)

    def get_alllast_ticks(
        self, symbol: str, start_time: str, end_time: str = ""
    ) -> pd.DataFrame:
        """
        Get AllLast historical tick data (includes off-exchange trades).
        
        AllLast includes additional trade types such as combos, derivatives, 
        and average price trades that are not included in regular TRADES.
        This may capture some dark pool activity.

        Args:
            symbol: Stock symbol
            start_time: Start time in format "YYYYMMDD HH:MM:SS" or with timezone
            end_time: End time (optional, defaults to empty string)

        Returns:
            DataFrame with AllLast historical ticks
        """
        return self.get_historical_ticks(symbol, start_time, end_time, tick_type="ALLLAST")

    def get_historical_ticks_batch(
        self,
        symbol: str,
        start_time: str,
        end_time: str = "",
        max_ticks_per_request: int = 1000,
    ) -> pd.DataFrame:
        """
        Retrieve historical tick data for a given stock symbol, automatically batching requests to handle IB's 1000-tick limit.

        Args:
            symbol (str): Stock symbol (e.g., 'AAPL').
            start_time (str): Start time in 'YYYYMMDD HH:MM:SS' format.
            end_time (str): End time in 'YYYYMMDD HH:MM:SS' format (optional).
            max_ticks_per_request (int): Maximum ticks per IB request (default 1000).

        Returns:
            pd.DataFrame: DataFrame with deduplicated historical ticks indexed by timestamp.

        Raises:
            ConnectionError: If not connected to IB Gateway.
            ValueError: If no data is received or an IB error occurs.

        Notes:
            - Handles batching and deduplication automatically.
            - Uses delayed data by default.
            - May return no data if requested period is outside market hours or too far in the past.
        """
        all_ticks = []

        # If no end_time, just do a single request
        if not end_time:
            return self.get_historical_ticks(symbol, start_time, end_time)

        # Parse start and end times
        start_dt = pd.to_datetime(start_time.replace(" US/Eastern", ""))
        end_dt = pd.to_datetime(end_time.replace(" US/Eastern", ""))

        current_time = start_dt
        batch_count = 0

        logger.info(f"Batching tick requests for {symbol} from {start_dt} to {end_dt}")

        while current_time < end_dt:
            batch_count += 1

            # Request ticks from current_time
            try:
                batch_ticks = self.get_historical_ticks(
                    symbol=symbol,
                    start_time=current_time.strftime("%Y%m%d %H:%M:%S"),
                    end_time="",
                )

                if batch_ticks is not None and not batch_ticks.empty:
                    all_ticks.append(batch_ticks)

                    # Update current_time to the last tick time + 1 second to avoid overlap
                    last_tick_time = pd.to_datetime(batch_ticks["timestamp"].iloc[-1])
                    current_time = last_tick_time + pd.Timedelta(seconds=1)

                    logger.info(
                        f"Batch {batch_count}: Got {len(batch_ticks)} ticks, next start: {current_time}"
                    )

                    # If we got fewer than max_ticks_per_request, we've reached the end
                    if len(batch_ticks) < max_ticks_per_request:
                        break
                else:
                    # No more data available
                    break

                # Rate limiting - small delay between requests
                time.sleep(0.1)

            except Exception as e:
                # If it's a ValueError with an IB error code, print the message
                if hasattr(e, 'args') and e.args and 'Code' in str(e.args[0]):
                    print(f"Batch {batch_count} failed: {e.args[0]}")
                else:
                    print(f"Batch {batch_count} failed: {e}")
                # Move forward by 1 hour and try again
                current_time += pd.Timedelta(hours=1)
                continue

        if not all_ticks:
            logger.warning(f"No tick data retrieved for {symbol}")
            return pd.DataFrame()

        # Combine all batches
        combined_df = pd.concat(all_ticks, ignore_index=True)

        # Deduplicate based on ALL fields (only remove exact duplicates)
        # Cross-exchange reports of same trade are valid and should be kept
        before_dedup = len(combined_df)
        combined_df = combined_df.drop_duplicates(
            subset=["timestamp", "price", "size", "exchange", "conditions"],
            keep="first",
        ).reset_index(drop=True)
        after_dedup = len(combined_df)

        # Sort by timestamp
        combined_df = combined_df.sort_values("timestamp").reset_index(drop=True)

        logger.info(
            f"Tick aggregation complete for {symbol}: {before_dedup} -> {after_dedup} ticks after deduplication"
        )

        return combined_df

    def get_historical_ticks(
        self, symbol: str, start_time: str, end_time: str = "", tick_type: str = "TRADES"
    ) -> pd.DataFrame:
        """
        Retrieve historical tick data for a given stock symbol from Interactive Brokers.

        Args:
            symbol (str): Stock symbol (e.g., 'AAPL').
            start_time (str): Start time in 'YYYYMMDD HH:MM:SS' format.
            end_time (str): End time in 'YYYYMMDD HH:MM:SS' format (optional).
            tick_type (str): Type of ticks ('TRADES', 'ALLLAST', 'MIDPOINT', 'BID_ASK').

        Returns:
            pd.DataFrame: DataFrame with historical ticks indexed by timestamp.

        Raises:
            ConnectionError: If not connected to IB Gateway.
            ValueError: If no data is received or an IB error occurs.

        Notes:
            - IB limits to 1000 ticks per request; use get_historical_ticks_batch for larger ranges.
            - Uses delayed data by default.
            - May return no data if requested period is outside market hours or too far in the past.
        """
        with LogContext(logger, f"get_historical_ticks for {symbol}"):
            if not self.is_connected():
                raise ConnectionError("Not connected to IB Gateway")

            # Create contract
            contract = self.create_stock_contract(symbol)

            # Clear existing historical ticks and error tracking
            self.wrapper.historical_ticks = []
            self.wrapper.historical_ticks_done = False
            self.wrapper.last_error = None

            # Request historical ticks
            req_id = self._get_next_req_id()

            # Convert time format to IB standard: YYYYMMDD-HH:MM:SS (dash format)
            # If no time component, add default market open time
            if len(start_time) == 8:  # Just date like "20250711"
                start_time = f"{start_time}-09:30:00"
            elif " " in start_time and "-" not in start_time:
                # Convert "20250711 09:30:00" to "20250711-09:30:00"
                start_time = start_time.replace(" ", "-", 1)
            # Remove timezone if present (use contract's timezone)
            if "US/Eastern" in start_time:
                start_time = start_time.split(" US/Eastern")[0]

            logger.info(f"Requesting historical ticks for {symbol} from {start_time}")

            # IB limits: max 1000 ticks per request
            # Request the maximum number of ticks allowed by IB
            self.client.reqHistoricalTicks(
                req_id,
                contract,
                start_time,
                "",  # end_time should be empty string as per sample
                1000,  # Request maximum 1000 ticks instead of 10
                tick_type,  # What to show (TRADES, ALLLAST, MIDPOINT, BID, ASK)
                1,  # useRTH (1 = regular trading hours only as per sample)
                True,  # ignoreSize
                [],
            )

            # Wait for data or completion
            timeout = 30
            start = time.time()
            while time.time() - start < timeout:
                # Check if we got data
                if (
                    hasattr(self.wrapper, "historical_ticks_done")
                    and self.wrapper.historical_ticks_done
                ):
                    break
                # Check if there was an error
                if hasattr(self.wrapper, "last_error") and self.wrapper.last_error:
                    error_code = self.wrapper.last_error_code
                    error_msg = get_ib_error_message(error_code) if error_code else None
                    if error_msg:
                        logger.error(f"IB Error during tick request: Code {error_code}: {error_msg}")
                    else:
                        logger.error(f"IB Error during tick request: {self.wrapper.last_error}")
                    break
                time.sleep(0.1)

            if (
                hasattr(self.wrapper, "historical_ticks")
                and self.wrapper.historical_ticks
            ):
                df = pd.DataFrame(self.wrapper.historical_ticks)
                logger.info(f"Received {len(df)} historical ticks for {symbol}")
                return df
            else:
                logger.warning(f"No historical tick data received for {symbol}")
                if hasattr(self.wrapper, "last_error") and self.wrapper.last_error:
                    error_code = self.wrapper.last_error_code
                    error_msg = get_ib_error_message(error_code) if error_code else None
                    if error_msg:
                        raise ValueError(f"CRITICAL: IB Error for {symbol}: Code {error_code}: {error_msg}")
                    else:
                        raise ValueError(f"CRITICAL: IB Error for {symbol}: {self.wrapper.last_error}")
                else:
                    raise ValueError(
                        f"CRITICAL: No historical tick data available for {symbol} - cannot proceed with incomplete analysis"
                    )

    def analyze_tick_patterns(self, tick_df: pd.DataFrame) -> dict:
        """
        Analyze tick data for unusual patterns that might indicate insider trading.

        Args:
            tick_df: DataFrame with tick data

        Returns:
            Dictionary with analysis results
        """
        if tick_df.empty:
            return {}

        analysis = {}

        # For real-time tick snapshots
        if "last" in tick_df.columns:
            # Extract last prices
            last_prices = []
            for _, row in tick_df.iterrows():
                if isinstance(row.get("last"), dict) and "price" in row["last"]:
                    last_prices.append(row["last"]["price"])

            if last_prices:
                analysis["price_volatility"] = pd.Series(last_prices).std()
                analysis["price_range"] = max(last_prices) - min(last_prices)
                analysis["num_price_changes"] = len(set(last_prices))

        # For historical ticks
        if "price" in tick_df.columns:
            analysis["avg_price"] = tick_df["price"].mean()
            analysis["price_std"] = tick_df["price"].std()
            analysis["total_volume"] = (
                tick_df["size"].sum() if "size" in tick_df.columns else 0
            )

            # Detect unusual volume spikes
            if "size" in tick_df.columns:
                sizes = tick_df["size"]
                analysis["max_trade_size"] = sizes.max()
                analysis["avg_trade_size"] = sizes.mean()
                analysis["large_trades"] = len(
                    sizes[sizes > sizes.mean() * 3]
                )  # Trades 3x average

        # Time-based analysis
        if "timestamp" in tick_df.columns:
            tick_df["timestamp"] = pd.to_datetime(tick_df["timestamp"])
            time_diffs = tick_df["timestamp"].diff().dt.total_seconds()
            analysis["avg_time_between_ticks"] = time_diffs.mean()
            analysis["min_time_between_ticks"] = time_diffs.min()

            # Detect rapid trading (potential algo/insider activity)
            rapid_trades = time_diffs[time_diffs < 1].count()  # Trades within 1 second
            analysis["rapid_trades"] = rapid_trades
            analysis["rapid_trade_ratio"] = (
                rapid_trades / len(tick_df) if len(tick_df) > 0 else 0
            )

        return analysis

    def detect_insider_accumulation(
        self, symbol: str, date: str, lookback_days: int = 5
    ) -> dict:
        """
        CRITICAL: Detect insider accumulation patterns using minute and tick data.

        Per specs line 61: "unusual volume activity. i.e insiders buying the stock"
        Per specs line 166: "strange volume tick data analysis"

        Args:
            symbol: Stock symbol
            date: Date to analyze (YYYY-MM-DD format)
            lookback_days: Days to look back for patterns

        Returns:
            Dictionary with insider detection analysis
        """
        logger.info(f"Detecting insider accumulation for {symbol} before {date}")

        analysis = {
            "insider_detected": False,
            "confidence": 0.0,
            "volume_surge_days": [],
            "accumulation_score": 0.0,
            "pattern_details": [],
        }

        try:
            # Parse date
            end_date = pd.to_datetime(date)

            # Analyze each day in lookback period
            accumulation_days = 0
            total_score = 0.0

            for i in range(lookback_days):
                check_date = end_date - timedelta(days=i + 1)

                # Skip weekends
                if check_date.weekday() >= 5:
                    continue

                # Get minute data for the day
                duration = "1 D"
                minute_data = self.get_historical_data(
                    symbol, duration, "1 min", data_type="TRADES"
                )

                if minute_data.empty:
                    continue

                # Filter to specific date
                minute_data_day = minute_data[
                    minute_data.index.date == check_date.date()
                ]

                if minute_data_day.empty:
                    continue

                # Analyze minute patterns
                day_analysis = self._analyze_intraday_accumulation(minute_data_day)

                if day_analysis["is_accumulation"]:
                    accumulation_days += 1
                    total_score += day_analysis["score"]

                    analysis["volume_surge_days"].append(
                        {
                            "date": check_date.strftime("%Y-%m-%d"),
                            "volume_ratio": day_analysis["volume_ratio"],
                            "price_stability": day_analysis["price_stability"],
                            "unusual_patterns": day_analysis["patterns"],
                        }
                    )

                    logger.info(
                        f"  Accumulation detected on {check_date.date()}: "
                        f"score={day_analysis['score']:.2f}"
                    )

            # Determine if insider accumulation pattern exists
            if accumulation_days >= 2:  # At least 2 days of accumulation
                analysis["insider_detected"] = True
                analysis["confidence"] = min(
                    accumulation_days / 3.0, 1.0
                )  # Max confidence at 3+ days
                analysis["accumulation_score"] = total_score / accumulation_days

                logger.info(
                    f"✅ Insider accumulation pattern detected for {symbol}: "
                    f"{accumulation_days} days, confidence={analysis['confidence']:.0%}"
                )

        except Exception as e:
            logger.error(f"Error detecting insider accumulation for {symbol}: {e}")

        return analysis

    def _analyze_intraday_accumulation(self, minute_data: pd.DataFrame) -> dict:
        """
        Analyze intraday minute data for accumulation patterns.

        Key patterns:
        1. High volume with stable price (accumulation)
        2. Volume surges in specific time windows
        3. Consistent buying pressure
        """
        result = {
            "is_accumulation": False,
            "score": 0.0,
            "volume_ratio": 1.0,
            "price_stability": 0.0,
            "patterns": [],
        }

        if len(minute_data) < 30:  # Need at least 30 minutes
            return result

        # Calculate metrics
        total_volume = minute_data["volume"].sum()
        avg_volume_per_min = minute_data["volume"].mean()

        # Price stability (low volatility)
        price_range = minute_data["high"].max() - minute_data["low"].min()
        avg_price = minute_data["close"].mean()
        price_stability = 1.0 - (price_range / avg_price) if avg_price > 0 else 0

        # Volume analysis
        volume_std = minute_data["volume"].std()
        volume_spikes = minute_data[
            minute_data["volume"] > avg_volume_per_min + 2 * volume_std
        ]

        # Time-based patterns
        minute_data["hour"] = minute_data.index.hour
        minute_data["minute"] = minute_data.index.minute

        # Check for specific accumulation patterns
        patterns = []
        score = 0.0

        # Pattern 1: High volume with stable price
        if (
            price_stability > 0.85
            and total_volume > avg_volume_per_min * len(minute_data) * 1.5
        ):
            patterns.append("High volume with stable price")
            score += 0.4

        # Pattern 2: Volume clustering (multiple spikes)
        if len(volume_spikes) >= 5:
            patterns.append(f"Volume clustering ({len(volume_spikes)} spikes)")
            score += 0.3

        # Pattern 3: Consistent positive price movement on volume
        volume_weighted_moves = []
        for i in range(1, len(minute_data)):
            price_move = minute_data.iloc[i]["close"] - minute_data.iloc[i - 1]["close"]
            volume = minute_data.iloc[i]["volume"]
            if volume > 0:
                volume_weighted_moves.append(price_move * volume)

        if volume_weighted_moves and sum(volume_weighted_moves) > 0:
            patterns.append("Positive volume-weighted price movement")
            score += 0.3

        # Set results
        result["is_accumulation"] = score >= 0.5
        result["score"] = score
        result["volume_ratio"] = total_volume / (avg_volume_per_min * len(minute_data))
        result["price_stability"] = price_stability
        result["patterns"] = patterns

        return result

    def _get_next_req_id(self):
        """Get next request ID"""
        req_id = self.wrapper.next_order_id
        self.wrapper.next_order_id += 1
        return req_id

    def is_connected(self) -> bool:
        """Check if connected to IB Gateway"""
        return self.client.isConnected()

    def get_fundamental_data(
        self, symbol: str, report_type: str = "ReportSnapshot"
    ) -> str:
        """
        Retrieve fundamental data for a given stock symbol from Interactive Brokers.

        Args:
            symbol (str): Stock symbol (e.g., 'AAPL').
            report_type (str): Type of fundamental report (e.g., 'ReportSnapshot', 'ReportsFinSummary', 'ReportRatios').

        Returns:
            str: XML string containing the requested fundamental data, or an empty string if unavailable.

        Raises:
            ConnectionError: If not connected to IB Gateway.

        Notes:
            - Requires IB account permissions for fundamental data.
            - Uses a 10-second timeout for the request.
            - May return an empty string if permissions are missing or data is unavailable.
        """
        with LogContext(logger, f"get_fundamental_data for {symbol}"):
            if not self.is_connected():
                raise ConnectionError("Not connected to IB Gateway")

            # Create contract
            contract = Contract()
            contract.symbol = symbol
            contract.secType = "STK"
            contract.exchange = "SMART"
            contract.currency = "USD"

            # Request fundamental data
            req_id = self._get_next_req_id()

            # Store result
            self.wrapper.fundamental_data = None

            self.client.reqFundamentalData(req_id, contract, report_type, [])

            # Wait for data
            timeout = 30
            start = time.time()
            while time.time() - start < timeout:
                if (
                    hasattr(self.wrapper, "fundamental_data")
                    and self.wrapper.fundamental_data
                ):
                    return self.wrapper.fundamental_data
                time.sleep(0.1)

            logger.warning(f"No fundamental data received for {symbol}")
            return ""

    def get_news_providers(self) -> list:
        """
        Retrieve a list of available news providers from Interactive Brokers.

        Returns:
            list: List of news provider dictionaries with 'code' and 'name'.

        Raises:
            ConnectionError: If not connected to IB Gateway.

        Notes:
            - Uses a 2-second wait for provider list to populate.
            - May return an empty list if no providers are available or on timeout.
        """
        if not self.is_connected():
            raise ConnectionError("Not connected to IB Gateway")

        self.wrapper.news_providers = []
        self.client.reqNewsProviders()

        # Wait for providers
        time.sleep(2)

        return (
            self.wrapper.news_providers
            if hasattr(self.wrapper, "news_providers")
            else []
        )

    def get_historical_news(
        self,
        symbol: str,
        provider_codes: str,
        start_date: str,
        end_date: str = "",
        total_results: int = 100,
    ) -> list:
        """
        Retrieve historical news articles for a given stock symbol from Interactive Brokers.

        Args:
            symbol (str): Stock symbol (e.g., 'AAPL').
            provider_codes (str): Provider codes (e.g., 'BRFG+DJNL').
            start_date (str): Start date in 'YYYYMMDD HH:MM:SS' format.
            end_date (str): End date in 'YYYYMMDD HH:MM:SS' format (optional).
            total_results (int): Maximum number of news articles to retrieve.

        Returns:
            list: List of news article headlines or summaries.

        Raises:
            ConnectionError: If not connected to IB Gateway.

        Notes:
            - Requires IB account permissions for news data.
            - Uses a 10-second timeout for the request.
            - May return an empty list if permissions are missing or no news is available in the period.
        """
        with LogContext(logger, f"get_historical_news for {symbol}"):
            if not self.is_connected():
                raise ConnectionError("Not connected to IB Gateway")

            # Create contract
            contract = Contract()
            contract.symbol = symbol
            contract.secType = "STK"
            contract.exchange = "SMART"
            contract.currency = "USD"

            # Get contract details first to get conId
            req_id = self._get_next_req_id()
            self.wrapper.contract_details = []
            self.client.reqContractDetails(req_id, contract)

            # Wait for contract details
            time.sleep(2)

            if (
                not hasattr(self.wrapper, "contract_details")
                or not self.wrapper.contract_details
            ):
                logger.error(f"Could not get contract details for {symbol}")
                return []

            con_id = self.wrapper.contract_details[0].contract.conId

            # Request historical news
            req_id = self._get_next_req_id()
            self.wrapper.historical_news = []
            self.wrapper.historical_news_end = False

            self.client.reqHistoricalNews(
                req_id, con_id, provider_codes, start_date, end_date, total_results, []
            )

            # Wait for news
            timeout = 30
            start = time.time()
            while time.time() - start < timeout:
                if (
                    hasattr(self.wrapper, "historical_news_end")
                    and self.wrapper.historical_news_end
                ):
                    break
                time.sleep(0.1)

            return (
                self.wrapper.historical_news
                if hasattr(self.wrapper, "historical_news")
                else []
            )

    def create_stock_contract(self, symbol: str) -> Contract:
        """
        Create a stock contract for the given symbol.

        Args:
            symbol: Stock symbol

        Returns:
            IB Contract object
        """
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "STK"
        contract.exchange = "SMART"
        contract.currency = "USD"
        return contract

    def stream_ticks(
        self, symbol: str, tick_handler: callable, tick_type: str = "Last"
    ) -> int:
        """
        Start streaming real-time tick data.

        Args:
            symbol: Stock symbol
            tick_handler: Callback function to handle ticks
            tick_type: Type of ticks to stream

        Returns:
            Request ID for the stream
        """
        if not self.is_connected():
            raise ConnectionError("Not connected to IB Gateway")

        contract = self.create_stock_contract(symbol)
        req_id = self._get_next_req_id()

        # Store the tick handler
        if not hasattr(self.wrapper, "tick_handlers"):
            self.wrapper.tick_handlers = {}
        self.wrapper.tick_handlers[req_id] = tick_handler

        # Request market data
        self.client.reqMktData(req_id, contract, "", False, False, [])

        return req_id

    def stop_streaming(self, req_id: int):
        """
        Stop streaming tick data for the given request ID.

        Args:
            req_id: Request ID to stop
        """
        self.client.cancelMktData(req_id)

        # Remove tick handler
        if (
            hasattr(self.wrapper, "tick_handlers")
            and req_id in self.wrapper.tick_handlers
        ):
            del self.wrapper.tick_handlers[req_id]

    def get_contract_details(self, symbol: str) -> list:
        """
        Get contract details for a symbol.

        Args:
            symbol: Stock symbol

        Returns:
            List of contract details
        """
        if not self.is_connected():
            raise ConnectionError("Not connected to IB Gateway")

        contract = self.create_stock_contract(symbol)
        req_id = self._get_next_req_id()

        # Clear any existing contract details
        self.wrapper.contract_details = []

        self.client.reqContractDetails(req_id, contract)

        # Wait for contract details
        timeout = 5
        start = time.time()
        while time.time() - start < timeout:
            if (
                hasattr(self.wrapper, "contract_details")
                and self.wrapper.contract_details
            ):
                return self.wrapper.contract_details
            time.sleep(0.1)

        logger.warning(f"No contract details received for {symbol}")
        return []

    def reconnect(self) -> bool:
        """
        Attempt to reconnect to IB Gateway.

        Returns:
            True if reconnection successful, False otherwise
        """
        try:
            # Disconnect if still connected
            if self.client.isConnected():
                self.client.disconnect()
                time.sleep(1)

            # Reset wrapper state
            self.wrapper.connection_ready = False
            self.wrapper.next_order_id = None
            self.wrapper.historical_data_end = False

            # Create new client instance (important for reconnection)
            self.client = IBClient(self.wrapper)

            # Start new message processing thread
            if hasattr(self, "thread") and self.thread.is_alive():
                # Stop old thread gracefully
                self.client.disconnect()
                time.sleep(0.5)

            # Connect with new client
            self.client.connect(self.host, self.port, self.client_id)

            # Start new message processing thread
            self.thread = threading.Thread(target=self._run_client, daemon=True)
            self.thread.start()

            # Wait for connection
            timeout = 10
            start_time = time.time()
            while not self.wrapper.connection_ready:
                if time.time() - start_time > timeout:
                    logger.error("Reconnection timeout")
                    return False
                time.sleep(0.1)

            # Extra delay to ensure connection is fully established
            time.sleep(1)
            logger.info(
                f"Successfully reconnected to IB Gateway at {self.host}:{self.port}"
            )
            return True

        except Exception as e:
            logger.error(f"Reconnection failed: {e}")
            return False


# Example usage and testing
if __name__ == "__main__":
    # Test IB connection
    logger.info("Testing IB connection...")

    try:
        # Connect to IB Gateway (ensure it's running on port 4001)
        ib = IBConnector(port=4001)

        # Test fetching minute data
        symbol = "AAPL"
        logger.info(f"Fetching minute data for {symbol}...")
        minute_data = ib.get_minute_bars(symbol, days=1)

        if not minute_data.empty:
            logger.info(f"Got {len(minute_data)} minute bars")
            logger.info(f"First bar: {minute_data.iloc[0]}")
            logger.info(f"Last bar: {minute_data.iloc[-1]}")

            # Convert to Alpaca format
            alpaca_format = ib.convert_to_alpaca_format(minute_data)
            logger.info(f"Converted to Alpaca format: {alpaca_format.columns.tolist()}")

        # Test fetching daily data
        logger.info(f"Fetching daily data for {symbol}...")
        daily_data = ib.get_daily_bars(symbol, days=30)

        if not daily_data.empty:
            logger.info(f"Got {len(daily_data)} daily bars")

        # Disconnect
        ib.disconnect()
        logger.info("Test completed successfully")

    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)

    def get_fundamental_data(self, symbol: str, report_type: str = "ReportsFinSummary", timeout: int = 10) -> str:
        """
        Get fundamental data for a stock from IB.
        
        Args:
            symbol: Stock symbol
            report_type: Type of fundamental report:
                - ReportsFinSummary: Financial summary
                - ReportsOwnership: Ownership data
                - ReportSnapshot: Snapshot report
                - ReportsFinStatements: Financial statements
                - RESC: Estimates
                - CalendarReport: Calendar data
            timeout: Timeout in seconds
            
        Returns:
            XML string with fundamental data or empty string if failed
        """
        with LogContext(logger, f"get_fundamental_data for {symbol}"):
            if not self.is_connected():
                raise ConnectionError("Not connected to IB Gateway")
            
            # Create contract
            contract = Contract()
            contract.symbol = symbol
            contract.secType = "STK"
            contract.exchange = "SMART"
            contract.currency = "USD"
            
            # Reset fundamental data
            self.fundamental_data_received = False
            if hasattr(self, "fundamental_data"):
                self.fundamental_data.clear()
            
            # Request fundamental data
            req_id = self._get_next_req_id()
            logger.info(f"Requesting {report_type} for {symbol}...")
            
            self.client.reqFundamentalData(req_id, contract, report_type, [])
            
            # Wait for response
            wait_time = 0
            while wait_time < timeout and not self.fundamental_data_received:
                time.sleep(0.1)
                wait_time += 0.1
            
            if self.fundamental_data_received and req_id in self.fundamental_data:
                data = self.fundamental_data[req_id]
                logger.info(f"✅ Received fundamental data for {symbol}: {len(data)} characters")
                return data
            else:
                logger.warning(f"❌ No fundamental data received for {symbol} within {timeout}s")
                return ""
