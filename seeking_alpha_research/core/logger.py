import logging
import logging.handlers
import os
from datetime import datetime

# Create logs directory if it doesn't exist
LOGS_DIR = os.path.join(os.path.dirname(__file__), "logs")
os.makedirs(LOGS_DIR, exist_ok=True)

# Log file paths
MAIN_LOG_FILE = os.path.join(LOGS_DIR, "stk_v5.log")
ERROR_LOG_FILE = os.path.join(LOGS_DIR, "stk_v5_errors.log")
TRADE_LOG_FILE = os.path.join(LOGS_DIR, "trades.log")


def setup_logger(name, log_file=None, level=logging.INFO):
    """
    Set up a logger with file and console handlers.

    Args:
        name: Logger name (usually module name)
        log_file: Optional specific log file path
        level: Logging level

    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Avoid adding duplicate handlers
    if logger.handlers:
        return logger

    # Create formatters
    detailed_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    simple_formatter = logging.Formatter(
        "%(asctime)s - %(levelname)s - %(message)s", datefmt="%H:%M:%S"
    )

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)

    # Main file handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_file or MAIN_LOG_FILE, maxBytes=10 * 1024 * 1024, backupCount=5  # 10MB
    )
    file_handler.setLevel(level)
    file_handler.setFormatter(detailed_formatter)
    logger.addHandler(file_handler)

    # Error file handler for ERROR and above
    error_handler = logging.handlers.RotatingFileHandler(
        ERROR_LOG_FILE, maxBytes=10 * 1024 * 1024, backupCount=5  # 10MB
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    logger.addHandler(error_handler)

    return logger


# Create specialized loggers
def get_logger(name):
    """Get a logger for a specific module"""
    return setup_logger(name)


def get_trade_logger():
    """Get a specialized logger for trade events"""
    logger = setup_logger("trades", TRADE_LOG_FILE)
    return logger


class LogContext:
    """Context manager for logging execution time and errors"""

    def __init__(self, logger, operation):
        self.logger = logger
        self.operation = operation
        self.start_time = None

    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.info(f"Starting {self.operation}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (datetime.now() - self.start_time).total_seconds()

        if exc_type:
            self.logger.error(
                f"Failed {self.operation} after {duration:.2f}s: {exc_type.__name__}: {exc_val}",
                exc_info=True,
            )
        else:
            self.logger.info(f"Completed {self.operation} in {duration:.2f}s")

        # Don't suppress exceptions
        return False


# Example usage functions
def log_api_call(logger, api_name, symbol, params=None):
    """Log API call details"""
    logger.info(f"API Call: {api_name} for {symbol} with params: {params}")


def log_data_fetch(logger, source, symbol, record_count, duration):
    """Log data fetching results"""
    logger.info(
        f"Data fetch from {source}: {symbol} - "
        f"{record_count} records in {duration:.2f}s "
        f"({record_count/duration:.1f} records/s)"
    )


def log_error_with_context(logger, error, context):
    """Log error with additional context"""
    logger.error(
        f"Error in {context.get('operation', 'unknown')}: {error}",
        extra={
            "symbol": context.get("symbol"),
            "date_range": context.get("date_range"),
            "api": context.get("api"),
        },
        exc_info=True,
    )


if __name__ == "__main__":
    # Test the logging setup
    test_logger = get_logger(__name__)

    test_logger.info("Testing info message")
    test_logger.warning("Testing warning message")
    test_logger.error("Testing error message")

    # Test context manager
    with LogContext(test_logger, "test operation"):
        test_logger.info("Inside operation")

    # Test error context
    try:
        with LogContext(test_logger, "failing operation"):
            raise ValueError("Test error")
    except ValueError:
        pass

    print(f"Logs written to: {LOGS_DIR}")
