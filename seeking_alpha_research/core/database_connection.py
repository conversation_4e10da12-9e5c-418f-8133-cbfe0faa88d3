#!/usr/bin/env python3
"""
Database connection class for trading system.
Per specs: "real db, real api. no silent fails"
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class Database:
    """
    Database connection and query execution.
    Uses SQLite for development, can switch to PostgreSQL for production.
    """

    def __init__(self, db_path: str = None):
        if db_path is None:
            # Use centralized database configuration
            from .database_config import get_database_url

            db_path = get_database_url()

        self.engine = create_engine(db_path, echo=False)
        self.Session = sessionmaker(bind=self.engine)
        logger.info(f"Database connection established: {db_path}")

    def execute_query(self, query: str, params: dict = None):
        """
        Execute a query and return results.

        FAILS LOUDLY on error - no silent failures.
        """
        try:
            with self.engine.connect() as conn:
                if params:
                    result = conn.execute(text(query), params)
                else:
                    result = conn.execute(text(query))

                # For SELECT queries, fetch results
                if query.strip().upper().startswith("SELECT"):
                    return result.fetchall()
                else:
                    conn.commit()
                    return result.rowcount

        except Exception as e:
            logger.error(f"Database query failed: {query}")
            logger.error(f"Error: {e}")
            # FAIL LOUDLY - money is on the line
            raise ValueError(f"CRITICAL: Database operation failed: {e}")

    def create_tables(self):
        """Create all required tables for the trading system."""

        # Historical ATM offerings table
        self.execute_query(
            """
        CREATE TABLE IF NOT EXISTS historical_atm_offerings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            offering_date DATE NOT NULL,
            offering_amount REAL,
            cash_before_offering REAL,
            monthly_burn_rate REAL,
            had_atm_shelf BOOLEAN,
            days_since_shelf_filed INTEGER,
            stock_price_change_after REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        )

        # Historical trades table
        self.execute_query(
            """
        CREATE TABLE IF NOT EXISTS historical_trades (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            trade_date DATE NOT NULL,
            symbol TEXT NOT NULL,
            market_cap REAL,
            entry_price REAL NOT NULL,
            exit_price REAL,
            position_size_pct REAL,
            pnl_percentage REAL,
            max_drawdown REAL,
            holding_days INTEGER,
            gap_percentage REAL,
            atm_occurred BOOLEAN,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        )

        # Historical executions table
        self.execute_query(
            """
        CREATE TABLE IF NOT EXISTS historical_executions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_date TIMESTAMP NOT NULL,
            symbol TEXT NOT NULL,
            side TEXT NOT NULL,
            shares INTEGER NOT NULL,
            price REAL NOT NULL,
            commission_paid REAL,
            expected_entry_price REAL,
            market_cap REAL,
            avg_daily_volume REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        )

        logger.info("Database tables created successfully")

    def close(self):
        """Close database connection."""
        self.engine.dispose()
        logger.info("Database connection closed")


# Export Database class directly from database.py
if __name__ == "__main__":
    # Test database connection
    db = Database()
    db.create_tables()

    # Test query
    result = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
    print(f"Tables in database: {result}")

    db.close()
