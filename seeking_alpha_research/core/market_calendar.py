#!/usr/bin/env python3
"""
Market Calendar Utilities

Handles trading days, market holidays, and proper date validation using
pandas-market-calendars to avoid errors when markets are closed.

NO FAKES, NO MOCKS - Real market calendar data.
"""

import pandas as pd
import pandas_market_calendars as mcal
from datetime import datetime, timedelta
from typing import List, Tuple, Optional
from .logger import get_logger

logger = get_logger(__name__)


class MarketCalendar:
    """Market calendar utilities for proper trading day handling."""
    
    def __init__(self, exchange: str = "NYSE"):
        """
        Initialize market calendar.
        
        Args:
            exchange: Exchange to use for calendar (NYSE, NASDAQ, etc.)
        """
        self.exchange = exchange
        try:
            self.calendar = mcal.get_calendar(exchange)
            logger.info(f"Initialized {exchange} market calendar")
        except Exception as e:
            logger.warning(f"Failed to load {exchange} calendar, falling back to NYSE: {e}")
            self.calendar = mcal.get_calendar("NYSE")
            self.exchange = "NYSE"
    
    def is_trading_day(self, date: str | datetime) -> bool:
        """
        Check if a given date is a trading day.
        
        Args:
            date: Date to check (YYYY-MM-DD string or datetime)
            
        Returns:
            True if trading day, False if holiday/weekend
        """
        try:
            if isinstance(date, str):
                date = pd.to_datetime(date)
            elif isinstance(date, datetime):
                date = pd.Timestamp(date)
            
            # Get trading days for the date
            trading_days = self.calendar.valid_days(
                start_date=date.strftime("%Y-%m-%d"),
                end_date=date.strftime("%Y-%m-%d")
            )
            
            is_trading = len(trading_days) > 0
            
            if not is_trading:
                logger.info(f"{date.strftime('%Y-%m-%d')} is not a trading day ({self.exchange})")
            
            return is_trading
            
        except Exception as e:
            logger.error(f"Error checking trading day for {date}: {e}")
            # Conservative fallback: assume it's a trading day to avoid blocking data
            return True
    
    def get_trading_days(self, start_date: str, end_date: str) -> pd.DatetimeIndex:
        """
        Get all trading days between start and end dates.
        
        Args:
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            DatetimeIndex of trading days
        """
        try:
            trading_days = self.calendar.valid_days(
                start_date=start_date,
                end_date=end_date
            )
            
            logger.info(f"Found {len(trading_days)} trading days from {start_date} to {end_date}")
            return trading_days
            
        except Exception as e:
            logger.error(f"Error getting trading days: {e}")
            # Fallback to business days
            return pd.bdate_range(start=start_date, end=end_date)
    
    def get_next_trading_day(self, date: str | datetime) -> pd.Timestamp:
        """
        Get the next trading day after the given date.
        
        Args:
            date: Reference date
            
        Returns:
            Next trading day
        """
        try:
            if isinstance(date, str):
                date = pd.to_datetime(date)
            elif isinstance(date, datetime):
                date = pd.Timestamp(date)
            
            # Look ahead up to 10 days to find next trading day
            for i in range(1, 11):
                next_date = date + pd.Timedelta(days=i)
                if self.is_trading_day(next_date):
                    return next_date
            
            # Fallback if no trading day found in 10 days
            logger.warning(f"No trading day found within 10 days of {date}")
            return date + pd.Timedelta(days=1)
            
        except Exception as e:
            logger.error(f"Error finding next trading day: {e}")
            return pd.Timestamp(date) + pd.Timedelta(days=1)
    
    def get_previous_trading_day(self, date: str | datetime) -> pd.Timestamp:
        """
        Get the previous trading day before the given date.
        
        Args:
            date: Reference date
            
        Returns:
            Previous trading day
        """
        try:
            if isinstance(date, str):
                date = pd.to_datetime(date)
            elif isinstance(date, datetime):
                date = pd.Timestamp(date)
            
            # Look back up to 10 days to find previous trading day
            for i in range(1, 11):
                prev_date = date - pd.Timedelta(days=i)
                if self.is_trading_day(prev_date):
                    return prev_date
            
            # Fallback if no trading day found in 10 days
            logger.warning(f"No trading day found within 10 days before {date}")
            return date - pd.Timedelta(days=1)
            
        except Exception as e:
            logger.error(f"Error finding previous trading day: {e}")
            return pd.Timestamp(date) - pd.Timedelta(days=1)
    
    def validate_date_range(self, start_date: str, end_date: str) -> Tuple[str, str, int]:
        """
        Validate and adjust date range to ensure trading days exist.
        
        Args:
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            Tuple of (adjusted_start, adjusted_end, expected_trading_days)
        """
        try:
            # Get trading days in the range
            trading_days = self.get_trading_days(start_date, end_date)
            
            if len(trading_days) == 0:
                logger.warning(f"No trading days found between {start_date} and {end_date}")
                
                # Try to find nearest trading days
                start_ts = pd.to_datetime(start_date)
                end_ts = pd.to_datetime(end_date)
                
                # Find next trading day after start
                adjusted_start = self.get_next_trading_day(start_ts)
                # Find previous trading day before end
                adjusted_end = self.get_previous_trading_day(end_ts)
                
                # Re-calculate trading days
                trading_days = self.get_trading_days(
                    adjusted_start.strftime("%Y-%m-%d"),
                    adjusted_end.strftime("%Y-%m-%d")
                )
                
                logger.info(f"Adjusted range: {adjusted_start.strftime('%Y-%m-%d')} to {adjusted_end.strftime('%Y-%m-%d')}")
                
                return (
                    adjusted_start.strftime("%Y-%m-%d"),
                    adjusted_end.strftime("%Y-%m-%d"),
                    len(trading_days)
                )
            else:
                return (start_date, end_date, len(trading_days))
                
        except Exception as e:
            logger.error(f"Error validating date range: {e}")
            # Return original dates if validation fails
            return (start_date, end_date, 0)
    
    def get_market_holidays(self, start_date: str, end_date: str) -> List[str]:
        """
        Get list of market holidays in the given date range.
        
        Args:
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            List of holiday dates (YYYY-MM-DD strings)
        """
        try:
            # Get all calendar days in range
            all_days = pd.date_range(start=start_date, end=end_date, freq='D')
            
            # Get trading days in range
            trading_days = self.get_trading_days(start_date, end_date)
            
            # Find weekdays that are not trading days (holidays)
            weekdays = all_days[all_days.weekday < 5]  # Monday=0, Friday=4
            holidays = []
            
            for day in weekdays:
                if day not in trading_days:
                    holidays.append(day.strftime("%Y-%m-%d"))
            
            if holidays:
                logger.info(f"Found {len(holidays)} market holidays: {holidays}")
            
            return holidays
            
        except Exception as e:
            logger.error(f"Error getting market holidays: {e}")
            return []


# Global market calendar instance
_market_calendar = None


def get_market_calendar(exchange: str = "NYSE") -> MarketCalendar:
    """Get global market calendar instance."""
    global _market_calendar
    if _market_calendar is None or _market_calendar.exchange != exchange:
        _market_calendar = MarketCalendar(exchange)
    return _market_calendar


def is_trading_day(date: str | datetime, exchange: str = "NYSE") -> bool:
    """Check if date is a trading day."""
    return get_market_calendar(exchange).is_trading_day(date)


def get_trading_days(start_date: str, end_date: str, exchange: str = "NYSE") -> pd.DatetimeIndex:
    """Get trading days in range."""
    return get_market_calendar(exchange).get_trading_days(start_date, end_date)


def validate_target_date(target_date: str, exchange: str = "NYSE") -> Tuple[str, bool, str]:
    """
    Validate target date and provide trading day info.
    
    Returns:
        Tuple of (validated_date, is_trading_day, message)
    """
    calendar = get_market_calendar(exchange)
    
    if calendar.is_trading_day(target_date):
        return (target_date, True, f"{target_date} is a valid trading day")
    else:
        # Find nearest trading day
        next_trading = calendar.get_next_trading_day(target_date)
        prev_trading = calendar.get_previous_trading_day(target_date)
        
        message = f"{target_date} is not a trading day (holiday/weekend). "
        message += f"Previous trading day: {prev_trading.strftime('%Y-%m-%d')}, "
        message += f"Next trading day: {next_trading.strftime('%Y-%m-%d')}"
        
        return (target_date, False, message)


if __name__ == "__main__":
    # Test market calendar functionality
    cal = MarketCalendar()
    
    # Test July 4, 2024 (Independence Day - market holiday)
    print("Testing July 4, 2024 (Independence Day):")
    print(f"Is trading day: {cal.is_trading_day('2024-07-04')}")
    
    # Test July 11, 2025 (should be normal Friday)
    print(f"\nTesting July 11, 2025:")
    print(f"Is trading day: {cal.is_trading_day('2025-07-11')}")
    
    # Get trading days for a week with a holiday
    print(f"\nTrading days July 1-8, 2024:")
    trading_days = cal.get_trading_days("2024-07-01", "2024-07-08")
    for day in trading_days:
        print(f"  {day.strftime('%Y-%m-%d %A')}")
    
    # Get holidays
    holidays = cal.get_market_holidays("2024-07-01", "2024-07-08")
    print(f"\nHolidays July 1-8, 2024: {holidays}")