"""
Enhanced IB connector using ib_async library with multiple connection support.
Maintains backward compatibility with existing ib_connector.py data format.
"""

import asyncio
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Union
import nest_asyncio
from ib_insync import IB, Stock, util, Contract, BarData
from .logger import get_logger, LogContext
from .database import engine, stock_bars_minute, stock_bars_daily, stock_ticks
from sqlalchemy import select, and_, func
import time
from concurrent.futures import ThreadPoolExecutor
import threading

# Enable nested event loops for Jupyter/async environments
nest_asyncio.apply()

logger = get_logger(__name__)


class ConnectionPool:
    """Manages multiple IB connections for parallel data fetching"""
    
    def __init__(self, host="127.0.0.1", port=4001, max_connections=10):
        self.host = host
        self.port = port
        self.max_connections = max_connections
        self.connections = []
        self.available = []
        self.lock = threading.Lock()
        # Use random client IDs to avoid conflicts
        import random
        self._client_id_base = random.randint(1000, 9000)
        
    async def get_connection(self) -> 'IBAsyncConnectorEnhanced':
        """Get an available connection from the pool"""
        with self.lock:
            if self.available:
                return self.available.pop()
            elif len(self.connections) < self.max_connections:
                # Create new connection with unique client ID
                client_id = self._client_id_base + len(self.connections)
                connector = IBAsyncConnectorEnhanced(
                    host=self.host,
                    port=self.port,
                    client_id=client_id,
                    is_pooled=True
                )
                if await connector.connect():
                    self.connections.append(connector)
                    return connector
                else:
                    raise ConnectionError(f"Failed to create connection {client_id}")
            else:
                # Wait for a connection to become available
                # In production, implement proper waiting logic
                raise ConnectionError("No connections available")
    
    def release_connection(self, connector: 'IBAsyncConnectorEnhanced'):
        """Return connection to the pool"""
        with self.lock:
            if connector in self.connections:
                self.available.append(connector)
    
    async def close_all(self):
        """Close all connections in the pool"""
        for conn in self.connections:
            await conn.disconnect()
        self.connections.clear()
        self.available.clear()


class IBAsyncConnectorEnhanced:
    """
    Enhanced IB connector using ib_async library with backward compatibility.
    Maintains the same data format as the original ib_connector.py.
    """
    
    def __init__(self, host="127.0.0.1", port=4001, client_id=1, is_pooled=False):
        """
        Initialize IB async connector.
        
        Args:
            host: IB Gateway host (default localhost)
            port: IB Gateway port (4001 for live, 4002 for paper)
            client_id: Unique client ID
            is_pooled: Whether this connection is part of a pool
        """
        self.host = host
        self.port = port
        self.client_id = client_id
        self.ib = IB()
        self._connected = False
        self.is_pooled = is_pooled
        
        # Set up error handling
        self.ib.errorEvent += self._on_error
        self.ib.disconnectedEvent += self._on_disconnect
        
        # Cache management
        self.cache_enabled = True
        self.cache_duration_minutes = 60  # 1 hour default
        
    def _on_error(self, reqId, errorCode, errorString, contract):
        """Handle IB errors"""
        # Info codes that should not be treated as errors
        info_codes = [2104, 2106, 2107, 2108, 2158, 2174]
        
        if errorCode in info_codes:
            logger.info(f"IB Info {errorCode}: {errorString}")
        else:
            logger.warning(f"IB Error {errorCode}: {errorString}")
            
    def _on_disconnect(self):
        """Handle disconnection"""
        logger.warning(f"IB connection lost (client_id={self.client_id})")
        self._connected = False
        
    async def connect(self) -> bool:
        """
        Connect to IB Gateway with automatic retry.
        
        Returns:
            bool: True if connected successfully
        """
        if self._connected and self.ib.isConnected():
            return True
            
        try:
            logger.info(f"Connecting to IB Gateway at {self.host}:{self.port} (client_id={self.client_id})")
            await self.ib.connectAsync(self.host, self.port, clientId=self.client_id, timeout=20)
            
            # Wait a moment for connection to stabilize
            await asyncio.sleep(1)
            
            # Request delayed market data by default (free for all accounts)
            self.ib.reqMarketDataType(3)
            
            if self.ib.isConnected():
                self._connected = True
                logger.info(f"✅ Connected to IB Gateway (client_id={self.client_id}, server version: {self.ib.client.serverVersion()})")
                return True
            else:
                logger.error(f"❌ Failed to connect to IB Gateway (client_id={self.client_id})")
                return False
                
        except Exception as e:
            logger.error(f"❌ Connection error (client_id={self.client_id}): {e}")
            return False
    
    def connect_sync(self) -> bool:
        """Synchronous wrapper for connect()"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.connect())
        finally:
            loop.close()
            
    async def disconnect(self):
        """Disconnect from IB Gateway"""
        if self.ib.isConnected():
            self.ib.disconnect()
            self._connected = False
            logger.info(f"Disconnected from IB Gateway (client_id={self.client_id})")
    
    def disconnect_sync(self):
        """Synchronous wrapper for disconnect()"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.disconnect())
        finally:
            loop.close()
            
    def is_connected(self) -> bool:
        """Check if connected to IB Gateway"""
        return self._connected and self.ib.isConnected()
        
    async def _ensure_connected(self) -> bool:
        """Ensure connection is active, reconnect if needed"""
        if not self.is_connected():
            logger.info(f"Connection lost (client_id={self.client_id}), attempting to reconnect...")
            return await self.connect()
        return True

    async def _check_cache(self, symbol: str, data_type: str, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """Check if we have cached data for the requested range"""
        if not self.cache_enabled:
            return None
            
        # Query cache metadata
        with engine.connect() as conn:
            if data_type == "minute":
                table = stock_bars_minute
            elif data_type == "daily":
                table = stock_bars_daily
            else:
                return None
                
            # Check if we have data for the exact range
            query = select(
                func.min(table.c.timestamp).label('min_date'),
                func.max(table.c.timestamp).label('max_date'),
                func.count(table.c.id).label('count')
            ).where(
                and_(
                    table.c.symbol == symbol,
                    table.c.timestamp >= start_date,
                    table.c.timestamp <= end_date
                )
            )
            
            result = conn.execute(query).fetchone()
            
            if result and result['count'] > 0:
                # Check if data is recent enough
                cache_age = datetime.now() - result['max_date']
                if cache_age.total_seconds() / 60 < self.cache_duration_minutes:
                    # Fetch the cached data
                    query = select(table).where(
                        and_(
                            table.c.symbol == symbol,
                            table.c.timestamp >= start_date,
                            table.c.timestamp <= end_date
                        )
                    ).order_by(table.c.timestamp)
                    
                    df = pd.read_sql(query, conn, index_col='timestamp')
                    logger.info(f"Using cached {data_type} data for {symbol}: {len(df)} bars")
                    return df
                    
        return None

    async def _save_to_cache(self, df: pd.DataFrame, symbol: str, data_type: str):
        """Save data to cache database"""
        if not self.cache_enabled or df.empty:
            return
            
        try:
            with engine.connect() as conn:
                if data_type == "minute":
                    table = stock_bars_minute
                elif data_type == "daily":
                    table = stock_bars_daily
                else:
                    return
                    
                # Prepare data for insertion
                df_copy = df.copy()
                df_copy['symbol'] = symbol
                df_copy['source'] = 'ib_async'
                df_copy['connection_id'] = self.client_id
                df_copy['created_at'] = datetime.utcnow()
                
                # Reset index to make timestamp a column
                df_copy = df_copy.reset_index()
                
                # Insert with conflict resolution (update on duplicate)
                # This is PostgreSQL specific - adjust for other databases
                from sqlalchemy.dialects.postgresql import insert
                stmt = insert(table).values(df_copy.to_dict('records'))
                stmt = stmt.on_conflict_do_update(
                    index_elements=['symbol', 'timestamp'],
                    set_={
                        'open': stmt.excluded.open,
                        'high': stmt.excluded.high,
                        'low': stmt.excluded.low,
                        'close': stmt.excluded.close,
                        'volume': stmt.excluded.volume,
                        'updated_at': datetime.utcnow()
                    }
                )
                conn.execute(stmt)
                conn.commit()
                
                logger.info(f"Cached {len(df)} {data_type} bars for {symbol}")
                
        except Exception as e:
            logger.error(f"Failed to cache data: {e}")

    async def get_historical_data(
        self, 
        symbol: str, 
        duration: str, 
        bar_size: str,
        data_type: str = "TRADES",
        end_date: str = "",
        max_retries: int = 3
    ) -> pd.DataFrame:
        """
        Get historical data with caching and IB volume conversion.
        Maintains compatibility with original ib_connector.py.
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            duration: Duration string (e.g., '1 D', '30 D', '1 Y')
            bar_size: Bar size (e.g., '1 min', '1 day')
            data_type: Type of data ('TRADES', 'BID', 'ASK', 'MIDPOINT')
            end_date: End date/time for historical data (empty string for current time)
            max_retries: Number of retry attempts on failure
            
        Returns:
            pd.DataFrame: DataFrame with OHLCV data in Alpaca-compatible format
        """
        if not await self._ensure_connected():
            raise ConnectionError("Cannot connect to IB Gateway")
            
        # Determine cache data type
        cache_type = "minute" if "min" in bar_size else "daily"
        
        # Parse end date for caching
        if end_date:
            end_dt = pd.to_datetime(end_date.replace(" US/Eastern", ""))
        else:
            end_dt = datetime.now()
            
        # Calculate start date from duration
        duration_parts = duration.split()
        duration_num = int(duration_parts[0])
        duration_unit = duration_parts[1].upper()
        
        if duration_unit == 'D':
            start_dt = end_dt - timedelta(days=duration_num)
        elif duration_unit == 'W':
            start_dt = end_dt - timedelta(weeks=duration_num)
        elif duration_unit == 'M':
            start_dt = end_dt - timedelta(days=duration_num * 30)
        elif duration_unit == 'Y':
            start_dt = end_dt - timedelta(days=duration_num * 365)
        else:
            start_dt = end_dt - timedelta(days=30)  # Default
            
        # Check cache first
        cached_df = await self._check_cache(symbol, cache_type, start_dt, end_dt)
        if cached_df is not None:
            return self.convert_to_alpaca_format(cached_df)
            
        # Fetch from IB
        for attempt in range(max_retries + 1):
            try:
                # Create contract
                contract = Stock(symbol, 'SMART', 'USD')
                
                # Qualify the contract to get full details
                await self.ib.qualifyContractsAsync(contract)
                
                # Request historical data
                logger.info(f"Requesting {duration} of {bar_size} bars for {symbol} (attempt {attempt + 1})")
                
                bars = await self.ib.reqHistoricalDataAsync(
                    contract=contract,
                    endDateTime=end_date or '',
                    durationStr=duration,
                    barSizeSetting=bar_size,
                    whatToShow=data_type,
                    useRTH=False,  # Include extended hours like original
                    formatDate=1,
                    keepUpToDate=False,
                    timeout=45  # Increased timeout
                )
                
                if not bars:
                    logger.warning(f"No historical data received for {symbol}")
                    if attempt < max_retries:
                        wait_time = 2 * (attempt + 1)
                        logger.warning(f"Retrying in {wait_time}s...")
                        await asyncio.sleep(wait_time)
                        continue
                    return pd.DataFrame()
                    
                # Convert to DataFrame
                df = util.df(bars)
                
                if df.empty:
                    logger.warning(f"Empty DataFrame for {symbol}")
                    return df
                    
                # Set datetime index
                df.set_index('date', inplace=True)
                df.index = pd.to_datetime(df.index)
                
                # Add symbol column
                df['symbol'] = symbol
                
                # Save to cache
                await self._save_to_cache(df, symbol, cache_type)
                
                # Convert to Alpaca format (includes volume scaling)
                return self.convert_to_alpaca_format(df)
                
            except Exception as e:
                logger.error(f"Error getting historical data for {symbol} (attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    wait_time = 2 * (attempt + 1)
                    logger.warning(f"Retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
                    continue
                raise

    def get_historical_data_sync(
        self, 
        symbol: str, 
        duration: str, 
        bar_size: str,
        data_type: str = "TRADES",
        end_date: str = "",
        max_retries: int = 3
    ) -> pd.DataFrame:
        """Synchronous wrapper for get_historical_data"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.get_historical_data(symbol, duration, bar_size, data_type, end_date, max_retries)
            )
        finally:
            loop.close()

    def get_minute_bars(self, symbol: str, days: int = 1, end_date: str = "") -> pd.DataFrame:
        """
        Get minute bars for a symbol (backward compatible).
        
        Args:
            symbol: Stock symbol
            days: Number of days of data (max 30 for minute data)
            end_date: End date string (empty = current time)
            
        Returns:
            DataFrame with minute OHLCV data
        """
        # CRITICAL FIX: IB single-day requests corrupt timestamps
        # Force minimum 2 days to get proper date ranges, then filter to requested days
        original_days = days
        effective_days = max(days, 2)  # Always request at least 2 days
        
        if original_days == 1 and effective_days == 2:
            logger.info(f"TIMESTAMP FIX: Requesting {effective_days} days instead of {original_days} to avoid IB timestamp corruption")
        
        # IB limits minute data to 30 days
        days = min(effective_days, 30)
        duration = f"{days} D"
        
        bars = self.get_historical_data_sync(symbol, duration, "1 min", end_date=end_date)
        
        # If we requested extra days for the timestamp fix, filter back to original request
        if original_days == 1 and not bars.empty and len(bars) > 0:
            # Get the most recent trading day only
            unique_dates = bars.index.date
            if len(set(unique_dates)) > 1:
                latest_date = max(unique_dates)
                bars_filtered = bars[bars.index.date == latest_date]
                logger.info(f"TIMESTAMP FIX SUCCESS: Filtered {len(bars)} bars to {len(bars_filtered)} bars for latest date {latest_date}")
                return bars_filtered
            else:
                logger.warning(f"TIMESTAMP CORRUPTION DETECTED: All {len(bars)} bars have same date despite requesting {effective_days} days")
        
        return bars

    def get_daily_bars(self, symbol: str, days: int = 30) -> pd.DataFrame:
        """
        Get daily bars for a symbol (backward compatible).
        
        Args:
            symbol: Stock symbol
            days: Number of days of data
            
        Returns:
            DataFrame with daily OHLCV data
        """
        # IB API rule: For periods > 365 days, use years instead of days
        if days > 365:
            years = int(days / 365.25)
            duration = f"{years} Y"
            logger.info(f"Requesting {days} days ({duration}) of historical data for {symbol}")
        else:
            duration = f"{days} D"
            logger.info(f"Requesting {duration} of historical data for {symbol}")
            
        return self.get_historical_data_sync(symbol, duration, "1 day")

    def convert_to_alpaca_format(self, ib_df: pd.DataFrame) -> pd.DataFrame:
        """
        Convert IB data format to match Alpaca format with proper volume scaling.
        
        CRITICAL: IB returns volume in lots for US stocks - must multiply by 100!
        
        Args:
            ib_df: DataFrame from IB
            
        Returns:
            DataFrame in Alpaca format with correct volume
        """
        if ib_df.empty:
            return ib_df
            
        # Create a copy to avoid modifying original
        alpaca_df = ib_df.copy()
        
        # Rename columns to match Alpaca
        rename_map = {
            'barCount': 'trade_count',
            'average': 'vwap'  # IB uses 'average' instead of 'wap'
        }
        
        # Only rename columns that exist
        rename_map = {k: v for k, v in rename_map.items() if k in alpaca_df.columns}
        if rename_map:
            alpaca_df = alpaca_df.rename(columns=rename_map)
        
        # CRITICAL FIX: IB volume scaling for US stocks
        # Per IB documentation: "Volume for US Stocks are quoted in lots.
        # The actual number of shares in volume can be calculated by multiplying 100."
        if "volume" in alpaca_df.columns:
            # Multiply by 100 for US stocks (IB reports in lots, not shares)
            alpaca_df["volume"] = (alpaca_df["volume"] * 100).astype(int)
            logger.debug("Applied IB volume scaling (x100) for US stocks")
        
        # Ensure proper column order and types
        essential_cols = ['open', 'high', 'low', 'close', 'volume']
        available_cols = [col for col in essential_cols if col in alpaca_df.columns]
        
        # Add optional columns if they exist
        optional_cols = ['vwap', 'trade_count', 'symbol']
        available_cols.extend([col for col in optional_cols if col in alpaca_df.columns])
        
        # Select only available columns
        alpaca_df = alpaca_df[available_cols]
        
        # Ensure proper data types
        numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'vwap', 'trade_count']
        for col in numeric_cols:
            if col in alpaca_df.columns:
                alpaca_df[col] = pd.to_numeric(alpaca_df[col], errors='coerce')
                
        logger.info(f"Converted {len(alpaca_df)} bars to Alpaca format")
        return alpaca_df

    # Additional methods for tick data compatibility
    async def get_historical_ticks(
        self,
        symbol: str,
        start_time: str,
        end_time: str = "",
        tick_type: str = "TRADES"
    ) -> pd.DataFrame:
        """Get historical tick data (not fully implemented in ib_async yet)"""
        logger.warning("Historical tick data not fully implemented in ib_async connector")
        return pd.DataFrame()

    def get_historical_ticks_batch(
        self,
        symbol: str,
        start_time: str,
        end_time: str = "",
        max_ticks_per_request: int = 1000
    ) -> pd.DataFrame:
        """Get historical tick data in batches (not fully implemented)"""
        logger.warning("Historical tick batch not fully implemented in ib_async connector")
        return pd.DataFrame()


# Global connection pool for shared use
_global_connection_pool: Optional[ConnectionPool] = None

def get_connection_pool(host="127.0.0.1", port=4001, max_connections=10) -> ConnectionPool:
    """
    Get or create global connection pool.
    
    Args:
        host: IB Gateway host
        port: IB Gateway port
        max_connections: Maximum number of concurrent connections
        
    Returns:
        ConnectionPool instance
    """
    global _global_connection_pool
    
    if _global_connection_pool is None:
        logger.info(f"Creating connection pool with max {max_connections} connections")
        _global_connection_pool = ConnectionPool(host, port, max_connections)
        
    return _global_connection_pool

async def get_pooled_connection() -> IBAsyncConnectorEnhanced:
    """
    Get a connection from the pool for temporary use.
    Remember to release it when done!
    
    Returns:
        IBAsyncConnectorEnhanced instance
    """
    pool = get_connection_pool()
    return await pool.get_connection()

def release_pooled_connection(connector: IBAsyncConnectorEnhanced):
    """
    Return a connection to the pool.
    
    Args:
        connector: The connector to release
    """
    if connector.is_pooled:
        pool = get_connection_pool()
        pool.release_connection(connector)

async def parallel_data_fetch(symbols: List[str], duration: str = "30 D", bar_size: str = "1 day") -> Dict[str, pd.DataFrame]:
    """
    Fetch data for multiple symbols in parallel using connection pool.
    
    Args:
        symbols: List of stock symbols
        duration: Duration string
        bar_size: Bar size
        
    Returns:
        Dictionary mapping symbols to DataFrames
    """
    pool = get_connection_pool()
    results = {}
    
    async def fetch_symbol(symbol: str):
        connector = await pool.get_connection()
        try:
            df = await connector.get_historical_data(symbol, duration, bar_size)
            results[symbol] = df
        except Exception as e:
            logger.error(f"Failed to fetch data for {symbol}: {e}")
            results[symbol] = pd.DataFrame()
        finally:
            pool.release_connection(connector)
    
    # Run all fetches in parallel
    await asyncio.gather(*[fetch_symbol(symbol) for symbol in symbols])
    
    return results

def parallel_data_fetch_sync(symbols: List[str], duration: str = "30 D", bar_size: str = "1 day") -> Dict[str, pd.DataFrame]:
    """Synchronous wrapper for parallel_data_fetch"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(parallel_data_fetch(symbols, duration, bar_size))
    finally:
        loop.close()

# Cleanup function
async def cleanup_connection_pool():
    """Clean up all connections in the pool"""
    global _global_connection_pool
    if _global_connection_pool:
        await _global_connection_pool.close_all()
        _global_connection_pool = None
        logger.info("Cleaned up connection pool")

def cleanup_connection_pool_sync():
    """Synchronous wrapper for cleanup"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(cleanup_connection_pool())
    finally:
        loop.close()