"""
Centralized database configuration with proper path resolution.
This ensures all tests and modules use the same database path.
"""

import os
from pathlib import Path

# Get the project root directory (seeking_alpha_research)
PROJECT_ROOT = Path(__file__).parent.parent.absolute()

# Database file location - use absolute path
DATABASE_PATH = PROJECT_ROOT / "seeking_alpha.db"
DATABASE_URL = f"sqlite:///{DATABASE_PATH}"

# Alternative database paths (for migration/compatibility)
LEGACY_DATABASE_PATH = PROJECT_ROOT / "seeking_alpha_research" / "seeking_alpha.db"
TRADING_SYSTEM_DB_PATH = PROJECT_ROOT / "trading_system.db"

# Cache directories
CACHE_DIR = PROJECT_ROOT / "cache"
TEST_CACHE_DIR = PROJECT_ROOT / "test_cache"


def get_database_url():
    """
    Get the database URL, ensuring the directory exists.
    """
    # Ensure the database directory exists
    DATABASE_PATH.parent.mkdir(parents=True, exist_ok=True)

    # Check if we need to use legacy database
    if not DATABASE_PATH.exists() and LEGACY_DATABASE_PATH.exists():
        print(f"Using legacy database at {LEGACY_DATABASE_PATH}")
        return f"sqlite:///{LEGACY_DATABASE_PATH}"

    return DATABASE_URL


def ensure_database_exists():
    """
    Ensure the database file exists. If not, create it.
    """
    if not DATABASE_PATH.exists():
        # Try to copy from legacy location if it exists
        if LEGACY_DATABASE_PATH.exists():
            print(f"Migrating database from {LEGACY_DATABASE_PATH} to {DATABASE_PATH}")
            import shutil

            shutil.copy2(LEGACY_DATABASE_PATH, DATABASE_PATH)
            return DATABASE_PATH

    # Check if tables exist
    from sqlalchemy import create_engine, inspect

    engine = create_engine(DATABASE_URL)
    inspector = inspect(engine)

    # If no tables exist, create them
    if not inspector.get_table_names():
        print(f"Creating database tables at {DATABASE_PATH}")
        # Import here to avoid circular imports
        from . import database

        database.metadata.create_all(engine)
        print("Database tables created successfully.")

    return DATABASE_PATH


# For backward compatibility
def get_db_path():
    """Legacy function for compatibility."""
    return str(DATABASE_PATH)
