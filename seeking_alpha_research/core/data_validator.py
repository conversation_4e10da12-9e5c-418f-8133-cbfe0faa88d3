#!/usr/bin/env python3
"""
Data Validation Layer

Prevents silent failures by validating all market data before use.
CRITICAL for real money trading - bad data can cause massive losses.

REAL DATA ONLY - Validates actual market data, no mocks.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ValidationSeverity(Enum):
    """Severity levels for validation issues."""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationIssue:
    """Represents a data validation issue."""

    severity: ValidationSeverity
    field: str
    message: str
    value: Any = None
    expected: Any = None

    def __str__(self):
        return f"{self.severity.value.upper()}: {self.field} - {self.message}"


class DataValidator:
    """Comprehensive data validation for market data."""

    def __init__(self, strict_mode: bool = True):
        self.strict_mode = strict_mode
        self.issues: List[ValidationIssue] = []

    def reset(self):
        """Reset validation issues."""
        self.issues = []

    def add_issue(
        self,
        severity: ValidationSeverity,
        field: str,
        message: str,
        value: Any = None,
        expected: Any = None,
    ):
        """Add a validation issue."""
        issue = ValidationIssue(severity, field, message, value, expected)
        self.issues.append(issue)

        if severity == ValidationSeverity.CRITICAL:
            logger.critical(f"CRITICAL DATA ISSUE: {issue}")
        elif severity == ValidationSeverity.ERROR:
            logger.error(f"DATA ERROR: {issue}")
        elif severity == ValidationSeverity.WARNING:
            logger.warning(f"DATA WARNING: {issue}")

    def validate_price_data(self, bars: pd.DataFrame, symbol: str) -> bool:
        """
        Validate price bar data for anomalies.

        Args:
            bars: DataFrame with OHLCV data
            symbol: Stock symbol for context

        Returns:
            True if data is valid, False if critical issues found
        """
        if bars.empty:
            self.add_issue(
                ValidationSeverity.ERROR, "bars", f"Empty price data for {symbol}"
            )
            return False

        # Required columns
        required_cols = ["open", "high", "low", "close", "volume"]
        missing_cols = [col for col in required_cols if col not in bars.columns]
        if missing_cols:
            self.add_issue(
                ValidationSeverity.CRITICAL,
                "columns",
                f"Missing required columns: {missing_cols}",
            )
            return False

        # Check for reasonable price ranges
        for col in ["open", "high", "low", "close"]:
            prices = bars[col].dropna()
            if prices.empty:
                continue

            # Check for negative prices
            if (prices < 0).any():
                self.add_issue(
                    ValidationSeverity.CRITICAL, col, f"Negative prices found in {col}"
                )
                return False

            # Check for extremely high prices (> $10,000)
            if (prices > 10000).any():
                max_price = prices.max()
                self.add_issue(
                    ValidationSeverity.WARNING,
                    col,
                    f"Extremely high price: ${max_price:.2f}",
                )

            # Check for extremely low prices (< $0.01)
            if (prices < 0.01).any():
                min_price = prices.min()
                self.add_issue(
                    ValidationSeverity.WARNING,
                    col,
                    f"Extremely low price: ${min_price:.4f}",
                )

        # OHLC relationship validation
        for idx, row in bars.iterrows():
            high, low, open_price, close = (
                row["high"],
                row["low"],
                row["open"],
                row["close"],
            )

            # High should be >= all others
            if high < max(low, open_price, close):
                self.add_issue(
                    ValidationSeverity.ERROR,
                    "ohlc",
                    f"High ({high}) < max(low, open, close) on {idx}",
                )

            # Low should be <= all others
            if low > min(high, open_price, close):
                self.add_issue(
                    ValidationSeverity.ERROR,
                    "ohlc",
                    f"Low ({low}) > min(high, open, close) on {idx}",
                )

        # Volume validation
        volumes = bars["volume"].dropna()
        if not volumes.empty:
            # Check for negative volume
            if (volumes < 0).any():
                self.add_issue(
                    ValidationSeverity.CRITICAL, "volume", "Negative volume found"
                )
                return False

            # Check for suspiciously high volume (> 1B shares)
            if (volumes > 1_000_000_000).any():
                max_vol = volumes.max()
                self.add_issue(
                    ValidationSeverity.WARNING,
                    "volume",
                    f"Extremely high volume: {max_vol:,.0f}",
                )

        # Check for data gaps
        if len(bars) > 1:
            dates = pd.to_datetime(bars.index)
            date_diffs = dates.diff().dropna()

            # Look for gaps > 7 days (accounting for weekends)
            large_gaps = date_diffs[date_diffs > timedelta(days=7)]
            if not large_gaps.empty:
                self.add_issue(
                    ValidationSeverity.WARNING,
                    "dates",
                    f"Found {len(large_gaps)} large date gaps",
                )

        return (
            len([i for i in self.issues if i.severity == ValidationSeverity.CRITICAL])
            == 0
        )

    def validate_market_data_consistency(
        self, symbol: str, daily_bars: pd.DataFrame, minute_bars: pd.DataFrame = None
    ) -> bool:
        """Validate consistency between different timeframe data."""

        if daily_bars.empty:
            return True

        if minute_bars is not None and not minute_bars.empty:
            # Check if minute data aggregates to daily data
            daily_from_minute = minute_bars.groupby(minute_bars.index.date).agg(
                {
                    "open": "first",
                    "high": "max",
                    "low": "min",
                    "close": "last",
                    "volume": "sum",
                }
            )

            # Compare with actual daily data for overlapping dates
            common_dates = set(daily_bars.index.date) & set(daily_from_minute.index)

            for date in common_dates:
                daily_row = daily_bars[daily_bars.index.date == date].iloc[0]
                minute_agg = daily_from_minute.loc[date]

                # Check price consistency (allow small tolerance)
                tolerance = 0.02  # 2% tolerance

                for col in ["open", "high", "low", "close"]:
                    daily_val = daily_row[col]
                    minute_val = minute_agg[col]

                    if abs(daily_val - minute_val) / daily_val > tolerance:
                        self.add_issue(
                            ValidationSeverity.ERROR,
                            "consistency",
                            f"{col} mismatch on {date}: daily={daily_val:.2f}, "
                            f"minute_agg={minute_val:.2f}",
                        )

        return (
            len([i for i in self.issues if i.severity == ValidationSeverity.CRITICAL])
            == 0
        )

    def validate_filing_data(self, filing_data: Dict) -> bool:
        """Validate SEC filing data."""
        required_fields = ["symbol", "form_type", "filed_at", "content"]

        for field in required_fields:
            if field not in filing_data:
                self.add_issue(
                    ValidationSeverity.CRITICAL,
                    field,
                    f"Missing required filing field: {field}",
                )
                return False

        # Validate date format
        try:
            filed_date = pd.to_datetime(filing_data["filed_at"])

            # Check if filing date is reasonable (not in future, not too old)
            now = datetime.now()
            if filed_date > now:
                self.add_issue(
                    ValidationSeverity.ERROR,
                    "filed_at",
                    f"Filing date in future: {filed_date}",
                )

            if filed_date < datetime(2000, 1, 1):
                self.add_issue(
                    ValidationSeverity.WARNING,
                    "filed_at",
                    f"Very old filing date: {filed_date}",
                )

        except Exception as e:
            self.add_issue(
                ValidationSeverity.ERROR, "filed_at", f"Invalid date format: {e}"
            )

        # Validate content
        content = filing_data.get("content", "")
        if not content or len(content.strip()) < 100:
            self.add_issue(
                ValidationSeverity.WARNING,
                "content",
                "Filing content appears too short",
            )

        return (
            len([i for i in self.issues if i.severity == ValidationSeverity.CRITICAL])
            == 0
        )

    def validate_news_data(self, news_df: pd.DataFrame) -> bool:
        """Validate news data."""
        if news_df.empty:
            return True

        required_cols = ["headline", "summary", "created_at"]
        missing_cols = [col for col in required_cols if col not in news_df.columns]

        if missing_cols:
            self.add_issue(
                ValidationSeverity.ERROR,
                "news_columns",
                f"Missing news columns: {missing_cols}",
            )
            return False

        # Check for empty headlines
        empty_headlines = news_df["headline"].isna() | (news_df["headline"] == "")
        if empty_headlines.any():
            self.add_issue(
                ValidationSeverity.WARNING,
                "headlines",
                f"{empty_headlines.sum()} empty headlines found",
            )

        # Validate timestamps
        try:
            news_dates = pd.to_datetime(news_df["created_at"])
            future_dates = news_dates > datetime.now()
            if future_dates.any():
                self.add_issue(
                    ValidationSeverity.WARNING,
                    "news_dates",
                    f"{future_dates.sum()} news items with future dates",
                )
        except Exception as e:
            self.add_issue(
                ValidationSeverity.ERROR, "news_dates", f"Invalid news date format: {e}"
            )

        return True

    def validate_portfolio_data(self, positions: List[Dict]) -> bool:
        """Validate portfolio position data."""
        for i, position in enumerate(positions):
            # Required fields
            required_fields = ["symbol", "shares", "entry_price", "entry_date"]
            for field in required_fields:
                if field not in position:
                    self.add_issue(
                        ValidationSeverity.CRITICAL,
                        f"position_{i}",
                        f"Missing field: {field}",
                    )
                    return False

            # Validate values
            shares = position.get("shares", 0)
            if shares <= 0:
                self.add_issue(
                    ValidationSeverity.ERROR,
                    f"position_{i}_shares",
                    f"Invalid shares: {shares}",
                )

            entry_price = position.get("entry_price", 0)
            if entry_price <= 0:
                self.add_issue(
                    ValidationSeverity.ERROR,
                    f"position_{i}_price",
                    f"Invalid entry price: {entry_price}",
                )

            # Validate P&L if present
            if "pnl_percentage" in position:
                pnl_pct = position["pnl_percentage"]
                if abs(pnl_pct) > 1000:  # 1000% gain/loss seems extreme
                    self.add_issue(
                        ValidationSeverity.WARNING,
                        f"position_{i}_pnl",
                        f"Extreme P&L: {pnl_pct:.1f}%",
                    )

        return (
            len([i for i in self.issues if i.severity == ValidationSeverity.CRITICAL])
            == 0
        )

    def get_validation_summary(self) -> Dict[str, Any]:
        """Get summary of validation results."""
        by_severity = {}
        for severity in ValidationSeverity:
            by_severity[severity.value] = len(
                [i for i in self.issues if i.severity == severity]
            )

        return {
            "total_issues": len(self.issues),
            "by_severity": by_severity,
            "critical_issues": [
                str(i) for i in self.issues if i.severity == ValidationSeverity.CRITICAL
            ],
            "error_issues": [
                str(i) for i in self.issues if i.severity == ValidationSeverity.ERROR
            ],
            "has_critical_issues": by_severity["critical"] > 0,
            "has_errors": by_severity["error"] > 0,
        }

    def should_proceed(self) -> bool:
        """Check if data is valid enough to proceed with trading/analysis."""
        critical_count = len(
            [i for i in self.issues if i.severity == ValidationSeverity.CRITICAL]
        )
        error_count = len(
            [i for i in self.issues if i.severity == ValidationSeverity.ERROR]
        )

        if critical_count > 0:
            return False

        if self.strict_mode and error_count > 0:
            return False

        return True


def validate_market_data_pipeline(
    data_service, symbol: str, start_date: str, end_date: str
) -> bool:
    """
    Full pipeline validation for market data.

    Args:
        data_service: DataService instance
        symbol: Stock symbol
        start_date: Start date
        end_date: End date

    Returns:
        True if all data passes validation
    """
    validator = DataValidator(strict_mode=True)

    try:
        # Get daily bars
        daily_bars = data_service.get_daily_bars(symbol, start_date, end_date)

        # Validate daily data
        if not validator.validate_price_data(daily_bars, symbol):
            logger.error(f"Daily price data validation failed for {symbol}")
            return False

        # Get news data if available
        try:
            news_df = data_service.get_news(symbol, start_date, end_date)
            validator.validate_news_data(news_df)
        except Exception as e:
            logger.warning(f"Could not validate news data for {symbol}: {e}")

        # Check validation results
        summary = validator.get_validation_summary()

        if summary["has_critical_issues"]:
            logger.error(
                f"CRITICAL data issues found for {symbol}: {summary['critical_issues']}"
            )
            return False

        if summary["has_errors"]:
            logger.warning(f"Data errors found for {symbol}: {summary['error_issues']}")

        logger.info(
            f"Data validation passed for {symbol}: {summary['total_issues']} total issues"
        )
        return validator.should_proceed()

    except Exception as e:
        logger.error(f"Data validation pipeline failed for {symbol}: {e}")
        return False


def test_data_validator():
    """Test the data validation system."""
    print("=== Testing Data Validator ===")

    validator = DataValidator()

    # Test valid price data
    valid_data = pd.DataFrame(
        {
            "open": [10.0, 10.5, 11.0],
            "high": [10.5, 11.0, 11.5],
            "low": [9.5, 10.0, 10.5],
            "close": [10.2, 10.8, 11.2],
            "volume": [100000, 150000, 120000],
        },
        index=pd.date_range("2024-01-01", periods=3),
    )

    result = validator.validate_price_data(valid_data, "TEST")
    print(f"Valid data test: {result}")

    # Test invalid data
    validator.reset()
    invalid_data = pd.DataFrame(
        {
            "open": [10.0, -5.0, 11.0],  # Negative price
            "high": [10.5, 11.0, 11.5],
            "low": [12.0, 10.0, 10.5],  # Low > High
            "close": [10.2, 10.8, 11.2],
            "volume": [100000, -50000, 120000],  # Negative volume
        },
        index=pd.date_range("2024-01-01", periods=3),
    )

    result = validator.validate_price_data(invalid_data, "TEST")
    print(f"Invalid data test: {result}")
    print(f"Issues found: {len(validator.issues)}")

    # Print validation summary
    summary = validator.get_validation_summary()
    print(f"Validation summary: {summary}")

    print("=== Data Validator Test Complete ===")


if __name__ == "__main__":
    test_data_validator()
