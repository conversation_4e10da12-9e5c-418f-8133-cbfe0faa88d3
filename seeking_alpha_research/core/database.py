import sqlalchemy

# Import Database class for compatibility
from .database_connection import Database
from .database_config import get_database_url, ensure_database_exists

DATABASE_URL = get_database_url()
engine = sqlalchemy.create_engine(DATABASE_URL)
metadata = sqlalchemy.MetaData()

stock_bars_daily = sqlalchemy.Table(
    "stock_bars_daily",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("symbol", sqlalchemy.String, nullable=False, index=True),
    sqlalchemy.Column("timestamp", sqlalchemy.DateTime, nullable=False),
    sqlalchemy.Column("open", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("high", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("low", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("close", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("volume", sqlalchemy.Integer, nullable=False),
    sqlalchemy.Column("trade_count", sqlalchemy.Integer),
    sqlalchemy.Column("vwap", sqlalchemy.Float),
    sqlalchemy.UniqueConstraint("symbol", "timestamp", name="uq_symbol_timestamp"),
)

stock_bars_minute = sqlalchemy.Table(
    "stock_bars_minute",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("symbol", sqlalchemy.String, nullable=False, index=True),
    sqlalchemy.Column("timestamp", sqlalchemy.DateTime, nullable=False, index=True),
    sqlalchemy.Column("open", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("high", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("low", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("close", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("volume", sqlalchemy.Integer, nullable=False),
    sqlalchemy.Column("trade_count", sqlalchemy.Integer),
    sqlalchemy.Column("vwap", sqlalchemy.Float),
    sqlalchemy.UniqueConstraint(
        "symbol", "timestamp", name="uq_minute_symbol_timestamp"
    ),
)

stock_news = sqlalchemy.Table(
    "stock_news",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("symbol", sqlalchemy.String, nullable=False, index=True),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime, nullable=False),
    sqlalchemy.Column("headline", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("summary", sqlalchemy.Text),
    sqlalchemy.Column("url", sqlalchemy.String),
    sqlalchemy.Column("source", sqlalchemy.String),
)

sec_filings = sqlalchemy.Table(
    "sec_filings",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("symbol", sqlalchemy.String, nullable=False, index=True),
    sqlalchemy.Column(
        "accession_number", sqlalchemy.String, unique=True, nullable=False
    ),
    sqlalchemy.Column("filed_at", sqlalchemy.DateTime, nullable=False),
    sqlalchemy.Column("form_type", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("report_url", sqlalchemy.String),
    sqlalchemy.Column("filing_url", sqlalchemy.String),
)

llm_filing_analysis = sqlalchemy.Table(
    "llm_filing_analysis",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column(
        "filing_id", sqlalchemy.ForeignKey("sec_filings.id"), nullable=False
    ),
    sqlalchemy.Column("cash_burn_m_usd", sqlalchemy.Float),
    sqlalchemy.Column("cash_on_hand_m_usd", sqlalchemy.Float),
    sqlalchemy.Column("has_active_atm", sqlalchemy.Boolean),
    sqlalchemy.Column("atm_details", sqlalchemy.Text),
    sqlalchemy.Column("confidence_score", sqlalchemy.Float),
    sqlalchemy.Column("raw_analysis", sqlalchemy.Text),
)

corporate_actions = sqlalchemy.Table(
    "corporate_actions",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("symbol", sqlalchemy.String, nullable=False, index=True),
    sqlalchemy.Column(
        "action_type", sqlalchemy.String, nullable=False
    ),  # split, dividend, delisting
    sqlalchemy.Column("ex_date", sqlalchemy.Date, nullable=False),
    sqlalchemy.Column("record_date", sqlalchemy.Date),
    sqlalchemy.Column("payment_date", sqlalchemy.Date),
    sqlalchemy.Column("ratio", sqlalchemy.Float),  # for splits
    sqlalchemy.Column("amount", sqlalchemy.Float),  # for dividends
    sqlalchemy.Column("cash_amount", sqlalchemy.Float),  # cash component for dividends
    sqlalchemy.Column("description", sqlalchemy.Text),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime, default=sqlalchemy.func.now()),
    sqlalchemy.UniqueConstraint(
        "symbol", "action_type", "ex_date", name="uq_corporate_action"
    ),
)

# Table for storing tick data
stock_ticks = sqlalchemy.Table(
    "stock_ticks",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True, autoincrement=True),
    sqlalchemy.Column("symbol", sqlalchemy.String(10), nullable=False, index=True),
    sqlalchemy.Column("timestamp", sqlalchemy.DateTime, nullable=False, index=True),
    sqlalchemy.Column("price", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("size", sqlalchemy.Integer, nullable=False),
    sqlalchemy.Column("exchange", sqlalchemy.String(10)),
    sqlalchemy.Column("conditions", sqlalchemy.String(50)),
    sqlalchemy.Column("bid", sqlalchemy.Float),
    sqlalchemy.Column("ask", sqlalchemy.Float),
    sqlalchemy.Column("bid_size", sqlalchemy.Integer),
    sqlalchemy.Column("ask_size", sqlalchemy.Integer),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime, default=sqlalchemy.func.now()),
    sqlalchemy.Index("idx_tick_symbol_timestamp", "symbol", "timestamp"),
)

# Table for tick analysis results
tick_analysis = sqlalchemy.Table(
    "tick_analysis",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True, autoincrement=True),
    sqlalchemy.Column("symbol", sqlalchemy.String(10), nullable=False, index=True),
    sqlalchemy.Column("analysis_date", sqlalchemy.Date, nullable=False, index=True),
    sqlalchemy.Column(
        "time_window", sqlalchemy.String(20), nullable=False
    ),  # e.g., "09:30-10:00"
    sqlalchemy.Column("avg_price", sqlalchemy.Float),
    sqlalchemy.Column("price_volatility", sqlalchemy.Float),
    sqlalchemy.Column("total_volume", sqlalchemy.Integer),
    sqlalchemy.Column("avg_trade_size", sqlalchemy.Float),
    sqlalchemy.Column("max_trade_size", sqlalchemy.Integer),
    sqlalchemy.Column(
        "large_trades", sqlalchemy.Integer
    ),  # Count of unusually large trades
    sqlalchemy.Column("rapid_trades", sqlalchemy.Integer),  # Trades within 1 second
    sqlalchemy.Column(
        "rapid_trade_ratio", sqlalchemy.Float
    ),  # Percentage of rapid trades
    sqlalchemy.Column(
        "insider_score", sqlalchemy.Float
    ),  # 0-1 score for insider trading likelihood
    sqlalchemy.Column("analysis_metadata", sqlalchemy.JSON),  # Additional analysis data
    sqlalchemy.Column("created_at", sqlalchemy.DateTime, default=sqlalchemy.func.now()),
    sqlalchemy.UniqueConstraint(
        "symbol", "analysis_date", "time_window", name="uq_tick_analysis"
    ),
)

# Table for AllLast tick data (includes off-exchange trades)
stock_ticks_alllast = sqlalchemy.Table(
    "stock_ticks_alllast",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True, autoincrement=True),
    sqlalchemy.Column("symbol", sqlalchemy.String(10), nullable=False, index=True),
    sqlalchemy.Column("timestamp", sqlalchemy.DateTime, nullable=False, index=True),
    sqlalchemy.Column("price", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("size", sqlalchemy.Integer, nullable=False),
    sqlalchemy.Column("exchange", sqlalchemy.String(10)),
    sqlalchemy.Column("conditions", sqlalchemy.String(50)),
    sqlalchemy.Column("tick_type", sqlalchemy.String(10), default="ALLLAST"),  # ALLLAST, TRADES, etc.
    sqlalchemy.Column("created_at", sqlalchemy.DateTime, default=sqlalchemy.func.now()),
    sqlalchemy.Index("idx_alllast_symbol_timestamp", "symbol", "timestamp"),
    sqlalchemy.UniqueConstraint(
        "symbol", "timestamp", "price", "size", name="uq_alllast_tick"
    ),
)

# Table for dark pool analysis results
dark_pool_analysis = sqlalchemy.Table(
    "dark_pool_analysis",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True, autoincrement=True),
    sqlalchemy.Column("symbol", sqlalchemy.String(10), nullable=False, index=True),
    sqlalchemy.Column("analysis_date", sqlalchemy.Date, nullable=False, index=True),
    sqlalchemy.Column("lookback_days", sqlalchemy.Integer, nullable=False),
    sqlalchemy.Column("minute_bars_analyzed", sqlalchemy.Integer),
    sqlalchemy.Column("trading_days_analyzed", sqlalchemy.Integer),
    sqlalchemy.Column("dark_pool_confidence", sqlalchemy.Float),  # 0-1 confidence score
    sqlalchemy.Column("avg_eod_volume_ratio", sqlalchemy.Float),  # End-of-day volume concentration
    sqlalchemy.Column("max_eod_spike", sqlalchemy.Float),  # Maximum EOD volume spike
    sqlalchemy.Column("unusual_eod_days", sqlalchemy.Integer),  # Days with unusual EOD activity
    sqlalchemy.Column("block_trades_detected", sqlalchemy.Integer),  # Number of block trades
    sqlalchemy.Column("volume_skewness", sqlalchemy.Float),  # Volume distribution skewness
    sqlalchemy.Column("volume_kurtosis", sqlalchemy.Float),  # Volume distribution kurtosis
    sqlalchemy.Column("total_ticks_analyzed", sqlalchemy.Integer),  # AllLast ticks analyzed
    sqlalchemy.Column("off_exchange_ratio", sqlalchemy.Float),  # Estimated off-exchange ratio
    sqlalchemy.Column("summary", sqlalchemy.Text),  # Human-readable summary
    sqlalchemy.Column("raw_analysis", sqlalchemy.JSON),  # Full analysis results
    sqlalchemy.Column("created_at", sqlalchemy.DateTime, default=sqlalchemy.func.now()),
    sqlalchemy.UniqueConstraint(
        "symbol", "analysis_date", "lookback_days", name="uq_dark_pool_analysis"
    ),
)

# Table for SEC filing text cache
sec_filing_text_cache = sqlalchemy.Table(
    "sec_filing_text_cache",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True, autoincrement=True),
    sqlalchemy.Column(
        "filing_id", sqlalchemy.ForeignKey("sec_filings.id"), nullable=False, unique=True
    ),
    sqlalchemy.Column("accession_number", sqlalchemy.String, nullable=False, index=True),
    sqlalchemy.Column("filing_url", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("filing_text", sqlalchemy.Text, nullable=False),  # Full filing text
    sqlalchemy.Column("text_length", sqlalchemy.Integer, nullable=False),  # Character count
    sqlalchemy.Column("download_date", sqlalchemy.DateTime, nullable=False),
    sqlalchemy.Column("source", sqlalchemy.String),  # 'edgar', 'alpaca', 'url_fetch'
    sqlalchemy.Column("text_hash", sqlalchemy.String(64)),  # SHA256 hash for deduplication
    sqlalchemy.Column("created_at", sqlalchemy.DateTime, default=sqlalchemy.func.now()),
    sqlalchemy.Column("updated_at", sqlalchemy.DateTime, default=sqlalchemy.func.now(), onupdate=sqlalchemy.func.now()),
    sqlalchemy.Index("idx_filing_text_accession", "accession_number"),
    sqlalchemy.Index("idx_filing_text_url", "filing_url"),
    sqlalchemy.Index("idx_filing_text_hash", "text_hash"),
)

# Table for strategy execution results
strategy_results = sqlalchemy.Table(
    "strategy_results",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True, autoincrement=True),
    sqlalchemy.Column("symbol", sqlalchemy.String(10), nullable=False, index=True),
    sqlalchemy.Column("target_date", sqlalchemy.Date, nullable=False, index=True),
    sqlalchemy.Column("execution_timestamp", sqlalchemy.DateTime, nullable=False),
    sqlalchemy.Column("bars_downloaded", sqlalchemy.Integer),
    sqlalchemy.Column("gaps_found", sqlalchemy.Integer),
    sqlalchemy.Column("max_gap_pct", sqlalchemy.Float),
    sqlalchemy.Column("gap_warning", sqlalchemy.String),  # Corporate action warnings
    sqlalchemy.Column("sec_filings_analyzed", sqlalchemy.Integer),
    sqlalchemy.Column("atm_probability", sqlalchemy.Float),
    sqlalchemy.Column("monthly_burn_rate", sqlalchemy.Float),
    sqlalchemy.Column("dark_pool_confidence", sqlalchemy.Float),
    sqlalchemy.Column("entry_score", sqlalchemy.Integer),
    sqlalchemy.Column("entry_recommended", sqlalchemy.Boolean),
    sqlalchemy.Column("strategy_type", sqlalchemy.String),
    sqlalchemy.Column("execution_time_seconds", sqlalchemy.Float),
    sqlalchemy.Column("full_result", sqlalchemy.JSON),  # Complete JSON result
    sqlalchemy.Column("created_at", sqlalchemy.DateTime, default=sqlalchemy.func.now()),
    sqlalchemy.Index("idx_strategy_symbol_date", "symbol", "target_date"),
)


def create_db_and_tables():
    """
    Creates the database and all the tables defined in the metadata.
    """
    try:
        metadata.create_all(engine)
        print("Database and tables created successfully.")
    except Exception as e:
        print(f"Error creating database or tables: {e}")


if __name__ == "__main__":
    create_db_and_tables()
