"""
DEPRECATED: IB Data Manager - Use IBConnector directly instead

This module is deprecated and not used in the main application.
The functionality has been consolidated into:
- IBConnector: Core IB connection and data fetching
- DataService: Main data service with IB integration
- IBDataService: IB-exclusive service for bars/ticks

This file is kept for reference but should not be used.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import sqlite3
import threading
import time

# Add IB API path
ib_api_path = "/Users/<USER>/PycharmProjects/stk_v5/lib/twsapi_macunix.1038.01/IBJts/source/pythonclient"
if ib_api_path not in sys.path:
    sys.path.append(ib_api_path)

try:
    from ibapi.client import EClient
    from ibapi.wrapper import EWrapper
    from ibapi.contract import Contract
    from ibapi.common import BarData

    IB_AVAILABLE = True
except ImportError:
    IB_AVAILABLE = False

from core.logger import get_logger

logger = get_logger(__name__)


class IBDataWrapper(EWrapper):
    """IB Wrapper for historical data with 1000 point handling."""

    def __init__(self):
        EWrapper.__init__(self)
        self.historical_data = {}
        self.data_end_events = {}
        self.errors = []

    def historicalData(self, reqId: int, bar: BarData):
        """Receive historical bar data."""
        if reqId not in self.historical_data:
            self.historical_data[reqId] = []

        bar_dict = {
            "date": bar.date,
            "open": bar.open,
            "high": bar.high,
            "low": bar.low,
            "close": bar.close,
            "volume": bar.volume,
            "wap": bar.wap,
            "count": bar.barCount,
        }

        self.historical_data[reqId].append(bar_dict)

    def historicalDataEnd(self, reqId: int, start: str, end: str):
        """Signal end of historical data."""
        logger.debug(f"Historical data end for reqId {reqId}: {start} to {end}")
        if reqId in self.data_end_events:
            self.data_end_events[reqId].set()

    def error(self, reqId, errorCode, errorString):
        """Handle errors."""
        error_msg = f"IB Error {errorCode} for reqId {reqId}: {errorString}"
        logger.warning(error_msg)
        self.errors.append({"reqId": reqId, "code": errorCode, "message": errorString})

        # Signal completion for certain errors
        if errorCode in [162, 200, 354] and reqId in self.data_end_events:
            self.data_end_events[reqId].set()


class IBDataClient(EClient):
    """IB Client for efficient data retrieval with chunking."""

    def __init__(self, wrapper):
        EClient.__init__(self, wrapper)
        self.connected = False
        self.next_req_id = 1000

    def connect_to_ib(self, host="localhost", port=4001, client_id=3):
        """Connect to IB Gateway."""
        try:
            self.connect(host, port, client_id)

            # Start API thread
            api_thread = threading.Thread(target=self.run, daemon=True)
            api_thread.start()

            # Wait for connection
            start_time = time.time()
            while not self.isConnected() and (time.time() - start_time) < 10:
                time.sleep(0.1)

            if self.isConnected():
                self.connected = True
                logger.info(f"Connected to IB at {host}:{port}")
                return True
            else:
                logger.error("Failed to connect to IB")
                return False

        except Exception as e:
            logger.error(f"IB connection error: {e}")
            return False

    def get_next_req_id(self):
        """Get next request ID."""
        req_id = self.next_req_id
        self.next_req_id += 1
        return req_id


class IBDataManager:
    """
    Comprehensive IB Data Manager with 1000 point limit handling.

    Features:
    1. Automatic chunking for large date ranges
    2. Append system to build multi-thousand point datasets
    3. Local SQLite caching for efficiency
    4. Conversion to Alpaca-compatible format
    """

    def __init__(self, db_path: str = "ib_data_cache.db"):
        self.db_path = db_path
        self.wrapper = IBDataWrapper()
        self.client = IBDataClient(self.wrapper)
        self.connected = False

        # Initialize database
        self._init_database()

        # Connect to IB
        if IB_AVAILABLE:
            self.connected = self.client.connect_to_ib()

        logger.info(f"IB Data Manager initialized. Connected: {self.connected}")

    def _init_database(self):
        """Initialize SQLite database for caching."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create tables for different data types
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS ib_bars (
                    symbol TEXT,
                    date TEXT,
                    timeframe TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume INTEGER,
                    wap REAL,
                    count INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (symbol, date, timeframe)
                )
            """
            )

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS ib_ticks (
                    symbol TEXT,
                    timestamp TEXT,
                    tick_type TEXT,
                    price REAL,
                    size INTEGER,
                    exchange TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (symbol, timestamp, tick_type)
                )
            """
            )

            conn.commit()
            conn.close()
            logger.info("IB data cache database initialized")

        except Exception as e:
            logger.error(f"Database initialization failed: {e}")

    def get_historical_bars_chunked(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        timeframe: str = "1 day",
        max_points: int = 950,
    ) -> pd.DataFrame:
        """
        Get historical bars with automatic chunking for 1000 point limit.

        Args:
            symbol: Stock symbol
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            timeframe: Bar size ("1 day", "1 hour", "5 mins", etc.)
            max_points: Maximum points per request (default 950 for safety)

        Returns:
            Combined DataFrame with all historical data
        """
        if not self.connected:
            logger.error("Not connected to IB")
            raise ValueError(
                "CRITICAL: No data available - cannot proceed without real data. Money is on the line!"
            )

        # Check cache first
        cached_data = self._get_cached_bars(symbol, start_date, end_date, timeframe)
        if not cached_data.empty:
            logger.info(f"Using cached data for {symbol} {timeframe}")
            return cached_data

        # Calculate date chunks
        chunks = self._calculate_date_chunks(
            start_date, end_date, timeframe, max_points
        )

        all_data = []

        for i, (chunk_start, chunk_end) in enumerate(chunks):
            logger.info(
                f"Fetching chunk {i+1}/{len(chunks)}: {chunk_start} to {chunk_end}"
            )

            chunk_data = self._fetch_single_chunk(
                symbol, chunk_start, chunk_end, timeframe
            )

            if not chunk_data.empty:
                all_data.append(chunk_data)
                # Cache each chunk
                self._cache_bars(chunk_data, symbol, timeframe)

            # Rate limiting between requests
            time.sleep(0.5)

        # Combine all chunks
        if all_data:
            combined = pd.concat(all_data, ignore_index=True)
            combined = combined.drop_duplicates(subset=["date"]).sort_values("date")

            logger.info(
                f"Retrieved {len(combined)} bars for {symbol} ({len(chunks)} chunks)"
            )
            return combined

        raise ValueError(
            "CRITICAL: No data available - cannot proceed without real data. Money is on the line!"
        )

    def _calculate_date_chunks(
        self, start_date: str, end_date: str, timeframe: str, max_points: int
    ) -> List[Tuple[str, str]]:
        """Calculate optimal date chunks to stay under 1000 point limit."""

        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)

        # Estimate points per day based on timeframe
        if "day" in timeframe:
            points_per_day = 1
        elif "hour" in timeframe:
            points_per_day = 24  # Assuming market hours
        elif "min" in timeframe:
            mins = int(timeframe.split()[0]) if timeframe.split()[0].isdigit() else 1
            points_per_day = 390 // mins  # Market minutes per day
        else:
            points_per_day = 10  # Conservative default

        # Calculate chunk size in days
        max_days_per_chunk = max(1, max_points // points_per_day)

        chunks = []
        current_start = start_dt

        while current_start < end_dt:
            current_end = min(
                current_start + timedelta(days=max_days_per_chunk), end_dt
            )

            chunks.append(
                (current_start.strftime("%Y-%m-%d"), current_end.strftime("%Y-%m-%d"))
            )

            current_start = current_end + timedelta(days=1)

        logger.info(f"Created {len(chunks)} chunks for {start_date} to {end_date}")
        return chunks

    def _fetch_single_chunk(
        self, symbol: str, start_date: str, end_date: str, timeframe: str
    ) -> pd.DataFrame:
        """Fetch a single chunk of data from IB."""

        try:
            req_id = self.client.get_next_req_id()

            # Create contract
            contract = Contract()
            contract.symbol = symbol
            contract.secType = "STK"
            contract.exchange = "SMART"
            contract.currency = "USD"

            # Set up event for completion
            self.wrapper.data_end_events[req_id] = threading.Event()

            # Request historical data
            end_datetime = pd.to_datetime(end_date).strftime("%Y%m%d %H:%M:%S")

            # Calculate duration
            duration_days = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days
            duration_str = f"{max(1, duration_days)} D"

            self.client.reqHistoricalData(
                reqId=req_id,
                contract=contract,
                endDateTime=end_datetime,
                durationStr=duration_str,
                barSizeSetting=timeframe,
                whatToShow="TRADES",
                useRTH=1,  # Regular trading hours
                formatDate=1,
                keepUpToDate=False,
                chartOptions=[],
            )

            # Wait for completion
            if self.wrapper.data_end_events[req_id].wait(timeout=30):
                raw_data = self.wrapper.historical_data.get(req_id, [])

                if raw_data:
                    df = pd.DataFrame(raw_data)
                    df["symbol"] = symbol
                    df["timeframe"] = timeframe
                    df["date"] = pd.to_datetime(df["date"])

                    return df

            # Cleanup
            if req_id in self.wrapper.data_end_events:
                del self.wrapper.data_end_events[req_id]
            if req_id in self.wrapper.historical_data:
                del self.wrapper.historical_data[req_id]

            raise ValueError(
                "CRITICAL: No data available - cannot proceed without real data. Money is on the line!"
            )

        except Exception as e:
            logger.error(f"Failed to fetch chunk {start_date} to {end_date}: {e}")
            raise ValueError(
                "CRITICAL: No data available - cannot proceed without real data. Money is on the line!"
            )

    def _get_cached_bars(
        self, symbol: str, start_date: str, end_date: str, timeframe: str
    ) -> pd.DataFrame:
        """Get cached bar data if available."""
        try:
            conn = sqlite3.connect(self.db_path)

            query = """
                SELECT * FROM ib_bars 
                WHERE symbol = ? AND timeframe = ? 
                AND date >= ? AND date <= ?
                ORDER BY date
            """

            df = pd.read_sql_query(
                query, conn, params=[symbol, timeframe, start_date, end_date]
            )
            conn.close()

            if not df.empty:
                df["date"] = pd.to_datetime(df["date"])

                # Check if we have complete coverage
                expected_start = pd.to_datetime(start_date)
                expected_end = pd.to_datetime(end_date)
                actual_start = df["date"].min()
                actual_end = df["date"].max()

                # If we have good coverage, return cached data
                if actual_start <= expected_start + timedelta(
                    days=1
                ) and actual_end >= expected_end - timedelta(days=1):
                    return df

            raise ValueError(
                "CRITICAL: No data available - cannot proceed without real data. Money is on the line!"
            )

        except Exception as e:
            logger.warning(f"Error reading cache: {e}")
            raise ValueError(
                "CRITICAL: No data available - cannot proceed without real data. Money is on the line!"
            )

    def _cache_bars(self, df: pd.DataFrame, symbol: str, timeframe: str):
        """Cache bar data to database."""
        try:
            conn = sqlite3.connect(self.db_path)

            # Prepare data for insertion
            cache_data = df.copy()
            cache_data["symbol"] = symbol
            cache_data["timeframe"] = timeframe
            cache_data["date"] = cache_data["date"].dt.strftime("%Y-%m-%d %H:%M:%S")

            # Insert with REPLACE to handle duplicates
            cache_data.to_sql(
                "ib_bars", conn, if_exists="append", index=False, method="multi"
            )

            conn.commit()
            conn.close()

            logger.debug(f"Cached {len(df)} bars for {symbol}")

        except Exception as e:
            logger.warning(f"Error caching data: {e}")

    def convert_to_alpaca_format(self, ib_data: pd.DataFrame) -> pd.DataFrame:
        """
        Convert IB bar data to Alpaca-compatible format.

        This allows seamless integration with existing Alpaca-based code.
        """
        if ib_data.empty:
            raise ValueError(
                "CRITICAL: No data available - cannot proceed without real data. Money is on the line!"
            )

        try:
            alpaca_format = ib_data.copy()

            # Rename columns to match Alpaca format
            column_mapping = {
                "date": "timestamp",
                "wap": "vwap",
                "count": "trade_count",
            }

            alpaca_format = alpaca_format.rename(columns=column_mapping)

            # Ensure proper data types
            alpaca_format["timestamp"] = pd.to_datetime(alpaca_format["timestamp"])
            alpaca_format["open"] = alpaca_format["open"].astype(float)
            alpaca_format["high"] = alpaca_format["high"].astype(float)
            alpaca_format["low"] = alpaca_format["low"].astype(float)
            alpaca_format["close"] = alpaca_format["close"].astype(float)
            alpaca_format["volume"] = alpaca_format["volume"].astype(int)

            # Add source identifier
            alpaca_format["source"] = "ib_converted"

            logger.info(f"Converted {len(alpaca_format)} IB bars to Alpaca format")
            return alpaca_format

        except Exception as e:
            logger.error(f"Error converting to Alpaca format: {e}")
            raise ValueError(
                "CRITICAL: No data available - cannot proceed without real data. Money is on the line!"
            )

    def get_large_dataset(
        self, symbol: str, start_date: str, end_date: str, timeframe: str = "1 day"
    ) -> pd.DataFrame:
        """
        Get large dataset that would exceed 1000 point limit.

        This is the main entry point for getting large amounts of historical data.
        """
        logger.info(
            f"Fetching large dataset for {symbol}: {start_date} to {end_date} ({timeframe})"
        )

        # Get data with chunking
        ib_data = self.get_historical_bars_chunked(
            symbol, start_date, end_date, timeframe
        )

        if ib_data.empty:
            logger.warning(f"No data retrieved for {symbol}")
            raise ValueError(
                "CRITICAL: No data available - cannot proceed without real data. Money is on the line!"
            )

        # Convert to Alpaca format for compatibility
        alpaca_format = self.convert_to_alpaca_format(ib_data)

        logger.info(
            f"Successfully retrieved {len(alpaca_format)} data points for {symbol}"
        )
        return alpaca_format

    def close(self):
        """Close IB connection and cleanup."""
        try:
            if self.client and self.connected:
                self.client.disconnect()
                self.connected = False
                logger.info("IB Data Manager disconnected")
        except Exception as e:
            logger.error(f"Error closing IB connection: {e}")


def test_ib_data_manager():
    """Test the IB Data Manager with 1000 point limit handling."""
    print("Testing IB Data Manager...")

    if not IB_AVAILABLE:
        print("❌ IB API not available")
        return

    manager = IBDataManager()

    if not manager.connected:
        print("❌ Could not connect to IB")
        return

    try:
        symbol = "AAPL"

        # Test 1: Small dataset (under 1000 points)
        print(f"\n1. Testing small dataset for {symbol}:")
        small_data = manager.get_historical_bars_chunked(
            symbol, "2024-01-01", "2024-01-31", "1 day"
        )
        print(f"   Retrieved {len(small_data)} daily bars")

        # Test 2: Large dataset (over 1000 points with chunking)
        print(f"\n2. Testing large dataset for {symbol}:")
        large_data = manager.get_large_dataset(
            symbol, "2020-01-01", "2024-01-01", "1 hour"
        )
        print(f"   Retrieved {len(large_data)} hourly bars (with chunking)")

        # Test 3: Alpaca format conversion
        print(f"\n3. Testing Alpaca format conversion:")
        if not large_data.empty:
            print(f"   Columns: {list(large_data.columns)}")
            print(f"   Data types: {large_data.dtypes.to_dict()}")
            print(f"   Sample data: {large_data.head(1).to_dict('records')}")

        print("\n✅ IB Data Manager test completed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
    finally:
        manager.close()


if __name__ == "__main__":
    test_ib_data_manager()
