"""
Enhanced DataService that uses the new IB async connector with connection pooling.
Maintains backward compatibility while enabling parallel data fetching.
"""

import os
import pandas as pd
from dotenv import load_dotenv
from sqlalchemy import create_engine, select, text, insert
from sqlalchemy.exc import IntegrityError
import requests
from alpaca.data.historical import StockHistoricalDataClient
from alpaca.data.requests import StockBarsRequest, CorporateActionsRequest, NewsRequest
from alpaca.data.timeframe import TimeFrame
from alpaca.trading.client import TradingClient
from datetime import datetime, timedelta
import edgar
import asyncio
from typing import List, Dict, Optional

# Import logging
from .logger import (
    get_logger,
    LogContext,
    log_api_call,
    log_data_fetch,
    log_error_with_context,
)
import functools
import time
from typing import Any, Callable, Dict, Optional
from alpaca.common.exceptions import APIError

# Import the enhanced IB connector
from .ib_async_connector_enhanced import (
    IBAsyncConnectorEnhanced,
    get_connection_pool,
    parallel_data_fetch_sync,
    cleanup_connection_pool_sync
)

# Import original DataService for inheritance
from .data_service import DataService, api_error_handler, safe_database_write

# Set up logger for this module
logger = get_logger(__name__)


class DataServiceEnhanced(DataService):
    """
    Enhanced DataService with connection pooling for parallel IB data fetching.
    Maintains backward compatibility with existing code.
    """
    
    def __init__(self, max_connections: int = 5):
        """
        Initialize enhanced data service with connection pooling.
        
        Args:
            max_connections: Maximum number of concurrent IB connections
        """
        # Call parent init but skip IB connector setup
        super().__init__()
        
        # Replace the single IB connector with connection pool
        self.ib_connector = None  # Clear the old connector
        self.connection_pool = get_connection_pool(max_connections=max_connections)
        
        # Create a primary connector for single-symbol operations
        self.primary_connector = IBAsyncConnectorEnhanced()
        if self.primary_connector.connect_sync():
            logger.info("✅ Primary IB connector established")
            # For backward compatibility, set ib_connector to primary
            self.ib_connector = self.primary_connector
        else:
            logger.warning("❌ Failed to establish primary IB connector")
            
    def get_parallel_daily_bars(
        self, 
        symbols: List[str], 
        start: str, 
        end: str,
        source: str = "ib"
    ) -> Dict[str, pd.DataFrame]:
        """
        Get daily bars for multiple symbols in parallel.
        
        Args:
            symbols: List of stock symbols
            start: Start date (YYYY-MM-DD)
            end: End date (YYYY-MM-DD)
            source: Data source (default "ib")
            
        Returns:
            Dictionary mapping symbols to DataFrames
        """
        if source != "ib":
            raise ValueError("Only IB source supported for parallel fetching")
            
        start_date = datetime.strptime(start, "%Y-%m-%d")
        end_date = datetime.strptime(end, "%Y-%m-%d")
        days = (end_date - start_date).days + 1
        
        # Use parallel fetch
        duration = f"{days} D"
        results = parallel_data_fetch_sync(symbols, duration, "1 day")
        
        # Filter results to requested date range
        filtered_results = {}
        for symbol, df in results.items():
            if not df.empty:
                mask = (df.index >= start_date) & (df.index <= end_date)
                filtered_results[symbol] = df[mask]
            else:
                filtered_results[symbol] = df
                
        return filtered_results
        
    def get_parallel_minute_bars(
        self,
        symbols: List[str],
        days: int = 1,
        end_date: str = ""
    ) -> Dict[str, pd.DataFrame]:
        """
        Get minute bars for multiple symbols in parallel.
        
        Args:
            symbols: List of stock symbols
            days: Number of days of data (max 30)
            end_date: End date string (empty = current time)
            
        Returns:
            Dictionary mapping symbols to DataFrames
        """
        # IB limits minute data to 30 days
        days = min(days, 30)
        duration = f"{days} D"
        
        # Use parallel fetch
        results = parallel_data_fetch_sync(symbols, duration, "1 min")
        
        return results
        
    def get_market_data_batch(
        self,
        symbols: List[str],
        data_type: str = "daily",
        **kwargs
    ) -> Dict[str, pd.DataFrame]:
        """
        Generic batch data fetching method.
        
        Args:
            symbols: List of symbols
            data_type: "daily" or "minute"
            **kwargs: Additional arguments for specific data types
            
        Returns:
            Dictionary mapping symbols to DataFrames
        """
        if data_type == "daily":
            start = kwargs.get("start", (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
            end = kwargs.get("end", datetime.now().strftime("%Y-%m-%d"))
            return self.get_parallel_daily_bars(symbols, start, end)
        elif data_type == "minute":
            days = kwargs.get("days", 1)
            end_date = kwargs.get("end_date", "")
            return self.get_parallel_minute_bars(symbols, days, end_date)
        else:
            raise ValueError(f"Unsupported data type: {data_type}")
            
    def _get_bars_from_ib(self, symbol: str, start: str, end: str, bar_type: str = "daily") -> pd.DataFrame:
        """
        Override parent method to use enhanced connector.
        Maintains backward compatibility.
        """
        if not self.ib_connector:
            raise ValueError("IB connector not available")
            
        try:
            # Calculate days from date range
            start_date = datetime.strptime(start, "%Y-%m-%d")
            end_date = datetime.strptime(end, "%Y-%m-%d")
            days_from_today = (datetime.now() - start_date).days + 1
            
            if bar_type == "daily":
                bars = self.ib_connector.get_daily_bars(symbol, days=days_from_today)
            else:  # minute
                bars = self.ib_connector.get_minute_bars(symbol, days=min(days_from_today, 30))
                
            # Filter to requested date range
            if not bars.empty:
                mask = (bars.index >= start_date) & (bars.index <= end_date)
                bars = bars[mask]
                
            return bars
            
        except Exception as e:
            logger.error(f"Failed to get {bar_type} bars from IB for {symbol}: {e}")
            raise
            
    def cleanup(self):
        """Clean up all connections when done"""
        if hasattr(self, 'primary_connector') and self.primary_connector:
            self.primary_connector.disconnect_sync()
        cleanup_connection_pool_sync()
        # Call parent cleanup
        if hasattr(super(), 'cleanup'):
            super().cleanup()
            
    def __del__(self):
        """Ensure cleanup on deletion"""
        try:
            self.cleanup()
        except:
            pass


# Example usage functions
def example_parallel_gap_scan(symbols: List[str], lookback_days: int = 30):
    """
    Example: Scan multiple symbols for gaps in parallel.
    """
    service = DataServiceEnhanced(max_connections=10)
    
    try:
        # Get data for all symbols in parallel
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=lookback_days)).strftime("%Y-%m-%d")
        
        logger.info(f"Fetching daily bars for {len(symbols)} symbols in parallel...")
        
        daily_data = service.get_parallel_daily_bars(symbols, start_date, end_date)
        
        # Analyze gaps
        gap_results = {}
        for symbol, df in daily_data.items():
            if df.empty:
                logger.warning(f"No data for {symbol}")
                continue
                
            # Calculate gaps
            df['gap_pct'] = (df['open'] - df['close'].shift(1)) / df['close'].shift(1) * 100
            
            # Find significant gaps (> 30%)
            significant_gaps = df[df['gap_pct'] > 30]
            
            if not significant_gaps.empty:
                gap_results[symbol] = {
                    'gap_count': len(significant_gaps),
                    'max_gap': significant_gaps['gap_pct'].max(),
                    'gap_dates': significant_gaps.index.tolist()
                }
                
        return gap_results
        
    finally:
        service.cleanup()


def example_tick_data_parallel(symbols: List[str]):
    """
    Example: Get tick data for multiple symbols using separate connections.
    """
    service = DataServiceEnhanced(max_connections=len(symbols))
    
    try:
        # This would need to be implemented in the enhanced connector
        # For now, just showing the structure
        tick_results = {}
        
        for symbol in symbols:
            logger.info(f"Getting tick data for {symbol}...")
            # Would use a dedicated connection per symbol
            # tick_results[symbol] = service.get_tick_data(symbol)
            
        return tick_results
        
    finally:
        service.cleanup()


if __name__ == "__main__":
    # Test the enhanced service
    logger.info("Testing enhanced data service...")
    
    # Test single symbol (backward compatibility)
    service = DataServiceEnhanced()
    try:
        df = service.get_daily_bars("AAPL", "2024-01-01", "2024-01-31")
        logger.info(f"Single symbol test: Retrieved {len(df)} bars for AAPL")
    except Exception as e:
        logger.error(f"Single symbol test failed: {e}")
    finally:
        service.cleanup()
        
    # Test parallel fetching
    symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]
    results = example_parallel_gap_scan(symbols, lookback_days=30)
    
    logger.info("Gap scan results:")
    for symbol, gaps in results.items():
        logger.info(f"{symbol}: {gaps}")