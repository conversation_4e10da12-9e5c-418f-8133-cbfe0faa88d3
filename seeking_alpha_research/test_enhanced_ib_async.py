#!/usr/bin/env python3
"""
Test script for enhanced ib_async connector with:
1. Improved tick data download using ib_async library
2. Parallel clients with random client IDs
3. Enhanced data chunking (30 days for minute data, proper concatenation)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.ib_async_connector import IBAsyncConnector, create_parallel_ib_clients, fetch_data_parallel
from core.data_service import DataService
from datetime import datetime, timedelta
import pandas as pd
import time


def test_enhanced_tick_data():
    """Test enhanced tick data functionality."""
    print("🔥 Testing Enhanced Tick Data with ib_async")
    print("=" * 60)
    
    try:
        # Test single client tick data
        print("\n1. Testing Single Client Tick Data:")
        connector = IBAsyncConnector()
        
        if connector.connect_sync():
            print(f"✅ Connected with client ID: {connector.client_id}")
            
            # Test historical ticks
            symbol = "AAPL"
            start_time = (datetime.now() - timedelta(hours=2)).strftime("%Y%m%d %H:%M:%S")
            
            print(f"   Fetching ticks for {symbol} from {start_time}...")
            ticks = connector.get_historical_ticks(symbol, start_time)
            
            if not ticks.empty:
                print(f"   ✅ Retrieved {len(ticks)} ticks")
                print(f"   📊 Sample data:")
                print(ticks.head(3).to_string())
            else:
                print("   ⚠️  No tick data received (may be outside market hours)")
                
            connector.disconnect_sync()
        else:
            print("   ❌ Failed to connect to IB Gateway")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")


def test_parallel_clients():
    """Test parallel client functionality."""
    print("\n\n🚀 Testing Parallel Clients")
    print("=" * 60)
    
    try:
        # Create multiple clients
        print("\n2. Testing Parallel Client Creation:")
        clients = create_parallel_ib_clients(num_clients=3)
        
        if clients:
            print(f"   ✅ Created {len(clients)} parallel clients")
            for i, client in enumerate(clients):
                print(f"   📡 Client {i+1}: ID {client.client_id}")
            
            # Clean up
            for client in clients:
                client.disconnect_sync()
        else:
            print("   ❌ Failed to create parallel clients")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")


def test_parallel_data_fetch():
    """Test parallel data fetching."""
    print("\n\n⚡ Testing Parallel Data Fetching")
    print("=" * 60)
    
    try:
        print("\n3. Testing Parallel Minute Data Fetch:")
        symbols = ["AAPL", "MSFT", "GOOGL"]
        
        start_time = time.time()
        results = fetch_data_parallel(symbols, data_type="minute", days=1, num_clients=3)
        end_time = time.time()
        
        print(f"   ⏱️  Parallel fetch took {end_time - start_time:.2f} seconds")
        
        for symbol, data in results.items():
            if not data.empty:
                print(f"   ✅ {symbol}: {len(data)} minute bars")
            else:
                print(f"   ⚠️  {symbol}: No data received")
                
    except Exception as e:
        print(f"   ❌ Error: {e}")


def test_enhanced_chunking():
    """Test enhanced data chunking for large requests."""
    print("\n\n📊 Testing Enhanced Data Chunking")
    print("=" * 60)
    
    try:
        print("\n4. Testing 60-Day Minute Data (Auto-Chunking):")
        connector = IBAsyncConnector()
        
        if connector.connect_sync():
            symbol = "AAPL"
            
            # This should automatically chunk into 30-day segments
            print(f"   Requesting 60 days of minute data for {symbol}...")
            start_time = time.time()
            
            # Use the enhanced get_historical_bars method which auto-detects chunking need
            bars = connector.get_historical_bars(
                symbol=symbol,
                duration="60 D",  # This will trigger chunking
                bar_size="1 min",
                use_rth=False
            )
            
            end_time = time.time()
            
            if not bars.empty:
                print(f"   ✅ Retrieved {len(bars)} minute bars in {end_time - start_time:.2f} seconds")
                print(f"   📅 Date range: {bars.index.min()} to {bars.index.max()}")
                print(f"   📊 Sample data:")
                print(bars.head(3).to_string())
            else:
                print("   ⚠️  No data received")
                
            connector.disconnect_sync()
        else:
            print("   ❌ Failed to connect to IB Gateway")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")


def test_data_service_integration():
    """Test integration with DataService."""
    print("\n\n🔗 Testing DataService Integration")
    print("=" * 60)
    
    try:
        print("\n5. Testing DataService with Enhanced IB Connector:")
        data_service = DataService()
        
        symbol = "AAPL"
        
        # Test minute data
        print(f"   Fetching minute data for {symbol}...")
        minute_data = data_service.get_minute_bars(symbol, days=2)
        
        if not minute_data.empty:
            print(f"   ✅ Retrieved {len(minute_data)} minute bars via DataService")
        else:
            print("   ⚠️  No minute data received")
        
        # Test tick data
        print(f"   Fetching tick data for {symbol}...")
        start_time = (datetime.now() - timedelta(hours=1)).strftime("%Y%m%d %H:%M:%S")
        tick_data = data_service.get_tick_data(symbol, start_time=start_time)
        
        if not tick_data.empty:
            print(f"   ✅ Retrieved {len(tick_data)} ticks via DataService")
        else:
            print("   ⚠️  No tick data received (may be outside market hours)")
            
        data_service.close()
        
    except Exception as e:
        print(f"   ❌ Error: {e}")


def main():
    """Run all enhancement tests."""
    print("🎯 Enhanced IB Async Connector Test Suite")
    print("=" * 80)
    print("Testing improvements:")
    print("• ib_async library for tick data (replacing old IB API)")
    print("• Parallel clients with random client IDs")
    print("• Enhanced chunking (30 days for minute data)")
    print("• Automatic concatenation for large requests")
    print("=" * 80)
    
    # Run all tests
    test_enhanced_tick_data()
    test_parallel_clients()
    test_parallel_data_fetch()
    test_enhanced_chunking()
    test_data_service_integration()
    
    print("\n\n🎉 Enhancement Test Suite Complete!")
    print("=" * 80)
    print("Key improvements implemented:")
    print("✅ Modern ib_async library for better reliability")
    print("✅ Random client IDs for parallel connections")
    print("✅ Intelligent data chunking (30-day segments)")
    print("✅ Automatic concatenation and deduplication")
    print("✅ Enhanced error handling and logging")
    print("✅ Backward compatibility with existing DataService")


if __name__ == "__main__":
    main()
