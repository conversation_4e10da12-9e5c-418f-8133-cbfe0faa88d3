# Migration Guide: IB Connector to IB Async Enhanced

This document outlines the migration from the deprecated `ib_connector.py` to the new `ib_async_connector_enhanced.py`.

## Overview

The new IB async connector provides:
- ✅ **Connection pooling** for parallel data fetching
- ✅ **Volume scaling fix** (IB returns volumes in lots, must multiply by 100)
- ✅ **Backward compatibility** with existing code
- ✅ **Better error handling** and automatic reconnection
- ✅ **Database caching** to reduce API calls

## Key Changes

### 1. Volume Data Correction
**CRITICAL**: IB returns volume in lots for US stocks. The new connector automatically multiplies by 100 to get actual share volume.

```python
# Old (incorrect): volume = 1000 (lots)
# New (correct): volume = 100000 (shares)
```

### 2. Connection Management
- **Old**: Single connection, sequential data fetching
- **New**: Connection pool with up to N concurrent connections

### 3. Data Format
The new connector maintains the same data format as the old one for backward compatibility.

## Migration Steps

### Step 1: Install Dependencies
```bash
pip install ib_async nest-asyncio
```

### Step 2: Update Imports
```python
# Old
from .ib_connector import IBConnector

# New - for single connection (backward compatible)
from .ib_async_connector_enhanced import IBAsyncConnectorEnhanced

# New - for parallel fetching
from .ib_async_connector_enhanced import get_connection_pool, parallel_data_fetch_sync
```

### Step 3: Update DataService (Option A - Minimal Changes)
For minimal disruption, update the DataService to use the enhanced connector:

```python
# In data_service.py __init__ method
# Replace:
from .ib_connector import IBConnector
self.ib_connector = IBConnector()

# With:
from .ib_async_connector_enhanced import IBAsyncConnectorEnhanced
self.ib_connector = IBAsyncConnectorEnhanced()
if self.ib_connector.connect_sync():
    logger.info("Connected to IB Gateway with enhanced connector")
```

### Step 4: Update DataService (Option B - Full Migration)
Use the enhanced DataService for parallel capabilities:

```python
# Replace
from core.data_service import DataService
data_service = DataService()

# With
from core.data_service_enhanced import DataServiceEnhanced
data_service = DataServiceEnhanced(max_connections=5)
```

### Step 5: Parallel Data Fetching (New Feature)
```python
# Fetch data for multiple symbols in parallel
symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"]
results = data_service.get_parallel_daily_bars(
    symbols, 
    start="2024-01-01", 
    end="2024-12-31"
)

# results is a dict: {symbol: DataFrame}
for symbol, df in results.items():
    print(f"{symbol}: {len(df)} bars")
```

## Testing the Migration

### 1. Test Single Symbol (Backward Compatibility)
```python
# This should work exactly as before
df = data_service.get_daily_bars("AAPL", "2024-01-01", "2024-01-31")
print(f"Volume on 2024-01-02: {df.iloc[0]['volume']}")  # Should be in shares, not lots
```

### 2. Test Volume Scaling
```python
# Verify volume is correctly scaled
# IB returns lots, we should see shares (100x larger)
df = data_service.get_minute_bars("AAPL", days=1)
print(f"Average volume: {df['volume'].mean():,.0f} shares")
```

### 3. Test Parallel Fetching
```python
# Test connection pool
import time

start = time.time()
results = data_service.get_parallel_daily_bars(
    ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"],
    "2024-01-01", 
    "2024-01-31"
)
print(f"Parallel fetch took {time.time() - start:.2f} seconds")

# Compare with sequential
start = time.time()
for symbol in ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"]:
    df = data_service.get_daily_bars(symbol, "2024-01-01", "2024-01-31")
print(f"Sequential fetch took {time.time() - start:.2f} seconds")
```

## Common Issues and Solutions

### Issue 1: Connection Refused
```
Solution: Ensure IB Gateway is running on port 4001
```

### Issue 2: Import Errors
```
Solution: Run `pip install ib_async nest-asyncio`
```

### Issue 3: Volume Data Looks Wrong
```
Solution: The new connector fixes this automatically. 
If you see small volumes, you may be looking at old cached data.
Clear the cache or fetch fresh data.
```

### Issue 4: Async Event Loop Errors
```
Solution: The connector uses nest_asyncio to handle this.
If issues persist, ensure you're using the sync wrappers.
```

## Rollback Plan

If you need to rollback:

1. Keep the old `ib_connector.py` file
2. In `data_service.py`, revert the import:
   ```python
   from .ib_connector import IBConnector  # Revert to old
   ```
3. Remove the volume scaling fix if you added it manually

## Performance Improvements

With connection pooling, expect:
- **5x faster** for fetching 5 symbols (with 5 connections)
- **10x faster** for fetching 10 symbols (with 10 connections)
- Linear scaling up to the connection pool limit

## Next Steps

1. Test the migration in development
2. Monitor volume data for accuracy
3. Gradually increase parallel usage
4. Consider increasing connection pool size for large scans

## Support

For issues:
1. Check IB Gateway logs
2. Enable debug logging: `logger.setLevel(logging.DEBUG)`
3. Verify IB Gateway settings match (port 4001, API enabled)