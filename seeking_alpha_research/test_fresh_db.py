#!/usr/bin/env python3
"""Test fresh database with IB data."""

import sys
import os
import time

# Add project to path
sys.path.insert(0, os.path.dirname(__file__))

from core.data_service import DataService


def main():
    print('🧪 Testing Fresh Database with IB Data...')
    print('=' * 50)

    ds = DataService()

    # Test 1: Fetch fresh minute data
    print('\n📊 Fetching Fresh Minute Data:')
    start_time = time.time()
    minute_bars = ds.get_minute_bars('AAPL', '2025-07-14', '2025-07-14')
    duration = time.time() - start_time

    print(f'   Records: {len(minute_bars)}')
    print(f'   Duration: {duration:.2f}s')
    print(f'   Date range: {minute_bars.index[0]} to {minute_bars.index[-1]}')
    print(f'   Volume sample: {minute_bars["volume"].head(3).tolist()}')

    # Test 2: Test caching (second request)
    print('\n💾 Testing Caching (Second Request):')
    start_time = time.time()
    cached_bars = ds.get_minute_bars('AAPL', '2025-07-14', '2025-07-14')
    cache_duration = time.time() - start_time

    print(f'   Records: {len(cached_bars)}')
    print(f'   Duration: {cache_duration:.2f}s')

    if cache_duration < duration * 0.5:
        print('   ✅ Caching working perfectly!')
    else:
        print('   ⚠️ Caching might not be working')

    # Test 3: Fetch daily data
    print('\n📈 Fetching Daily Data:')
    daily_bars = ds.get_daily_bars('AAPL', '2025-07-10', '2025-07-14')
    print(f'   Records: {len(daily_bars)}')
    if not daily_bars.empty:
        print(f'   Date range: {daily_bars.index[0]} to {daily_bars.index[-1]}')
        print(f'   Volume sample: {daily_bars["volume"].iloc[0]:,}')

    print('\n✅ Fresh database testing complete!')


if __name__ == "__main__":
    main()
