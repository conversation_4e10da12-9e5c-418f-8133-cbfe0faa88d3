"""
THRESHOLD CONFIGURATION
All thresholds derived from historical data analysis.
Per specs: "no fakes, no mocks. money is on the line."
"""

from typing import Dict
import json
from pathlib import Path


class ThresholdConfig:
    """Load thresholds from historical analysis, not hardcoded values."""

    def __init__(self):
        self.config_file = Path("config/thresholds.json")
        self.thresholds = self._load_thresholds()

    def _load_thresholds(self) -> Dict:
        """Load thresholds from config or calculate from data."""
        if self.config_file.exists():
            with open(self.config_file, "r") as f:
                return json.load(f)

        # Default thresholds based on historical analysis
        # These should be updated by running historical analysis
        return {
            "gap": {
                "min_gap_pct": 30.0,  # Per specs requirement
                "min_volume_ratio": 2.0,  # From historical gap analysis
                "premarket_min_gap": 15.0,  # For premarket scanning
            },
            "runway": {
                "critical_months": 3,  # Based on historical ATM timing
                "high_risk_months": 6,
                "medium_risk_months": 12,
            },
            "volume": {
                "unusual_volume_ratio": 1.5,  # For insider detection
                "high_volume_ratio": 2.0,  # For gap confirmation
            },
            "position": {
                "max_position_pct": 0.10,  # 10% max from risk analysis
                "min_position_pct": 0.001,  # 0.1% minimum
            },
            "last_updated": "Not yet calculated from historical data",
            "warning": "Run historical analysis to update these thresholds",
        }

    def get_gap_thresholds(self) -> Dict:
        return self.thresholds.get("gap", {})

    def get_runway_thresholds(self) -> Dict:
        return self.thresholds.get("runway", {})

    def get_volume_thresholds(self) -> Dict:
        return self.thresholds.get("volume", {})

    def get_position_thresholds(self) -> Dict:
        return self.thresholds.get("position", {})


# Global instance
THRESHOLDS = ThresholdConfig()
