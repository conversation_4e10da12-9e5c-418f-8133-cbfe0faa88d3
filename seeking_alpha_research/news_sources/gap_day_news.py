#!/usr/bin/env python3
"""
Gap Day News Fetcher - Gets news with timestamps for a specific date.
Money is on the line - NO FAKES, NO MOCKS.
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

from news_sources.alpaca import get_news as get_alpaca_news
from news_sources.finviz import get_finviz_news
from news_sources.ib_news import get_ib_news

logger = logging.getLogger(__name__)


def get_gap_day_news(symbol: str, gap_date: str) -> Dict[str, any]:
    """
    Get all news for a specific gap date with precise timestamps.
    
    This is critical for determining if news caused the gap (premarket)
    or came after (intraday/afterhours).
    
    Args:
        symbol: Stock symbol
        gap_date: Date of the gap (YYYY-MM-DD format)
        
    Returns:
        Dictionary with news categorized by timing:
        - premarket_news: News before 9:30 AM ET
        - regular_hours_news: News during 9:30 AM - 4:00 PM ET
        - afterhours_news: News after 4:00 PM ET
    """
    logger.info(f"Getting gap day news for {symbol} on {gap_date}")
    
    # Parse date and ensure timezone-naive
    gap_date_obj = pd.to_datetime(gap_date)
    if gap_date_obj.tz is not None:
        gap_date_obj = gap_date_obj.tz_localize(None)
    
    # Define time windows (Eastern Time) - ensure timezone-naive for comparison
    premarket_start = gap_date_obj.replace(hour=4, minute=0, second=0, microsecond=0)
    market_open = gap_date_obj.replace(hour=9, minute=30, second=0, microsecond=0)
    market_close = gap_date_obj.replace(hour=16, minute=0, second=0, microsecond=0)
    afterhours_end = gap_date_obj.replace(hour=20, minute=0, second=0, microsecond=0)

    # Also check previous day afterhours (could influence gap)
    prev_day = gap_date_obj - timedelta(days=1)
    prev_afterhours_start = prev_day.replace(hour=16, minute=0, second=0, microsecond=0)

    # Ensure all time windows are timezone-naive
    if premarket_start.tz is not None:
        premarket_start = premarket_start.tz_localize(None)
    if market_open.tz is not None:
        market_open = market_open.tz_localize(None)
    if market_close.tz is not None:
        market_close = market_close.tz_localize(None)
    if afterhours_end.tz is not None:
        afterhours_end = afterhours_end.tz_localize(None)
    if prev_afterhours_start.tz is not None:
        prev_afterhours_start = prev_afterhours_start.tz_localize(None)
    
    # Fetch news from multiple sources
    all_news = []
    
    # 1. Alpaca News (includes Benzinga)
    try:
        # Get news from previous afterhours through gap day
        start_str = prev_afterhours_start.strftime('%Y-%m-%dT%H:%M:%SZ')
        end_str = afterhours_end.strftime('%Y-%m-%dT%H:%M:%SZ')
        
        alpaca_df = get_alpaca_news(symbol, start_str, end_str)
        if not alpaca_df.empty:
            alpaca_df['source'] = 'Alpaca/Benzinga'

            # Ensure timezone-naive timestamps
            if 'created_at' in alpaca_df.columns:
                alpaca_df['created_at'] = pd.to_datetime(alpaca_df['created_at'])
                if alpaca_df['created_at'].dt.tz is not None:
                    alpaca_df['created_at'] = alpaca_df['created_at'].dt.tz_localize(None)

            all_news.append(alpaca_df)
            logger.info(f"Found {len(alpaca_df)} news items from Alpaca")
    except Exception as e:
        logger.error(f"Failed to get Alpaca news: {e}")
    
    # 2. Finviz News (recent headlines)
    try:
        finviz_df = get_finviz_news(symbol)
        if not finviz_df.empty:
            # Filter for gap day only
            finviz_df['created_at'] = pd.to_datetime(finviz_df['timestamp'])

            # Ensure timezone-naive for comparison
            if finviz_df['created_at'].dt.tz is not None:
                finviz_df['created_at'] = finviz_df['created_at'].dt.tz_localize(None)

            finviz_df = finviz_df[finviz_df['created_at'].dt.date == gap_date_obj.date()]
            finviz_df['source'] = 'Finviz'
            
            if not finviz_df.empty:
                all_news.append(finviz_df[['created_at', 'headline', 'source']])
                logger.info(f"Found {len(finviz_df)} news items from Finviz")
    except Exception as e:
        logger.error(f"Failed to get Finviz news: {e}")

    # 3. Interactive Brokers News (historical)
    try:
        ib_df = get_ib_news(symbol, gap_date, gap_date)
        if not ib_df.empty:
            # IB news is already in the right format with timezone-naive timestamps
            all_news.append(ib_df[['created_at', 'headline', 'source']])
            logger.info(f"Found {len(ib_df)} news items from Interactive Brokers")
    except Exception as e:
        logger.error(f"Failed to get IB news: {e}")

    # Combine all news
    if all_news:
        combined_news = pd.concat(all_news, ignore_index=True)

        # All timestamps should now be timezone-naive, safe to sort
        combined_news = combined_news.sort_values('created_at')
        
        # Remove duplicates (same headline within 1 hour)
        combined_news['headline_clean'] = combined_news['headline'].str.lower().str.strip()
        combined_news['hour'] = combined_news['created_at'].dt.floor('h')
        combined_news = combined_news.drop_duplicates(subset=['headline_clean', 'hour'])
        combined_news = combined_news.drop(['headline_clean', 'hour'], axis=1)
    else:
        combined_news = pd.DataFrame()
    
    # Categorize by timing
    premarket_news = []
    regular_news = []
    afterhours_news = []
    prev_afterhours_news = []
    
    if not combined_news.empty:
        for _, row in combined_news.iterrows():
            timestamp = row['created_at']

            # Ensure timestamp is timezone-naive for comparison
            if hasattr(timestamp, 'tz') and timestamp.tz is not None:
                timestamp = timestamp.tz_localize(None)

            # Handle summary field safely
            summary = row.get('summary', '')
            if isinstance(summary, str):
                summary = summary[:200]
            else:
                summary = ''

            news_item = {
                'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                'headline': row['headline'],
                'source': row.get('source', 'Unknown'),
                'summary': summary
            }

            # Categorize by time (now all timezone-naive)
            if prev_afterhours_start <= timestamp < gap_date_obj:
                prev_afterhours_news.append(news_item)
            elif premarket_start <= timestamp < market_open:
                premarket_news.append(news_item)
            elif market_open <= timestamp < market_close:
                regular_news.append(news_item)
            elif market_close <= timestamp <= afterhours_end:
                afterhours_news.append(news_item)
    
    # Determine if gap was news-driven
    has_catalyst = len(prev_afterhours_news) > 0 or len(premarket_news) > 0
    
    result = {
        'symbol': symbol,
        'gap_date': gap_date,
        'has_catalyst': has_catalyst,
        'catalyst_count': len(prev_afterhours_news) + len(premarket_news),
        'total_news_count': len(combined_news),
        'prev_afterhours_news': prev_afterhours_news,
        'premarket_news': premarket_news,
        'regular_hours_news': regular_news,
        'afterhours_news': afterhours_news,
        'summary': _create_news_summary(has_catalyst, prev_afterhours_news, premarket_news)
    }
    
    logger.info(
        f"Gap day news analysis: Catalyst={has_catalyst}, "
        f"Premarket={len(premarket_news)}, Total={len(combined_news)}"
    )
    
    return result


def _create_news_summary(has_catalyst: bool, prev_ah: List, premarket: List) -> str:
    """Create a summary of the news catalyst."""
    if not has_catalyst:
        return "No news catalyst found for gap"
    
    catalyst_news = prev_ah + premarket
    if catalyst_news:
        first_news = catalyst_news[0]
        return f"Gap catalyst at {first_news['timestamp']}: {first_news['headline'][:100]}..."
    
    return "News catalyst detected but details unavailable"


# Example usage
if __name__ == "__main__":
    import json
    
    # Test with PLUG on a known gap day
    result = get_gap_day_news('PLUG', '2024-01-15')
    
    print(f"\n📰 Gap Day News Analysis for {result['symbol']} on {result['gap_date']}")
    print(f"Has Catalyst: {result['has_catalyst']}")
    print(f"Catalyst Count: {result['catalyst_count']}")
    print(f"\nSummary: {result['summary']}")
    
    if result['premarket_news']:
        print(f"\n⏰ Premarket News ({len(result['premarket_news'])} items):")
        for news in result['premarket_news'][:3]:
            print(f"  {news['timestamp']}: {news['headline'][:80]}...")