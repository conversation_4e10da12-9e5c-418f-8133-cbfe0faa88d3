#!/usr/bin/env python3
"""
Interactive Brokers News Provider
Fetches historical news data directly from IB Gateway for comprehensive coverage.
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
import time

from core.data_service import DataService

logger = logging.getLogger(__name__)


def get_ib_news(symbol: str, start_date: str, end_date: str = None) -> pd.DataFrame:
    """
    Get historical news from Interactive Brokers.
    
    Args:
        symbol: Stock symbol
        start_date: Start date (YYYY-MM-DD format)
        end_date: End date (YYYY-MM-DD format), defaults to start_date
        
    Returns:
        DataFrame with columns: created_at, headline, summary, source
    """
    logger.info(f"Fetching IB news for {symbol} from {start_date}")
    
    if end_date is None:
        end_date = start_date
    
    try:
        # Get DataService with IB connection
        ds = DataService()
        
        if not ds.ib_connector:
            logger.warning("IB Gateway not connected, cannot fetch IB news")
            return pd.DataFrame()
        
        # Convert dates to datetime objects
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        # IB news API expects specific format
        # Request news for the date range
        news_data = []
        
        # Try to get news using IB's news API
        try:
            # Use IB's historical news functionality
            ib_news_df = ds.get_news(symbol, start_date, end_date)
            
            if not ib_news_df.empty:
                # Standardize the format
                standardized_news = []
                
                for _, row in ib_news_df.iterrows():
                    news_item = {
                        'created_at': pd.to_datetime(row.get('timestamp', row.name)),
                        'headline': row.get('headline', row.get('title', 'No headline')),
                        'summary': row.get('summary', row.get('text', ''))[:500],  # Limit summary length
                        'source': 'Interactive Brokers'
                    }
                    standardized_news.append(news_item)
                
                if standardized_news:
                    result_df = pd.DataFrame(standardized_news)
                    
                    # Ensure timezone-naive timestamps
                    if result_df['created_at'].dt.tz is not None:
                        result_df['created_at'] = result_df['created_at'].dt.tz_localize(None)
                    
                    # Filter to requested date range
                    start_filter = start_dt.replace(hour=0, minute=0, second=0)
                    end_filter = end_dt.replace(hour=23, minute=59, second=59)
                    
                    result_df = result_df[
                        (result_df['created_at'] >= start_filter) & 
                        (result_df['created_at'] <= end_filter)
                    ]
                    
                    logger.info(f"Retrieved {len(result_df)} news items from IB for {symbol}")
                    return result_df.sort_values('created_at')
        
        except Exception as e:
            logger.warning(f"IB news API failed: {e}")
            # Try alternative IB news method if available
            return _get_ib_news_alternative(ds, symbol, start_dt, end_dt)
        
        # Close the connection properly
        ds.close()
        
        logger.info(f"No IB news found for {symbol} on {start_date}")
        return pd.DataFrame()
        
    except Exception as e:
        logger.error(f"Failed to get IB news for {symbol}: {e}")
        return pd.DataFrame()


def _get_ib_news_alternative(ds: DataService, symbol: str, start_dt: datetime, end_dt: datetime) -> pd.DataFrame:
    """
    Alternative method to get IB news using direct IB API calls.
    """
    try:
        logger.info(f"Trying alternative IB news method for {symbol}")
        
        # Use the IB connector directly if available
        if hasattr(ds.ib_connector, 'get_historical_news'):
            # Some IB implementations have direct news methods
            news_data = ds.ib_connector.get_historical_news(
                symbol, 
                start_dt.strftime("%Y%m%d"), 
                end_dt.strftime("%Y%m%d")
            )
            
            if news_data and not news_data.empty:
                # Standardize format
                news_data['source'] = 'Interactive Brokers (Direct)'
                
                # Ensure required columns
                if 'created_at' not in news_data.columns and 'timestamp' in news_data.columns:
                    news_data['created_at'] = pd.to_datetime(news_data['timestamp'])
                
                if 'headline' not in news_data.columns and 'title' in news_data.columns:
                    news_data['headline'] = news_data['title']
                
                if 'summary' not in news_data.columns:
                    news_data['summary'] = news_data.get('text', '').astype(str).str[:500]
                
                # Ensure timezone-naive
                if news_data['created_at'].dt.tz is not None:
                    news_data['created_at'] = news_data['created_at'].dt.tz_localize(None)
                
                logger.info(f"Alternative IB news method got {len(news_data)} items")
                return news_data[['created_at', 'headline', 'summary', 'source']]
        
        # If no direct news API, try using IB's fundamental data which sometimes includes news
        logger.debug("No direct IB news API available")
        return pd.DataFrame()
        
    except Exception as e:
        logger.warning(f"Alternative IB news method failed: {e}")
        return pd.DataFrame()


def get_ib_realtime_news(symbol: str, duration_minutes: int = 60) -> pd.DataFrame:
    """
    Get real-time news from IB for the last N minutes.
    
    Args:
        symbol: Stock symbol
        duration_minutes: How many minutes back to look
        
    Returns:
        DataFrame with recent news
    """
    logger.info(f"Getting real-time IB news for {symbol} (last {duration_minutes} minutes)")
    
    try:
        ds = DataService()
        
        if not ds.ib_connector:
            logger.warning("IB Gateway not connected")
            return pd.DataFrame()
        
        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=duration_minutes)
        
        # Get recent news
        news_df = get_ib_news(
            symbol, 
            start_time.strftime("%Y-%m-%d"), 
            end_time.strftime("%Y-%m-%d")
        )
        
        # Filter to last N minutes
        if not news_df.empty:
            recent_news = news_df[news_df['created_at'] >= start_time]
            logger.info(f"Found {len(recent_news)} recent IB news items")
            return recent_news
        
        ds.close()
        return pd.DataFrame()
        
    except Exception as e:
        logger.error(f"Failed to get real-time IB news: {e}")
        return pd.DataFrame()


def test_ib_news():
    """Test function to verify IB news functionality."""
    print("🧪 Testing IB News Provider...")
    
    try:
        # Test historical news
        print("Testing historical news...")
        news_df = get_ib_news("AAPL", "2025-07-14")
        
        if not news_df.empty:
            print(f"✅ Got {len(news_df)} historical news items")
            print(f"Columns: {list(news_df.columns)}")
            print("\nSample headlines:")
            for headline in news_df['headline'].head(3):
                print(f"  - {headline}")
        else:
            print("⚠️ No historical news found (might be outside IB news coverage)")
        
        # Test real-time news
        print("\nTesting real-time news...")
        realtime_df = get_ib_realtime_news("AAPL", 120)  # Last 2 hours
        
        if not realtime_df.empty:
            print(f"✅ Got {len(realtime_df)} real-time news items")
        else:
            print("⚠️ No real-time news found")
        
        print("\n✅ IB News Provider test complete!")
        
    except Exception as e:
        print(f"❌ IB News test failed: {e}")


if __name__ == "__main__":
    test_ib_news()
