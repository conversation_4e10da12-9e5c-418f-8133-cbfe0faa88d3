"""
Optimized Comprehensive ATM Strategy Analyzer

Key optimizations:
1. Parallel filing analysis with concurrent.futures
2. Smarter filing selection (focus on most recent Q/K)
3. Shorter, more focused prompts for faster LLM response
4. Better caching with early cache checks
5. Timeout handling for individual analyses
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
import time
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
import threading

from core.data_service import DataService as ComprehensiveDataService
from utils.filing_cache_manager import FilingCacheManager
from utils.filing_text_cache import FilingTextCache
from utils.backtest_aware_llm_cache import BacktestAwareLLMCache
from core.logger import get_logger
import litellm

logger = get_logger(__name__)

# Configure LiteLLM
os.environ["GEMINI_API_KEY"] = os.getenv("GEMINI_API_KEY", "")
litellm.set_verbose = False


class OptimizedATMAnalyzer:
    """
    Optimized ATM analyzer for faster performance.
    """

    def __init__(self, max_workers: int = 3):
        self.data_service = ComprehensiveDataService()
        self.cache_manager = FilingCacheManager()
        self.text_cache = FilingTextCache()
        self.llm_cache = BacktestAwareLLMCache()
        self.max_workers = max_workers  # Parallel LLM calls
        
        # Thread-local storage for IB connections
        self.local = threading.local()

        # Validate requirements
        if not os.getenv("GEMINI_API_KEY"):
            raise ValueError("GEMINI_API_KEY required for filing analysis")

        logger.info(f"Optimized ATM Analyzer initialized with {max_workers} workers")

    def analyze_atm_risk(
        self, symbol: str, analysis_date: str = None, lookback_days: int = 730, 
        as_of_date: str = None
    ) -> Dict[str, Any]:
        """
        Optimized ATM risk analysis.
        """
        if analysis_date is None:
            analysis_date = datetime.now().strftime("%Y-%m-%d")
        
        if as_of_date is None:
            as_of_date = analysis_date

        start_time = datetime.now()

        logger.info(
            f"Starting optimized ATM analysis for {symbol} as of {analysis_date}"
        )

        try:
            # Step 1: Get SEC filings (already optimized with caching)
            end_date = pd.to_datetime(analysis_date)
            start_date = end_date - timedelta(days=lookback_days)
            
            filings = self.data_service.get_sec_filings(
                symbol, 
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )

            if filings.empty:
                raise ValueError(f"CRITICAL: No SEC filings found for {symbol}")

            # Step 2: Smart filing selection - focus on most important ones
            selected_filings = self._select_key_filings(filings)
            logger.info(f"Selected {len(selected_filings)} key filings for fast analysis")

            # Step 3: Parallel filing analysis with timeout
            filing_analyses = self._analyze_filings_parallel(
                selected_filings, symbol, as_of_date
            )

            if len(filing_analyses) < 1:
                raise ValueError(
                    f"CRITICAL: No filings analyzed for {symbol} - "
                    f"insufficient for ATM prediction"
                )

            # Step 4: Aggregate results (simplified)
            final_assessment = self._create_fast_assessment(
                symbol=symbol,
                analysis_date=analysis_date,
                filing_analyses=filing_analyses
            )

            processing_time = (datetime.now() - start_time).total_seconds()
            final_assessment["processing_time"] = processing_time

            logger.info(
                f"Completed optimized ATM analysis for {symbol} in {processing_time:.1f}s"
            )
            return final_assessment

        except Exception as e:
            logger.error(f"ATM analysis failed for {symbol}: {e}")
            raise ValueError(f"CRITICAL: ATM analysis failed for {symbol} - {e}")

    def _select_key_filings(self, filings: pd.DataFrame) -> pd.DataFrame:
        """Select only the most important filings for analysis."""
        
        # Priority 1: Most recent 10-Q and 10-K
        quarterly = filings[filings["form_type"].isin(["10-Q", "10-K"])].head(2)
        
        # Priority 2: Recent S-3 or 424B5 (ATM indicators)
        atm_forms = filings[filings["form_type"].isin(["S-3", "424B5", "S-3/A"])].head(1)
        
        # Priority 3: Most recent 8-K
        eight_k = filings[filings["form_type"] == "8-K"].head(1)
        
        # Combine
        selected = pd.concat([quarterly, atm_forms, eight_k], ignore_index=True)
        selected = selected.drop_duplicates(subset=["accession_number"])
        
        # Limit to 4 filings max for speed
        return selected.head(4)

    def _analyze_filings_parallel(
        self, filings: pd.DataFrame, symbol: str, as_of_date: str
    ) -> List[Dict[str, Any]]:
        """Analyze filings in parallel with timeout handling."""
        
        analyses = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all filing analyses
            future_to_filing = {}
            
            for _, filing in filings.iterrows():
                future = executor.submit(
                    self._analyze_single_filing_with_timeout,
                    filing, symbol, as_of_date
                )
                future_to_filing[future] = filing
            
            # Collect results with timeout
            for future in future_to_filing:
                filing = future_to_filing[future]
                try:
                    # 15 second timeout per filing
                    result = future.result(timeout=15)
                    if result:
                        analyses.append(result)
                except FutureTimeoutError:
                    logger.warning(
                        f"Timeout analyzing {filing['form_type']} - skipping"
                    )
                except Exception as e:
                    logger.warning(
                        f"Failed to analyze {filing['form_type']}: {e}"
                    )
        
        return analyses

    def _analyze_single_filing_with_timeout(
        self, filing: pd.Series, symbol: str, as_of_date: str
    ) -> Optional[Dict[str, Any]]:
        """Analyze a single filing with timeout protection."""
        
        try:
            # Check cache first
            filing_date = filing['filed_at'].strftime('%Y-%m-%d') if hasattr(filing['filed_at'], 'strftime') else str(filing['filed_at'])
            
            # Get filing text
            filing_text = self._get_filing_text(filing)
            if not filing_text or len(filing_text) < 1000:
                logger.warning(f"Insufficient text for {filing['form_type']}")
                return None
            
            # Check backtest-aware cache
            cached_result = self.llm_cache.get_as_of_date(
                symbol=symbol,
                analysis_type="optimized_atm_risk",
                filing_date=filing_date,
                prompt=filing_text[:500],  # Shorter identifier
                as_of_date=as_of_date,
                model="gemini/gemini-1.5-flash"
            )
            
            if cached_result:
                cache_meta = cached_result.pop('_cache_metadata', {})
                logger.info(f"Using cached analysis for {filing['form_type']}")
                return self._format_cached_result(cached_result, filing, filing_date)
            
            # Not cached - analyze with optimized prompt
            result = self._fast_llm_analysis(filing_text, filing, symbol)
            
            if result:
                # Cache the result
                self.llm_cache.set(
                    symbol=symbol,
                    analysis_type="optimized_atm_risk",
                    filing_date=filing_date,
                    prompt=filing_text[:500],
                    response=result,
                    analysis_date=as_of_date,
                    model="gemini/gemini-1.5-flash"
                )
                
                return self._format_analysis_result(result, filing, filing_date)
            
        except Exception as e:
            logger.error(f"Error analyzing {filing['form_type']}: {e}")
            return None

    def _fast_llm_analysis(
        self, filing_text: str, filing: pd.Series, symbol: str
    ) -> Optional[Dict[str, Any]]:
        """Fast, focused LLM analysis with shorter prompt."""
        
        # Take only the most relevant parts of the filing
        text_sample = filing_text[:8000]  # Reduced from 12000
        
        # Focused prompt for speed
        prompt = f"""
Analyze this {filing['form_type']} filing for {symbol} to assess ATM offering risk.

CRITICAL QUESTIONS (answer with specific numbers):
1. Cash position: Current cash and equivalents in millions
2. Burn rate: Quarterly operating expenses in millions
3. ATM program: Is there an active ATM facility? (YES/NO)
4. ATM capacity: Available amount under ATM in millions

FILING EXCERPT:
{text_sample}

Return ONLY this JSON (no explanation):
{{
  "cash_position": [number in USD],
  "quarterly_burn": [number in USD],
  "has_atm_program": [true/false],
  "atm_capacity": [number in USD or 0],
  "confidence": [0.1-1.0]
}}
"""

        try:
            response = litellm.completion(
                model="gemini/gemini-1.5-flash",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,  # Reduced for faster response
                temperature=0.1,
                timeout=10  # 10 second timeout
            )

            response_text = response.choices[0].message.content.strip()
            
            # Parse JSON
            import re
            json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                
                # Quick validation
                if result.get("confidence", 0) >= 0.1:
                    return result
                    
        except Exception as e:
            logger.warning(f"LLM analysis failed: {e}")
            
        return None

    def _format_cached_result(
        self, cached_result: Dict, filing: pd.Series, filing_date: str
    ) -> Dict[str, Any]:
        """Format cached result."""
        
        # Calculate derived fields
        cash = cached_result.get('cash_position', 0) or 0
        burn = cached_result.get('quarterly_burn', 0) or 0
        monthly_burn = burn / 3 if burn > 0 else 0
        runway = (cash / monthly_burn) if monthly_burn > 0 else 999
        
        return {
            "filing_type": filing["form_type"],
            "filed_date": filing_date,
            "cash_position": cash,
            "monthly_burn": monthly_burn,
            "cash_runway_months": min(runway, 999),
            "has_atm_program": cached_result.get('has_atm_program', False),
            "atm_capacity": cached_result.get('atm_capacity', 0),
            "confidence": cached_result.get('confidence', 0.5),
            "from_cache": True
        }

    def _format_analysis_result(
        self, result: Dict, filing: pd.Series, filing_date: str
    ) -> Dict[str, Any]:
        """Format fresh analysis result."""
        
        # Calculate derived fields
        cash = result.get('cash_position', 0) or 0
        burn = result.get('quarterly_burn', 0) or 0
        monthly_burn = burn / 3 if burn > 0 else 0
        runway = (cash / monthly_burn) if monthly_burn > 0 else 999
        
        # Simple ATM date prediction
        if runway < 999 and runway > 0:
            filing_date_obj = pd.to_datetime(filing_date)
            depletion_date = filing_date_obj + pd.DateOffset(months=int(runway))
            atm_date = depletion_date - pd.DateOffset(months=2)  # 2 month buffer
            predicted_atm_date = atm_date.strftime('%Y-%m-%d')
        else:
            predicted_atm_date = "Not needed"
        
        return {
            "filing_type": filing["form_type"],
            "filed_date": filing_date,
            "cash_position": cash,
            "monthly_burn": monthly_burn,
            "cash_runway_months": min(runway, 999),
            "predicted_atm_date": predicted_atm_date,
            "has_atm_program": result.get('has_atm_program', False),
            "atm_capacity": result.get('atm_capacity', 0),
            "confidence": result.get('confidence', 0.5),
            "from_cache": False
        }

    def _create_fast_assessment(
        self, symbol: str, analysis_date: str, filing_analyses: List[Dict]
    ) -> Dict[str, Any]:
        """Create simplified final assessment."""
        
        # Aggregate data from analyses
        total_cash = 0
        total_burn = 0
        has_atm = False
        max_capacity = 0
        earliest_atm_date = None
        
        for analysis in filing_analyses:
            if analysis.get('cash_position', 0) > total_cash:
                total_cash = analysis['cash_position']
                total_burn = analysis.get('monthly_burn', 0)
            
            if analysis.get('has_atm_program'):
                has_atm = True
                max_capacity = max(max_capacity, analysis.get('atm_capacity', 0))
            
            atm_date = analysis.get('predicted_atm_date')
            if atm_date and atm_date != "Not needed":
                if earliest_atm_date is None or atm_date < earliest_atm_date:
                    earliest_atm_date = atm_date
        
        # Calculate probability
        if has_atm and total_burn > 0:
            runway = (total_cash / total_burn) if total_burn > 0 else 999
            if runway < 6:
                atm_probability = 0.9
            elif runway < 12:
                atm_probability = 0.7
            else:
                atm_probability = 0.3
        else:
            atm_probability = 0.1
        
        # Risk category
        if atm_probability >= 0.7:
            risk_category = "HIGH"
        elif atm_probability >= 0.4:
            risk_category = "MEDIUM"
        else:
            risk_category = "LOW"
        
        return {
            "symbol": symbol,
            "analysis_date": analysis_date,
            "analyses": filing_analyses,
            "latest_cash_position": total_cash,
            "avg_monthly_burn": total_burn,
            "estimated_runway_months": (total_cash / total_burn) if total_burn > 0 else 999,
            "predicted_atm_date": earliest_atm_date or "Not calculated",
            "has_active_atm": has_atm,
            "max_atm_capacity": max_capacity,
            "atm_probability": atm_probability,
            "risk_category": risk_category,
            "filings_analyzed": len(filing_analyses),
            "confidence_score": np.mean([a.get('confidence', 0.5) for a in filing_analyses])
        }

    def _get_filing_text(self, filing: pd.Series) -> str:
        """Get filing text from cache."""
        
        filing_id = filing.get('id') or filing.get('filing_id')
        accession_number = filing.get('accession_number', '')
        filing_url = filing.get('filing_url', '')
        symbol = filing.get('symbol', '')
        form_type = filing.get('form_type', '')
        
        if filing_id and accession_number:
            try:
                text, metadata = self.text_cache.get_filing_text(
                    filing_id=filing_id,
                    accession_number=accession_number,
                    filing_url=filing_url,
                    symbol=symbol,
                    form_type=form_type
                )
                
                if text:
                    return text
                    
            except Exception as e:
                logger.warning(f"Text cache error: {e}")
        
        return ""

    def close(self):
        """Clean up resources."""
        self.data_service.close()


# Alias for backward compatibility
FinalATMAnalyzer = OptimizedATMAnalyzer