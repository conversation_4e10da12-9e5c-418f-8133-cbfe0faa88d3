"""
Final Comprehensive ATM Strategy Analyzer

Integrates all data sources and analysis methods:
1. Comprehensive Data Service (Alpaca + Edgar + IB Fundamental + IB Tick)
2. LiteLLM for real SEC filing analysis
3. Sophisticated caching
4. FAILS LOUDLY when data is insufficient

This replaces all previous analyzers with one unified solution.
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import time

from core.data_service import DataService as ComprehensiveDataService
from utils.filing_cache_manager import FilingCacheManager
from utils.filing_text_cache import FilingTextCache
from utils.backtest_aware_llm_cache import BacktestAwareLLMCache
from core.logger import get_logger
import litellm

logger = get_logger(__name__)

# Configure LiteLLM
os.environ["GEMINI_API_KEY"] = os.getenv("GEMINI_API_KEY", "")
litellm.set_verbose = False


class FinalATMAnalyzer:
    """
    Final comprehensive ATM analyzer using all available data sources.

    This is the production-ready analyzer that combines:
    - Multiple SEC filing sources (Alpaca + Edgar)
    - IB fundamental data for cross-validation
    - IB tick data for insider detection
    - Real LLM analysis with validation
    - Sophisticated caching
    - Fails loudly on insufficient data
    """

    def __init__(self):
        self.data_service = ComprehensiveDataService()
        self.cache_manager = FilingCacheManager()
        self.text_cache = FilingTextCache()
        self.llm_cache = BacktestAwareLLMCache()  # Use backtest-aware cache

        # Validate requirements
        if not os.getenv("GEMINI_API_KEY"):
            raise ValueError("GEMINI_API_KEY required for filing analysis")

        logger.info("Final ATM Analyzer initialized with comprehensive data sources")

    def analyze_atm_risk(
        self, symbol: str, analysis_date: str = None, lookback_days: int = 730, 
        as_of_date: str = None
    ) -> Dict[str, Any]:
        """
        Comprehensive ATM risk analysis for a symbol.

        This is the main entry point that replaces all previous analyze_* functions.

        Args:
            symbol: Stock symbol to analyze
            analysis_date: Date to analyze as of (default: today)
            lookback_days: Days to look back for filings (default: 2 years)
            as_of_date: Date for point-in-time analysis (for backtesting)

        Returns:
            Comprehensive ATM risk assessment
        """
        if analysis_date is None:
            analysis_date = datetime.now().strftime("%Y-%m-%d")
        
        if as_of_date is None:
            as_of_date = analysis_date  # Default to analysis_date for backtesting

        start_time = datetime.now()

        logger.info(
            f"Starting comprehensive ATM analysis for {symbol} as of {analysis_date}"
        )

        # Check cache first
        cache_key = f"{symbol}_{analysis_date}_{lookback_days}"
        cached_result = self._get_cached_analysis(cache_key)
        if cached_result:
            logger.info(f"Using cached analysis for {symbol}")
            return cached_result

        try:
            # Step 1: Get comprehensive filing data
            # Calculate date range for filings
            end_date = pd.to_datetime(analysis_date)
            start_date = end_date - timedelta(days=lookback_days)
            
            filings = self.data_service.get_sec_filings(
                symbol, 
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )

            if filings.empty:
                raise ValueError(f"CRITICAL: No SEC filings found for {symbol}")

            # Step 2: Get fundamental data for cross-validation
            # For now, use empty dict as IB fundamental data requires subscription
            fundamental_data = {
                "market_cap": None,
                "shares_outstanding": None,
                "revenue": None,
                "earnings": None
            }

            # Step 3: Analyze important filings with LLM
            filing_analyses = self._analyze_important_filings(filings, fundamental_data, as_of_date)

            if len(filing_analyses) < 1:
                raise ValueError(
                    f"CRITICAL: No filings analyzed for {symbol} - "
                    f"insufficient for ATM prediction"
                )
            elif len(filing_analyses) < 2:
                logger.warning(
                    f"Only analyzed {len(filing_analyses)} filing for {symbol} - "
                    f"analysis may be limited"
                )

            # Step 4: Get market context
            market_context = self._get_market_context(symbol, analysis_date)

            # Step 5: Insider accumulation detection
            insider_analysis = self.data_service.detect_insider_accumulation(symbol)

            # Step 6: Aggregate into final assessment
            final_assessment = self._create_final_assessment(
                symbol=symbol,
                analysis_date=analysis_date,
                filing_analyses=filing_analyses,
                fundamental_data=fundamental_data,
                market_context=market_context,
                insider_analysis=insider_analysis,
            )

            # Step 7: Cache the result
            processing_time = (datetime.now() - start_time).total_seconds()
            final_assessment["processing_time"] = processing_time

            self._cache_analysis(cache_key, final_assessment)

            logger.info(
                f"Completed comprehensive ATM analysis for {symbol} in {processing_time:.1f}s"
            )
            return final_assessment

        except Exception as e:
            logger.error(f"ATM analysis failed for {symbol}: {e}")
            # FAIL LOUDLY - do not return fake data
            raise ValueError(f"CRITICAL: ATM analysis failed for {symbol} - {e}")

    def _analyze_important_filings(
        self, filings: pd.DataFrame, fundamental_data: Dict, as_of_date: str
    ) -> List[Dict[str, Any]]:
        """Analyze the most important filings for ATM prediction."""

        # Prioritize filing types for ATM analysis
        priority_forms = ["10-Q", "10-K", "8-K", "S-3", "424B5", "S-1"]

        # Sort by priority and recency
        important_filings = []
        for form in priority_forms:
            form_filings = filings[filings["form_type"] == form].head(
                3
            )  # Max 3 per type
            important_filings.append(form_filings)

        # Add any other recent filings (last 6 months)
        recent_cutoff = (
            pd.to_datetime(filings["filed_at"].max()) - timedelta(days=180)
        ).strftime("%Y-%m-%d")
        recent_others = filings[
            (~filings["form_type"].isin(priority_forms))
            & (filings["filed_at"] >= recent_cutoff)
        ].head(5)
        important_filings.append(recent_others)

        # Combine and deduplicate
        combined = pd.concat(important_filings, ignore_index=True)
        combined = combined.drop_duplicates(subset=["accession_number"]).sort_values(
            "filed_at", ascending=False
        )

        logger.info(f"Selected {len(combined)} important filings for analysis")
        
        # If we still don't have filing URLs, try to construct them
        if 'filing_url' in combined.columns:
            mask = combined['filing_url'].isna() | (combined['filing_url'] == '')
            if mask.any():
                # Construct URLs for missing ones
                # Standard SEC URL format
                symbol = combined.iloc[0]['symbol'] if not combined.empty else ''
                for idx in combined[mask].index:
                    accession = combined.loc[idx, 'accession_number']
                    # Basic URL construction - may need CIK lookup for perfect URLs
                    url = f"https://www.sec.gov/Archives/edgar/data/0000000000/{accession.replace('-', '')}/{accession}.txt"
                    combined.loc[idx, 'filing_url'] = url
                    logger.info(f"Constructed URL for {accession}")

        # Analyze each filing
        analyses = []
        for _, filing in combined.iterrows():
            try:
                analysis = self._analyze_single_filing(filing, fundamental_data, as_of_date)
                if analysis:
                    analyses.append(analysis)
            except Exception as e:
                logger.warning(
                    f"Failed to analyze {filing['form_type']} from {filing['filed_at']}: {e}"
                )
                continue

        return analyses

    def _analyze_single_filing(
        self, filing: pd.Series, fundamental_data: Dict, as_of_date: str = None
    ) -> Dict[str, Any]:
        """Analyze a single filing with comprehensive context."""

        try:
            # Set as_of_date for backtesting
            if as_of_date is None:
                as_of_date = datetime.now().strftime('%Y-%m-%d')
            
            # Check backtest-aware LLM cache first
            symbol = filing['symbol']
            # Convert Timestamp to string for caching
            filing_date = filing['filed_at'].strftime('%Y-%m-%d') if hasattr(filing['filed_at'], 'strftime') else str(filing['filed_at'])
            analysis_type = "comprehensive_atm_risk"
            
            # Get filing text
            filing_text = self._get_filing_text(filing)
            if not filing_text or len(filing_text) < 1000:
                logger.warning(f"Insufficient text for {filing['form_type']}")
                return None

            # Create comprehensive analysis prompt
            fundamental_context = self._format_fundamental_context(fundamental_data)

            prompt = f"""
Analyze this SEC filing for At-The-Market (ATM) offering risk.

FILING DETAILS:
- Symbol: {filing['symbol']}
- Form: {filing['form_type']}
- Filed: {filing_date}
- Source: {filing.get('source', 'unknown')}

FUNDAMENTAL DATA CONTEXT:
{fundamental_context}

FILING TEXT (first 12000 chars):
{filing_text[:12000]}

ANALYSIS REQUIREMENTS:

1. FINANCIAL POSITION:
   - Extract exact cash and cash equivalents
   - Calculate quarterly/monthly burn rate
   - Assess cash runway in months
   - CRITICAL: Calculate expected ATM date (filing date + runway - 2 month buffer)

2. ATM PROGRAM ANALYSIS:
   - Search for "at-the-market", "equity distribution agreement", "ATM"
   - Identify shelf registration capacity
   - Check for recent equity offerings

3. DILUTION RISK FACTORS:
   - Recent stock issuances or warrant exercises
   - Conversion features (convertible debt, preferred)
   - Employee stock option exercises

4. BUSINESS RISKS:
   - Clinical trial risks (biotech)
   - Regulatory risks
   - Market competition

5. CROSS-VALIDATION:
   - Compare filing data with fundamental data provided
   - Flag any major discrepancies

Return ONLY valid JSON:
{{
  "cash_position": [exact USD amount from filing],
  "quarterly_burn": [quarterly cash burn from operations],
  "monthly_burn": [monthly burn calculated],
  "cash_runway_months": [months until cash depletion],
  "cash_depletion_date": "YYYY-MM-DD when cash runs out",
  "predicted_atm_date": "YYYY-MM-DD when ATM likely (2 months before depletion)",
  "atm_date_confidence": "HIGH/MEDIUM/LOW confidence in date prediction",
  "atm_date_justification": "Calculation: filing date + X months runway - 2 month buffer = DATE",
  "has_atm_program": [true/false],
  "atm_capacity": [USD amount available],
  "shelf_capacity": [total shelf registration capacity],
  "recent_dilution": [true if recent equity offerings],
  "dilution_risk_score": [0.0-1.0 scale],
  "business_risk_score": [0.0-1.0 scale],
  "fundamental_consistency": [true if consistent with fundamental data],
  "key_risk_factors": ["list", "of", "key", "risks"],
  "confidence": [0.0-1.0 confidence in analysis],
  "analysis_notes": "2-3 sentence summary including ATM timing"
}}

CRITICAL: Return real numbers extracted from filing. Fail if unable to extract meaningful data.
"""

            # Check backtest-aware cache before calling LLM
            cached_result = self.llm_cache.get_as_of_date(
                symbol=symbol,
                analysis_type=analysis_type,
                filing_date=filing_date,
                prompt=filing_text[:1000],  # Use first 1000 chars as identifier
                as_of_date=as_of_date,
                model="gemini/gemini-1.5-flash"
            )
            
            if cached_result:
                # Extract cache metadata
                cache_meta = cached_result.pop('_cache_metadata', {})
                logger.info(
                    f"Using cached LLM analysis for {symbol} {filing['form_type']} "
                    f"(analysis from {cache_meta.get('analysis_date')} for as_of {as_of_date})"
                )
                # Add metadata to cached result
                cached_result.update({
                    "filing_type": filing["form_type"],
                    "filed_date": filing_date,  # Already converted to string
                    "source": filing.get("source", "unknown"),
                    "accession_number": filing.get("accession_number", ""),
                    "analysis_timestamp": cache_meta.get('analysis_date', datetime.now().isoformat()),
                    "from_cache": True,
                    "cache_analysis_date": cache_meta.get('analysis_date')
                })
                return cached_result

            # Not in cache, call LLM
            response = litellm.completion(
                model="gemini/gemini-1.5-flash",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
                temperature=0.1,
            )

            # Parse and validate response
            response_text = response.choices[0].message.content.strip()

            # Clean markdown if present
            response_text = response_text.replace("```json", "").replace("```", "")

            # Parse JSON
            import re

            json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
            if not json_match:
                raise ValueError("No JSON found in LLM response")

            result = json.loads(json_match.group())

            # Validation - fail loudly if suspicious data
            self._validate_filing_analysis(result, filing["form_type"])

            # Save to backtest-aware cache before adding metadata
            filing_id = filing.get('id') or filing.get('filing_id')
            self.llm_cache.set(
                symbol=symbol,
                analysis_type=analysis_type,
                filing_date=filing_date,
                prompt=filing_text[:1000],
                response=result,
                analysis_date=as_of_date,  # Critical for backtesting
                model="gemini/gemini-1.5-flash",
                filing_id=filing_id
            )

            # Add metadata
            result.update(
                {
                    "filing_type": filing["form_type"],
                    "filed_date": filing_date,  # Already converted to string
                    "source": filing.get("source", "unknown"),
                    "accession_number": filing.get("accession_number", ""),
                    "analysis_timestamp": datetime.now().isoformat(),
                    "from_cache": False
                }
            )

            # Safe logging with None handling
            cash_pos = result.get('cash_position', 0) or 0
            runway = result.get('cash_runway_months', 0) or 0
            atm_date = result.get('predicted_atm_date', 'Not calculated')
            
            logger.info(
                f"Analyzed {filing['form_type']} - Cash: ${cash_pos:,}, "
                f"Runway: {runway} months, "
                f"ATM Date: {atm_date}"
            )

            return result

        except Exception as e:
            logger.error(f"Failed to analyze {filing['form_type']}: {e}")
            raise ValueError(f"Filing analysis failed: {e}")

    def _get_filing_text(self, filing: pd.Series) -> str:
        """Get filing text from cache or various sources."""
        
        # Get filing ID and accession number
        filing_id = filing.get('id') or filing.get('filing_id')
        accession_number = filing.get('accession_number', '')
        filing_url = filing.get('filing_url', '')
        edgar_filing = filing.get('edgar_filing')
        symbol = filing.get('symbol', '')
        form_type = filing.get('form_type', '')
        
        # If we have a filing ID, use the text cache
        if filing_id and accession_number:
            try:
                text, metadata = self.text_cache.get_filing_text(
                    filing_id=filing_id,
                    accession_number=accession_number,
                    filing_url=filing_url,
                    edgar_filing_obj=edgar_filing,
                    symbol=symbol,
                    form_type=form_type
                )
                
                if text:
                    cache_status = "from cache" if metadata.get('from_cache') else "newly cached"
                    logger.info(f"Got filing text ({cache_status}): {metadata.get('text_length', 0):,} chars")
                    return text
                    
            except Exception as e:
                logger.warning(f"Text cache error: {e}, falling back to direct methods")
        
        # Fallback: Method 1 - Edgar filing object (if available)
        if "edgar_filing" in filing and filing["edgar_filing"] is not None:
            try:
                return filing["edgar_filing"].text
            except Exception as e:
                logger.warning(f"Could not get text from edgar filing: {e}")

        # Fallback: Method 2 - Direct URL fetch
        if "filing_url" in filing and filing["filing_url"]:
            try:
                import requests

                response = requests.get(filing["filing_url"], timeout=30)
                if response.status_code == 200:
                    # Basic text extraction
                    text = response.text
                    # Remove HTML tags
                    import re

                    text = re.sub(r"<[^>]+>", " ", text)
                    text = re.sub(r"\s+", " ", text)
                    return text
            except Exception as e:
                logger.warning(f"Could not fetch filing from URL: {e}")

        return ""

    def _format_fundamental_context(self, fundamental_data: Dict) -> str:
        """Format fundamental data for LLM context."""

        if not fundamental_data or not fundamental_data.get("sources"):
            return "No fundamental data available for cross-validation."

        context = (
            f"Fundamental Data Sources: {', '.join(fundamental_data['sources'])}\n\n"
        )

        # Latest EPS
        if fundamental_data.get("latest_eps_ttm"):
            context += f"Latest EPS TTM: ${fundamental_data['latest_eps_ttm']}\n"

        # Income statement data
        if fundamental_data.get("income_quarter"):
            context += "Recent Quarterly Income Data Available\n"

        # Derived data from filings
        if fundamental_data.get("derived_cash_position"):
            context += f"Derived Cash Position: ${fundamental_data['derived_cash_position']:,}\n"

        if fundamental_data.get("derived_revenue"):
            context += f"Derived Revenue: ${fundamental_data['derived_revenue']:,}\n"

        return context

    def _validate_filing_analysis(self, result: Dict, form_type: str):
        """Validate filing analysis for suspicious placeholder values."""

        # Check for common placeholder values
        suspicious_cash = [5_000_000, 1_000_000, 10_000_000, 50_000_000]
        suspicious_burn = [1_000_000, 500_000, 2_000_000]

        cash_pos = result.get("cash_position", 0)
        monthly_burn = result.get("monthly_burn", 0)

        if cash_pos in suspicious_cash:
            raise ValueError(
                f"Suspicious cash position: ${cash_pos:,} (likely placeholder)"
            )

        if monthly_burn in suspicious_burn:
            raise ValueError(
                f"Suspicious burn rate: ${monthly_burn:,}/month (likely placeholder)"
            )

        # Basic sanity checks
        if cash_pos and cash_pos < 0:
            raise ValueError("Negative cash position extracted")

        if monthly_burn and monthly_burn < 0:
            raise ValueError("Negative burn rate extracted")

        confidence = result.get("confidence", 0)
        if confidence < 0.1:  # Lowered threshold - even low confidence analysis is valuable
            logger.warning(
                f"Very low confidence analysis ({confidence:.1f}) for {form_type} - data may be limited"
            )
            # Don't reject, just warn - let the analysis proceed

    def _get_market_context(self, symbol: str, analysis_date: str) -> Dict[str, Any]:
        """Get market context for the analysis."""

        try:
            # Get recent price data
            end_date = pd.to_datetime(analysis_date)
            start_date = end_date - timedelta(days=90)

            price_data = self.data_service.get_market_data(
                symbol, start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
            )

            if price_data.empty:
                return {"market_data_available": False}

            # Calculate market metrics
            latest_price = price_data.iloc[-1]["close"]
            avg_volume = price_data["volume"].mean()
            volatility = price_data["close"].pct_change().std() * np.sqrt(
                252
            )  # Annualized

            # Price trend (30-day)
            if len(price_data) >= 30:
                price_30d_ago = price_data.iloc[-30]["close"]
                price_trend = (latest_price - price_30d_ago) / price_30d_ago
            else:
                price_trend = 0

            return {
                "market_data_available": True,
                "latest_price": latest_price,
                "avg_daily_volume": avg_volume,
                "annualized_volatility": volatility,
                "price_trend_30d": price_trend,
                "market_cap_estimate": latest_price * 1_000_000,  # Rough estimate
            }

        except Exception as e:
            logger.warning(f"Could not get market context for {symbol}: {e}")
            return {"market_data_available": False}

    def _create_final_assessment(
        self,
        symbol: str,
        analysis_date: str,
        filing_analyses: List[Dict],
        fundamental_data: Dict,
        market_context: Dict,
        insider_analysis: Dict,
    ) -> Dict[str, Any]:
        """Create final comprehensive ATM risk assessment."""

        # Aggregate filing analyses
        cash_positions = [
            a["cash_position"] for a in filing_analyses if a.get("cash_position")
        ]
        burn_rates = [
            a["monthly_burn"] for a in filing_analyses if a.get("monthly_burn")
        ]
        runways = [
            a["cash_runway_months"]
            for a in filing_analyses
            if a.get("cash_runway_months")
        ]

        # ATM program detection
        has_atm = any(a.get("has_atm_program", False) for a in filing_analyses)
        atm_capacities = [
            a["atm_capacity"] for a in filing_analyses if a.get("atm_capacity", 0) > 0
        ]
        recent_dilution = any(a.get("recent_dilution", False) for a in filing_analyses)

        # Risk scores
        dilution_scores = [
            a["dilution_risk_score"]
            for a in filing_analyses
            if a.get("dilution_risk_score")
        ]
        business_scores = [
            a["business_risk_score"]
            for a in filing_analyses
            if a.get("business_risk_score")
        ]

        # Calculate sophisticated ATM probability
        atm_probability = self._calculate_final_atm_probability(
            cash_positions=cash_positions,
            burn_rates=burn_rates,
            runways=runways,
            has_atm=has_atm,
            recent_dilution=recent_dilution,
            dilution_scores=dilution_scores,
            market_context=market_context,
            insider_analysis=insider_analysis,
        )

        # Determine trend
        cash_trend = "stable"
        if len(cash_positions) >= 2:
            if cash_positions[-1] < cash_positions[0] * 0.8:
                cash_trend = "declining"
            elif cash_positions[-1] > cash_positions[0] * 1.2:
                cash_trend = "increasing"

        # Risk category
        if atm_probability > 0.75:
            risk_category = "HIGH"
        elif atm_probability > 0.45:
            risk_category = "MEDIUM"
        else:
            risk_category = "LOW"

        # Extract ATM dates from filing analyses
        atm_dates = [
            a["predicted_atm_date"] for a in filing_analyses 
            if a.get("predicted_atm_date") and a["predicted_atm_date"] != "N/A"
        ]
        atm_date = min(atm_dates) if atm_dates else None  # Take earliest predicted date
        
        # Get justification for the date
        atm_justifications = [
            a["atm_date_justification"] for a in filing_analyses 
            if a.get("predicted_atm_date") == atm_date
        ]
        atm_justification = atm_justifications[0] if atm_justifications else "No date calculation available"
        
        return {
            "symbol": symbol,
            "analysis_date": analysis_date,
            "analysis_timestamp": datetime.now().isoformat(),
            # Core ATM Assessment
            "atm_probability": atm_probability,
            "risk_category": risk_category,
            "predicted_atm_date": atm_date,
            "atm_date_justification": atm_justification,
            "has_active_atm": has_atm,
            "max_atm_capacity": max(atm_capacities) if atm_capacities else 0,
            # Financial Metrics
            "latest_cash_position": cash_positions[-1] if cash_positions else None,
            "avg_monthly_burn": np.mean(burn_rates) if burn_rates else None,
            "estimated_runway_months": np.mean(runways) if runways else None,
            "cash_trend": cash_trend,
            # Risk Factors
            "recent_dilution": recent_dilution,
            "avg_dilution_risk": np.mean(dilution_scores) if dilution_scores else 0.5,
            "avg_business_risk": np.mean(business_scores) if business_scores else 0.5,
            # Market Context
            "current_price": market_context.get("latest_price"),
            "volatility": market_context.get("annualized_volatility"),
            "recent_price_trend": market_context.get("price_trend_30d"),
            # Insider Activity
            "insider_accumulation_detected": insider_analysis.get(
                "insider_detected", False
            ),
            "insider_confidence": insider_analysis.get("confidence", 0),
            # Analysis Quality
            "filings_analyzed": len(filing_analyses),
            "data_sources": fundamental_data.get("sources", []),
            "overall_confidence": np.mean(
                [a.get("confidence", 0.5) for a in filing_analyses]
            ),
            # Summary
            "summary": f"ATM Risk: {risk_category} ({atm_probability:.0%}). "
            f"Analyzed {len(filing_analyses)} filings. "
            f"Cash trend: {cash_trend}. "
            f"Estimated runway: {np.mean(runways) if runways else 'Unknown'} months.",
            "analyzer": "final_comprehensive_v1",
            "version": "1.0.0",
        }

    def _calculate_final_atm_probability(
        self,
        cash_positions: List[float],
        burn_rates: List[float],
        runways: List[float],
        has_atm: bool,
        recent_dilution: bool,
        dilution_scores: List[float],
        market_context: Dict,
        insider_analysis: Dict,
    ) -> float:
        """Calculate final ATM probability using all available factors."""

        probability = 0.05  # Base probability

        # Factor 1: Has existing ATM program (40% weight)
        if has_atm:
            probability += 0.40

        # Factor 2: Recent dilution activity (20% weight)
        if recent_dilution:
            probability += 0.20

        # Factor 3: Cash runway analysis (25% weight)
        if runways:
            min_runway = min(runways)
            if min_runway < 3:
                probability += 0.25
            elif min_runway < 6:
                probability += 0.20
            elif min_runway < 12:
                probability += 0.15
            elif min_runway < 18:
                probability += 0.10

        # Factor 4: Dilution risk scores (10% weight)
        if dilution_scores:
            avg_dilution_risk = np.mean(dilution_scores)
            probability += avg_dilution_risk * 0.10

        # Factor 5: Market volatility (higher volatility = higher ATM probability)
        volatility = market_context.get("annualized_volatility", 0)
        if volatility > 0.8:  # 80% annual volatility
            probability += 0.05
        elif volatility > 1.5:  # 150% annual volatility
            probability += 0.10

        # Factor 6: Insider activity (contrarian indicator)
        if insider_analysis.get("insider_detected"):
            # Insider buying suggests less likely ATM
            probability -= 0.05

        return min(probability, 1.0)

    def _get_cached_analysis(self, cache_key: str) -> Optional[Dict]:
        """Get cached analysis if available and fresh."""
        try:
            # Implementation would check cache database
            # For now, return None (no cache)
            return None
        except Exception:
            return None

    def _cache_analysis(self, cache_key: str, analysis: Dict):
        """Cache analysis result."""
        try:
            # Implementation would save to cache database
            pass
        except Exception as e:
            logger.warning(f"Failed to cache analysis: {e}")

    def close(self):
        """Clean up resources."""
        if self.data_service:
            self.data_service.close()
        if self.cache_manager:
            self.cache_manager.close()
        if self.text_cache:
            self.text_cache.close()


def test_final_analyzer():
    """Test the final comprehensive analyzer."""
    print("Testing Final Comprehensive ATM Analyzer...")

    analyzer = FinalATMAnalyzer()

    try:
        # Test with a known biotech stock (likely to have ATM programs)
        symbol = "SAVA"

        result = analyzer.analyze_atm_risk(symbol)

        print(f"\n🎯 FINAL ATM ANALYSIS for {symbol}:")
        print(f"   ATM Probability: {result['atm_probability']:.0%}")
        print(f"   Risk Category: {result['risk_category']}")
        print(f"   Has Active ATM: {result['has_active_atm']}")
        print(f"   Cash Runway: {result.get('estimated_runway_months', 'N/A')} months")
        print(f"   Cash Trend: {result['cash_trend']}")
        print(f"   Recent Dilution: {result['recent_dilution']}")
        print(f"   Filings Analyzed: {result['filings_analyzed']}")
        print(f"   Data Sources: {result['data_sources']}")
        print(f"   Overall Confidence: {result['overall_confidence']:.0%}")
        print(f"   Summary: {result['summary']}")

        # Validate critical requirements
        assert result["filings_analyzed"] >= 2, "Must analyze multiple filings"
        assert 0 <= result["atm_probability"] <= 1, "ATM probability must be 0-1"
        assert result["risk_category"] in [
            "LOW",
            "MEDIUM",
            "HIGH",
        ], "Invalid risk category"

        print("\n✅ Final analyzer test PASSED - real analysis completed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
    finally:
        analyzer.close()


if __name__ == "__main__":
    test_final_analyzer()
