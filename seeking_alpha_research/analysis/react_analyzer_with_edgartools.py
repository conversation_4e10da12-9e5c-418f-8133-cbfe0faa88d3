#!/usr/bin/env python3
"""
Enhanced ReAct Analyzer with EdgarTools Integration
Tries EdgarTools XBRL extraction first for accurate financial data

NO FALLBACKS - If <PERSON><PERSON><PERSON><PERSON> fails, we fail (money is on the line)
"""

import os
from typing import Dict, Any, Optional
from datetime import datetime

from core.logger import get_logger

logger = get_logger(__name__)

# Try to import EdgarTools analyzer
try:
    from analysis.react_edgartools_analyzer import ReactEdgarToolsAnalyzer
    EDGARTOOLS_AVAILABLE = True
except ImportError as e:
    EDGARTOOLS_AVAILABLE = False
    logger.warning(f"EdgarTools analyzer not available: {e}")

# Import regular analyzer as backup
from analysis.react_comprehensive_analyzer import ReactATMAnalyzer


class ReactAnalyzerWithEdgarTools:
    """
    Enhanced ReAct analyzer that uses EdgarTools for accurate XBRL extraction.
    NO FALLBACKS - If extraction fails, analysis fails.
    """
    
    def __init__(self, max_workers: int = 2):
        """Initialize with <PERSON><PERSON><PERSON><PERSON> if available."""
        
        self.max_workers = max_workers
        
        if EDGARTOOLS_AVAILABLE:
            logger.info("✅ Using EdgarTools for XBRL extraction")
            self.analyzer = ReactEdgarToolsAnalyzer(max_workers=max_workers)
            self.using_edgartools = True
        else:
            raise ValueError(
                "CRITICAL: EdgarTools not available! "
                "Install with: pip install edgartools"
            )
    
    def analyze_atm_risk(
        self, 
        symbol: str, 
        analysis_date: str = None, 
        lookback_days: int = 365,
        as_of_date: str = None, 
        fundamentals_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Analyze ATM risk using EdgarTools for accurate financial data.
        
        NO FALLBACKS - Real data only!
        """
        
        if analysis_date is None:
            analysis_date = datetime.now().strftime("%Y-%m-%d")
        
        logger.info(f"Analyzing {symbol} with EdgarTools XBRL extraction")
        
        try:
            # Use EdgarTools analyzer
            result = self.analyzer.analyze_atm_risk(
                symbol=symbol,
                analysis_date=analysis_date,
                lookback_days=lookback_days,
                as_of_date=as_of_date,
                fundamentals_data=fundamentals_data
            )
            
            # Validate we got real data
            cash = result.get('latest_cash_position', 0)
            burn = result.get('avg_monthly_burn', 0)
            revenue = result.get('latest_revenue', 0)
            
            if cash == 0 and burn == 0 and revenue == 0:
                raise ValueError(
                    f"CRITICAL: EdgarTools extracted $0 for all metrics! "
                    f"Symbol: {symbol}, Filings analyzed: {result.get('filings_analyzed', 0)}"
                )
            
            # Add metadata
            result['extraction_method'] = 'EdgarTools_XBRL'
            result['extraction_quality'] = 'HIGH'
            
            logger.info(
                f"✅ EdgarTools extraction successful: "
                f"Cash=${cash:,.0f}, Burn=${burn:,.0f}, Revenue=${revenue:,.0f}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"EdgarTools extraction failed: {e}")
            # NO FALLBACK - FAIL HARD
            raise ValueError(
                f"CRITICAL: EdgarTools extraction failed for {symbol}. "
                f"Error: {str(e)}. "
                f"NO FALLBACK - money is on the line!"
            )
    
    def close(self):
        """Clean up resources."""
        if hasattr(self, 'analyzer'):
            self.analyzer.close()


# Alias for compatibility
EnhancedReActAnalyzer = ReactAnalyzerWithEdgarTools