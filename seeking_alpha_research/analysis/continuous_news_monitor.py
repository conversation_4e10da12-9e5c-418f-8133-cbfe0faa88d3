"""
Continuous News Monitor

Monitors news every single day from entry to exit with accurate timestamps.
Critical for detecting:
1. Catalyst news that could trigger gaps
2. Negative news that requires immediate exit
3. ATM announcement news
4. Regulatory/suspension news

NO FAKES, NO MOCKS - Real news with real timestamps
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import pytz

from core.data_service import DataService

logger = logging.getLogger(__name__)


class ContinuousNewsMonitor:
    """Monitors news continuously from entry to exit."""
    
    def __init__(self, data_service: DataService = None):
        self.data_service = data_service or DataService()
        self.eastern_tz = pytz.timezone('US/Eastern')
        logger.info("Continuous News Monitor initialized")
    
    def monitor_news_for_position(
        self, 
        symbol: str,
        entry_date: str,
        exit_date: str = None,
        check_premarket: bool = True
    ) -> Dict[str, any]:
        """
        Monitor news for every day from entry to exit.
        
        Args:
            symbol: Stock symbol
            entry_date: Position entry date
            exit_date: Position exit date (or current date if None)
            check_premarket: Whether to classify premarket vs regular hours
        
        Returns:
            Comprehensive news analysis with daily breakdown
        """
        logger.info(f"Monitoring news for {symbol} from {entry_date} to {exit_date or 'present'}")
        
        start = pd.to_datetime(entry_date)
        end = pd.to_datetime(exit_date) if exit_date else datetime.now()
        
        # Get all news in the period
        try:
            news_df = self.data_service.get_news(
                symbol, 
                start.strftime('%Y-%m-%d'),
                end.strftime('%Y-%m-%d')
            )
            
            if news_df.empty:
                logger.warning(f"No news found for {symbol} in period")
                return self._empty_results(symbol, entry_date, exit_date)
            
            # Process news with accurate timestamps
            processed_news = self._process_news_with_timestamps(news_df, check_premarket)
            
            # Analyze news by day
            daily_analysis = self._analyze_daily_news(processed_news, start, end)
            
            # Detect critical events
            critical_events = self._detect_critical_events(processed_news)
            
            # Analyze news patterns
            patterns = self._analyze_news_patterns(daily_analysis)
            
            # Generate alerts
            alerts = self._generate_news_alerts(critical_events, patterns)
            
            return {
                'symbol': symbol,
                'monitoring_period': {
                    'start': entry_date,
                    'end': exit_date or end.strftime('%Y-%m-%d'),
                    'days_monitored': (end - start).days + 1
                },
                'total_news_count': len(news_df),
                'daily_breakdown': daily_analysis,
                'critical_events': critical_events,
                'news_patterns': patterns,
                'alerts': alerts,
                'summary': self._generate_summary(daily_analysis, critical_events, alerts)
            }
            
        except Exception as e:
            logger.error(f"News monitoring failed: {e}")
            return self._empty_results(symbol, entry_date, exit_date, error=str(e))
    
    def _process_news_with_timestamps(
        self, news_df: pd.DataFrame, check_premarket: bool
    ) -> pd.DataFrame:
        """Process news with accurate timestamp classification."""
        
        processed = news_df.copy()
        
        # Ensure we have proper timestamps
        if 'created_at' in processed.columns:
            processed['timestamp'] = pd.to_datetime(processed['created_at'])
        else:
            # Fallback to any available date column
            date_cols = [col for col in processed.columns if 'date' in col.lower()]
            if date_cols:
                processed['timestamp'] = pd.to_datetime(processed[date_cols[0]])
            else:
                processed['timestamp'] = pd.Timestamp.now()
        
        # Convert to Eastern time
        if processed['timestamp'].dt.tz is None:
            processed['timestamp'] = processed['timestamp'].dt.tz_localize('UTC')
        processed['timestamp_et'] = processed['timestamp'].dt.tz_convert(self.eastern_tz)
        
        # Extract date and time components
        processed['date'] = processed['timestamp_et'].dt.date
        processed['time'] = processed['timestamp_et'].dt.time
        processed['hour'] = processed['timestamp_et'].dt.hour
        processed['minute'] = processed['timestamp_et'].dt.minute
        
        # Classify timing
        if check_premarket:
            processed['market_session'] = processed.apply(
                lambda row: self._classify_market_session(row['hour'], row['minute']), 
                axis=1
            )
        
        # Sort by timestamp
        processed = processed.sort_values('timestamp_et')
        
        return processed
    
    def _classify_market_session(self, hour: int, minute: int) -> str:
        """Classify which market session the news appeared in."""
        
        time_minutes = hour * 60 + minute
        
        if time_minutes < 4 * 60:  # Before 4:00 AM
            return 'overnight'
        elif time_minutes < 9 * 60 + 30:  # 4:00 AM - 9:30 AM
            return 'premarket'
        elif time_minutes < 16 * 60:  # 9:30 AM - 4:00 PM
            return 'regular'
        elif time_minutes < 20 * 60:  # 4:00 PM - 8:00 PM
            return 'afterhours'
        else:  # After 8:00 PM
            return 'overnight'
    
    def _analyze_daily_news(
        self, news_df: pd.DataFrame, start: pd.Timestamp, end: pd.Timestamp
    ) -> List[Dict]:
        """Analyze news for each day in the period."""
        
        daily_analysis = []
        current_date = start
        
        while current_date <= end:
            day_str = current_date.strftime('%Y-%m-%d')
            day_news = news_df[news_df['date'] == current_date.date()]
            
            day_analysis = {
                'date': day_str,
                'weekday': current_date.strftime('%A'),
                'news_count': len(day_news),
                'has_news': len(day_news) > 0
            }
            
            if len(day_news) > 0:
                # Count by session
                session_counts = day_news['market_session'].value_counts().to_dict()
                day_analysis['session_breakdown'] = session_counts
                
                # Premarket news details
                premarket_news = day_news[day_news['market_session'] == 'premarket']
                day_analysis['premarket_news_count'] = len(premarket_news)
                day_analysis['has_premarket_catalyst'] = len(premarket_news) > 0
                
                # Key headlines
                day_analysis['headlines'] = day_news['headline'].head(3).tolist()
                
                # Sentiment analysis (basic keyword approach)
                sentiment = self._analyze_headlines_sentiment(day_news['headline'].tolist())
                day_analysis['sentiment'] = sentiment
                
                # Critical news check
                critical = self._check_critical_keywords(day_news['headline'].tolist())
                day_analysis['has_critical_news'] = critical['has_critical']
                day_analysis['critical_type'] = critical['type']
                
                # Sources
                day_analysis['sources'] = day_news['source'].unique().tolist() if 'source' in day_news.columns else []
                
                # Timing details
                first_news = day_news.iloc[0]
                day_analysis['first_news_time'] = first_news['time'].strftime('%H:%M:%S')
                day_analysis['first_news_session'] = first_news['market_session']
            else:
                day_analysis['session_breakdown'] = {}
                day_analysis['premarket_news_count'] = 0
                day_analysis['has_premarket_catalyst'] = False
                day_analysis['headlines'] = []
                day_analysis['sentiment'] = 'neutral'
                day_analysis['has_critical_news'] = False
                day_analysis['critical_type'] = None
            
            daily_analysis.append(day_analysis)
            current_date += timedelta(days=1)
        
        return daily_analysis
    
    def _analyze_headlines_sentiment(self, headlines: List[str]) -> str:
        """Basic sentiment analysis of headlines."""
        
        if not headlines:
            return 'neutral'
        
        positive_keywords = [
            'surge', 'soar', 'jump', 'rally', 'gain', 'rise', 'breakthrough',
            'record', 'beat', 'exceed', 'positive', 'strong', 'growth', 'profit',
            'upgrade', 'buy', 'outperform', 'success', 'milestone'
        ]
        
        negative_keywords = [
            'plunge', 'crash', 'fall', 'drop', 'decline', 'loss', 'weak',
            'concern', 'warning', 'investigation', 'lawsuit', 'bankruptcy',
            'delisting', 'suspension', 'violation', 'fraud', 'scandal',
            'downgrade', 'sell', 'underperform', 'fail', 'miss'
        ]
        
        atm_keywords = [
            'offering', 'dilution', 'shares', 'atm', 'at-the-market',
            'capital raise', 'financing', 'shelf', 'registration'
        ]
        
        combined_text = ' '.join(headlines).lower()
        
        positive_count = sum(1 for kw in positive_keywords if kw in combined_text)
        negative_count = sum(1 for kw in negative_keywords if kw in combined_text)
        atm_count = sum(1 for kw in atm_keywords if kw in combined_text)
        
        if atm_count > 0:
            return 'atm_related'
        elif positive_count > negative_count * 2:
            return 'positive'
        elif negative_count > positive_count * 2:
            return 'negative'
        else:
            return 'mixed'
    
    def _check_critical_keywords(self, headlines: List[str]) -> Dict[str, any]:
        """Check for critical keywords that require immediate attention."""
        
        combined_text = ' '.join(headlines).lower()
        
        critical_categories = {
            'suspension': ['suspend', 'delist', 'halt', 'investigation', 'sec investigation'],
            'offering': ['offering', 'atm', 'at-the-market', 'dilut', 'shelf'],
            'bankruptcy': ['bankrupt', 'chapter', 'liquidat', 'insolvency'],
            'fraud': ['fraud', 'scandal', 'misrepresent', 'restatement'],
            'management': ['ceo resign', 'cfo resign', 'board change', 'management change']
        }
        
        for category, keywords in critical_categories.items():
            if any(kw in combined_text for kw in keywords):
                return {
                    'has_critical': True,
                    'type': category,
                    'keywords_found': [kw for kw in keywords if kw in combined_text]
                }
        
        return {'has_critical': False, 'type': None, 'keywords_found': []}
    
    def _detect_critical_events(self, news_df: pd.DataFrame) -> List[Dict]:
        """Detect critical news events that require action."""
        
        critical_events = []
        
        for _, news in news_df.iterrows():
            headline_lower = news['headline'].lower()
            
            # Check each critical pattern
            event = None
            
            # ATM/Offering detection
            if any(kw in headline_lower for kw in ['offering', 'atm', 'dilut', 'shelf', 'registration']):
                event = {
                    'type': 'offering',
                    'severity': 'high',
                    'action_required': 'monitor_closely'
                }
            
            # Suspension/Delisting
            elif any(kw in headline_lower for kw in ['suspend', 'delist', 'halt']):
                event = {
                    'type': 'suspension_risk',
                    'severity': 'critical',
                    'action_required': 'exit_immediately'
                }
            
            # Bankruptcy
            elif any(kw in headline_lower for kw in ['bankrupt', 'chapter', 'liquidat']):
                event = {
                    'type': 'bankruptcy_risk',
                    'severity': 'critical',
                    'action_required': 'exit_immediately'
                }
            
            # Major gap catalyst
            elif any(kw in headline_lower for kw in ['fda approval', 'breakthrough', 'partnership', 'acquisition']):
                event = {
                    'type': 'gap_catalyst',
                    'severity': 'medium',
                    'action_required': 'prepare_exit'
                }
            
            if event:
                event.update({
                    'date': news['date'].strftime('%Y-%m-%d'),
                    'time': news['time'].strftime('%H:%M:%S'),
                    'session': news['market_session'],
                    'headline': news['headline'],
                    'source': news.get('source', 'Unknown')
                })
                critical_events.append(event)
        
        return sorted(critical_events, key=lambda x: x['date'])
    
    def _analyze_news_patterns(self, daily_analysis: List[Dict]) -> Dict[str, any]:
        """Analyze patterns in news flow."""
        
        if not daily_analysis:
            return {}
        
        # Extract metrics
        news_counts = [d['news_count'] for d in daily_analysis]
        days_with_news = sum(1 for d in daily_analysis if d['has_news'])
        premarket_days = sum(1 for d in daily_analysis if d.get('has_premarket_catalyst', False))
        
        # News frequency
        total_days = len(daily_analysis)
        news_frequency = days_with_news / total_days if total_days > 0 else 0
        
        # Sentiment distribution
        sentiments = [d.get('sentiment', 'neutral') for d in daily_analysis if d['has_news']]
        sentiment_dist = pd.Series(sentiments).value_counts().to_dict()
        
        # Critical news days
        critical_days = [d for d in daily_analysis if d.get('has_critical_news', False)]
        
        # News clustering (consecutive days with news)
        clusters = []
        current_cluster = []
        
        for day in daily_analysis:
            if day['has_news']:
                current_cluster.append(day['date'])
            elif current_cluster:
                if len(current_cluster) > 1:
                    clusters.append({
                        'start': current_cluster[0],
                        'end': current_cluster[-1],
                        'days': len(current_cluster)
                    })
                current_cluster = []
        
        # Don't forget the last cluster
        if len(current_cluster) > 1:
            clusters.append({
                'start': current_cluster[0],
                'end': current_cluster[-1],
                'days': len(current_cluster)
            })
        
        # Average news per day
        avg_news_per_day = np.mean(news_counts) if news_counts else 0
        max_news_day = max(news_counts) if news_counts else 0
        
        return {
            'total_days_monitored': total_days,
            'days_with_news': days_with_news,
            'news_frequency': float(news_frequency),
            'premarket_catalyst_days': premarket_days,
            'avg_news_per_day': float(avg_news_per_day),
            'max_news_in_day': int(max_news_day),
            'sentiment_distribution': sentiment_dist,
            'critical_news_days': len(critical_days),
            'news_clusters': clusters,
            'longest_cluster_days': max([c['days'] for c in clusters]) if clusters else 0,
            'days_without_news': total_days - days_with_news
        }
    
    def _generate_news_alerts(
        self, critical_events: List[Dict], patterns: Dict[str, any]
    ) -> List[Dict]:
        """Generate actionable alerts based on news analysis."""
        
        alerts = []
        
        # Critical event alerts
        for event in critical_events:
            if event['severity'] == 'critical':
                alerts.append({
                    'priority': 'URGENT',
                    'type': event['type'],
                    'date': event['date'],
                    'message': f"CRITICAL: {event['type']} detected - {event['action_required']}",
                    'action': event['action_required']
                })
        
        # Pattern-based alerts
        if patterns.get('news_frequency', 0) > 0.8:
            alerts.append({
                'priority': 'HIGH',
                'type': 'high_news_frequency',
                'message': 'Unusually high news frequency - major event may be developing',
                'action': 'monitor_closely'
            })
        
        if patterns.get('sentiment_distribution', {}).get('negative', 0) > 3:
            alerts.append({
                'priority': 'HIGH',
                'type': 'negative_sentiment',
                'message': 'Multiple days of negative news - consider risk reduction',
                'action': 'review_position'
            })
        
        if patterns.get('sentiment_distribution', {}).get('atm_related', 0) > 0:
            alerts.append({
                'priority': 'HIGH',
                'type': 'atm_news',
                'message': 'ATM-related news detected - prepare for potential dilution',
                'action': 'prepare_exit'
            })
        
        # Sort by priority
        priority_order = {'URGENT': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3}
        alerts.sort(key=lambda x: priority_order.get(x['priority'], 99))
        
        return alerts
    
    def _generate_summary(
        self, daily_analysis: List[Dict], critical_events: List[Dict], alerts: List[Dict]
    ) -> Dict[str, any]:
        """Generate executive summary of news monitoring."""
        
        # Key metrics
        total_days = len(daily_analysis)
        days_with_news = sum(1 for d in daily_analysis if d['has_news'])
        total_news = sum(d['news_count'] for d in daily_analysis)
        
        # Risk assessment
        risk_level = 'LOW'
        if any(a['priority'] == 'URGENT' for a in alerts):
            risk_level = 'CRITICAL'
        elif any(a['priority'] == 'HIGH' for a in alerts):
            risk_level = 'HIGH'
        elif len(critical_events) > 2:
            risk_level = 'MEDIUM'
        
        # Key dates
        high_activity_days = [
            d['date'] for d in daily_analysis 
            if d['news_count'] > 3
        ]
        
        premarket_catalyst_days = [
            d['date'] for d in daily_analysis 
            if d.get('has_premarket_catalyst', False)
        ]
        
        return {
            'monitoring_summary': f"{total_news} news items over {total_days} days",
            'news_coverage': f"{days_with_news}/{total_days} days had news",
            'risk_level': risk_level,
            'urgent_alerts': sum(1 for a in alerts if a['priority'] == 'URGENT'),
            'critical_events_count': len(critical_events),
            'high_activity_days': high_activity_days[:5],  # Top 5
            'premarket_catalyst_days': premarket_catalyst_days,
            'recommendation': self._generate_recommendation(risk_level, alerts, critical_events)
        }
    
    def _generate_recommendation(
        self, risk_level: str, alerts: List[Dict], critical_events: List[Dict]
    ) -> str:
        """Generate recommendation based on news analysis."""
        
        if risk_level == 'CRITICAL':
            return "EXIT IMMEDIATELY - Critical news events detected"
        elif risk_level == 'HIGH':
            if any(e['type'] == 'offering' for e in critical_events):
                return "PREPARE EXIT - ATM offering likely, exit on next gap"
            else:
                return "MONITOR CLOSELY - High risk news events detected"
        elif risk_level == 'MEDIUM':
            return "STAY ALERT - Multiple news events, watch for gaps"
        else:
            return "HOLD POSITION - No concerning news patterns"
    
    def _empty_results(
        self, symbol: str, entry_date: str, exit_date: str = None, error: str = None
    ) -> Dict[str, any]:
        """Return empty results structure."""
        
        return {
            'symbol': symbol,
            'monitoring_period': {
                'start': entry_date,
                'end': exit_date or datetime.now().strftime('%Y-%m-%d'),
                'days_monitored': 0
            },
            'total_news_count': 0,
            'daily_breakdown': [],
            'critical_events': [],
            'news_patterns': {},
            'alerts': [],
            'summary': {
                'monitoring_summary': 'No news found',
                'risk_level': 'UNKNOWN',
                'recommendation': 'No data available'
            },
            'error': error
        }
    
    def get_realtime_news_check(self, symbol: str) -> Dict[str, any]:
        """Quick real-time news check for current day."""
        
        today = datetime.now().strftime('%Y-%m-%d')
        
        try:
            # Get today's news
            news_df = self.data_service.get_news(symbol, today, today)
            
            if news_df.empty:
                return {
                    'has_news_today': False,
                    'premarket_news': False,
                    'news_count': 0
                }
            
            # Process with timestamps
            processed = self._process_news_with_timestamps(news_df, True)
            
            # Check for premarket news
            premarket = processed[processed['market_session'] == 'premarket']
            
            # Quick sentiment check
            sentiment = self._analyze_headlines_sentiment(processed['headline'].tolist())
            
            # Critical check
            critical = self._check_critical_keywords(processed['headline'].tolist())
            
            return {
                'has_news_today': True,
                'premarket_news': len(premarket) > 0,
                'news_count': len(processed),
                'latest_headline': processed.iloc[-1]['headline'],
                'latest_time': processed.iloc[-1]['time'].strftime('%H:%M:%S'),
                'sentiment': sentiment,
                'has_critical': critical['has_critical'],
                'critical_type': critical.get('type'),
                'action_required': 'monitor' if not critical['has_critical'] else 'review_immediately'
            }
            
        except Exception as e:
            logger.error(f"Realtime news check failed: {e}")
            return {
                'has_news_today': False,
                'error': str(e)
            }