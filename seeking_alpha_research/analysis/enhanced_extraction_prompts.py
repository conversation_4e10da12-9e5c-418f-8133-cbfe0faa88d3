#!/usr/bin/env python3
"""
Enhanced extraction prompts with specific guidance for finding financial data.
NO FAKES, NO MOCKS - Real extraction from real filings.
"""

def get_extraction_prompt(form_type: str, symbol: str, filing_date: str) -> str:
    """Get form-specific extraction prompt with detailed guidance."""
    
    if form_type in ['10-Q', '10-K']:
        return f"""You are analyzing a {form_type} filing for {symbol} filed on {filing_date}.

I will send you the filing in chunks. Your task is to extract SPECIFIC financial data.

CRITICAL EXTRACTION TARGETS:

1. CASH POSITION - Look for these EXACT patterns:
   - "Cash and cash equivalents" (usually in balance sheet or financial position)
   - "Cash, cash equivalents" 
   - "Total cash"
   - Look for lines like: "Cash and cash equivalents ... $XXX,XXX"
   - In tables, find the row with "Cash" and get the most recent period value

2. OPERATING EXPENSES/CASH BURN - Look for:
   - "Operating expenses" (in income statement)
   - "Total operating expenses"
   - "Loss from operations"
   - "Net loss"
   - For quarterly filings, use 3-month period
   - Look for: "Operating expenses ... $XXX,XXX"

3. ATM PROGRAM - Search for keywords:
   - "at-the-market"
   - "ATM"
   - "equity distribution agreement"
   - "sales agreement"
   - "Open Market Sale Agreement"
   - Common agents: Jefferies, B. Riley, Cowen, H.C. Wainwright

4. SHELF REGISTRATION - Look for:
   - "$XXX million shelf"
   - "universal shelf registration"
   - "aggregate offering price"
   - Usually large round numbers like $250M, $500M, $1B

IMPORTANT: Extract ACTUAL NUMBERS, not text descriptions.

For each chunk, respond with ONLY this JSON:
{{
  "cash_position": [number or null],
  "quarterly_expenses": [number or null],
  "has_atm_mentions": [true/false],
  "shelf_capacity": [number or null],
  "key_quotes": ["exact quote with the number", "another quote"]
}}

Example response:
{{
  "cash_position": 172900000,
  "quarterly_expenses": 95000000,
  "has_atm_mentions": true,
  "shelf_capacity": 1000000000,
  "key_quotes": ["Cash and cash equivalents totaled $172.9 million", "Operating expenses were $95.0 million for the three months"]
}}"""

    elif form_type == '8-K':
        return f"""You are analyzing an 8-K filing for {symbol} filed on {filing_date}.

8-K filings report material events. Extract any mentions of:

1. FINANCING EVENTS:
   - ATM sales executed
   - Equity offerings completed
   - Cash raised amounts
   - Look for: "sold X shares for $Y million"

2. LIQUIDITY UPDATES:
   - Going concern warnings
   - Cash position updates
   - Covenant violations

3. STRATEGIC CHANGES:
   - CEO/CFO changes (affects burn rate)
   - Restructuring (affects expenses)
   - Major partnerships (affects cash needs)

Respond with ONLY this JSON for each chunk:
{{
  "cash_raised": [amount or null],
  "shares_sold": [number or null],
  "has_atm_activity": [true/false],
  "material_events": ["event 1", "event 2"],
  "key_quotes": ["relevant quote"]
}}"""

    else:  # Generic for other forms
        return f"""You are analyzing a {form_type} filing for {symbol} filed on {filing_date}.

Extract any financial data related to:
1. Cash position
2. Expenses or burn rate
3. ATM programs or equity offerings
4. Any dilution events

Focus on NUMBERS and EXACT QUOTES.

Respond with JSON containing any relevant data found."""


def get_aggregation_prompt(all_extractions: list, filing_type: str) -> str:
    """Get prompt for aggregating extracted data from chunks."""
    
    return f"""You have extracted data from {len(all_extractions)} chunks of a {filing_type} filing.

Now AGGREGATE the results to find the BEST values:

1. CASH POSITION: Take the MOST RECENT or HIGHEST confidence value
2. EXPENSES: Take the value that represents QUARTERLY expenses (3 months)
3. ATM MENTIONS: True if ANY chunk mentioned ATM
4. SHELF CAPACITY: Take the LARGEST value mentioned

Here are all the extractions:
{all_extractions}

Respond with the FINAL aggregated values in this format:
{{
  "cash_position": [best value],
  "quarterly_expenses": [best value],
  "has_atm_mentions": [true/false],
  "shelf_capacity": [best value],
  "confidence": [0.0-1.0],
  "key_insights": ["most important finding 1", "finding 2", "finding 3"]
}}"""


def debug_extraction(filing_text: str, form_type: str) -> None:
    """Debug helper to print what patterns exist in filing."""
    
    print(f"\n🔍 DEBUG: Searching for financial data patterns in {form_type}")
    print("=" * 60)
    
    # Search for cash patterns
    cash_patterns = [
        "cash and cash equivalents",
        "total cash",
        "cash position",
        "cash, cash equivalents"
    ]
    
    for pattern in cash_patterns:
        import re
        matches = re.finditer(
            pattern + r".*?\$?([\d,]+(?:\.\d+)?)\s*(?:million|thousand)?", 
            filing_text.lower(), 
            re.IGNORECASE
        )
        for match in matches:
            context = filing_text[max(0, match.start()-50):match.end()+50]
            print(f"\nFound '{pattern}':")
            print(f"  Context: ...{context}...")
            break
    
    # Search for expense patterns
    expense_patterns = [
        "operating expenses",
        "total operating expenses",
        "loss from operations",
        "net loss"
    ]
    
    for pattern in expense_patterns:
        matches = re.finditer(
            pattern + r".*?\$?([\d,]+(?:\.\d+)?)\s*(?:million|thousand)?",
            filing_text.lower(),
            re.IGNORECASE
        )
        for match in matches:
            context = filing_text[max(0, match.start()-50):match.end()+50]
            print(f"\nFound '{pattern}':")
            print(f"  Context: ...{context}...")
            break