"""
Professional News Catalyst Validation System

Professional-level news catalyst analysis for gap confirmation:
1. Multi-source news aggregation
2. LLM-powered relevance analysis
3. Sentiment and impact scoring
4. Timing correlation analysis
5. Historical catalyst pattern matching
6. Fake news and manipulation detection
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
import sqlite3
import logging
from dataclasses import dataclass, asdict
import requests
import re
from urllib.parse import urlencode
import time


@dataclass
class NewsEvent:
    """Professional news event with complete analysis"""

    symbol: str
    headline: str
    content: str
    source: str
    timestamp: datetime
    url: Optional[str] = None

    # Analysis results
    relevance_score: float = 0.0
    sentiment_score: float = 0.0  # -1 to 1
    impact_score: float = 0.0  # 0 to 1
    credibility_score: float = 0.0  # 0 to 1

    # Categorization
    catalyst_type: Optional[str] = None  # earnings, fda, merger, etc.
    market_moving: bool = False
    gap_justifying: bool = False

    # Metadata
    word_count: int = 0
    mentions_company: bool = False
    mentions_financials: bool = False
    mentions_products: bool = False

    # Risk factors
    manipulation_risk: float = 0.0
    pump_dump_indicators: List[str] = None


@dataclass
class CatalystValidation:
    """Complete catalyst validation result"""

    symbol: str
    analysis_date: datetime
    gap_percentage: float

    # News analysis
    news_events: List[NewsEvent]
    primary_catalyst: Optional[NewsEvent]
    supporting_catalysts: List[NewsEvent]

    # Validation scores
    catalyst_strength: float = 0.0  # Overall catalyst strength (0-1)
    gap_justification: float = 0.0  # How well news justifies gap (0-1)
    timing_correlation: float = 0.0  # News timing vs gap timing (0-1)
    credibility_assessment: float = 0.0  # Overall news credibility (0-1)

    # Risk assessment
    manipulation_probability: float = 0.0  # Probability of manipulation (0-1)
    pump_dump_risk: float = 0.0  # Pump and dump risk (0-1)
    fake_news_risk: float = 0.0  # Fake news risk (0-1)

    # Validation result
    gap_validated: bool = False
    confidence_level: str = "low"
    validation_reasons: List[str] = None


class ProfessionalNewsValidator:
    """
    Professional news catalyst validation system

    Features:
    - Multi-source news aggregation (Finviz, Alpha Vantage, NewsAPI)
    - LLM-powered relevance and sentiment analysis
    - Historical catalyst pattern matching
    - Manipulation and fake news detection
    - Timing correlation analysis
    - Gap justification scoring
    """

    def __init__(self, database_path: str, config: Dict = None):
        self.db_path = database_path
        self.config = config or {}
        self.logger = logging.getLogger(__name__)

        # News sources configuration
        self.NEWS_SOURCES = {
            "finviz": self.config.get("finviz_enabled", True),
            "alpha_vantage": self.config.get("alpha_vantage_key") is not None,
            "newsapi": self.config.get("newsapi_key") is not None,
            "seeking_alpha": self.config.get("seeking_alpha_enabled", True),
        }

        # Analysis parameters
        self.NEWS_LOOKBACK_HOURS = 48  # Look back 48 hours for news
        self.MIN_RELEVANCE_SCORE = 0.6  # Minimum relevance for consideration
        self.MIN_IMPACT_SCORE = 0.5  # Minimum impact for gap justification
        self.MAX_MANIPULATION_RISK = 0.3  # Maximum acceptable manipulation risk

        # Catalyst types and their typical impact ranges
        self.CATALYST_IMPACT_RANGES = {
            "earnings_beat": (0.1, 0.5),  # 10-50% typical
            "fda_approval": (0.3, 2.0),  # 30-200% typical
            "merger_acquisition": (0.2, 0.8),  # 20-80% typical
            "contract_win": (0.1, 0.3),  # 10-30% typical
            "partnership": (0.1, 0.4),  # 10-40% typical
            "clinical_trial": (0.2, 1.0),  # 20-100% typical
            "regulatory_approval": (0.2, 0.8),  # 20-80% typical
            "guidance_raise": (0.1, 0.3),  # 10-30% typical
        }

        self._init_database()

    def _init_database(self):
        """Initialize news validation database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # News events table
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS news_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                headline TEXT NOT NULL,
                content TEXT,
                source TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                url TEXT,
                relevance_score REAL,
                sentiment_score REAL,
                impact_score REAL,
                credibility_score REAL,
                catalyst_type TEXT,
                market_moving BOOLEAN,
                gap_justifying BOOLEAN,
                word_count INTEGER,
                mentions_company BOOLEAN,
                mentions_financials BOOLEAN,
                mentions_products BOOLEAN,
                manipulation_risk REAL,
                pump_dump_indicators_json TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                INDEX(symbol, timestamp),
                INDEX(catalyst_type),
                INDEX(relevance_score)
            )
        """
        )

        # Catalyst validations table
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS catalyst_validations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                analysis_date DATETIME NOT NULL,
                gap_percentage REAL NOT NULL,
                primary_catalyst_id INTEGER,
                catalyst_strength REAL,
                gap_justification REAL,
                timing_correlation REAL,
                credibility_assessment REAL,
                manipulation_probability REAL,
                pump_dump_risk REAL,
                fake_news_risk REAL,
                gap_validated BOOLEAN,
                confidence_level TEXT,
                validation_reasons_json TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (primary_catalyst_id) REFERENCES news_events (id),
                INDEX(symbol, analysis_date),
                INDEX(gap_validated)
            )
        """
        )

        # Historical catalyst patterns
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS catalyst_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                catalyst_type TEXT NOT NULL,
                avg_gap_percentage REAL,
                success_rate REAL,
                sample_size INTEGER,
                typical_impact_range_min REAL,
                typical_impact_range_max REAL,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(catalyst_type)
            )
        """
        )

        conn.commit()
        conn.close()

    def validate_gap_catalyst(
        self, symbol: str, gap_percentage: float, analysis_date: datetime = None
    ) -> CatalystValidation:
        """
        Professional catalyst validation for gap event

        Args:
            symbol: Stock symbol
            gap_percentage: Size of gap to validate
            analysis_date: Date of analysis (default: now)

        Returns:
            Complete catalyst validation result
        """
        analysis_date = analysis_date or datetime.now()

        self.logger.info(
            f"Starting catalyst validation for {symbol} {gap_percentage:.1f}% gap"
        )

        try:
            # Step 1: Collect news from all sources
            news_events = self._collect_comprehensive_news(symbol, analysis_date)

            # Step 2: Analyze each news event
            analyzed_events = []
            for event in news_events:
                analyzed_event = self._analyze_news_event(event, symbol, gap_percentage)
                analyzed_events.append(analyzed_event)
                # Store in database
                self._store_news_event(analyzed_event)

            # Step 3: Identify primary and supporting catalysts
            primary_catalyst = self._identify_primary_catalyst(
                analyzed_events, gap_percentage
            )
            supporting_catalysts = self._identify_supporting_catalysts(
                analyzed_events, primary_catalyst
            )

            # Step 4: Calculate validation scores
            validation_scores = self._calculate_validation_scores(
                analyzed_events, primary_catalyst, gap_percentage, analysis_date
            )

            # Step 5: Assess manipulation risks
            risk_assessment = self._assess_manipulation_risks(
                analyzed_events, gap_percentage
            )

            # Step 6: Determine final validation
            final_validation = self._determine_final_validation(
                validation_scores, risk_assessment, gap_percentage
            )

            # Create validation result
            catalyst_validation = CatalystValidation(
                symbol=symbol,
                analysis_date=analysis_date,
                gap_percentage=gap_percentage,
                news_events=analyzed_events,
                primary_catalyst=primary_catalyst,
                supporting_catalysts=supporting_catalysts,
                catalyst_strength=validation_scores["catalyst_strength"],
                gap_justification=validation_scores["gap_justification"],
                timing_correlation=validation_scores["timing_correlation"],
                credibility_assessment=validation_scores["credibility_assessment"],
                manipulation_probability=risk_assessment["manipulation_probability"],
                pump_dump_risk=risk_assessment["pump_dump_risk"],
                fake_news_risk=risk_assessment["fake_news_risk"],
                gap_validated=final_validation["validated"],
                confidence_level=final_validation["confidence_level"],
                validation_reasons=final_validation["reasons"],
            )

            # Store validation result
            self._store_catalyst_validation(catalyst_validation)

            self.logger.info(
                f"Catalyst validation completed for {symbol}: "
                f"validated={catalyst_validation.gap_validated}, "
                f"confidence={catalyst_validation.confidence_level}"
            )

            return catalyst_validation

        except Exception as e:
            self.logger.error(f"Catalyst validation failed for {symbol}: {e}")

            # Return empty validation on error
            return CatalystValidation(
                symbol=symbol,
                analysis_date=analysis_date,
                gap_percentage=gap_percentage,
                news_events=[],
                primary_catalyst=None,
                supporting_catalysts=[],
                validation_reasons=[f"Validation failed: {str(e)}"],
            )

    def _collect_comprehensive_news(
        self, symbol: str, analysis_date: datetime
    ) -> List[NewsEvent]:
        """Collect news from all available sources"""
        all_news = []

        # Calculate lookback window
        start_time = analysis_date - timedelta(hours=self.NEWS_LOOKBACK_HOURS)

        self.logger.info(
            f"Collecting news for {symbol} from {start_time} to {analysis_date}"
        )

        # Source 1: Finviz
        if self.NEWS_SOURCES["finviz"]:
            finviz_news = self._get_finviz_news(symbol, start_time, analysis_date)
            all_news.extend(finviz_news)

        # Source 2: Alpha Vantage
        if self.NEWS_SOURCES["alpha_vantage"]:
            av_news = self._get_alpha_vantage_news(symbol, start_time, analysis_date)
            all_news.extend(av_news)

        # Source 3: NewsAPI
        if self.NEWS_SOURCES["newsapi"]:
            newsapi_news = self._get_newsapi_news(symbol, start_time, analysis_date)
            all_news.extend(newsapi_news)

        # Source 4: Seeking Alpha
        if self.NEWS_SOURCES["seeking_alpha"]:
            sa_news = self._get_seeking_alpha_news(symbol, start_time, analysis_date)
            all_news.extend(sa_news)

        # Deduplicate based on headline similarity
        deduplicated_news = self._deduplicate_news(all_news)

        self.logger.info(
            f"Collected {len(deduplicated_news)} unique news events for {symbol}"
        )

        return deduplicated_news

    def _get_finviz_news(
        self, symbol: str, start_time: datetime, end_time: datetime
    ) -> List[NewsEvent]:
        """Get news from Finviz"""
        try:
            from news_sources.finviz import get_finviz_news

            news_data = get_finviz_news(symbol)
            news_events = []

            for item in news_data:
                # Convert timestamp and filter by time range
                news_time = item.get("timestamp")
                if isinstance(news_time, str):
                    # Parse timestamp string
                    news_time = datetime.strptime(news_time, "%Y-%m-%d %H:%M:%S")

                if start_time <= news_time <= end_time:
                    news_events.append(
                        NewsEvent(
                            symbol=symbol,
                            headline=item.get("headline", ""),
                            content=item.get("content", ""),
                            source="Finviz",
                            timestamp=news_time,
                            url=item.get("url"),
                        )
                    )

            return news_events

        except Exception as e:
            self.logger.error(f"Finviz news collection failed: {e}")
            return []

    def _get_alpha_vantage_news(
        self, symbol: str, start_time: datetime, end_time: datetime
    ) -> List[NewsEvent]:
        """Get news from Alpha Vantage"""
        try:
            api_key = self.config.get("alpha_vantage_key")
            if not api_key:
                return []

            url = f"https://www.alphavantage.co/query"
            params = {
                "function": "NEWS_SENTIMENT",
                "tickers": symbol,
                "apikey": api_key,
                "limit": 50,
            }

            response = requests.get(url, params=params, timeout=10)
            data = response.json()

            news_events = []
            for item in data.get("feed", []):
                # Parse timestamp
                time_published = item.get("time_published", "")
                if time_published:
                    news_time = datetime.strptime(time_published, "%Y%m%dT%H%M%S")

                    if start_time <= news_time <= end_time:
                        news_events.append(
                            NewsEvent(
                                symbol=symbol,
                                headline=item.get("title", ""),
                                content=item.get("summary", ""),
                                source="Alpha Vantage",
                                timestamp=news_time,
                                url=item.get("url"),
                            )
                        )

            return news_events

        except Exception as e:
            self.logger.error(f"Alpha Vantage news collection failed: {e}")
            return []

    def _get_newsapi_news(
        self, symbol: str, start_time: datetime, end_time: datetime
    ) -> List[NewsEvent]:
        """Get news from NewsAPI"""
        try:
            api_key = self.config.get("newsapi_key")
            if not api_key:
                return []

            url = "https://newsapi.org/v2/everything"
            params = {
                "q": f'"{symbol}" OR "company name"',  # Would need company name lookup
                "from": start_time.isoformat(),
                "to": end_time.isoformat(),
                "sortBy": "publishedAt",
                "apiKey": api_key,
                "pageSize": 50,
            }

            response = requests.get(url, params=params, timeout=10)
            data = response.json()

            news_events = []
            for item in data.get("articles", []):
                published_at = item.get("publishedAt")
                if published_at:
                    news_time = datetime.fromisoformat(
                        published_at.replace("Z", "+00:00")
                    )

                    news_events.append(
                        NewsEvent(
                            symbol=symbol,
                            headline=item.get("title", ""),
                            content=item.get("description", ""),
                            source="NewsAPI",
                            timestamp=news_time,
                            url=item.get("url"),
                        )
                    )

            return news_events

        except Exception as e:
            self.logger.error(f"NewsAPI collection failed: {e}")
            return []

    def _get_seeking_alpha_news(
        self, symbol: str, start_time: datetime, end_time: datetime
    ) -> List[NewsEvent]:
        """Get news from Seeking Alpha (placeholder)"""
        try:
            # This would implement Seeking Alpha scraping/API
            # Placeholder implementation
            return []

        except Exception as e:
            self.logger.error(f"Seeking Alpha news collection failed: {e}")
            return []

    def _deduplicate_news(self, news_events: List[NewsEvent]) -> List[NewsEvent]:
        """Remove duplicate news based on headline similarity"""

        def headline_similarity(h1: str, h2: str) -> float:
            """Calculate headline similarity (0-1)"""
            words1 = set(h1.lower().split())
            words2 = set(h2.lower().split())

            if not words1 or not words2:
                return 0.0

            intersection = words1.intersection(words2)
            union = words1.union(words2)

            return len(intersection) / len(union)

        deduplicated = []

        for event in news_events:
            is_duplicate = False

            for existing in deduplicated:
                similarity = headline_similarity(event.headline, existing.headline)
                if similarity > 0.8:  # 80% similarity threshold
                    is_duplicate = True
                    break

            if not is_duplicate:
                deduplicated.append(event)

        return deduplicated

    def _analyze_news_event(
        self, event: NewsEvent, symbol: str, gap_percentage: float
    ) -> NewsEvent:
        """Comprehensive analysis of individual news event"""

        # Basic metadata
        event.word_count = len(event.content.split()) if event.content else 0
        event.mentions_company = (
            symbol.lower() in event.headline.lower()
            or symbol.lower() in event.content.lower()
        )

        # Content analysis
        event.mentions_financials = self._check_financial_mentions(event.content)
        event.mentions_products = self._check_product_mentions(event.content)

        # LLM-powered analysis
        llm_analysis = self._llm_analyze_news(event, symbol, gap_percentage)

        event.relevance_score = llm_analysis.get("relevance_score", 0.0)
        event.sentiment_score = llm_analysis.get("sentiment_score", 0.0)
        event.impact_score = llm_analysis.get("impact_score", 0.0)
        event.catalyst_type = llm_analysis.get("catalyst_type")

        # Credibility assessment
        event.credibility_score = self._assess_credibility(event)

        # Market impact assessment
        event.market_moving = event.impact_score > 0.5
        event.gap_justifying = self._assess_gap_justification(event, gap_percentage)

        # Manipulation risk assessment
        manipulation_analysis = self._assess_manipulation_risk(event)
        event.manipulation_risk = manipulation_analysis["risk_score"]
        event.pump_dump_indicators = manipulation_analysis["indicators"]

        return event

    def _check_financial_mentions(self, content: str) -> bool:
        """Check if content mentions financial terms"""
        financial_terms = [
            "earnings",
            "revenue",
            "profit",
            "loss",
            "sales",
            "growth",
            "margin",
            "ebitda",
            "guidance",
            "forecast",
            "beat",
            "miss",
        ]

        content_lower = content.lower()
        return any(term in content_lower for term in financial_terms)

    def _check_product_mentions(self, content: str) -> bool:
        """Check if content mentions products/services"""
        product_terms = [
            "product",
            "service",
            "launch",
            "release",
            "approval",
            "trial",
            "study",
            "patent",
            "technology",
            "platform",
        ]

        content_lower = content.lower()
        return any(term in content_lower for term in product_terms)

    def _llm_analyze_news(
        self, event: NewsEvent, symbol: str, gap_percentage: float
    ) -> Dict:
        """Use LLM to analyze news relevance, sentiment, and impact"""
        try:
            # Import LLM functionality
            from analysis.llm_agent import analyze_news_catalyst

            analysis_text = f"Headline: {event.headline}\n\nContent: {event.content}"

            # LLM analysis prompt
            prompt = f"""
            Analyze this news for stock {symbol} that gapped {gap_percentage:.1f}%:
            
            {analysis_text}
            
            Provide analysis in JSON format:
            {{
                "relevance_score": 0.0-1.0,
                "sentiment_score": -1.0 to 1.0,
                "impact_score": 0.0-1.0,
                "catalyst_type": "earnings|fda_approval|merger|contract|partnership|clinical_trial|other",
                "gap_justification": "explanation of how this news justifies the gap",
                "market_impact_reasoning": "why this would move the stock"
            }}
            """

            # Call LLM (placeholder - would use actual LLM)
            llm_result = self._call_llm_analysis(prompt)

            return llm_result

        except Exception as e:
            self.logger.error(f"LLM news analysis failed: {e}")

            # Fallback to rule-based analysis
            return self._fallback_news_analysis(event, symbol, gap_percentage)

    def _call_llm_analysis(self, prompt: str) -> Dict:
        """Call LLM for news analysis (placeholder)"""
        # Placeholder implementation
        # In production, would call Gemini/OpenAI/Claude
        return {
            "relevance_score": 0.7,
            "sentiment_score": 0.5,
            "impact_score": 0.6,
            "catalyst_type": "earnings",
            "gap_justification": "Positive earnings news",
            "market_impact_reasoning": "Beat expectations",
        }

    def _fallback_news_analysis(
        self, event: NewsEvent, symbol: str, gap_percentage: float
    ) -> Dict:
        """Fallback rule-based news analysis"""

        content = (event.headline + " " + event.content).lower()

        # Relevance scoring
        relevance = 0.5  # Base score
        if symbol.lower() in content:
            relevance += 0.3
        if any(term in content for term in ["earnings", "fda", "approval", "merger"]):
            relevance += 0.2

        # Sentiment scoring
        positive_words = ["beat", "approved", "positive", "growth", "up", "gain", "win"]
        negative_words = [
            "miss",
            "denied",
            "negative",
            "decline",
            "down",
            "loss",
            "fail",
        ]

        pos_count = sum(1 for word in positive_words if word in content)
        neg_count = sum(1 for word in negative_words if word in content)

        if pos_count > neg_count:
            sentiment = 0.5
        elif neg_count > pos_count:
            sentiment = -0.5
        else:
            sentiment = 0.0

        # Impact scoring
        impact = min(relevance * abs(sentiment), 1.0)

        # Catalyst type detection
        catalyst_type = "other"
        if "earnings" in content:
            catalyst_type = "earnings"
        elif "fda" in content or "approval" in content:
            catalyst_type = "fda_approval"
        elif "merger" in content or "acquisition" in content:
            catalyst_type = "merger"

        return {
            "relevance_score": min(relevance, 1.0),
            "sentiment_score": sentiment,
            "impact_score": impact,
            "catalyst_type": catalyst_type,
            "gap_justification": f"News analysis for {gap_percentage:.1f}% gap",
            "market_impact_reasoning": f"Catalyst type: {catalyst_type}",
        }

    def _assess_credibility(self, event: NewsEvent) -> float:
        """Assess news source credibility"""

        credibility_scores = {
            "Reuters": 0.95,
            "Bloomberg": 0.95,
            "Wall Street Journal": 0.90,
            "Financial Times": 0.90,
            "MarketWatch": 0.85,
            "Yahoo Finance": 0.80,
            "Seeking Alpha": 0.75,
            "Finviz": 0.70,
            "Alpha Vantage": 0.80,
            "NewsAPI": 0.60,  # Variable quality
        }

        base_score = credibility_scores.get(event.source, 0.50)

        # Adjust based on content quality
        if event.word_count > 100:
            base_score += 0.1
        if event.url:
            base_score += 0.05
        if event.mentions_company:
            base_score += 0.1

        return min(base_score, 1.0)

    def _assess_gap_justification(
        self, event: NewsEvent, gap_percentage: float
    ) -> bool:
        """Assess if news justifies the gap size"""

        if not event.catalyst_type:
            return False

        # Get typical impact range for this catalyst type
        impact_range = self.CATALYST_IMPACT_RANGES.get(
            event.catalyst_type, (0.05, 0.20)
        )
        min_impact, max_impact = impact_range

        # Convert to percentage
        min_gap = min_impact * 100
        max_gap = max_impact * 100

        # Check if gap is within reasonable range
        gap_within_range = (
            min_gap <= gap_percentage <= max_gap * 2
        )  # Allow 2x max for exceptional cases

        # Also consider impact and relevance scores
        scores_sufficient = event.impact_score >= 0.5 and event.relevance_score >= 0.6

        return gap_within_range and scores_sufficient

    def _assess_manipulation_risk(self, event: NewsEvent) -> Dict:
        """Assess manipulation and pump/dump risk"""

        risk_score = 0.0
        indicators = []

        content = (event.headline + " " + event.content).lower()

        # Check for pump language
        pump_words = [
            "explosive",
            "rocket",
            "moon",
            "massive gains",
            "guaranteed",
            "secret",
        ]
        if any(word in content for word in pump_words):
            risk_score += 0.3
            indicators.append("Pump language detected")

        # Check for vague claims
        vague_words = ["rumor", "speculation", "sources say", "insider info"]
        if any(word in content for word in vague_words):
            risk_score += 0.2
            indicators.append("Vague claims")

        # Check for excessive urgency
        urgent_words = ["urgent", "act now", "limited time", "before its too late"]
        if any(word in content for word in urgent_words):
            risk_score += 0.2
            indicators.append("Excessive urgency")

        # Low credibility source
        if event.credibility_score < 0.6:
            risk_score += 0.2
            indicators.append("Low credibility source")

        # Very short content
        if event.word_count < 50:
            risk_score += 0.1
            indicators.append("Insufficient detail")

        return {"risk_score": min(risk_score, 1.0), "indicators": indicators}

    def _identify_primary_catalyst(
        self, events: List[NewsEvent], gap_percentage: float
    ) -> Optional[NewsEvent]:
        """Identify the primary catalyst driving the gap"""

        # Filter to relevant, gap-justifying events
        candidates = [
            event
            for event in events
            if event.relevance_score >= self.MIN_RELEVANCE_SCORE
            and event.gap_justifying
            and event.manipulation_risk <= self.MAX_MANIPULATION_RISK
        ]

        if not candidates:
            return None

        # Score each candidate
        for event in candidates:
            # Composite score: relevance + impact + credibility - manipulation_risk
            composite_score = (
                event.relevance_score * 0.3
                + event.impact_score * 0.3
                + event.credibility_score * 0.3
                - event.manipulation_risk * 0.1
            )
            event.composite_score = composite_score

        # Return highest scoring candidate
        return max(candidates, key=lambda x: getattr(x, "composite_score", 0))

    def _identify_supporting_catalysts(
        self, events: List[NewsEvent], primary_catalyst: Optional[NewsEvent]
    ) -> List[NewsEvent]:
        """Identify supporting catalysts"""

        if not primary_catalyst:
            return []

        supporting = [
            event
            for event in events
            if event != primary_catalyst
            and event.relevance_score >= 0.4  # Lower threshold for supporting
            and event.manipulation_risk <= self.MAX_MANIPULATION_RISK
        ]

        # Sort by relevance score
        supporting.sort(key=lambda x: x.relevance_score, reverse=True)

        # Return top 3 supporting catalysts
        return supporting[:3]

    def _calculate_validation_scores(
        self,
        events: List[NewsEvent],
        primary_catalyst: Optional[NewsEvent],
        gap_percentage: float,
        analysis_date: datetime,
    ) -> Dict:
        """Calculate comprehensive validation scores"""

        if not primary_catalyst:
            return {
                "catalyst_strength": 0.0,
                "gap_justification": 0.0,
                "timing_correlation": 0.0,
                "credibility_assessment": 0.0,
            }

        # Catalyst strength: composite score of primary catalyst
        catalyst_strength = (
            primary_catalyst.relevance_score * 0.3
            + primary_catalyst.impact_score * 0.3
            + primary_catalyst.credibility_score * 0.4
        )

        # Gap justification: how well the catalyst explains the gap size
        expected_impact = self._get_expected_impact(
            primary_catalyst.catalyst_type, gap_percentage
        )
        gap_justification = min(expected_impact, 1.0)

        # Timing correlation: how well news timing aligns with gap
        timing_correlation = self._calculate_timing_correlation(
            primary_catalyst, analysis_date
        )

        # Credibility assessment: weighted credibility of all relevant events
        relevant_events = [e for e in events if e.relevance_score >= 0.5]
        if relevant_events:
            credibility_assessment = np.mean(
                [e.credibility_score for e in relevant_events]
            )
        else:
            credibility_assessment = 0.0

        return {
            "catalyst_strength": catalyst_strength,
            "gap_justification": gap_justification,
            "timing_correlation": timing_correlation,
            "credibility_assessment": credibility_assessment,
        }

    def _get_expected_impact(self, catalyst_type: str, actual_gap: float) -> float:
        """Calculate expected impact score based on catalyst type and actual gap"""

        if not catalyst_type:
            return 0.0

        impact_range = self.CATALYST_IMPACT_RANGES.get(catalyst_type, (0.05, 0.20))
        min_expected, max_expected = impact_range

        # Convert to percentage
        min_gap = min_expected * 100
        max_gap = max_expected * 100

        if actual_gap < min_gap:
            # Gap smaller than expected
            return actual_gap / min_gap
        elif actual_gap <= max_gap:
            # Gap within expected range
            return 1.0
        else:
            # Gap larger than expected - diminishing returns
            excess = actual_gap - max_gap
            excess_penalty = excess / max_gap * 0.5  # 50% penalty for excess
            return max(1.0 - excess_penalty, 0.2)

    def _calculate_timing_correlation(
        self, catalyst: NewsEvent, gap_time: datetime
    ) -> float:
        """Calculate how well catalyst timing correlates with gap"""

        time_diff = abs((gap_time - catalyst.timestamp).total_seconds() / 3600)  # Hours

        # Perfect correlation within 2 hours
        if time_diff <= 2:
            return 1.0
        # Good correlation within 12 hours
        elif time_diff <= 12:
            return 1.0 - (time_diff - 2) / 10 * 0.3  # Linear decay
        # Moderate correlation within 24 hours
        elif time_diff <= 24:
            return 0.7 - (time_diff - 12) / 12 * 0.4  # Linear decay
        # Poor correlation beyond 24 hours
        else:
            return max(0.3 - (time_diff - 24) / 24 * 0.3, 0.0)

    def _assess_manipulation_risks(
        self, events: List[NewsEvent], gap_percentage: float
    ) -> Dict:
        """Assess overall manipulation risks"""

        if not events:
            return {
                "manipulation_probability": 0.5,  # Unknown = medium risk
                "pump_dump_risk": 0.5,
                "fake_news_risk": 0.5,
            }

        # Calculate average manipulation risk
        avg_manipulation_risk = np.mean([e.manipulation_risk for e in events])

        # Pump and dump risk factors
        pump_dump_indicators = 0
        for event in events:
            if event.pump_dump_indicators:
                pump_dump_indicators += len(event.pump_dump_indicators)

        pump_dump_risk = min(pump_dump_indicators * 0.2, 1.0)

        # Fake news risk based on credibility
        avg_credibility = np.mean([e.credibility_score for e in events])
        fake_news_risk = 1.0 - avg_credibility

        # Excessive gap risk (gaps >100% are suspicious without major catalyst)
        if gap_percentage > 100:
            manipulation_probability = min(avg_manipulation_risk + 0.3, 1.0)
        else:
            manipulation_probability = avg_manipulation_risk

        return {
            "manipulation_probability": manipulation_probability,
            "pump_dump_risk": pump_dump_risk,
            "fake_news_risk": fake_news_risk,
        }

    def _determine_final_validation(
        self, validation_scores: Dict, risk_assessment: Dict, gap_percentage: float
    ) -> Dict:
        """Determine final validation result"""

        # Calculate overall validation score
        validation_score = (
            validation_scores["catalyst_strength"] * 0.3
            + validation_scores["gap_justification"] * 0.3
            + validation_scores["timing_correlation"] * 0.2
            + validation_scores["credibility_assessment"] * 0.2
        )

        # Apply risk penalties
        risk_penalty = (
            risk_assessment["manipulation_probability"] * 0.4
            + risk_assessment["pump_dump_risk"] * 0.3
            + risk_assessment["fake_news_risk"] * 0.3
        )

        final_score = validation_score * (1 - risk_penalty)

        # Determine validation result
        if final_score >= 0.7 and risk_assessment["manipulation_probability"] < 0.3:
            validated = True
            confidence = "high"
            reasons = ["Strong catalyst with low manipulation risk"]
        elif final_score >= 0.5 and risk_assessment["manipulation_probability"] < 0.5:
            validated = True
            confidence = "medium"
            reasons = ["Moderate catalyst strength"]
        elif final_score >= 0.3:
            validated = True
            confidence = "low"
            reasons = ["Weak but present catalyst"]
        else:
            validated = False
            confidence = "low"
            reasons = ["Insufficient catalyst strength or high manipulation risk"]

        return {
            "validated": validated,
            "confidence_level": confidence,
            "reasons": reasons,
            "final_score": final_score,
        }

    def _store_news_event(self, event: NewsEvent):
        """Store analyzed news event in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            INSERT OR REPLACE INTO news_events
            (symbol, headline, content, source, timestamp, url, relevance_score,
             sentiment_score, impact_score, credibility_score, catalyst_type,
             market_moving, gap_justifying, word_count, mentions_company,
             mentions_financials, mentions_products, manipulation_risk,
             pump_dump_indicators_json)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                event.symbol,
                event.headline,
                event.content,
                event.source,
                event.timestamp,
                event.url,
                event.relevance_score,
                event.sentiment_score,
                event.impact_score,
                event.credibility_score,
                event.catalyst_type,
                event.market_moving,
                event.gap_justifying,
                event.word_count,
                event.mentions_company,
                event.mentions_financials,
                event.mentions_products,
                event.manipulation_risk,
                json.dumps(event.pump_dump_indicators or []),
            ),
        )

        conn.commit()
        conn.close()

    def _store_catalyst_validation(self, validation: CatalystValidation):
        """Store catalyst validation result"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Get primary catalyst ID if exists
        primary_catalyst_id = None
        if validation.primary_catalyst:
            cursor.execute(
                """
                SELECT id FROM news_events 
                WHERE symbol = ? AND headline = ? AND timestamp = ?
            """,
                (
                    validation.symbol,
                    validation.primary_catalyst.headline,
                    validation.primary_catalyst.timestamp,
                ),
            )
            result = cursor.fetchone()
            if result:
                primary_catalyst_id = result[0]

        cursor.execute(
            """
            INSERT INTO catalyst_validations
            (symbol, analysis_date, gap_percentage, primary_catalyst_id,
             catalyst_strength, gap_justification, timing_correlation,
             credibility_assessment, manipulation_probability, pump_dump_risk,
             fake_news_risk, gap_validated, confidence_level, validation_reasons_json)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                validation.symbol,
                validation.analysis_date,
                validation.gap_percentage,
                primary_catalyst_id,
                validation.catalyst_strength,
                validation.gap_justification,
                validation.timing_correlation,
                validation.credibility_assessment,
                validation.manipulation_probability,
                validation.pump_dump_risk,
                validation.fake_news_risk,
                validation.gap_validated,
                validation.confidence_level,
                json.dumps(validation.validation_reasons or []),
            ),
        )

        conn.commit()
        conn.close()

    def get_validation_summary(self, symbol: str, days_back: int = 30) -> Dict:
        """Get professional validation summary for symbol"""

        conn = sqlite3.connect(self.db_path)

        summary_query = """
            SELECT 
                COUNT(*) as total_validations,
                COUNT(CASE WHEN gap_validated = 1 THEN 1 END) as validated_gaps,
                AVG(catalyst_strength) as avg_catalyst_strength,
                AVG(gap_justification) as avg_gap_justification,
                AVG(manipulation_probability) as avg_manipulation_risk
            FROM catalyst_validations 
            WHERE symbol = ? AND analysis_date >= date('now', '-{} days')
        """.format(
            days_back
        )

        summary_df = pd.read_sql_query(summary_query, conn, params=(symbol,))

        conn.close()

        return {
            "symbol": symbol,
            "period_days": days_back,
            "summary": summary_df.iloc[0].to_dict() if not summary_df.empty else {},
            "validation_rate": (
                summary_df.iloc[0]["validated_gaps"]
                / max(summary_df.iloc[0]["total_validations"], 1)
                if not summary_df.empty
                else 0
            ),
        }
