#!/usr/bin/env python3
"""
Enhanced Volume Accumulation Detector - COMPREHENSIVE DATA VERSION

Returns extensive volume analysis data including:
- Daily volume profiles with hourly breakdowns
- Price levels with most volume (VWAP, volume nodes)
- Dark pool indicators and unusual timing
- Specific accumulation times and patterns
- Volume vs price correlations
- Support/resistance levels from volume
- Intraday patterns and anomalies

NO FAKES, NO MOCKS - Real comprehensive analysis for confident trading decisions.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
from collections import defaultdict

from core.data_service import DataService

def get_trading_days_back(end_date: datetime, trading_days: int) -> datetime:
    """
    Calculate a start date that is N trading days before the end date.
    Skips weekends and gives extra buffer for holidays.

    Args:
        end_date: The end date
        trading_days: Number of trading days to go back

    Returns:
        datetime: Start date that should capture N trading days
    """
    # Add extra calendar days to account for weekends and holidays
    # Rule of thumb: 1.4x trading days to account for weekends (5/7 ratio)
    # Plus extra buffer for holidays
    calendar_days_needed = int(trading_days * 1.4) + 7  # Extra week buffer

    start_date = end_date - timedelta(days=calendar_days_needed)

    # Log the calculation for debugging
    logger = logging.getLogger(__name__)
    logger.info(f"Calculating {trading_days} trading days back from {end_date.strftime('%Y-%m-%d')}")
    logger.info(f"Using {calendar_days_needed} calendar days to ensure coverage")
    logger.info(f"Start date: {start_date.strftime('%Y-%m-%d')} (will filter to trading days only)")

    return start_date

logger = logging.getLogger(__name__)


class VolumeAccumulationDetector:
    """Enhanced volume accumulation detector with comprehensive data output."""
    
    def __init__(self, data_service: DataService = None):
        self.data_service = data_service or DataService()
        
    def detect_accumulation(
        self, 
        symbol: str, 
        target_date: str, 
        lookback_days: int = 14,
        include_tick_data: bool = True
    ) -> Dict[str, Any]:
        """
        Perform comprehensive volume accumulation analysis.
        
        Returns extensive data for maximum trading confidence.
        """
        logger.info(f"Running comprehensive volume analysis for {symbol} before {target_date}")
        
        # Parse dates and calculate proper trading day ranges
        end_date = pd.to_datetime(target_date)

        # Use trading days calculation instead of calendar days
        daily_start_date = get_trading_days_back(end_date, lookback_days + 5)
        minute_start_date = get_trading_days_back(end_date, lookback_days)

        try:
            # Fetch all data types using trading day ranges
            daily_bars = self.data_service.get_daily_bars(
                symbol,
                daily_start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )

            minute_bars = self.data_service.get_minute_bars(
                symbol,
                minute_start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )

            # Filter out weekend data if any slipped through
            if not daily_bars.empty:
                # Remove weekends (Saturday=5, Sunday=6)
                daily_bars = daily_bars[daily_bars.index.to_series().dt.dayofweek < 5]
                logger.info(f"Filtered daily bars to trading days only: {len(daily_bars)} bars")

            if not minute_bars.empty:
                # Remove weekends from minute data too
                minute_bars = minute_bars[minute_bars.index.to_series().dt.dayofweek < 5]
                logger.info(f"Filtered minute bars to trading days only: {len(minute_bars)} bars")

            # Initialize comprehensive result
            result = {
                'symbol': symbol,
                'analysis_date': target_date,
                'lookback_days': lookback_days,
                'data_quality': {
                    'daily_bars': len(daily_bars),
                    'minute_bars': len(minute_bars),
                    'tick_data_available': False
                }
            }
            
            if daily_bars.empty or minute_bars.empty:
                logger.warning(f"Insufficient data for {symbol}")
                return self._empty_comprehensive_result(result)
            
            # 1. Daily Volume Profiles
            result['daily_volume_profiles'] = self._analyze_daily_profiles(minute_bars)
            
            # 2. Hourly Volume Patterns
            result['hourly_patterns'] = self._analyze_hourly_patterns(minute_bars)
            
            # 3. Volume at Price Levels (Volume Profile)
            result['volume_at_price'] = self._analyze_volume_at_price(minute_bars)
            
            # 4. Dark Pool and Unusual Activity
            result['dark_pool_indicators'] = self._analyze_dark_pool_activity(minute_bars, daily_bars)
            
            # 5. Specific Accumulation Times
            result['accumulation_windows'] = self._identify_accumulation_windows(minute_bars)
            
            # 6. Volume-Price Correlation Analysis
            result['volume_price_analysis'] = self._analyze_volume_price_correlation(minute_bars, daily_bars)
            
            # 7. Support/Resistance from Volume
            result['volume_based_levels'] = self._identify_volume_based_levels(minute_bars)
            
            # 8. Intraday Patterns and Anomalies
            result['intraday_patterns'] = self._analyze_intraday_patterns(minute_bars)
            
            # 9. Smart Money Indicators
            result['smart_money_indicators'] = self._detect_smart_money_patterns(minute_bars, daily_bars)
            
            # 10. Accumulation Scoring with Details
            result['accumulation_analysis'] = self._comprehensive_accumulation_scoring(result)
            
            # 11. Entry Recommendations with Specifics
            result['entry_analysis'] = self._generate_detailed_entry_analysis(result)
            
            # 12. Statistical Summary
            result['statistical_summary'] = self._generate_statistical_summary(minute_bars, daily_bars)
            
            logger.info(f"Comprehensive volume analysis complete for {symbol}")
            return result
            
        except Exception as e:
            logger.error(f"Comprehensive volume analysis failed: {e}")
            return self._empty_comprehensive_result({'error': str(e)})
    
    def _analyze_daily_profiles(self, minute_bars: pd.DataFrame) -> Dict[str, Any]:
        """Analyze volume profile for each day."""
        profiles = {}
        
        minute_bars = minute_bars.copy()
        minute_bars['date'] = minute_bars.index.date
        minute_bars['hour'] = minute_bars.index.hour
        minute_bars['minute'] = minute_bars.index.minute
        
        for date in sorted(minute_bars['date'].unique()):
            day_data = minute_bars[minute_bars['date'] == date]
            
            # Calculate VWAP
            day_data['vwap'] = (day_data['close'] * day_data['volume']).cumsum() / day_data['volume'].cumsum()
            
            # Volume distribution by hour
            hourly_volume = day_data.groupby('hour').agg({
                'volume': 'sum',
                'close': ['first', 'last', 'mean'],
                'high': 'max',
                'low': 'min'
            }).round(2)
            
            # Key metrics
            total_volume = day_data['volume'].sum()
            open_price = day_data['close'].iloc[0] if len(day_data) > 0 else 0
            close_price = day_data['close'].iloc[-1] if len(day_data) > 0 else 0
            
            profiles[str(date)] = {
                'total_volume': int(total_volume),
                'vwap': float(day_data['vwap'].iloc[-1]) if len(day_data) > 0 else 0,
                'open': float(open_price),
                'close': float(close_price),
                'high': float(day_data['high'].max()),
                'low': float(day_data['low'].min()),
                'price_change': float((close_price - open_price) / open_price * 100) if open_price > 0 else 0,
                'volume_weighted_price_range': float((day_data['high'].max() - day_data['low'].min()) / day_data['vwap'].mean()) if len(day_data) > 0 else 0,
                'hourly_distribution': hourly_volume.to_dict() if not hourly_volume.empty else {},
                'morning_vs_afternoon': {
                    'morning_volume': int(day_data[day_data['hour'] < 13]['volume'].sum()),
                    'afternoon_volume': int(day_data[day_data['hour'] >= 13]['volume'].sum()),
                    'ratio': float(day_data[day_data['hour'] >= 13]['volume'].sum() / max(1, day_data[day_data['hour'] < 13]['volume'].sum()))
                },
                'first_hour_volume': int(day_data[day_data['hour'] == 9]['volume'].sum() + day_data[(day_data['hour'] == 10) & (day_data['minute'] < 30)]['volume'].sum()),
                'last_hour_volume': int(day_data[day_data['hour'] == 15]['volume'].sum()),
                'volume_profile_shape': self._classify_volume_shape(day_data)
            }
        
        return profiles
    
    def _analyze_hourly_patterns(self, minute_bars: pd.DataFrame) -> Dict[str, Any]:
        """Analyze volume patterns by hour across all days."""
        minute_bars = minute_bars.copy()
        minute_bars['hour'] = minute_bars.index.hour
        minute_bars['date'] = minute_bars.index.date
        
        # Aggregate by hour
        hourly_stats = {}
        
        for hour in range(9, 16):  # Market hours
            hour_data = minute_bars[minute_bars['hour'] == hour]
            
            if hour_data.empty:
                continue
            
            hourly_stats[f"{hour:02d}:00-{hour:02d}:59"] = {
                'avg_volume': float(hour_data.groupby('date')['volume'].sum().mean()),
                'total_volume': int(hour_data['volume'].sum()),
                'volume_volatility': float(hour_data.groupby('date')['volume'].sum().std()),
                'avg_price_movement': float(hour_data.groupby('date').apply(
                    lambda x: abs(x['close'].iloc[-1] - x['close'].iloc[0]) / x['close'].iloc[0] * 100 if len(x) > 0 and x['close'].iloc[0] > 0 else 0
                ).mean()),
                'high_volume_days': int((hour_data.groupby('date')['volume'].sum() > hour_data.groupby('date')['volume'].sum().mean() * 1.5).sum()),
                'typical_range': {
                    'high': float(hour_data.groupby('date')['high'].max().mean()),
                    'low': float(hour_data.groupby('date')['low'].min().mean())
                }
            }
        
        # Identify unusual hours
        all_hourly_volumes = [stats['avg_volume'] for stats in hourly_stats.values()]
        avg_hourly_volume = np.mean(all_hourly_volumes) if all_hourly_volumes else 0
        
        unusual_hours = {}
        for hour, stats in hourly_stats.items():
            if stats['avg_volume'] > avg_hourly_volume * 1.5:
                unusual_hours[hour] = {
                    'volume_ratio': float(stats['avg_volume'] / avg_hourly_volume),
                    'frequency': stats['high_volume_days']
                }
        
        return {
            'hourly_statistics': hourly_stats,
            'unusual_activity_hours': unusual_hours,
            'most_active_hour': max(hourly_stats.items(), key=lambda x: x[1]['avg_volume'])[0] if hourly_stats else None,
            'quietest_hour': min(hourly_stats.items(), key=lambda x: x[1]['avg_volume'])[0] if hourly_stats else None
        }
    
    def _analyze_volume_at_price(self, minute_bars: pd.DataFrame) -> Dict[str, Any]:
        """Create volume profile showing volume at each price level."""
        if minute_bars.empty:
            return {}
        
        # Create price bins
        price_min = minute_bars['low'].min()
        price_max = minute_bars['high'].max()
        price_range = price_max - price_min
        
        # Use 50 bins for detailed profile
        num_bins = min(50, int(price_range * 100))  # Adjust bins based on price
        if num_bins < 10:
            num_bins = 10
        
        bins = np.linspace(price_min, price_max, num_bins + 1)
        
        # Calculate volume at each price level
        volume_profile = defaultdict(int)
        
        for _, bar in minute_bars.iterrows():
            # Distribute volume across the bar's range
            bar_bins = bins[(bins >= bar['low']) & (bins <= bar['high'])]
            if len(bar_bins) > 0:
                vol_per_bin = bar['volume'] / len(bar_bins)
                for price_level in bar_bins:
                    volume_profile[float(price_level)] += vol_per_bin
        
        # Convert to sorted list
        sorted_profile = sorted(volume_profile.items(), key=lambda x: x[1], reverse=True)
        
        # Identify key levels
        total_volume = sum(v for _, v in sorted_profile)
        
        # Point of Control (POC) - price with most volume
        poc = sorted_profile[0][0] if sorted_profile else 0
        
        # Value Area (70% of volume)
        cumulative_volume = 0
        value_area_prices = []
        for price, volume in sorted_profile:
            cumulative_volume += volume
            value_area_prices.append(price)
            if cumulative_volume >= total_volume * 0.7:
                break
        
        value_area_high = max(value_area_prices) if value_area_prices else 0
        value_area_low = min(value_area_prices) if value_area_prices else 0
        
        # High Volume Nodes (HVN) - peaks in volume profile
        hvn_levels = []
        for i in range(len(sorted_profile)):
            if sorted_profile[i][1] > total_volume / num_bins * 2:  # 2x average
                hvn_levels.append({
                    'price': float(sorted_profile[i][0]),
                    'volume': int(sorted_profile[i][1]),
                    'strength': float(sorted_profile[i][1] / sorted_profile[0][1])  # Relative to POC
                })
        
        # Low Volume Nodes (LVN) - potential breakout/breakdown levels
        lvn_levels = []
        avg_volume = total_volume / len(sorted_profile) if sorted_profile else 0
        for price, volume in sorted_profile:
            if volume < avg_volume * 0.5:  # Less than 50% of average
                lvn_levels.append({
                    'price': float(price),
                    'volume': int(volume),
                    'significance': 'high' if abs(price - minute_bars['close'].iloc[-1]) / minute_bars['close'].iloc[-1] < 0.02 else 'medium'
                })
        
        return {
            'point_of_control': float(poc),
            'value_area_high': float(value_area_high),
            'value_area_low': float(value_area_low),
            'value_area_volume_pct': 70.0,
            'high_volume_nodes': hvn_levels[:10],  # Top 10 HVN levels
            'low_volume_nodes': lvn_levels[:10],   # Top 10 LVN levels
            'current_price_vs_poc': float((minute_bars['close'].iloc[-1] - poc) / poc * 100) if poc > 0 else 0,
            'volume_distribution': {
                'above_poc': float(sum(v for p, v in sorted_profile if p > poc) / total_volume * 100) if total_volume > 0 else 0,
                'below_poc': float(sum(v for p, v in sorted_profile if p < poc) / total_volume * 100) if total_volume > 0 else 0
            },
            'profile_balance': 'balanced' if abs(sum(v for p, v in sorted_profile if p > poc) - sum(v for p, v in sorted_profile if p < poc)) / total_volume < 0.2 else 'imbalanced'
        }
    
    def _analyze_dark_pool_activity(self, minute_bars: pd.DataFrame, daily_bars: pd.DataFrame) -> Dict[str, Any]:
        """Detect potential dark pool activity patterns."""
        indicators = {
            'detected': False,
            'confidence': 0.0,
            'suspicious_patterns': []
        }
        
        if minute_bars.empty:
            return indicators
        
        minute_bars = minute_bars.copy()
        minute_bars['hour'] = minute_bars.index.hour
        minute_bars['minute'] = minute_bars.index.minute
        minute_bars['date'] = minute_bars.index.date
        
        # Pattern 1: Large volume with minimal price movement
        minute_bars['price_change'] = minute_bars['close'].pct_change().abs()
        minute_bars['volume_zscore'] = (minute_bars['volume'] - minute_bars['volume'].mean()) / minute_bars['volume'].std()
        
        # Find bars with high volume but low price impact
        suspicious_bars = minute_bars[
            (minute_bars['volume_zscore'] > 2) &  # High volume
            (minute_bars['price_change'] < minute_bars['price_change'].median())  # Low price impact
        ]
        
        if len(suspicious_bars) > 5:
            indicators['suspicious_patterns'].append({
                'type': 'high_volume_low_impact',
                'occurrences': len(suspicious_bars),
                'avg_volume_zscore': float(suspicious_bars['volume_zscore'].mean()),
                'times': suspicious_bars.index.strftime('%Y-%m-%d %H:%M').tolist()[:10]  # First 10
            })
        
        # Pattern 2: End-of-day volume spikes
        for date in minute_bars['date'].unique():
            day_data = minute_bars[minute_bars['date'] == date]
            last_30_min = day_data[day_data.index.time >= pd.Timestamp('15:30').time()]
            
            if not last_30_min.empty and len(day_data) > 100:
                last_30_vol = last_30_min['volume'].sum()
                day_avg_vol = day_data['volume'].mean() * 30  # 30 minutes worth
                
                if last_30_vol > day_avg_vol * 3:
                    indicators['suspicious_patterns'].append({
                        'type': 'end_of_day_spike',
                        'date': str(date),
                        'volume_ratio': float(last_30_vol / day_avg_vol),
                        'price_change': float(last_30_min['close'].iloc[-1] / last_30_min['close'].iloc[0] - 1) * 100
                    })
        
        # Pattern 3: Unusual timing (very early or very late)
        unusual_hours = minute_bars[
            (minute_bars['hour'] == 9) & (minute_bars['minute'] < 35) |  # First 5 minutes
            (minute_bars['hour'] == 15) & (minute_bars['minute'] > 55)   # Last 5 minutes
        ]
        
        if not unusual_hours.empty:
            unusual_volume_pct = unusual_hours['volume'].sum() / minute_bars['volume'].sum() * 100
            if unusual_volume_pct > 15:  # More than 15% of volume in unusual times
                indicators['suspicious_patterns'].append({
                    'type': 'unusual_timing',
                    'volume_percentage': float(unusual_volume_pct),
                    'primary_times': 'opening' if unusual_hours['hour'].iloc[0] == 9 else 'closing'
                })
        
        # Calculate overall confidence
        if indicators['suspicious_patterns']:
            indicators['detected'] = True
            indicators['confidence'] = min(0.9, len(indicators['suspicious_patterns']) * 0.3)
        
        # Add summary statistics
        indicators['summary'] = {
            'avg_trade_size': float(minute_bars['volume'].mean()),
            'large_trade_percentage': float((minute_bars['volume'] > minute_bars['volume'].quantile(0.95)).sum() / len(minute_bars) * 100),
            'price_efficiency': float(1 - minute_bars['price_change'].mean() / (minute_bars['volume'] / minute_bars['volume'].mean()).mean()) if minute_bars['volume'].mean() > 0 else 0
        }
        
        return indicators
    
    def _identify_accumulation_windows(self, minute_bars: pd.DataFrame) -> List[Dict[str, Any]]:
        """Identify specific time windows with accumulation patterns."""
        windows = []
        
        if minute_bars.empty:
            return windows
        
        minute_bars = minute_bars.copy()
        minute_bars['date'] = minute_bars.index.date
        minute_bars['time'] = minute_bars.index.time
        
        # Rolling metrics
        minute_bars['volume_ma'] = minute_bars['volume'].rolling(window=30).mean()
        minute_bars['price_ma'] = minute_bars['close'].rolling(window=30).mean()
        
        # Detect accumulation: volume > average, price stable or rising
        minute_bars['accumulation'] = (
            (minute_bars['volume'] > minute_bars['volume_ma'] * 1.2) &
            (minute_bars['close'] >= minute_bars['price_ma'] * 0.995)  # Within 0.5% or above MA
        )
        
        # Group consecutive accumulation periods
        minute_bars['accumulation_group'] = (minute_bars['accumulation'] != minute_bars['accumulation'].shift()).cumsum()
        
        # Extract accumulation windows
        for group in minute_bars[minute_bars['accumulation']].groupby('accumulation_group'):
            group_data = group[1]
            if len(group_data) >= 5:  # At least 5 minutes
                start_time = group_data.index[0]
                end_time = group_data.index[-1]
                
                window = {
                    'start': start_time.strftime('%Y-%m-%d %H:%M'),
                    'end': end_time.strftime('%Y-%m-%d %H:%M'),
                    'duration_minutes': len(group_data),
                    'total_volume': int(group_data['volume'].sum()),
                    'avg_volume_vs_normal': float(group_data['volume'].mean() / minute_bars['volume'].mean()),
                    'price_change': float((group_data['close'].iloc[-1] - group_data['close'].iloc[0]) / group_data['close'].iloc[0] * 100),
                    'vwap': float((group_data['close'] * group_data['volume']).sum() / group_data['volume'].sum()),
                    'time_of_day': 'morning' if start_time.hour < 12 else 'afternoon' if start_time.hour < 15 else 'late_day'
                }
                windows.append(window)
        
        # Sort by volume
        windows.sort(key=lambda x: x['total_volume'], reverse=True)
        
        return windows[:20]  # Top 20 accumulation windows
    
    def _analyze_volume_price_correlation(self, minute_bars: pd.DataFrame, daily_bars: pd.DataFrame) -> Dict[str, Any]:
        """Analyze correlation between volume and price movements."""
        if minute_bars.empty or daily_bars.empty:
            return {}
        
        # Minute-level correlation
        minute_bars = minute_bars.copy()
        minute_bars['price_change'] = minute_bars['close'].pct_change()
        minute_bars['volume_change'] = minute_bars['volume'].pct_change()
        
        # Remove outliers for correlation
        minute_clean = minute_bars[
            (minute_bars['price_change'].abs() < minute_bars['price_change'].abs().quantile(0.95)) &
            (minute_bars['volume_change'].abs() < minute_bars['volume_change'].abs().quantile(0.95))
        ]
        
        minute_correlation = minute_clean[['price_change', 'volume_change']].corr().iloc[0, 1] if len(minute_clean) > 10 else 0
        
        # Daily correlation
        daily_bars = daily_bars.copy()
        daily_bars['price_change'] = daily_bars['close'].pct_change()
        daily_bars['volume_change'] = daily_bars['volume'].pct_change()
        
        daily_correlation = daily_bars[['price_change', 'volume_change']].corr().iloc[0, 1] if len(daily_bars) > 5 else 0
        
        # Volume precedes price analysis
        minute_bars['volume_ma'] = minute_bars['volume'].rolling(window=30).mean()
        minute_bars['future_price_change'] = minute_bars['close'].pct_change().shift(-30)  # 30 min future
        
        # High volume impact on future prices
        high_volume_bars = minute_bars[minute_bars['volume'] > minute_bars['volume_ma'] * 2]
        
        if len(high_volume_bars) > 10:
            avg_future_move = high_volume_bars['future_price_change'].mean()
            win_rate = (high_volume_bars['future_price_change'] > 0).mean()
        else:
            avg_future_move = 0
            win_rate = 0.5
        
        # Volume trend analysis
        recent_volume_trend = np.polyfit(range(len(daily_bars)), daily_bars['volume'].values, 1)[0] if len(daily_bars) > 3 else 0
        recent_price_trend = np.polyfit(range(len(daily_bars)), daily_bars['close'].values, 1)[0] if len(daily_bars) > 3 else 0
        
        return {
            'minute_correlation': float(minute_correlation),
            'daily_correlation': float(daily_correlation),
            'correlation_interpretation': self._interpret_correlation(minute_correlation, daily_correlation),
            'volume_predictive_power': {
                'high_volume_future_return': float(avg_future_move * 100) if avg_future_move else 0,
                'high_volume_win_rate': float(win_rate),
                'predictive_confidence': 'high' if abs(avg_future_move) > 0.01 and win_rate > 0.6 else 'medium' if win_rate > 0.55 else 'low'
            },
            'trend_alignment': {
                'volume_trend': 'increasing' if recent_volume_trend > 0 else 'decreasing',
                'price_trend': 'increasing' if recent_price_trend > 0 else 'decreasing',
                'aligned': (recent_volume_trend > 0 and recent_price_trend > 0) or (recent_volume_trend < 0 and recent_price_trend < 0)
            },
            'volume_confirms_price': minute_correlation > 0.3 and daily_correlation > 0.3
        }
    
    def _identify_volume_based_levels(self, minute_bars: pd.DataFrame) -> Dict[str, Any]:
        """Identify support and resistance levels based on volume."""
        if len(minute_bars) < 100:
            return {}
        
        # Get volume profile
        volume_profile = self._analyze_volume_at_price(minute_bars)
        
        # High Volume Nodes become support/resistance
        support_resistance = []
        
        current_price = minute_bars['close'].iloc[-1]
        
        # From HVN levels
        for hvn in volume_profile.get('high_volume_nodes', [])[:5]:  # Top 5
            level = {
                'price': hvn['price'],
                'strength': hvn['strength'],
                'type': 'resistance' if hvn['price'] > current_price else 'support',
                'volume_at_level': hvn['volume'],
                'distance_from_current': float(abs(hvn['price'] - current_price) / current_price * 100)
            }
            support_resistance.append(level)
        
        # Add POC as major level
        poc = volume_profile.get('point_of_control', 0)
        if poc > 0:
            support_resistance.append({
                'price': poc,
                'strength': 1.0,  # Strongest
                'type': 'major_resistance' if poc > current_price else 'major_support',
                'volume_at_level': 'highest',
                'distance_from_current': float(abs(poc - current_price) / current_price * 100)
            })
        
        # Value Area boundaries
        vah = volume_profile.get('value_area_high', 0)
        val = volume_profile.get('value_area_low', 0)
        
        if vah > 0:
            support_resistance.append({
                'price': vah,
                'strength': 0.8,
                'type': 'value_area_high',
                'volume_at_level': 'significant',
                'distance_from_current': float(abs(vah - current_price) / current_price * 100)
            })
        
        if val > 0:
            support_resistance.append({
                'price': val,
                'strength': 0.8,
                'type': 'value_area_low',
                'volume_at_level': 'significant',
                'distance_from_current': float(abs(val - current_price) / current_price * 100)
            })
        
        # Sort by distance from current price
        support_resistance.sort(key=lambda x: x['distance_from_current'])
        
        # Identify nearest levels
        nearest_support = next((level for level in support_resistance if level['price'] < current_price), None)
        nearest_resistance = next((level for level in support_resistance if level['price'] > current_price), None)
        
        return {
            'support_resistance_levels': support_resistance[:10],  # Top 10 levels
            'nearest_support': nearest_support,
            'nearest_resistance': nearest_resistance,
            'current_price_position': {
                'in_value_area': val <= current_price <= vah if val > 0 and vah > 0 else False,
                'above_poc': current_price > poc if poc > 0 else False,
                'distance_to_poc': float((current_price - poc) / poc * 100) if poc > 0 else 0
            },
            'level_clustering': self._detect_level_clustering(support_resistance)
        }
    
    def _analyze_intraday_patterns(self, minute_bars: pd.DataFrame) -> Dict[str, Any]:
        """Detect specific intraday patterns and anomalies."""
        patterns = {
            'detected_patterns': [],
            'anomalies': [],
            'pattern_strength': 0.0
        }
        
        if len(minute_bars) < 390:  # Need at least one full day
            return patterns
        
        minute_bars = minute_bars.copy()
        minute_bars['hour'] = minute_bars.index.hour
        minute_bars['minute'] = minute_bars.index.minute
        minute_bars['date'] = minute_bars.index.date
        minute_bars['time_of_day'] = minute_bars['hour'] * 60 + minute_bars['minute']
        
        # Pattern 1: Opening Range Breakout
        for date in minute_bars['date'].unique()[-5:]:  # Last 5 days
            day_data = minute_bars[minute_bars['date'] == date]
            if len(day_data) < 30:
                continue
                
            # First 30 minutes
            opening_range = day_data.iloc[:30]
            opening_high = opening_range['high'].max()
            opening_low = opening_range['low'].min()
            
            # Check for breakout
            post_open = day_data.iloc[30:]
            if not post_open.empty:
                if post_open['high'].max() > opening_high * 1.01:  # 1% above
                    patterns['detected_patterns'].append({
                        'type': 'opening_range_breakout_up',
                        'date': str(date),
                        'breakout_time': post_open[post_open['high'] > opening_high * 1.01].index[0].strftime('%H:%M'),
                        'magnitude': float((post_open['high'].max() - opening_high) / opening_high * 100)
                    })
                elif post_open['low'].min() < opening_low * 0.99:  # 1% below
                    patterns['detected_patterns'].append({
                        'type': 'opening_range_breakout_down',
                        'date': str(date),
                        'breakout_time': post_open[post_open['low'] < opening_low * 0.99].index[0].strftime('%H:%M'),
                        'magnitude': float((opening_low - post_open['low'].min()) / opening_low * 100)
                    })
        
        # Pattern 2: Lunch Hour Reversal
        lunch_data = minute_bars[(minute_bars['hour'] >= 12) & (minute_bars['hour'] < 13)]
        if not lunch_data.empty:
            lunch_reversals = []
            for date in lunch_data['date'].unique():
                day_lunch = lunch_data[lunch_data['date'] == date]
                if len(day_lunch) > 10:
                    first_half = day_lunch.iloc[:len(day_lunch)//2]
                    second_half = day_lunch.iloc[len(day_lunch)//2:]
                    
                    first_direction = first_half['close'].iloc[-1] - first_half['close'].iloc[0]
                    second_direction = second_half['close'].iloc[-1] - second_half['close'].iloc[0]
                    
                    if first_direction * second_direction < 0:  # Opposite signs = reversal
                        lunch_reversals.append(date)
            
            if len(lunch_reversals) > 2:
                patterns['detected_patterns'].append({
                    'type': 'lunch_hour_reversal',
                    'frequency': len(lunch_reversals),
                    'dates': [str(d) for d in lunch_reversals[-3:]]  # Last 3
                })
        
        # Pattern 3: Power Hour (3-4 PM) Activity
        power_hour = minute_bars[minute_bars['hour'] == 15]
        if not power_hour.empty:
            power_hour_stats = power_hour.groupby('date').agg({
                'volume': 'sum',
                'close': ['first', 'last']
            })
            
            avg_hourly_volume = minute_bars.groupby(['date', 'hour'])['volume'].sum().mean()
            power_hour_avg = power_hour_stats['volume']['sum'].mean()
            
            if power_hour_avg > avg_hourly_volume * 1.5:
                patterns['detected_patterns'].append({
                    'type': 'power_hour_activity',
                    'volume_ratio': float(power_hour_avg / avg_hourly_volume),
                    'avg_movement': float(power_hour_stats.apply(
                        lambda x: abs(x['close']['last'] - x['close']['first']) / x['close']['first'] * 100, axis=1
                    ).mean())
                })
        
        # Anomaly Detection
        # 1. Volume spikes
        minute_bars['volume_zscore'] = (minute_bars['volume'] - minute_bars['volume'].rolling(30).mean()) / minute_bars['volume'].rolling(30).std()
        volume_spikes = minute_bars[minute_bars['volume_zscore'] > 3]
        
        for _, spike in volume_spikes.iterrows():
            patterns['anomalies'].append({
                'type': 'volume_spike',
                'time': spike.name.strftime('%Y-%m-%d %H:%M'),
                'magnitude': float(spike['volume_zscore']),
                'volume': int(spike['volume']),
                'price_impact': float(minute_bars.loc[spike.name:spike.name + pd.Timedelta(minutes=5), 'close'].pct_change().sum() * 100) if spike.name + pd.Timedelta(minutes=5) in minute_bars.index else 0
            })
        
        # 2. Price gaps
        minute_bars['gap'] = (minute_bars['open'] - minute_bars['close'].shift()) / minute_bars['close'].shift() * 100
        gaps = minute_bars[minute_bars['gap'].abs() > 0.5]  # 0.5% gaps
        
        for _, gap in gaps.iterrows():
            patterns['anomalies'].append({
                'type': 'intraday_gap',
                'time': gap.name.strftime('%Y-%m-%d %H:%M'),
                'size': float(gap['gap']),
                'filled': bool(minute_bars.loc[gap.name:gap.name + pd.Timedelta(hours=1), 'low'].min() <= gap['close'] if gap['gap'] > 0 else minute_bars.loc[gap.name:gap.name + pd.Timedelta(hours=1), 'high'].max() >= gap['close'])
            })
        
        # Calculate pattern strength
        patterns['pattern_strength'] = min(1.0, len(patterns['detected_patterns']) * 0.2 + len(patterns['anomalies']) * 0.1)
        
        return patterns
    
    def _detect_smart_money_patterns(self, minute_bars: pd.DataFrame, daily_bars: pd.DataFrame) -> Dict[str, Any]:
        """Detect patterns indicative of institutional or smart money activity."""
        smart_money = {
            'indicators': [],
            'confidence': 0.0,
            'institutional_footprint': False
        }
        
        if minute_bars.empty or daily_bars.empty:
            return smart_money
        
        minute_bars = minute_bars.copy()
        daily_bars = daily_bars.copy()
        
        # 1. Large Block Trades (volume significantly above average)
        avg_minute_volume = minute_bars['volume'].mean()
        std_minute_volume = minute_bars['volume'].std()
        
        block_trades = minute_bars[minute_bars['volume'] > avg_minute_volume + 3 * std_minute_volume]
        
        if len(block_trades) > 0:
            # Check if these occur at strategic times
            block_trades['hour'] = block_trades.index.hour
            strategic_times = block_trades[
                (block_trades['hour'] == 9) |  # Opening
                (block_trades['hour'] == 15) |  # Closing
                ((block_trades['hour'] == 12) | (block_trades['hour'] == 13))  # Lunch
            ]
            
            smart_money['indicators'].append({
                'type': 'block_trades',
                'count': len(block_trades),
                'strategic_timing_pct': float(len(strategic_times) / len(block_trades) * 100) if len(block_trades) > 0 else 0,
                'avg_size_vs_normal': float(block_trades['volume'].mean() / avg_minute_volume),
                'recent_examples': block_trades.index[-5:].strftime('%Y-%m-%d %H:%M').tolist()
            })
        
        # 2. Accumulation/Distribution Pattern
        daily_bars['money_flow'] = daily_bars['close'] * daily_bars['volume']
        daily_bars['ad_line'] = ((daily_bars['close'] - daily_bars['low']) - (daily_bars['high'] - daily_bars['close'])) / (daily_bars['high'] - daily_bars['low'] + 0.0001) * daily_bars['volume']
        daily_bars['ad_line_cum'] = daily_bars['ad_line'].cumsum()
        
        # Check if AD line diverges from price
        if len(daily_bars) > 10:
            price_trend = np.polyfit(range(len(daily_bars)), daily_bars['close'].values, 1)[0]
            ad_trend = np.polyfit(range(len(daily_bars)), daily_bars['ad_line_cum'].values, 1)[0]
            
            # Bullish divergence: price down, AD up
            if price_trend < 0 and ad_trend > 0:
                smart_money['indicators'].append({
                    'type': 'accumulation_divergence',
                    'description': 'Price declining while accumulation increasing',
                    'strength': 'strong' if abs(price_trend) > 0.1 and ad_trend > 1000 else 'moderate'
                })
            # Bearish divergence: price up, AD down
            elif price_trend > 0 and ad_trend < 0:
                smart_money['indicators'].append({
                    'type': 'distribution_divergence',
                    'description': 'Price rising while distribution occurring',
                    'strength': 'strong' if price_trend > 0.1 and abs(ad_trend) > 1000 else 'moderate'
                })
        
        # 3. VWAP Relationship
        minute_bars['vwap'] = (minute_bars['close'] * minute_bars['volume']).cumsum() / minute_bars['volume'].cumsum()
        minute_bars['above_vwap'] = minute_bars['close'] > minute_bars['vwap']
        
        # Institutional support often keeps price above VWAP
        vwap_support_pct = minute_bars['above_vwap'].mean() * 100
        
        if vwap_support_pct > 65:  # Price above VWAP 65%+ of the time
            smart_money['indicators'].append({
                'type': 'vwap_support',
                'description': 'Consistent buying support above VWAP',
                'above_vwap_pct': float(vwap_support_pct),
                'current_vs_vwap': float((minute_bars['close'].iloc[-1] - minute_bars['vwap'].iloc[-1]) / minute_bars['vwap'].iloc[-1] * 100)
            })
        
        # 4. Time-Weighted Accumulation
        minute_bars['time_weight'] = np.linspace(0.5, 1.0, len(minute_bars))  # Recent data weighted more
        weighted_volume_trend = np.polyfit(range(len(minute_bars)), minute_bars['volume'].values, 1, w=minute_bars['time_weight'].values)[0]
        
        if weighted_volume_trend > avg_minute_volume * 0.1:  # Significant positive trend
            smart_money['indicators'].append({
                'type': 'increasing_volume_trend',
                'description': 'Volume trending higher over time',
                'trend_strength': float(weighted_volume_trend / avg_minute_volume)
            })
        
        # Calculate confidence
        indicator_count = len(smart_money['indicators'])
        if indicator_count >= 3:
            smart_money['confidence'] = 0.9
            smart_money['institutional_footprint'] = True
        elif indicator_count >= 2:
            smart_money['confidence'] = 0.7
            smart_money['institutional_footprint'] = True
        elif indicator_count >= 1:
            smart_money['confidence'] = 0.5
        
        return smart_money
    
    def _comprehensive_accumulation_scoring(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive accumulation score with detailed breakdown."""
        scoring = {
            'overall_score': 0.0,
            'component_scores': {},
            'accumulation_detected': False,
            'confidence': 0.0,
            'accumulation_phase': 'none'
        }
        
        # Component weights
        weights = {
            'volume_profile': 0.20,
            'dark_pool': 0.15,
            'accumulation_windows': 0.15,
            'volume_price_correlation': 0.15,
            'smart_money': 0.20,
            'intraday_patterns': 0.15
        }
        
        # 1. Volume Profile Score
        vap = result.get('volume_at_price', {})
        if vap:
            profile_score = 0.0
            # Price near high volume node
            if vap.get('current_price_vs_poc', 0) < 2:  # Within 2% of POC
                profile_score += 0.4
            # Balanced profile
            if vap.get('profile_balance') == 'balanced':
                profile_score += 0.3
            # In value area
            if result.get('volume_based_levels', {}).get('current_price_position', {}).get('in_value_area', False):
                profile_score += 0.3
            scoring['component_scores']['volume_profile'] = profile_score
        
        # 2. Dark Pool Score
        dark_pool = result.get('dark_pool_indicators', {})
        if dark_pool.get('detected', False):
            scoring['component_scores']['dark_pool'] = dark_pool.get('confidence', 0)
        else:
            scoring['component_scores']['dark_pool'] = 0.0
        
        # 3. Accumulation Windows Score
        windows = result.get('accumulation_windows', [])
        if windows:
            # More windows = more accumulation
            window_score = min(1.0, len(windows) / 10)  # Max score at 10+ windows
            # Weight by recency
            recent_windows = [w for w in windows if 'afternoon' in w.get('time_of_day', '')]
            if recent_windows:
                window_score = min(1.0, window_score + 0.2)
            scoring['component_scores']['accumulation_windows'] = window_score
        else:
            scoring['component_scores']['accumulation_windows'] = 0.0
        
        # 4. Volume-Price Correlation Score
        vp_analysis = result.get('volume_price_analysis', {})
        if vp_analysis:
            corr_score = 0.0
            # Positive correlation is good
            if vp_analysis.get('minute_correlation', 0) > 0.3:
                corr_score += 0.3
            if vp_analysis.get('volume_confirms_price', False):
                corr_score += 0.4
            # Predictive power
            if vp_analysis.get('volume_predictive_power', {}).get('predictive_confidence') == 'high':
                corr_score += 0.3
            scoring['component_scores']['volume_price_correlation'] = corr_score
        else:
            scoring['component_scores']['volume_price_correlation'] = 0.0
        
        # 5. Smart Money Score
        smart_money = result.get('smart_money_indicators', {})
        scoring['component_scores']['smart_money'] = smart_money.get('confidence', 0)
        
        # 6. Intraday Patterns Score
        patterns = result.get('intraday_patterns', {})
        scoring['component_scores']['intraday_patterns'] = patterns.get('pattern_strength', 0)
        
        # Calculate overall score
        overall_score = 0.0
        for component, weight in weights.items():
            component_score = scoring['component_scores'].get(component, 0.0)
            overall_score += component_score * weight
        
        scoring['overall_score'] = float(overall_score)
        
        # Determine accumulation phase
        if overall_score >= 0.7:
            scoring['accumulation_phase'] = 'strong_accumulation'
            scoring['confidence'] = 0.9
        elif overall_score >= 0.5:
            scoring['accumulation_phase'] = 'moderate_accumulation'
            scoring['confidence'] = 0.7
        elif overall_score >= 0.3:
            scoring['accumulation_phase'] = 'early_accumulation'
            scoring['confidence'] = 0.5
        else:
            scoring['accumulation_phase'] = 'no_accumulation'
            scoring['confidence'] = 0.3
        
        scoring['accumulation_detected'] = overall_score >= 0.5
        
        # Add interpretation
        scoring['interpretation'] = self._interpret_accumulation_score(scoring)
        
        return scoring
    
    def _generate_detailed_entry_analysis(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate detailed entry recommendations based on comprehensive analysis."""
        accumulation = result.get('accumulation_analysis', {})
        score = accumulation.get('overall_score', 0)
        
        entry = {
            'recommendation': 'NO_ENTRY',
            'confidence': 0.0,
            'entry_price_zones': [],
            'stop_loss_levels': [],
            'position_size_suggestion': 0.0,
            'timing_windows': [],
            'risk_factors': [],
            'supporting_evidence': []
        }
        
        if score < 0.3:
            entry['recommendation'] = 'NO_ENTRY'
            entry['confidence'] = 0.9
            entry['risk_factors'].append('No significant accumulation detected')
            return entry
        
        # Determine entry recommendation
        if score >= 0.7:
            entry['recommendation'] = 'STRONG_BUY'
            entry['confidence'] = 0.9
            entry['position_size_suggestion'] = 2.0  # 2% of portfolio
        elif score >= 0.5:
            entry['recommendation'] = 'BUY'
            entry['confidence'] = 0.7
            entry['position_size_suggestion'] = 1.5  # 1.5% of portfolio
        elif score >= 0.3:
            entry['recommendation'] = 'WATCH'
            entry['confidence'] = 0.5
            entry['position_size_suggestion'] = 1.0  # 1% of portfolio
        
        # Entry price zones from volume analysis
        levels = result.get('volume_based_levels', {})
        vap = result.get('volume_at_price', {})
        
        if levels and vap:
            # Add value area as entry zone
            if vap.get('value_area_low'):
                entry['entry_price_zones'].append({
                    'type': 'value_area_low',
                    'price': vap['value_area_low'],
                    'strength': 'high',
                    'description': 'Lower boundary of value area - strong support'
                })
            
            # Add nearest support
            if levels.get('nearest_support'):
                entry['entry_price_zones'].append({
                    'type': 'volume_support',
                    'price': levels['nearest_support']['price'],
                    'strength': 'medium',
                    'description': 'Nearest high-volume support level'
                })
        
        # Stop loss levels
        if entry['entry_price_zones']:
            # Set stop below lowest support
            lowest_support = min(zone['price'] for zone in entry['entry_price_zones'])
            entry['stop_loss_levels'].append({
                'price': lowest_support * 0.98,  # 2% below support
                'type': 'technical',
                'risk_percentage': 2.0
            })
        
        # Timing windows from accumulation patterns
        windows = result.get('accumulation_windows', [])
        hourly = result.get('hourly_patterns', {})
        
        if hourly.get('unusual_activity_hours'):
            for hour, data in hourly['unusual_activity_hours'].items():
                entry['timing_windows'].append({
                    'time': hour,
                    'volume_ratio': data['volume_ratio'],
                    'description': f'High volume period - {data["volume_ratio"]:.1f}x normal'
                })
        
        # Risk factors
        dark_pool = result.get('dark_pool_indicators', {})
        if dark_pool.get('detected'):
            entry['risk_factors'].append('Dark pool activity detected - possible hidden supply')
        
        patterns = result.get('intraday_patterns', {})
        if patterns.get('anomalies'):
            entry['risk_factors'].append(f"{len(patterns['anomalies'])} intraday anomalies detected")
        
        # Supporting evidence
        if accumulation.get('component_scores', {}).get('smart_money', 0) > 0.7:
            entry['supporting_evidence'].append('Strong smart money accumulation signals')
        
        if accumulation.get('component_scores', {}).get('volume_profile', 0) > 0.7:
            entry['supporting_evidence'].append('Price supported by high-volume nodes')
        
        if result.get('volume_price_analysis', {}).get('volume_confirms_price'):
            entry['supporting_evidence'].append('Volume confirms price action')
        
        # Add key statistics
        entry['key_statistics'] = {
            'accumulation_score': float(score),
            'days_analyzed': result.get('lookback_days', 0),
            'total_accumulation_windows': len(windows),
            'smart_money_confidence': result.get('smart_money_indicators', {}).get('confidence', 0),
            'dark_pool_detected': dark_pool.get('detected', False)
        }
        
        return entry
    
    def _generate_statistical_summary(self, minute_bars: pd.DataFrame, daily_bars: pd.DataFrame) -> Dict[str, Any]:
        """Generate statistical summary of volume and price data."""
        summary = {}
        
        if not minute_bars.empty:
            summary['minute_data'] = {
                'total_bars': len(minute_bars),
                'avg_volume': float(minute_bars['volume'].mean()),
                'median_volume': float(minute_bars['volume'].median()),
                'volume_std': float(minute_bars['volume'].std()),
                'volume_skew': float(minute_bars['volume'].skew()),
                'volume_kurtosis': float(minute_bars['volume'].kurtosis()),
                'price_volatility': float(minute_bars['close'].pct_change().std() * np.sqrt(252 * 390)),  # Annualized
                'avg_spread': float((minute_bars['high'] - minute_bars['low']).mean()),
                'avg_range_pct': float(((minute_bars['high'] - minute_bars['low']) / minute_bars['close']).mean() * 100)
            }
        
        if not daily_bars.empty:
            summary['daily_data'] = {
                'total_days': len(daily_bars),
                'avg_daily_volume': float(daily_bars['volume'].mean()),
                'avg_daily_range': float(((daily_bars['high'] - daily_bars['low']) / daily_bars['close']).mean() * 100),
                'trend_strength': float(abs(np.polyfit(range(len(daily_bars)), daily_bars['close'].values, 1)[0])) if len(daily_bars) > 3 else 0,
                'volatility_regime': 'high' if daily_bars['close'].pct_change().std() > 0.02 else 'normal' if daily_bars['close'].pct_change().std() > 0.01 else 'low'
            }
        
        return summary
    
    def _classify_volume_shape(self, day_data: pd.DataFrame) -> str:
        """Classify the volume distribution shape for a day."""
        if len(day_data) < 100:
            return 'insufficient_data'
        
        # Divide day into thirds
        third = len(day_data) // 3
        morning_vol = day_data.iloc[:third]['volume'].sum()
        midday_vol = day_data.iloc[third:2*third]['volume'].sum()
        afternoon_vol = day_data.iloc[2*third:]['volume'].sum()
        
        total_vol = morning_vol + midday_vol + afternoon_vol
        
        # Classify shapes
        if morning_vol > total_vol * 0.45:
            return 'morning_heavy'
        elif afternoon_vol > total_vol * 0.45:
            return 'afternoon_heavy'
        elif midday_vol < total_vol * 0.25:
            return 'u_shaped'
        elif abs(morning_vol - afternoon_vol) < total_vol * 0.1:
            return 'balanced'
        else:
            return 'irregular'
    
    def _interpret_correlation(self, minute_corr: float, daily_corr: float) -> str:
        """Interpret volume-price correlation."""
        if minute_corr > 0.5 and daily_corr > 0.5:
            return 'Strong positive - volume confirms price moves'
        elif minute_corr > 0.3 and daily_corr > 0.3:
            return 'Moderate positive - generally supportive'
        elif minute_corr < -0.3 and daily_corr < -0.3:
            return 'Negative - possible distribution'
        else:
            return 'Mixed - no clear relationship'
    
    def _detect_level_clustering(self, levels: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Detect clustering of support/resistance levels."""
        if len(levels) < 3:
            return {'clusters_detected': False}
        
        # Sort by price
        sorted_levels = sorted(levels, key=lambda x: x['price'])
        
        clusters = []
        current_cluster = [sorted_levels[0]]
        
        for i in range(1, len(sorted_levels)):
            # If within 1% of previous level, add to cluster
            if abs(sorted_levels[i]['price'] - sorted_levels[i-1]['price']) / sorted_levels[i-1]['price'] < 0.01:
                current_cluster.append(sorted_levels[i])
            else:
                if len(current_cluster) >= 2:
                    clusters.append(current_cluster)
                current_cluster = [sorted_levels[i]]
        
        if len(current_cluster) >= 2:
            clusters.append(current_cluster)
        
        return {
            'clusters_detected': len(clusters) > 0,
            'cluster_count': len(clusters),
            'strongest_cluster': {
                'price_range': [clusters[0][0]['price'], clusters[0][-1]['price']],
                'level_count': len(clusters[0])
            } if clusters else None
        }
    
    def _interpret_accumulation_score(self, scoring: Dict[str, Any]) -> str:
        """Provide interpretation of accumulation score."""
        score = scoring['overall_score']
        components = scoring['component_scores']
        
        if score >= 0.7:
            return "Strong accumulation detected. Multiple indicators confirm institutional buying. High confidence entry signal."
        elif score >= 0.5:
            return "Moderate accumulation present. Key indicators positive but some mixed signals. Consider entry with appropriate risk management."
        elif score >= 0.3:
            return "Early-stage accumulation possible. Limited evidence but some positive signs. Monitor closely for confirmation."
        else:
            return "No significant accumulation detected. Volume patterns do not suggest institutional interest at this time."
    
    def _empty_comprehensive_result(self, partial_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """Return empty comprehensive result structure."""
        base = {
            'symbol': '',
            'analysis_date': '',
            'lookback_days': 0,
            'error': 'Insufficient data for analysis',
            'accumulation_analysis': {
                'overall_score': 0.0,
                'accumulation_detected': False,
                'confidence': 0.0
            },
            'entry_analysis': {
                'recommendation': 'NO_DATA',
                'confidence': 0.0
            }
        }
        
        if partial_result:
            base.update(partial_result)
        
        return base