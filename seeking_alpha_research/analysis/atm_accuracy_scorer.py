"""
ATM Date Accuracy Scorer

Scores the accuracy of ATM date predictions against actual gap events:
- Score 0: Perfect prediction (gap occurs exactly in middle of window)
- Score -1 to 1: Within predicted window (closer to middle = better)
- Score < -1 or > 1: Outside window (further = worse)

NO FAKES, NO MOCKS - Real scoring with real dates
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

logger = logging.getLogger(__name__)


class ATMAccuracyScorer:
    """Scores accuracy of ATM predictions against actual events."""
    
    def __init__(self):
        logger.info("ATM Accuracy Scorer initialized")
    
    def score_prediction(
        self, 
        predicted_date: str,
        window_start: str, 
        window_end: str,
        actual_gap_date: str,
        gap_info: Dict[str, any] = None
    ) -> Dict[str, any]:
        """
        Score ATM prediction accuracy.
        
        Args:
            predicted_date: Center of predicted ATM window
            window_start: Start of predicted window
            window_end: End of predicted window
            actual_gap_date: Date when gap actually occurred
            gap_info: Optional gap details (percentage, volume, etc.)
        
        Returns:
            Dictionary with score and analysis
        """
        logger.info(
            f"Scoring ATM prediction: Window {window_start} to {window_end}, "
            f"Actual gap: {actual_gap_date}"
        )
        
        # Convert to datetime objects
        pred_date = pd.to_datetime(predicted_date)
        start_date = pd.to_datetime(window_start)
        end_date = pd.to_datetime(window_end)
        gap_date = pd.to_datetime(actual_gap_date)
        
        # Calculate window properties
        window_size = (end_date - start_date).days
        window_center = start_date + timedelta(days=window_size / 2)
        
        # Calculate position within or outside window
        if start_date <= gap_date <= end_date:
            # Gap is within window
            score = self._calculate_window_score(gap_date, start_date, end_date, window_center)
            in_window = True
        else:
            # Gap is outside window
            score = self._calculate_outside_score(gap_date, start_date, end_date)
            in_window = False
        
        # Additional metrics
        days_from_center = abs((gap_date - window_center).days)
        days_from_prediction = abs((gap_date - pred_date).days)
        
        # Early/Late classification
        if gap_date < start_date:
            timing = 'early'
            days_outside = (start_date - gap_date).days
        elif gap_date > end_date:
            timing = 'late'
            days_outside = (gap_date - end_date).days
        else:
            timing = 'on_time'
            days_outside = 0
        
        # Quality assessment
        quality = self._assess_prediction_quality(score, window_size, days_from_center)
        
        results = {
            'score': float(score),
            'in_window': in_window,
            'within_window': in_window,  # Add alias for test compatibility
            'timing': timing,
            'days_from_center': int(days_from_center),
            'days_from_prediction': int(days_from_prediction),
            'days_outside_window': int(days_outside),
            'window_size_days': int(window_size),
            'prediction_quality': quality,
            'accuracy_percentage': self._calculate_accuracy_percentage(score, window_size),
            'insights': self._generate_insights(score, timing, days_from_center, window_size)
        }
        
        # Add gap info if provided
        if gap_info:
            results['gap_details'] = {
                'gap_percentage': gap_info.get('gap_percentage', 0),
                'gap_volume': gap_info.get('volume', 0),
                'has_news': gap_info.get('has_news', False)
            }
        
        logger.info(f"ATM prediction score: {score:.3f} ({quality})")
        
        return results
    
    def _calculate_window_score(
        self, gap_date: pd.Timestamp, start: pd.Timestamp, 
        end: pd.Timestamp, center: pd.Timestamp
    ) -> float:
        """Calculate score for gap within window (-1 to 1)."""
        
        window_size = (end - start).days
        half_window = window_size / 2
        
        # Handle edge case: zero-day window (start = end)
        if half_window == 0:
            # If gap date equals the single-day window, perfect score
            if gap_date == start:
                return 0.0
            # If gap date is outside single-day window, worst score
            else:
                return 1.0 if gap_date > start else -1.0
        
        # Distance from center (negative if before, positive if after)
        days_from_center = (gap_date - center).days
        
        # Normalize to -1 to 1 scale
        score = days_from_center / half_window
        
        # Ensure within bounds (handles edge cases)
        score = max(-1.0, min(1.0, score))
        
        return score
    
    def _calculate_outside_score(
        self, gap_date: pd.Timestamp, start: pd.Timestamp, end: pd.Timestamp
    ) -> float:
        """Calculate score for gap outside window (< -1 or > 1)."""
        
        window_size = (end - start).days
        
        if gap_date < start:
            # Early gap
            days_early = (start - gap_date).days
            # Each day early adds -0.1 to score (beyond -1)
            score = -1.0 - (days_early * 0.1)
        else:
            # Late gap
            days_late = (gap_date - end).days
            # Each day late adds 0.1 to score (beyond 1)
            score = 1.0 + (days_late * 0.1)
        
        return score
    
    def _assess_prediction_quality(
        self, score: float, window_size: int, days_from_center: int
    ) -> str:
        """Assess quality of prediction based on score."""
        
        abs_score = abs(score)
        
        if abs_score == 0.0:
            return 'PERFECT'
        elif abs_score <= 0.2:
            return 'EXCELLENT'
        elif abs_score <= 0.5:
            return 'GOOD'
        elif abs_score <= 1.0:
            return 'FAIR'
        elif abs_score <= 2.0:
            return 'POOR'
        else:
            return 'VERY_POOR'
    
    def _calculate_accuracy_percentage(self, score: float, window_size: int) -> float:
        """Convert score to accuracy percentage."""
        
        abs_score = abs(score)
        
        if abs_score <= 1.0:
            # Within window: 100% at center, decreasing linearly
            accuracy = 100 * (1 - abs_score)
        else:
            # Outside window: exponential decay
            accuracy = 100 * np.exp(-(abs_score - 1))
        
        return max(0, min(100, accuracy))
    
    def _generate_insights(
        self, score: float, timing: str, days_from_center: int, window_size: int
    ) -> List[str]:
        """Generate insights about the prediction accuracy."""
        
        insights = []
        
        # Score-based insights
        if -0.2 <= score <= 0.2:
            insights.append("Near-perfect prediction timing")
        elif score < -1:
            insights.append(f"Gap occurred {abs(score - (-1)):.1f} window-widths early")
        elif score > 1:
            insights.append(f"Gap occurred {score - 1:.1f} window-widths late")
        
        # Timing insights
        if timing == 'early':
            insights.append("Company diluted earlier than expected - possible urgent cash needs")
        elif timing == 'late':
            insights.append("Company delayed dilution - possibly waiting for better terms")
        
        # Window size insights
        if window_size > 60:
            insights.append("Large prediction window indicates uncertainty in timing")
        elif window_size < 20:
            insights.append("Narrow prediction window shows high confidence")
        
        # Practical insights
        if abs(score) <= 0.5:
            insights.append("Prediction accurate enough for profitable trading")
        elif abs(score) <= 1.0:
            insights.append("Prediction within window but timing could be improved")
        else:
            insights.append("Prediction missed - review cash burn assumptions")
        
        return insights[:3]  # Return top 3 insights
    
    def score_multiple_predictions(
        self, predictions: List[Dict[str, any]], actual_gaps: List[Dict[str, any]]
    ) -> Dict[str, any]:
        """
        Score multiple predictions to assess overall model performance.
        
        Args:
            predictions: List of prediction dicts with dates and windows
            actual_gaps: List of actual gap events
        
        Returns:
            Aggregate scoring metrics
        """
        if not predictions or not actual_gaps:
            return {
                'total_predictions': 0,
                'error': 'No predictions or gaps to score'
            }
        
        scores = []
        in_window_count = 0
        timing_counts = {'early': 0, 'on_time': 0, 'late': 0}
        quality_counts = {'EXCELLENT': 0, 'GOOD': 0, 'FAIR': 0, 'POOR': 0, 'VERY_POOR': 0}
        
        # Match predictions to gaps
        for pred in predictions:
            symbol = pred.get('symbol')
            
            # Find corresponding gap
            matching_gaps = [g for g in actual_gaps if g.get('symbol') == symbol]
            if not matching_gaps:
                continue
            
            # Use the gap closest to predicted date
            pred_date = pd.to_datetime(pred.get('predicted_atm_date'))
            closest_gap = min(
                matching_gaps, 
                key=lambda g: abs((pd.to_datetime(g['gap_date']) - pred_date).days)
            )
            
            # Score the prediction
            score_result = self.score_prediction(
                pred.get('predicted_atm_date'),
                pred.get('atm_window_start'),
                pred.get('atm_window_end'),
                closest_gap.get('gap_date'),
                closest_gap
            )
            
            scores.append(score_result['score'])
            if score_result['in_window']:
                in_window_count += 1
            timing_counts[score_result['timing']] += 1
            quality_counts[score_result['prediction_quality']] += 1
        
        # Calculate aggregate metrics
        if scores:
            avg_score = np.mean(scores)
            median_score = np.median(scores)
            std_score = np.std(scores)
            hit_rate = in_window_count / len(scores)
            
            # Calculate score distribution
            score_distribution = {
                'excellent': sum(1 for s in scores if abs(s) <= 0.2),
                'good': sum(1 for s in scores if 0.2 < abs(s) <= 0.5),
                'fair': sum(1 for s in scores if 0.5 < abs(s) <= 1.0),
                'poor': sum(1 for s in scores if 1.0 < abs(s) <= 2.0),
                'very_poor': sum(1 for s in scores if abs(s) > 2.0)
            }
            
            # Identify systematic biases
            bias = 'none'
            if avg_score < -0.3:
                bias = 'predictions_too_late'
            elif avg_score > 0.3:
                bias = 'predictions_too_early'
            
            return {
                'total_predictions': len(scores),
                'average_score': float(avg_score),
                'median_score': float(median_score),
                'score_std': float(std_score),
                'hit_rate': float(hit_rate),
                'timing_distribution': timing_counts,
                'quality_distribution': quality_counts,
                'score_distribution': score_distribution,
                'systematic_bias': bias,
                'model_reliability': self._assess_model_reliability(hit_rate, avg_score, std_score)
            }
        else:
            return {
                'total_predictions': 0,
                'error': 'No valid prediction-gap pairs found'
            }
    
    def _assess_model_reliability(
        self, hit_rate: float, avg_score: float, std_score: float
    ) -> str:
        """Assess overall model reliability."""
        
        # Combine metrics for assessment
        abs_avg = abs(avg_score)
        
        if hit_rate >= 0.7 and abs_avg <= 0.3 and std_score <= 0.5:
            return 'HIGHLY_RELIABLE'
        elif hit_rate >= 0.5 and abs_avg <= 0.5 and std_score <= 1.0:
            return 'RELIABLE'
        elif hit_rate >= 0.3 and abs_avg <= 1.0:
            return 'MODERATELY_RELIABLE'
        else:
            return 'UNRELIABLE'
    
    def generate_improvement_suggestions(
        self, aggregate_results: Dict[str, any]
    ) -> List[str]:
        """Generate suggestions for improving prediction accuracy."""
        
        suggestions = []
        
        bias = aggregate_results.get('systematic_bias')
        if bias == 'predictions_too_early':
            suggestions.append(
                "Model predicts ATM too early - consider more conservative cash burn estimates"
            )
        elif bias == 'predictions_too_late':
            suggestions.append(
                "Model predicts ATM too late - check for hidden expenses or accelerating burn"
            )
        
        hit_rate = aggregate_results.get('hit_rate', 0)
        if hit_rate < 0.5:
            suggestions.append(
                "Low hit rate - review window size calculation and confidence intervals"
            )
        
        timing = aggregate_results.get('timing_distribution', {})
        if timing.get('early', 0) > timing.get('on_time', 0):
            suggestions.append(
                "Many early gaps - companies may have undisclosed urgent cash needs"
            )
        
        quality = aggregate_results.get('quality_distribution', {})
        if quality.get('POOR', 0) + quality.get('VERY_POOR', 0) > len(aggregate_results) * 0.3:
            suggestions.append(
                "High proportion of poor predictions - consider additional data sources"
            )
        
        return suggestions