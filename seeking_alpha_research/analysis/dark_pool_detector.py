"""
Dark Pool Detection Module

Implements sophisticated dark pool and off-exchange trading detection using:
1. 2 weeks of 1-minute intraday data
2. TRADES tick data from IB API (historical limitation: ALLLAST only available real-time)
3. End-of-day volume spike analysis
4. Unusual volume distribution patterns

CRITICAL: This is for real money trading decisions - no approximations allowed.
NOTE: Uses TRADES tick type for historical analysis due to IB API limitations.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import time
import warnings
warnings.filterwarnings('ignore')

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.logger import get_logger, LogContext
from core.ib_connector import IBConnector
from core.data_service import DataService
from core.market_calendar import MarketCalendar

logger = get_logger(__name__)


class DarkPoolDetector:
    """
    Advanced dark pool detection using minute-level data and tick analysis.
    
    Dark pool indicators:
    1. Unusual volume spikes in last 30 minutes of trading
    2. Volume distribution inconsistent with normal market structure
    3. AllLast tick data showing off-exchange activity
    4. Block trading patterns in closing auction
    """
    
    def __init__(self, data_service=None):
        self.market_calendar = MarketCalendar()
        self.data_service = data_service if data_service else DataService()
        
    def analyze_dark_pool_activity(
        self, 
        symbol: str, 
        analysis_date: str,
        lookback_days: int = 14
    ) -> Dict:
        """
        Comprehensive dark pool analysis for a symbol.
        
        Args:
            symbol: Stock symbol to analyze
            analysis_date: Date of gap event (YYYY-MM-DD)
            lookback_days: Days to look back for patterns (default 14)
            
        Returns:
            Dict with dark pool indicators and confidence scores
        """
        with LogContext(logger, f"Dark pool analysis for {symbol}"):
            print(f"🔍 DARK POOL DETECTION: Analyzing {symbol} for {lookback_days} days before {analysis_date}")
            
            # Check for cached analysis first
            cached_result = self.data_service.get_cached_dark_pool_analysis(symbol, analysis_date, lookback_days)
            if cached_result:
                print(f"   ✅ Using cached dark pool analysis for {symbol}")
                return cached_result
            
            # Validate trading day
            if not self.market_calendar.is_trading_day(analysis_date):
                analysis_date = self.market_calendar.get_previous_trading_day(analysis_date)
                print(f"   📅 Adjusted to previous trading day: {analysis_date}")
            
            # Calculate date range for analysis
            end_date = pd.to_datetime(analysis_date)
            start_date = end_date - timedelta(days=lookback_days * 2)  # Buffer for weekends/holidays
            
            # Get trading days in range
            trading_days = self.market_calendar.get_trading_days(
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )
            
            if len(trading_days) < lookback_days:
                print(f"   ⚠️  Only {len(trading_days)} trading days available (need {lookback_days})")
            
            # Take last lookback_days trading days
            analysis_days = trading_days[-lookback_days:]
            
            # Safe formatting for date objects
            start_date_str = str(analysis_days[0]) if len(analysis_days) > 0 else "N/A"
            end_date_str = str(analysis_days[-1]) if len(analysis_days) > 0 else "N/A"
            print(f"   📊 Analyzing {len(analysis_days)} trading days: {start_date_str} to {end_date_str}")
            
            results = {
                'symbol': symbol,
                'analysis_date': analysis_date,
                'lookback_days': lookback_days,
                'trading_days_requested': len(analysis_days),  # Requested days
                'trading_days_analyzed': 0,  # Will be updated with actual processed days
                'date_range': {
                    'start': str(analysis_days[0]) if len(analysis_days) > 0 else None,
                    'end': str(analysis_days[-1]) if len(analysis_days) > 0 else None
                }
            }
            
            try:
                # STEP 1: Download 2 weeks of minute data
                minute_data = self._download_minute_data(symbol, analysis_days)
                if minute_data.empty:
                    print(f"   ❌ No minute data available for {symbol}")
                    results['error'] = 'No minute data available'
                    return results
                
                results['minute_bars_downloaded'] = len(minute_data)
                print(f"   ✅ Downloaded {len(minute_data)} minute bars")
                
                # STEP 2: Analyze end-of-day volume patterns
                eod_analysis = self._analyze_eod_volume_patterns(minute_data)
                results['eod_analysis'] = eod_analysis
                
                # Update trading_days_analyzed with actual processed days
                results['trading_days_analyzed'] = eod_analysis.get('days_analyzed', 0)
                
                # STEP 3: Detect unusual volume distribution
                volume_analysis = self._analyze_volume_distribution(minute_data)
                results['volume_analysis'] = volume_analysis
                
                # STEP 4: Download and analyze TRADES tick data with intelligent date selection
                tick_analysis = self._analyze_historical_trades_ticks_with_recent_dates(symbol, analysis_days[-3:])
                results['tick_analysis'] = tick_analysis
                
                # STEP 5: Calculate dark pool confidence score
                confidence_score = self._calculate_dark_pool_confidence(
                    eod_analysis, volume_analysis, tick_analysis
                )
                results['dark_pool_confidence'] = confidence_score
                
                # STEP 6: Generate summary
                summary = self._generate_dark_pool_summary(results)
                results['summary'] = summary
                
                print(f"   🎯 Dark pool confidence: {confidence_score:.1%}")
                print(f"   📋 {summary}")
                
                # Save results to database for caching
                try:
                    self.data_service.save_dark_pool_analysis(symbol, analysis_date, results)
                except Exception as e:
                    logger.warning(f"Failed to cache dark pool analysis: {e}")
                
                return results
                
            except Exception as e:
                logger.error(f"Dark pool analysis failed for {symbol}: {e}")
                # If this is a critical data availability error, re-raise it to stop execution
                if "CRITICAL:" in str(e):
                    raise e
                results['error'] = str(e)
                return results
    
    def _download_minute_data(self, symbol: str, trading_days: List[str]) -> pd.DataFrame:
        """Download minute-level data for specified trading days"""
        print(f"   📥 Downloading minute data for {len(trading_days)} days...")
        
        # CRITICAL: IB only provides minute data without specific end dates (Error 162)
        # We'll get the most recent data and filter for the days we need
        
        try:
            # Calculate how many days back we need
            logger.info(f"Starting _download_minute_data with {len(trading_days)} trading days")
            if len(trading_days) == 0:
                return pd.DataFrame()
            
            # Get the oldest day we need
            oldest_day = pd.to_datetime(trading_days[0])
            newest_day = pd.to_datetime(trading_days[-1])
            days_back = (datetime.now().date() - oldest_day.date()).days
            
            # IB has a 30-day limit for minute data
            if days_back > 30:
                print(f"     ❌ Requested data is {days_back} days old - IB only allows 30 days max for minute data")
                print(f"     ❌ Would need market data subscription to access older minute data")
                return pd.DataFrame()
            
            # Get all available minute data for the period
            # CRITICAL FIX: Don't use same date for start and end - this causes filtering issues
            # Instead, use a date range that covers the period we need
            end_date = datetime.now()
            start_date = end_date - timedelta(days=min(days_back + 5, 30))
            
            print(f"     📊 Getting minute data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} from IB...")
            
            try:
                all_data = self.data_service.get_minute_bars(
                    symbol, 
                    start_date.strftime('%Y-%m-%d'), 
                    end_date.strftime('%Y-%m-%d'), 
                    source="ib"
                )
            except Exception as e:
                logger.error(f"Failed to get minute bars: {e}")
                raise
            
            # Debug: Check what we got back
            logger.info(f"Got all_data type: {type(all_data)}, shape: {all_data.shape if hasattr(all_data, 'shape') else 'N/A'}")
            
            # Ensure we have a DataFrame
            if not isinstance(all_data, pd.DataFrame):
                logger.error(f"Expected DataFrame but got {type(all_data)}")
                return pd.DataFrame()
            
            if all_data.empty:
                print(f"     ❌ No minute data received from IB")
                return pd.DataFrame()
            
            # Filter for the days we actually need
            filtered_data = []
            for day in trading_days:
                try:
                    day_date = pd.to_datetime(day).date()
                    # Convert index to date for comparison
                    # Fix: Create proper boolean mask with matching index
                    day_mask = pd.Series([d.date() == day_date for d in pd.to_datetime(all_data.index)], index=all_data.index)
                    day_data = all_data[day_mask]
                    if not day_data.empty:
                        filtered_data.append(day_data)
                        print(f"     {day}: {len(day_data)} bars")
                    else:
                        print(f"     {day}: No data in IB response")
                except Exception as e:
                    logger.error(f"Error filtering data for {day}: {e}")
                    raise
            
            if filtered_data:
                combined_data = pd.concat(filtered_data, ignore_index=False)
                combined_data = combined_data.sort_index()
                
                print(f"     📊 Combined data: {len(combined_data)} bars from {combined_data.index.min()} to {combined_data.index.max()}")
                # Fix: Convert DatetimeIndex to list of dates properly
                unique_dates = [d.date() for d in pd.to_datetime(combined_data.index)]
                print(f"     📅 Date spread: {len(set(unique_dates))} unique days in combined data")
                
                return combined_data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Error downloading minute data: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            if "too old for IB minute data" in str(e):
                print(f"     ❌ {e}")
            return pd.DataFrame()
    
    def _analyze_eod_volume_patterns(self, minute_data: pd.DataFrame) -> Dict:
        """Analyze end-of-day volume patterns for dark pool activity"""
        print(f"   📊 Analyzing end-of-day volume patterns...")
        
        if minute_data.empty:
            return {'error': 'No minute data available'}
        
        # Group by trading day - ensure proper date extraction
        minute_data = minute_data.copy()  # Avoid SettingWithCopyWarning
        # Fix: Convert DatetimeIndex to date objects properly
        minute_data['date'] = [d.date() for d in pd.to_datetime(minute_data.index)]
        minute_data['time'] = [d.time() for d in pd.to_datetime(minute_data.index)]
        
        # Debug: Show unique dates found
        unique_dates = minute_data['date'].unique()
        print(f"     📅 Found {len(unique_dates)} unique trading days: {unique_dates[:5]}")
        
        eod_analysis = {
            'days_analyzed': 0,
            'avg_eod_volume_ratio': 0.0,
            'max_eod_spike': 0.0,
            'unusual_eod_days': 0,
            'closing_auction_spikes': []
        }
        
        daily_eod_ratios = []
        
        for date, day_data in minute_data.groupby('date'):
            # Allow days with fewer bars (some days might have shorter sessions)
            if len(day_data) < 50:  # Reduced threshold for partial trading days
                print(f"     {date}: Skipped - only {len(day_data)} bars")
                continue
                
            eod_analysis['days_analyzed'] += 1
            print(f"     {date}: Analyzing {len(day_data)} bars")
            
            # Define end-of-day period (last 30 minutes: 3:30-4:00 PM)
            # Fix: Create proper boolean mask for time comparison with matching index
            eod_mask = pd.Series([t.time() >= pd.Timestamp('15:30:00').time() for t in day_data.index], index=day_data.index)
            eod_data = day_data[eod_mask]
            
            if eod_data.empty:
                continue
            
            # Calculate volume ratios
            total_volume = day_data['volume'].sum()
            eod_volume = eod_data['volume'].sum()
            
            if total_volume > 0:
                eod_ratio = eod_volume / total_volume
                daily_eod_ratios.append(eod_ratio)
                
                # Look for unusual spikes (>50% of daily volume in last 30 min)
                if eod_ratio > 0.5:
                    eod_analysis['unusual_eod_days'] += 1
                    eod_analysis['closing_auction_spikes'].append({
                        'date': str(date),
                        'eod_volume_ratio': eod_ratio,
                        'total_volume': total_volume,
                        'eod_volume': eod_volume
                    })
        
        if daily_eod_ratios:
            eod_analysis['avg_eod_volume_ratio'] = np.mean(daily_eod_ratios)
            eod_analysis['max_eod_spike'] = max(daily_eod_ratios)
        
        print(f"     📈 Avg EOD volume ratio: {eod_analysis['avg_eod_volume_ratio']:.1%}")
        print(f"     🚨 Unusual EOD days: {eod_analysis['unusual_eod_days']}/{eod_analysis['days_analyzed']}")
        
        return eod_analysis
    
    def _analyze_volume_distribution(self, minute_data: pd.DataFrame) -> Dict:
        """Analyze volume distribution patterns for irregularities"""
        print(f"   📊 Analyzing volume distribution patterns...")
        
        if minute_data.empty:
            return {'error': 'No minute data available'}
        
        # Calculate volume statistics
        volumes = minute_data['volume']
        
        volume_analysis = {
            'total_volume': volumes.sum(),
            'avg_volume_per_minute': volumes.mean(),
            'volume_std': volumes.std(),
            'volume_skewness': volumes.skew(),
            'volume_kurtosis': volumes.kurtosis(),
            'high_volume_minutes': (volumes > volumes.quantile(0.95)).sum(),
            'block_trades_detected': 0,
            'unusual_distribution': False
        }
        
        # Detect block trades (>10x average volume in single minute)
        avg_volume = volumes.mean()
        if avg_volume > 0:
            block_threshold = avg_volume * 10
            block_trades = volumes[volumes > block_threshold]
            volume_analysis['block_trades_detected'] = len(block_trades)
        
        # Check for unusual distribution (high kurtosis indicates spikes)
        if volume_analysis['volume_kurtosis'] > 5:
            volume_analysis['unusual_distribution'] = True
        
        print(f"     📊 Block trades detected: {volume_analysis['block_trades_detected']}")
        print(f"     📈 Volume skewness: {volume_analysis['volume_skewness']:.2f}")
        
        return volume_analysis
    
    def _analyze_historical_trades_ticks(self, symbol: str, recent_days: List[str]) -> Dict:
        """Download and analyze historical TRADES tick data for trading patterns"""
        print(f"   ⚡ Analyzing TRADES tick data for {len(recent_days)} recent days...")
        
        tick_analysis = {
            'days_analyzed': 0,
            'total_ticks': 0,
            'trade_size_patterns': [],
            'unusual_timing_patterns': 0,
            'error': None
        }
        
        try:
            # Use existing IB connection from data_service to avoid conflicts
            if not self.data_service.ib_connector:
                print(f"     ⚠️  No IB connection available for tick analysis")
                tick_analysis['error'] = 'No IB connection available'
                return tick_analysis
            
            for i, day in enumerate(recent_days[-2:]):  # Only last 2 days to avoid rate limits
                try:
                    # CRITICAL: Add delay between tick requests
                    if i > 0:
                        delay = 3  # 3 second delay for tick data (heavier requests)
                        print(f"     ⏳ Waiting {delay}s before next tick request...")
                        time.sleep(delay)
                    
                    # Validate date is not in the future
                    if hasattr(day, 'date'):
                        day_date = day.date()
                    elif hasattr(day, 'strftime'):
                        day_date = day
                    else:
                        day_date = pd.to_datetime(str(day)).date()
                    
                    if day_date > datetime.now().date():
                        print(f"     ⚠️  Skipping future date {day_date} - no tick data available")
                        continue
                    
                    # Request AllLast tick data (includes off-exchange trades)
                    # Convert day to proper string format
                    if hasattr(day, 'strftime'):
                        day_str = day.strftime('%Y%m%d')
                    else:
                        day_str = str(day).replace('-', '')
                    start_time = f"{day_str} 09:30:00"
                    
                    # Use TRADES instead of ALLLAST for historical data (ALLLAST not available historically)
                    print(f"     ⚡ Requesting TRADES ticks for {day}: {start_time}")
                    # Use IB connector directly with TRADES tick type (ALLLAST not available for historical data)
                    ticks = self.data_service.ib_connector.get_historical_ticks(symbol, start_time, tick_type="TRADES")
                    
                    if not ticks.empty:
                        tick_analysis['days_analyzed'] += 1
                        tick_analysis['total_ticks'] += len(ticks)
                        
                        # Analyze trade size patterns for unusual activity
                        trade_patterns = self._analyze_trade_size_patterns(ticks)
                        tick_analysis['trade_size_patterns'].append({
                            'date': day,
                            'ticks': len(ticks),
                            'patterns': trade_patterns
                        })
                        
                        # Enhanced logging with pattern details
                        unusual_flag = "🚨 UNUSUAL" if trade_patterns.get('unusual_activity', False) else "✅ normal"
                        print(f"     {day}: {len(ticks)} TRADES ticks analyzed - {unusual_flag}")
                    else:
                        # HARD FAIL - insufficient data for reliable analysis
                        raise ValueError(f"CRITICAL: No tick data available for {symbol} on {day} - cannot proceed with incomplete dark pool analysis")
                    
                except Exception as e:
                    # HARD FAIL - any tick data retrieval failure is critical
                    raise ValueError(f"CRITICAL: Failed to get tick data for {symbol} on {day}: {e}")
            
            # Calculate overall trade pattern indicators with enhanced analysis
            if tick_analysis['total_ticks'] > 0:
                # Count days with unusual activity patterns
                tick_analysis['unusual_timing_patterns'] = len([p for p in tick_analysis['trade_size_patterns'] 
                                                               if p['patterns'].get('unusual_activity', False)])
                
                # Calculate aggregate statistics across all days
                all_patterns = [p['patterns'] for p in tick_analysis['trade_size_patterns']]
                if all_patterns:
                    avg_block_ratio = sum(p.get('block_trade_ratio', 0) for p in all_patterns) / len(all_patterns)
                    avg_institutional_ratio = sum(p.get('institutional_ratio', 0) for p in all_patterns) / len(all_patterns)
                    
                    tick_analysis['aggregate_patterns'] = {
                        'avg_block_trade_ratio': round(avg_block_ratio, 3),
                        'avg_institutional_ratio': round(avg_institutional_ratio, 3),
                        'days_with_unusual_activity': tick_analysis['unusual_timing_patterns'],
                        'total_days_analyzed': len(all_patterns)
                    }
            
        except Exception as e:
            logger.error(f"Historical TRADES tick analysis failed: {e}")
            # If this is a critical data availability error, re-raise it
            if "CRITICAL:" in str(e):
                raise e
            tick_analysis['error'] = str(e)
        
        finally:
            # Don't disconnect as we're using shared connection
            pass
        
        return tick_analysis
    
    def _analyze_historical_trades_ticks_with_recent_dates(self, symbol: str, original_days: List) -> Dict:
        """
        Get real tick data by using recent dates where IB actually has tick data available.
        
        Strategy:
        1. First try recent dates (last 30 days) where IB has full tick data
        2. If that fails, fallback to parallel minute-level analysis system
        3. Future-proof: When full tick data available, will use 100% tick analysis
        """
        
        from datetime import datetime, timedelta
        import pandas as pd
        
        try:
            # Get recent trading days where IB definitely has tick data
            today = datetime.now().date()
            # Don't include today if market is still open
            if datetime.now().hour < 16:  # Before 4 PM ET market close
                recent_end = today - timedelta(days=1)
            else:
                recent_end = today
                
            recent_start = recent_end - timedelta(days=30)  # Last 30 days
            
            # Get recent trading days in this range
            recent_trading_days = self.market_calendar.get_trading_days(
                recent_start.strftime('%Y-%m-%d'),
                recent_end.strftime('%Y-%m-%d')
            )
            
            # Filter out any future dates (shouldn't happen but be safe)
            recent_trading_days = [d for d in recent_trading_days if pd.to_datetime(d).date() <= recent_end]
            
            if len(recent_trading_days) < 2:
                raise ValueError(f"Not enough recent trading days available for {symbol}")
            
            # Use last 2-3 recent trading days for tick analysis
            recent_days_for_ticks = recent_trading_days[-3:]
            
            print(f"     ⚡ Using recent dates with available tick data: {recent_days_for_ticks[-2:]} instead of old dates")
            
            # Call the standard tick analysis with recent dates
            tick_analysis = self._analyze_historical_trades_ticks(symbol, recent_days_for_ticks)
            
            # Verify we got real tick data
            if tick_analysis['total_ticks'] > 0:
                print(f"     ✅ Got {tick_analysis['total_ticks']} real ticks - using tick analysis")
                return tick_analysis
            else:
                print(f"     ⚠️  No ticks received even for recent dates - falling back to minute analysis")
                
        except Exception as e:
            print(f"     ⚠️  Tick analysis failed: {e} - falling back to minute analysis")
        
        # Fallback to parallel minute-level analysis system
        print(f"     📊 Using parallel minute-level analysis system")
        return self._enhanced_minute_level_analysis(symbol, original_days)
    
    def _enhanced_minute_level_analysis(self, symbol: str, recent_days: List) -> Dict:
        """
        Enhanced minute-level analysis when tick data unavailable.
        Uses sophisticated volume and price action patterns to detect institutional activity.
        """
        
        tick_analysis = {
            'days_analyzed': 0,
            'total_ticks': 0,
            'trade_size_patterns': [],
            'unusual_timing_patterns': 0,
            'error': None,
            'fallback_method': 'enhanced_minute_analysis'
        }
        
        try:
            print(f"     📊 Enhanced minute analysis for {symbol} (fallback method)...")
            
            for day in recent_days[-3:]:  # Last 3 days
                try:
                    # Get minute-level data for this specific day
                    day_str = day.strftime('%Y-%m-%d') if hasattr(day, 'strftime') else str(day)
                    # Use correct DataService API: get_minute_bars(symbol, start, end)
                    minute_data = self.data_service.get_minute_bars(symbol, day_str, day_str)
                    
                    if not minute_data.empty:
                        # Analyze minute-level patterns for institutional activity indicators
                        patterns = self._analyze_minute_level_institutional_patterns(minute_data, day)
                        
                        if patterns['unusual_activity']:
                            tick_analysis['trade_size_patterns'].append({
                                'date': day,
                                'ticks': patterns['minute_bars_analyzed'],
                                'patterns': patterns
                            })
                            tick_analysis['days_analyzed'] += 1
                            tick_analysis['unusual_timing_patterns'] += 1
                        
                        print(f"     {day}: {patterns['minute_bars_analyzed']} minute bars analyzed - {'🚨 UNUSUAL' if patterns['unusual_activity'] else '✅ normal'}")
                    
                except Exception as e:
                    print(f"     {day}: Failed minute analysis - {e}")
            
            # Calculate aggregate patterns for minute-level analysis
            if tick_analysis['days_analyzed'] > 0:
                all_patterns = [p['patterns'] for p in tick_analysis['trade_size_patterns']]
                avg_volume_concentration = sum(p.get('volume_concentration', 0) for p in all_patterns) / len(all_patterns)
                avg_price_impact = sum(p.get('price_impact_score', 0) for p in all_patterns) / len(all_patterns)
                
                tick_analysis['aggregate_patterns'] = {
                    'avg_volume_concentration': round(avg_volume_concentration, 3),
                    'avg_price_impact': round(avg_price_impact, 3),
                    'days_with_unusual_activity': tick_analysis['unusual_timing_patterns'],
                    'total_days_analyzed': len(all_patterns),
                    'analysis_method': 'minute_level_fallback'
                }
                
                print(f"     ✅ Enhanced minute analysis complete: {tick_analysis['days_analyzed']} days with unusual patterns")
            else:
                print(f"     ❌ No unusual patterns detected in minute-level analysis")
            
        except Exception as e:
            logger.error(f"Enhanced minute analysis failed for {symbol}: {e}")
            tick_analysis['error'] = f"Fallback analysis failed: {str(e)}"
        
        return tick_analysis
    
    def _analyze_minute_level_institutional_patterns(self, minute_data: pd.DataFrame, day) -> Dict:
        """
        Analyze minute-level data for institutional activity patterns.
        Looks for volume concentration, price impact, and timing patterns.
        """
        
        patterns = {
            'unusual_activity': False,
            'minute_bars_analyzed': len(minute_data),
            'volume_concentration': 0.0,
            'price_impact_score': 0.0,
            'large_volume_minutes': 0,
            'end_of_day_activity': False
        }
        
        if minute_data.empty or len(minute_data) < 10:
            return patterns
        
        try:
            # Calculate volume statistics
            volumes = minute_data['volume']
            avg_volume = volumes.mean()
            volume_std = volumes.std()
            
            # Detect large volume minutes (institutional activity indicator)
            large_volume_threshold = avg_volume + (2 * volume_std)
            large_volume_minutes = volumes[volumes > large_volume_threshold]
            patterns['large_volume_minutes'] = len(large_volume_minutes)
            
            # Volume concentration analysis
            total_volume = volumes.sum()
            top_10_pct_volume = volumes.nlargest(int(len(volumes) * 0.1)).sum()
            patterns['volume_concentration'] = top_10_pct_volume / total_volume if total_volume > 0 else 0
            
            # Price impact analysis (large volume with minimal price change = dark pool)
            if 'close' in minute_data.columns and 'open' in minute_data.columns:
                price_changes = abs(minute_data['close'] - minute_data['open']) / minute_data['open']
                high_volume_low_impact = 0
                
                for idx, vol in enumerate(volumes):
                    if vol > large_volume_threshold and idx < len(price_changes):
                        if price_changes.iloc[idx] < 0.005:  # Less than 0.5% price impact
                            high_volume_low_impact += 1
                
                patterns['price_impact_score'] = high_volume_low_impact / len(large_volume_minutes) if len(large_volume_minutes) > 0 else 0
            
            # End-of-day activity check (last 30 minutes)
            if len(minute_data) >= 30:
                eod_volume = volumes.tail(30).sum()
                eod_ratio = eod_volume / total_volume if total_volume > 0 else 0
                patterns['end_of_day_activity'] = eod_ratio > 0.15  # >15% in last 30 minutes
            
            # Determine unusual activity based on multiple factors
            unusual_indicators = 0
            if patterns['volume_concentration'] > 0.4:  # Top 10% of minutes have >40% of volume
                unusual_indicators += 1
            if patterns['large_volume_minutes'] > len(minute_data) * 0.05:  # >5% of minutes have large volume
                unusual_indicators += 1
            if patterns['price_impact_score'] > 0.3:  # >30% of large volume trades have low price impact
                unusual_indicators += 1
            if patterns['end_of_day_activity']:
                unusual_indicators += 1
            
            patterns['unusual_activity'] = unusual_indicators >= 2  # At least 2 indicators
            
        except Exception as e:
            logger.warning(f"Minute-level pattern analysis failed: {e}")
        
        return patterns
    
    def _analyze_trade_size_patterns(self, ticks: pd.DataFrame) -> Dict:
        """Analyze TRADES tick data for unusual size and timing patterns"""
        if ticks.empty:
            return {'unusual_activity': False, 'avg_trade_size': 0, 'large_trades_count': 0, 'block_trade_ratio': 0.0}
        
        # Analyze trade sizes for institutional activity patterns
        if 'size' in ticks.columns:
            # Convert Decimal types to float for calculations
            sizes = pd.to_numeric(ticks['size'], errors='coerce')
            sizes = sizes.dropna()  # Remove any NaN values
            
            if len(sizes) == 0:
                return {'unusual_activity': False, 'avg_trade_size': 0, 'large_trades_count': 0, 'block_trade_ratio': 0.0}
            
            avg_size = sizes.mean()
            median_size = sizes.median()
            
            # Enhanced detection: Multiple thresholds for institutional activity
            large_trades_95th = sizes[sizes > sizes.quantile(0.95)]  # Top 5% of trades
            block_trades_10x = sizes[sizes > avg_size * 10]  # Block trades (10x average)
            institutional_trades_1000 = sizes[sizes >= 1000]  # Standard block size threshold
            
            # Calculate ratios for better pattern detection
            large_trade_ratio = len(large_trades_95th) / len(sizes)
            block_trade_ratio = len(block_trades_10x) / len(sizes)
            institutional_ratio = len(institutional_trades_1000) / len(sizes)
            
            # Enhanced unusual activity detection with multiple criteria
            unusual_activity = (
                large_trade_ratio > 0.15 or  # >15% of trades are in top 5%
                block_trade_ratio > 0.02 or  # >2% of trades are 10x average
                institutional_ratio > 0.1 or  # >10% are institutional size (≥1000 shares)
                (avg_size > median_size * 3)  # High skew indicating large trade concentration
            )
            
            return {
                'unusual_activity': unusual_activity,
                'avg_trade_size': round(float(avg_size), 2),
                'median_trade_size': round(float(median_size), 2),
                'large_trades_count': len(large_trades_95th),
                'block_trades_count': len(block_trades_10x),
                'institutional_trades_count': len(institutional_trades_1000),
                'total_trades': len(sizes),
                'large_trade_ratio': round(large_trade_ratio, 3),
                'block_trade_ratio': round(block_trade_ratio, 3),
                'institutional_ratio': round(institutional_ratio, 3),
                'size_skew': round(float(avg_size / median_size), 2) if median_size > 0 else 0.0
            }
        
        return {'unusual_activity': False, 'avg_trade_size': 0, 'large_trades_count': 0, 'block_trade_ratio': 0.0}
    
    def _calculate_dark_pool_confidence(
        self, 
        eod_analysis: Dict, 
        volume_analysis: Dict, 
        tick_analysis: Dict
    ) -> float:
        """Calculate overall dark pool confidence score (0-1)
        
        Enhanced detection with more sensitive thresholds for small-cap stocks
        where institutional activity is more significant.
        """
        
        confidence_factors = []
        
        # Factor 1: End-of-day volume concentration (more sensitive)
        if 'avg_eod_volume_ratio' in eod_analysis:
            eod_ratio = eod_analysis['avg_eod_volume_ratio']
            if eod_ratio > 0.15:  # >15% of volume in last 30 minutes (lowered from 30%)
                confidence_factors.append(0.4)
            elif eod_ratio > 0.10:  # >10% (lowered from 20%)
                confidence_factors.append(0.3)
            elif eod_ratio > 0.05:  # >5% still meaningful for small caps
                confidence_factors.append(0.2)
        
        # Factor 2: Unusual EOD days (more sensitive)
        if 'unusual_eod_days' in eod_analysis and 'days_analyzed' in eod_analysis:
            if eod_analysis['days_analyzed'] > 0:
                unusual_ratio = eod_analysis['unusual_eod_days'] / eod_analysis['days_analyzed']
                if unusual_ratio > 0.2:  # >20% of days have unusual EOD activity (lowered from 30%)
                    confidence_factors.append(0.3)
                elif unusual_ratio > 0.1:  # >10% is still significant
                    confidence_factors.append(0.2)
        
        # Factor 3: Block trades (more sensitive for small caps)
        if 'block_trades_detected' in volume_analysis:
            block_count = volume_analysis['block_trades_detected']
            if block_count > 100:  # Major institutional activity
                confidence_factors.append(0.4)
            elif block_count > 50:  # Significant activity
                confidence_factors.append(0.3)
            elif block_count > 20:  # Moderate activity (lowered from 5)
                confidence_factors.append(0.2)
            elif block_count > 5:  # Some activity
                confidence_factors.append(0.1)
        
        # Factor 4: Volume distribution irregularity (enhanced scoring)
        if volume_analysis.get('unusual_distribution', False):
            # Check kurtosis for extreme spikes
            kurtosis = volume_analysis.get('volume_kurtosis', 0)
            if kurtosis > 100:  # Extreme spikes
                confidence_factors.append(0.3)
            elif kurtosis > 50:  # High spikes
                confidence_factors.append(0.2)
            else:
                confidence_factors.append(0.1)
        
        # Factor 5: Enhanced unusual trade patterns from tick/minute data
        unusual_patterns = tick_analysis.get('unusual_timing_patterns', 0)
        aggregate_patterns = tick_analysis.get('aggregate_patterns', {})
        
        if unusual_patterns > 0:
            # Base points for any unusual activity
            confidence_factors.append(0.2)  # Increased from 0.1
            
            # Additional points for strong institutional activity patterns
            avg_block_ratio = aggregate_patterns.get('avg_block_trade_ratio', 0)
            avg_institutional_ratio = aggregate_patterns.get('avg_institutional_ratio', 0)
            avg_volume_concentration = aggregate_patterns.get('avg_volume_concentration', 0)
            
            if avg_block_ratio > 0.02:  # >2% block trades (lowered from 3%)
                confidence_factors.append(0.1)
            if avg_institutional_ratio > 0.10:  # >10% institutional size (lowered from 15%)
                confidence_factors.append(0.1)
            if avg_volume_concentration > 0.3:  # >30% volume in top 10% of minutes
                confidence_factors.append(0.1)
        
        # Factor 6: Single tick analysis success (when available)
        if tick_analysis.get('total_ticks', 0) > 1000:
            # We got real tick data - this itself is a positive signal
            confidence_factors.append(0.1)
        
        # Calculate final confidence score
        if confidence_factors:
            # Use weighted average instead of sum to keep score between 0-1
            total_confidence = sum(confidence_factors) / max(len(confidence_factors) * 0.5, 1)
            # Cap at 1.0 and ensure minimum detection
            total_confidence = min(total_confidence, 1.0)
            # If we have ANY factors, ensure minimum 0.3 confidence
            total_confidence = max(total_confidence, 0.3)
        else:
            total_confidence = 0.0
            
        return round(total_confidence, 2)  # Round to 2 decimal places
    
    def _generate_dark_pool_summary(self, results: Dict) -> str:
        """Generate human-readable summary of dark pool analysis"""
        confidence = results.get('dark_pool_confidence', 0)
        
        if confidence > 0.5:
            return f"HIGH dark pool activity detected ({confidence:.1%} confidence)"
        elif confidence > 0.2:
            return f"MODERATE dark pool activity detected ({confidence:.1%} confidence)"
        else:
            return f"LOW dark pool activity ({confidence:.1%} confidence)"