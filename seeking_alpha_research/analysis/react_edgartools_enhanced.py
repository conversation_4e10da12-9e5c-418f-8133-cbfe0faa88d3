#!/usr/bin/env python3
"""
Enhanced EdgarTools Analyzer - Sends Complete Financial Tables to LLM
Instead of picking specific fields, sends entire income statement and balance sheet

NO FAKES, NO MOCKS - Real XBRL data with intelligent LLM analysis
"""

import os
import json
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
import re
from concurrent.futures import ThreadPoolExecutor

# EdgarTools imports
try:
    from edgar import Company, set_identity
    set_identity(os.getenv("EDGAR_IDENTITY", "<EMAIL>"))
    EDGARTOOLS_AVAILABLE = True
except ImportError:
    EDGARTOOLS_AVAILABLE = False

from core.data_service import DataService as ComprehensiveDataService
from utils.backtest_aware_llm_cache import BacktestAwareLLMCache
from core.logger import get_logger
import litellm

logger = get_logger(__name__)

# Configure LiteLLM
os.environ["GEMINI_API_KEY"] = os.getenv("GEMINI_API_KEY", "")
litellm.set_verbose = False


class EnhancedEdgarToolsAnalyzer:
    """
    Enhanced EdgarTools analyzer that sends complete financial statements to LLM.
    This avoids missing hidden expenses and allows LLM to find cash burn intelligently.
    """
    
    def __init__(self, max_workers: int = 2):
        if not EDGARTOOLS_AVAILABLE:
            raise ImportError("EdgarTools required. Install with: pip install edgartools")
            
        self.data_service = ComprehensiveDataService()
        self.llm_cache = BacktestAwareLLMCache()
        self.max_workers = max_workers
        
        if not os.getenv("GEMINI_API_KEY"):
            raise ValueError("GEMINI_API_KEY required for LLM analysis")
            
        logger.info("Enhanced EdgarTools Analyzer initialized")
    
    def analyze_atm_risk(
        self, symbol: str, analysis_date: str = None, lookback_days: int = 365,
        as_of_date: str = None, fundamentals_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Analyze ATM risk using complete financial statement extraction."""
        
        if analysis_date is None:
            analysis_date = datetime.now().strftime("%Y-%m-%d")
        
        if as_of_date is None:
            as_of_date = analysis_date
            
        start_time = datetime.now()
        
        logger.info(f"Starting Enhanced EdgarTools analysis for {symbol}")
        
        try:
            # Get company
            company = Company(symbol)
            logger.info(f"Found company: {company.name}")
            
            # Get recent filings
            filings = company.get_filings(form=["10-Q", "10-K"]).head(3)
            
            if not filings:
                raise ValueError(f"No financial filings found for {symbol}")
            
            # Extract complete financial statements
            all_statements = []
            
            for filing in filings:
                try:
                    logger.info(f"Processing {filing.form} from {filing.filing_date}")
                    
                    # Get XBRL
                    xbrl = filing.xbrl()
                    
                    if not xbrl:
                        logger.warning(f"No XBRL data for {filing.form}")
                        continue
                    
                    # Extract complete financial statements
                    statements = self._extract_complete_statements(xbrl, filing)
                    
                    if statements:
                        all_statements.append({
                            'filing_date': filing.filing_date,
                            'form_type': filing.form,
                            'statements': statements
                        })
                        
                except Exception as e:
                    logger.error(f"Error processing filing: {e}")
                    continue
            
            if not all_statements:
                raise ValueError("No financial data extracted")
            
            # Send to LLM for intelligent analysis
            result = self._analyze_with_llm(
                symbol=symbol,
                company_name=company.name,
                statements=all_statements,
                fundamentals_data=fundamentals_data or {}
            )
            
            # Add metadata
            result['filings_analyzed'] = len(all_statements)
            result['extraction_method'] = 'EdgarTools_Complete_Statements'
            result['processing_time'] = (datetime.now() - start_time).total_seconds()
            
            logger.info(
                f"Analysis complete: Cash=${result.get('latest_cash_position', 0):,.0f}, "
                f"Burn=${result.get('avg_monthly_burn', 0):,.0f}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Enhanced EdgarTools analysis failed: {e}")
            raise
    
    def _extract_complete_statements(self, xbrl, filing) -> Dict[str, Any]:
        """Extract complete financial statements as tables."""
        
        statements = {}
        
        try:
            # Get all facts as DataFrame
            all_facts = xbrl.facts.to_dataframe()
            
            if all_facts.empty:
                return statements
            
            # Group by financial statement type
            # Balance Sheet (instant facts)
            balance_sheet_facts = all_facts[
                (all_facts['period_type'] == 'instant') & 
                (all_facts['numeric_value'].notna())
            ]
            
            if not balance_sheet_facts.empty:
                # Get most recent instant date
                latest_instant = balance_sheet_facts['period_instant'].max()
                bs_latest = balance_sheet_facts[
                    balance_sheet_facts['period_instant'] == latest_instant
                ]
                
                # Create balance sheet table
                bs_table = []
                for _, fact in bs_latest.iterrows():
                    bs_table.append({
                        'concept': fact['concept'],
                        'value': float(fact['numeric_value']),
                        'label': fact.get('label', fact['concept'])
                    })
                
                statements['balance_sheet'] = {
                    'date': str(latest_instant),
                    'items': bs_table
                }
                
                logger.info(f"Extracted {len(bs_table)} balance sheet items")
            
            # Income Statement (duration facts)
            income_facts = all_facts[
                (all_facts['period_type'] == 'duration') & 
                (all_facts['numeric_value'].notna())
            ]
            
            if not income_facts.empty:
                # Get most recent period
                latest_end = income_facts['period_end'].max()
                is_latest = income_facts[
                    income_facts['period_end'] == latest_end
                ]
                
                # Create income statement table
                is_table = []
                for _, fact in is_latest.iterrows():
                    is_table.append({
                        'concept': fact['concept'],
                        'value': float(fact['numeric_value']),
                        'label': fact.get('label', fact['concept'])
                    })
                
                statements['income_statement'] = {
                    'period_end': str(latest_end),
                    'items': is_table
                }
                
                logger.info(f"Extracted {len(is_table)} income statement items")
            
            # Cash Flow Statement
            cash_flow_concepts = [
                'NetCashProvidedByUsedInOperatingActivities',
                'NetCashProvidedByUsedInInvestingActivities',
                'NetCashProvidedByUsedInFinancingActivities'
            ]
            
            cf_table = []
            for concept in cash_flow_concepts:
                cf_facts = all_facts[all_facts['concept'].str.contains(concept, na=False)]
                if not cf_facts.empty:
                    latest = cf_facts.iloc[0]
                    cf_table.append({
                        'concept': concept,
                        'value': float(latest['numeric_value']),
                        'label': latest.get('label', concept)
                    })
            
            if cf_table:
                statements['cash_flow'] = {
                    'items': cf_table
                }
                
            return statements
            
        except Exception as e:
            logger.error(f"Error extracting statements: {e}")
            return {}
    
    def _analyze_with_llm(
        self, symbol: str, company_name: str, 
        statements: List[Dict], fundamentals_data: Dict
    ) -> Dict[str, Any]:
        """Send complete financial statements to LLM for analysis."""
        
        # Prepare financial data for LLM
        statements_text = self._format_statements_for_llm(statements)
        
        # Get fundamentals
        shares_outstanding = fundamentals_data.get('shares_outstanding_millions', 100)
        float_millions = fundamentals_data.get('float_millions', 80)
        
        prompt = f"""
You are analyzing {symbol} ({company_name}) for ATM dilution risk.

COMPLETE FINANCIAL STATEMENTS:
{statements_text}

FUNDAMENTALS:
- Float: {float_millions}M shares ({float_millions/shares_outstanding*100:.0f}% of {shares_outstanding}M outstanding)
- Low float (<20M) = explosive gap potential when volume surges

TASK: Extract cash burn and assess ATM dilution risk from the COMPLETE financial data above.

IMPORTANT: Look for ALL expenses, not just "Operating Expenses". Companies often hide burn in:
- Cost of Revenue/Sales
- Research & Development
- Sales & Marketing
- General & Administrative
- Other Operating Expenses
- Interest Expense

Calculate TOTAL monthly burn by adding ALL expense categories.

Return JSON:
{{
  "cash_position": [latest cash from balance sheet],
  "quarterly_expenses": [sum of ALL expense items from income statement],
  "quarterly_revenue": [total revenue],
  "monthly_burn": [calculated monthly burn rate],
  "cash_runway_months": [cash / monthly_burn],
  "hidden_expenses_found": [list of expense items that might be overlooked],
  "atm_probability": [0.0-1.0 based on burn rate and runway],
  "risk_level": ["LOW", "MEDIUM", "HIGH"],
  "key_insights": ["insight 1", "insight 2", ...]
}}
"""
        
        try:
            response = litellm.completion(
                model="gemini/gemini-1.5-flash",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
                temperature=0.1,
                timeout=30
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Parse JSON
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                analysis = json.loads(json_match.group())
                
                return {
                    "symbol": symbol,
                    "analysis_date": datetime.now().strftime("%Y-%m-%d"),
                    "latest_cash_position": analysis.get('cash_position', 0),
                    "avg_monthly_burn": analysis.get('monthly_burn', 0),
                    "latest_revenue": analysis.get('quarterly_revenue', 0),
                    "estimated_runway_months": analysis.get('cash_runway_months', 0),
                    "atm_probability": analysis.get('atm_probability', 0),
                    "risk_category": analysis.get('risk_level', 'UNKNOWN'),
                    "hidden_expenses": analysis.get('hidden_expenses_found', []),
                    "key_insights": analysis.get('key_insights', []),
                    "has_active_atm": analysis.get('atm_probability', 0) > 0.5
                }
                
        except Exception as e:
            logger.error(f"LLM analysis failed: {e}")
            raise
    
    def _format_statements_for_llm(self, statements: List[Dict]) -> str:
        """Format financial statements for LLM consumption."""
        
        output = []
        
        for filing in statements[:2]:  # Use latest 2 filings
            output.append(f"\n{filing['form_type']} filed {filing['filing_date']}:")
            
            # Balance Sheet
            if 'balance_sheet' in filing['statements']:
                output.append("\nBALANCE SHEET:")
                for item in filing['statements']['balance_sheet']['items'][:30]:  # Top 30 items
                    output.append(f"  {item['label']}: ${item['value']:,.0f}")
            
            # Income Statement
            if 'income_statement' in filing['statements']:
                output.append("\nINCOME STATEMENT:")
                for item in filing['statements']['income_statement']['items'][:30]:
                    output.append(f"  {item['label']}: ${item['value']:,.0f}")
            
            # Cash Flow
            if 'cash_flow' in filing['statements']:
                output.append("\nCASH FLOW:")
                for item in filing['statements']['cash_flow']['items']:
                    output.append(f"  {item['label']}: ${item['value']:,.0f}")
        
        return "\n".join(output)
    
    def close(self):
        """Clean up resources."""
        self.data_service.close()


# Alias for compatibility
EnhancedReActEdgarToolsAnalyzer = EnhancedEdgarToolsAnalyzer