"""
Dilution confirmation module.
Verifies if companies actually diluted shares after gap-up events.
CRITICAL for strategy validation - proves the thesis.
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import re

from core.data_service import DataService
from core.logger import get_logger, LogContext
from core.database import sec_filings, engine
from sqlalchemy import select, create_engine
from analysis.llm_agent import get_llm_agent

logger = get_logger(__name__)


class DilutionConfirmation:
    """
    Confirms whether dilution occurred after gap-up events.

    This is crucial for validating that our strategy correctly identifies
    companies that use volume spikes to raise capital through ATM offerings.
    """

    def __init__(self, data_service: DataService = None):
        self.data_service = data_service or DataService()
        # Use centralized database configuration
        from core.database_config import get_database_url

        self.engine = create_engine(get_database_url())

        # Initialize LLM agent for filing analysis
        try:
            self.llm_agent = get_llm_agent()
            logger.info("LLM agent initialized for dilution confirmation analysis")
        except Exception as e:
            logger.warning(f"Could not initialize LLM agent: {e}")
            self.llm_agent = None

    def _get_filing_confidence(self, form_type: str) -> float:
        """Get confidence level for a filing type."""
        confidence_map = {"424B5": 0.95, "EFFECT": 0.8, "FWP": 0.7, "8-K": 0.6}
        return confidence_map.get(form_type, 0.5)

    def check_for_dilution(
        self, symbol: str, gap_date: str, days_after: int = 30
    ) -> Dict:
        """
        Check if dilution occurred after a gap-up event.

        Args:
            symbol: Stock symbol
            gap_date: Date of the gap-up event
            days_after: Days to look forward for dilution filings

        Returns:
            Dict with dilution details or None if no dilution found
        """
        with LogContext(logger, f"dilution check for {symbol} after {gap_date}"):
            gap_dt = pd.to_datetime(gap_date)
            end_date = gap_dt + timedelta(days=days_after)

            # Look for specific SEC filings that indicate dilution
            dilution_forms = ["424B5", "S-3", "S-3/A", "EFFECT", "8-K", "FWP"]

            # Get filings after the gap
            filings = self._get_filings_in_range(
                symbol, gap_date, end_date.strftime("%Y-%m-%d")
            )

            if filings.empty:
                logger.info(f"No filings found for {symbol} after {gap_date}")
                return {"dilution_confirmed": False, "reason": "No filings found"}

            # Check each filing for dilution indicators
            dilution_filings = []

            for _, filing in filings.iterrows():
                form_type = filing["form_type"]

                # 424B5 is the most direct evidence of ATM offering
                if "424B5" in form_type:
                    dilution_filings.append(
                        {
                            "form_type": form_type,
                            "filed_at": filing["filed_at"],
                            "confidence": 0.95,
                            "reason": "Prospectus supplement for securities offering",
                        }
                    )

                # S-3 effectiveness indicates ability to sell
                elif form_type == "EFFECT":
                    dilution_filings.append(
                        {
                            "form_type": form_type,
                            "filed_at": filing["filed_at"],
                            "confidence": 0.8,
                            "reason": "Registration statement became effective",
                        }
                    )

                # 8-K might contain offering details
                elif form_type == "8-K":
                    # Would need to analyze content for Item 3.02 or 8.01
                    dilution_filings.append(
                        {
                            "form_type": form_type,
                            "filed_at": filing["filed_at"],
                            "confidence": 0.6,
                            "reason": "Current report - may contain offering details",
                        }
                    )

                # Free Writing Prospectus
                elif form_type == "FWP":
                    dilution_filings.append(
                        {
                            "form_type": form_type,
                            "filed_at": filing["filed_at"],
                            "confidence": 0.7,
                            "reason": "Free writing prospectus related to offering",
                        }
                    )

            if dilution_filings:
                # Sort by confidence
                dilution_filings.sort(key=lambda x: x["confidence"], reverse=True)

                # Calculate days to dilution
                best_match = dilution_filings[0]
                days_to_dilution = (
                    pd.to_datetime(best_match["filed_at"]) - gap_dt
                ).days

                return {
                    "dilution_confirmed": True,
                    "confidence": best_match["confidence"],
                    "form_type": best_match["form_type"],
                    "filed_at": best_match["filed_at"],
                    "days_after_gap": days_to_dilution,
                    "reason": best_match["reason"],
                    "all_dilution_filings": dilution_filings,
                }

            # Check for share count changes as indirect evidence
            share_change = self._check_share_count_change(symbol, gap_date, days_after)

            if share_change and share_change["shares_increased"]:
                return {
                    "dilution_confirmed": True,
                    "confidence": 0.7,
                    "shares_increase_pct": share_change["increase_pct"],
                    "reason": "Share count increased without clear filing",
                    "indirect_evidence": True,
                }

            return {
                "dilution_confirmed": False,
                "reason": "No dilution evidence found in filings",
                "filings_checked": len(filings),
            }

    def _get_filings_in_range(
        self, symbol: str, start_date: str, end_date: str
    ) -> pd.DataFrame:
        """Get SEC filings within date range."""
        try:
            filings = self.data_service.get_sec_filings(symbol, start_date, end_date)
            return filings
        except Exception as e:
            logger.error(f"Error fetching filings for {symbol}: {e}")
            raise ValueError(
                "CRITICAL: No data available - cannot proceed without real data. Money is on the line!"
            )

    def _check_share_count_change(
        self, symbol: str, start_date: str, days_after: int
    ) -> Optional[Dict]:
        """
        Check if outstanding shares increased.
        This would require 10-Q/10-K analysis or market data APIs.
        """
        # This is a placeholder - would need to implement actual share count tracking
        # Could use:
        # 1. Parse 10-Q/10-K for shares outstanding
        # 2. Use financial data APIs that track share count
        # 3. Compare market cap changes vs price changes

        logger.info(f"Share count change check not yet implemented for {symbol}")
        return None

    def batch_confirm_dilutions(self, gap_events: List[Dict]) -> pd.DataFrame:
        """
        Confirm dilutions for multiple gap events.

        Args:
            gap_events: List of dicts with 'symbol' and 'gap_date'

        Returns:
            DataFrame with dilution confirmation results
        """
        results = []

        for event in gap_events:
            symbol = event["symbol"]
            gap_date = event["gap_date"]

            logger.info(f"Checking dilution for {symbol} after {gap_date}")

            dilution_result = self.check_for_dilution(symbol, gap_date)

            result = {"symbol": symbol, "gap_date": gap_date, **dilution_result}

            results.append(result)

        return pd.DataFrame(results)

    def calculate_dilution_metrics(self, confirmed_dilutions: pd.DataFrame) -> Dict:
        """
        Calculate metrics about dilution patterns.

        Args:
            confirmed_dilutions: DataFrame from batch_confirm_dilutions

        Returns:
            Dictionary with dilution statistics
        """
        total_events = len(confirmed_dilutions)

        if total_events == 0:
            return {
                "total_gap_events": 0,
                "dilution_rate": 0,
                "avg_days_to_dilution": 0,
            }

        confirmed = confirmed_dilutions[
            confirmed_dilutions["dilution_confirmed"] == True
        ]
        dilution_count = len(confirmed)

        # Calculate average days to dilution
        avg_days = 0
        if dilution_count > 0:
            days_data = confirmed[confirmed["days_after_gap"].notna()]
            if not days_data.empty:
                avg_days = days_data["days_after_gap"].mean()

        # Confidence levels
        high_confidence = (
            len(confirmed[confirmed["confidence"] >= 0.8]) if dilution_count > 0 else 0
        )

        # Form type breakdown
        form_types = {}
        if dilution_count > 0:
            for _, row in confirmed.iterrows():
                form = row.get("form_type", "Unknown")
                form_types[form] = form_types.get(form, 0) + 1

        return {
            "total_gap_events": total_events,
            "dilution_count": dilution_count,
            "dilution_rate": (dilution_count / total_events) * 100,
            "avg_days_to_dilution": avg_days,
            "high_confidence_dilutions": high_confidence,
            "high_confidence_rate": (
                (high_confidence / dilution_count * 100) if dilution_count > 0 else 0
            ),
            "form_type_breakdown": form_types,
            "indirect_evidence_count": (
                len(confirmed[confirmed.get("indirect_evidence", False) == True])
                if dilution_count > 0
                else 0
            ),
        }

    def generate_dilution_report(self, metrics: Dict) -> str:
        """Generate a report on dilution patterns."""
        report = f"""
# Dilution Confirmation Report

## Summary Statistics
- Total Gap Events Analyzed: {metrics['total_gap_events']}
- Confirmed Dilutions: {metrics['dilution_count']}
- Dilution Rate: {metrics['dilution_rate']:.1f}%
- Average Days to Dilution: {metrics['avg_days_to_dilution']:.1f}

## Confidence Analysis
- High Confidence Dilutions: {metrics['high_confidence_dilutions']}
- High Confidence Rate: {metrics['high_confidence_rate']:.1f}%
- Indirect Evidence Count: {metrics['indirect_evidence_count']}

## Filing Type Breakdown
"""
        for form_type, count in metrics.get("form_type_breakdown", {}).items():
            report += f"- {form_type}: {count}\n"

        report += """
## Key Insights
"""
        if metrics["dilution_rate"] > 70:
            report += "- ✅ HIGH dilution rate validates the gap-up ATM strategy\n"
        elif metrics["dilution_rate"] > 50:
            report += "- ⚠️ MODERATE dilution rate suggests strategy has merit\n"
        else:
            report += "- ❌ LOW dilution rate - strategy may need refinement\n"

        if metrics["avg_days_to_dilution"] < 5:
            report += "- ✅ Companies dilute quickly after gaps (within 5 days)\n"
        elif metrics["avg_days_to_dilution"] < 15:
            report += "- ⚠️ Companies dilute within 2 weeks of gaps\n"
        else:
            report += "- ❌ Long delay between gaps and dilution\n"

        return report

    def is_atm_filing(self, filing: pd.Series) -> bool:
        """
        Check if a filing is related to ATM (At-The-Market) offerings using LLM analysis.

        Args:
            filing: A pandas Series containing filing information

        Returns:
            True if filing indicates ATM activity
        """
        try:
            form_type = filing.get("form_type", "").upper()

            # Direct ATM indicators - no LLM needed for obvious cases
            atm_forms = [
                "424B5",
                "FWP",
            ]  # Prospectus supplements and free writing prospectuses
            if form_type in atm_forms:
                return True

            # Get text to analyze
            description = str(filing.get("description", ""))
            title = str(filing.get("title", ""))
            text_to_check = (
                f"Form Type: {form_type}\nTitle: {title}\nDescription: {description}"
            )

            # If we have LLM available and meaningful text, use it for analysis
            if self.llm_agent and (len(description) > 10 or len(title) > 10):
                try:
                    prompt = f"""
                    Analyze this SEC filing information to determine if it's related to an ATM (At-The-Market) offering program.
                    
                    ATM offerings allow companies to sell shares directly into the market at prevailing prices through a sales agent.
                    
                    Key indicators of ATM filings:
                    - Form types: 424B5 (prospectus supplement), FWP (free writing prospectus), S-3 (shelf registration)
                    - Keywords: "at-the-market", "ATM program", "equity offering program", "sales agreement"
                    - Mentions of selling shares "from time to time" at "market prices"
                    - References to sales agents like Jefferies, Goldman Sachs, etc.
                    
                    Filing information:
                    {text_to_check}
                    
                    Return your response as JSON:
                    {{"is_atm_filing": true/false, "confidence": 0.0-1.0, "reasoning": "brief explanation"}}
                    """

                    response = self.llm_agent.analyze_text_with_prompt(prompt)

                    # Parse LLM response
                    import json

                    if isinstance(response, str):
                        start = response.find("{")
                        end = response.rfind("}") + 1
                        if start >= 0 and end > start:
                            json_str = response[start:end]
                            result = json.loads(json_str)

                            is_atm = result.get("is_atm_filing", False)
                            confidence = result.get("confidence", 0.0)
                            reasoning = result.get("reasoning", "")

                            logger.debug(
                                f"LLM ATM analysis: {form_type} -> {is_atm} (confidence: {confidence:.2f}) - {reasoning}"
                            )

                            # Only trust high confidence results
                            if confidence >= 0.7:
                                return bool(is_atm)

                except Exception as llm_error:
                    logger.debug(
                        f"LLM ATM analysis failed: {llm_error}, falling back to keywords"
                    )

            # Fallback to keyword-based analysis
            text_lower = text_to_check.lower()

            atm_keywords = [
                "at-the-market",
                "atm program",
                "atm offering",
                "equity offering program",
                "prospectus supplement",
                "sales agreement",
            ]

            for keyword in atm_keywords:
                if keyword in text_lower:
                    return True

            # S-3 filings that mention "at-the-market" in context
            if form_type.startswith("S-3"):
                return "market" in text_lower and (
                    "at" in text_lower or "atm" in text_lower
                )

            return False

        except Exception as e:
            logger.debug(f"Error checking if filing is ATM-related: {e}")
            return False

    def analyze_dilution_timing(
        self, symbol: str, gap_date: str, confirmed_date: str
    ) -> Dict:
        """
        Analyze the timing between gap event and dilution confirmation.
        """
        try:
            gap_dt = pd.to_datetime(gap_date)
            confirm_dt = pd.to_datetime(confirmed_date)

            days_diff = (confirm_dt - gap_dt).days

            # Categorize timing
            if days_diff <= 1:
                timing_category = "immediate"
            elif days_diff <= 7:
                timing_category = "within_week"
            elif days_diff <= 30:
                timing_category = "within_month"
            else:
                timing_category = "delayed"

            return {
                "gap_date": gap_date,
                "confirmed_date": confirmed_date,
                "days_difference": days_diff,
                "timing_category": timing_category,
                "business_days": max(
                    0, days_diff - (days_diff // 7) * 2
                ),  # Rough business days
            }

        except Exception as e:
            logger.error(f"Error analyzing dilution timing: {e}")
            return {"error": str(e)}

    def calculate_dilution_amount(
        self, symbol: str, filing_data: Dict
    ) -> Optional[float]:
        """
        Estimate dilution amount from filing data.
        This would typically require parsing the actual filing content.
        """
        try:
            # This is a simplified implementation
            # In practice, would need to parse the actual filing text for:
            # - Number of shares offered
            # - Offering price or price range
            # - Maximum offering amount

            # For now, return a placeholder based on filing type confidence
            confidence = filing_data.get("confidence", 0.5)

            if confidence >= 0.9:
                # High confidence filings like 424B5 typically have specific amounts
                estimated_amount = 10_000_000  # $10M placeholder
            elif confidence >= 0.7:
                estimated_amount = 5_000_000  # $5M placeholder
            else:
                estimated_amount = 1_000_000  # $1M placeholder

            logger.debug(
                f"Estimated dilution amount for {symbol}: ${estimated_amount:,}"
            )
            return estimated_amount

        except Exception as e:
            logger.error(f"Error calculating dilution amount: {e}")
            return None

    def extract_offering_details(self, filing_text: str) -> Dict:
        """
        Extract offering details from filing text using LLM analysis.

        Args:
            filing_text: Raw text content of the filing

        Returns:
            Dict with extracted details like amount, price, agent
        """
        try:
            # Fallback to regex if no LLM available
            if not self.llm_agent:
                return self._extract_offering_details_regex(filing_text)

            # Use LLM to extract offering details
            prompt = f"""
            Analyze this SEC filing text and extract the following offering details:
            
            1. OFFERING AMOUNT: The total dollar amount being offered (e.g., "$50,000,000", "up to $25 million")
            2. STOCK PRICE: The current stock price mentioned (e.g., "$2.45 per share", "last reported sale price of $1.23")
            3. SALES AGENT: The investment bank or sales agent (e.g., "Jefferies LLC", "Goldman Sachs & Co.")
            4. NUMBER OF SHARES: If mentioned, the number of shares in the offering
            
            Return your response as a JSON object with these exact keys:
            {{
                "amount": <number or null>,
                "price": <number or null>, 
                "agent": "<string or null>",
                "shares": <number or null>
            }}
            
            Convert millions to actual numbers (e.g., "50 million" becomes ********).
            Extract only numbers for amount, price, and shares.
            
            Filing text:
            {filing_text[:3000]}...
            """

            try:
                # Get LLM analysis
                response = self.llm_agent.analyze_text_with_prompt(prompt)

                # Try to parse JSON response
                import json

                if isinstance(response, str):
                    # Find JSON in response
                    start = response.find("{")
                    end = response.rfind("}") + 1
                    if start >= 0 and end > start:
                        json_str = response[start:end]
                        details = json.loads(json_str)
                    else:
                        raise ValueError("No JSON found in LLM response")
                elif isinstance(response, dict):
                    details = response
                else:
                    raise ValueError("Unexpected LLM response format")

                # Validate and clean up the response
                cleaned_details = {
                    "amount": None,
                    "price": None,
                    "agent": None,
                    "shares": None,
                }

                # Clean up amount
                if (
                    details.get("amount")
                    and isinstance(details["amount"], (int, float))
                    and details["amount"] > 0
                ):
                    cleaned_details["amount"] = float(details["amount"])

                # Clean up price
                if (
                    details.get("price")
                    and isinstance(details["price"], (int, float))
                    and details["price"] > 0
                ):
                    cleaned_details["price"] = float(details["price"])

                # Clean up agent
                if (
                    details.get("agent")
                    and isinstance(details["agent"], str)
                    and len(details["agent"]) > 3
                ):
                    cleaned_details["agent"] = details["agent"].strip()

                # Clean up shares
                if (
                    details.get("shares")
                    and isinstance(details["shares"], (int, float))
                    and details["shares"] > 0
                ):
                    cleaned_details["shares"] = float(details["shares"])
                elif cleaned_details["amount"] and cleaned_details["price"]:
                    # Calculate shares if we have amount and price
                    cleaned_details["shares"] = (
                        cleaned_details["amount"] / cleaned_details["price"]
                    )

                logger.info(f"LLM extracted offering details: {cleaned_details}")
                return cleaned_details

            except Exception as llm_error:
                logger.warning(
                    f"LLM extraction failed: {llm_error}, falling back to regex"
                )
                return self._extract_offering_details_regex(filing_text)

        except Exception as e:
            logger.error(f"Error extracting offering details: {e}")
            return {"error": str(e)}

    def _extract_offering_details_regex(self, filing_text: str) -> Dict:
        """Fallback regex-based extraction method."""
        try:
            details = {"amount": None, "price": None, "agent": None, "shares": None}

            # Extract dollar amounts (e.g., "$50,000,000" or "Up to $50 million")
            amount_patterns = [
                r"Up to \$([0-9,]+(?:\.[0-9]+)?)",
                r"\$([0-9,]+(?:\.[0-9]+)?)\s*(?:million|m)",
                r"\$([0-9,]+(?:\.[0-9]+)?)",
                r"([0-9,]+(?:\.[0-9]+)?)\s*million",
            ]

            for pattern in amount_patterns:
                match = re.search(pattern, filing_text, re.IGNORECASE)
                if match:
                    amount_str = match.group(1).replace(",", "")
                    amount = float(amount_str)

                    # Convert millions to actual amount
                    if (
                        "million" in match.group(0).lower()
                        or "m" in match.group(0).lower()
                    ):
                        if amount < 1000:  # If it's already in millions
                            amount *= 1_000_000

                    details["amount"] = amount
                    break

            # Extract stock price (e.g., "$2.45 per share")
            price_patterns = [
                r"\$([0-9]+\.[0-9]+)\s*per share",
                r"sale price.*?\$([0-9]+\.[0-9]+)",
                r"last reported.*?\$([0-9]+\.[0-9]+)",
            ]

            for pattern in price_patterns:
                match = re.search(pattern, filing_text, re.IGNORECASE)
                if match:
                    details["price"] = float(match.group(1))
                    break

            # Extract sales agent (e.g., "Jefferies LLC")
            agent_patterns = [
                r"(\w+(?:\s+\w+)*(?:\s+LLC|\s+Inc\.|\s+Corporation|\s+Co\.))",
                r"sales agreement with ([^,\n.]+)",
                r"through ([^,\n.]+)",
            ]

            for pattern in agent_patterns:
                matches = re.finditer(pattern, filing_text, re.IGNORECASE)
                for match in matches:
                    agent = match.group(1).strip()
                    # Filter out common false positives
                    if (
                        len(agent) > 3
                        and "common stock" not in agent.lower()
                        and "prospectus" not in agent.lower()
                        and "nasdaq" not in agent.lower()
                    ):
                        details["agent"] = agent
                        break
                if details["agent"]:
                    break

            # Calculate shares if we have both amount and price
            if details["amount"] and details["price"]:
                details["shares"] = details["amount"] / details["price"]

            logger.debug(f"Regex extracted offering details: {details}")
            return details

        except Exception as e:
            logger.error(f"Error in regex extraction: {e}")
            return {"error": str(e)}

    def parse_prospectus_metrics(self, supplement_text: str) -> Dict:
        """
        Parse key metrics from prospectus supplement text using LLM analysis.

        Args:
            supplement_text: Text content of the prospectus supplement

        Returns:
            Dict with parsed metrics
        """
        try:
            # Fallback to regex if no LLM available
            if not self.llm_agent:
                return self._parse_prospectus_metrics_regex(supplement_text)

            # Use LLM to parse prospectus metrics
            prompt = f"""
            Analyze this SEC prospectus supplement text and extract the following key metrics:
            
            1. SHARES OUTSTANDING: The current number of shares outstanding (e.g., "125,432,189 shares of common stock outstanding")
            2. OFFERING AMOUNT: The total dollar amount of this offering (e.g., "up to $75,000,000")
            3. OPTIONS OUTSTANDING: Number of shares issuable upon exercise of options (e.g., "15,234,567 shares issuable upon exercise of options")
            4. RESERVED SHARES: Shares reserved for future issuance under equity plans (e.g., "8,765,432 shares reserved for future issuance")
            5. CONVERTIBLE SHARES: Shares issuable upon conversion of preferred stock or convertible securities (e.g., "25,000,000 shares issuable upon conversion")
            
            Return your response as a JSON object with these exact keys:
            {{
                "shares_outstanding": <number or null>,
                "offering_amount": <number or null>,
                "options_outstanding": <number or null>,
                "reserved_shares": <number or null>,
                "convertible_shares": <number or null>
            }}
            
            Extract only the raw numbers (remove commas, convert to integers).
            If a value is not found, use null.
            
            Prospectus text:
            {supplement_text[:4000]}...
            """

            try:
                # Get LLM analysis
                response = self.llm_agent.analyze_text_with_prompt(prompt)

                # Try to parse JSON response
                import json

                if isinstance(response, str):
                    # Find JSON in response
                    start = response.find("{")
                    end = response.rfind("}") + 1
                    if start >= 0 and end > start:
                        json_str = response[start:end]
                        metrics = json.loads(json_str)
                    else:
                        raise ValueError("No JSON found in LLM response")
                elif isinstance(response, dict):
                    metrics = response
                else:
                    raise ValueError("Unexpected LLM response format")

                # Validate and clean up the response
                cleaned_metrics = {}

                # List of expected integer fields
                expected_fields = [
                    "shares_outstanding",
                    "offering_amount",
                    "options_outstanding",
                    "reserved_shares",
                    "convertible_shares",
                ]

                for field in expected_fields:
                    if (
                        metrics.get(field)
                        and isinstance(metrics[field], (int, float))
                        and metrics[field] > 0
                    ):
                        cleaned_metrics[field] = int(metrics[field])
                    else:
                        cleaned_metrics[field] = None

                logger.info(f"LLM parsed prospectus metrics: {cleaned_metrics}")
                return cleaned_metrics

            except Exception as llm_error:
                logger.warning(
                    f"LLM parsing failed: {llm_error}, falling back to regex"
                )
                return self._parse_prospectus_metrics_regex(supplement_text)

        except Exception as e:
            logger.error(f"Error parsing prospectus metrics: {e}")
            return {"error": str(e)}

    def _parse_prospectus_metrics_regex(self, supplement_text: str) -> Dict:
        """Fallback regex-based parsing method."""
        try:
            metrics = {}

            # Extract shares outstanding
            outstanding_patterns = [
                r"([0-9,]+)\s*shares of common stock outstanding",
                r"based on ([0-9,]+)\s*shares.*outstanding",
                r"outstanding.*?([0-9,]+)\s*shares",
            ]

            for pattern in outstanding_patterns:
                match = re.search(pattern, supplement_text, re.IGNORECASE)
                if match:
                    shares_str = match.group(1).replace(",", "")
                    metrics["shares_outstanding"] = int(shares_str)
                    break

            # Extract offering amount
            amount_patterns = [
                r"offering price of up to \$([0-9,]+)",
                r"aggregate.*?\$([0-9,]+)",
                r"Up to \$([0-9,]+)",
            ]

            for pattern in amount_patterns:
                match = re.search(pattern, supplement_text, re.IGNORECASE)
                if match:
                    amount_str = match.group(1).replace(",", "")
                    metrics["offering_amount"] = int(amount_str)
                    break

            # Extract options outstanding
            option_patterns = [
                r"([0-9,]+)\s*shares issuable upon exercise of options",
                r"options outstanding.*?([0-9,]+)",
                r"• ([0-9,]+)\s*shares.*options",
            ]

            for pattern in option_patterns:
                match = re.search(pattern, supplement_text, re.IGNORECASE)
                if match:
                    options_str = match.group(1).replace(",", "")
                    metrics["options_outstanding"] = int(options_str)
                    break

            # Extract reserved shares
            reserved_patterns = [
                r"([0-9,]+)\s*shares reserved for future issuance",
                r"reserved.*?([0-9,]+)\s*shares",
            ]

            for pattern in reserved_patterns:
                match = re.search(pattern, supplement_text, re.IGNORECASE)
                if match:
                    reserved_str = match.group(1).replace(",", "")
                    metrics["reserved_shares"] = int(reserved_str)
                    break

            # Extract convertible shares
            convertible_patterns = [
                r"([0-9,]+)\s*shares issuable upon conversion",
                r"conversion.*?([0-9,]+)\s*shares",
                r"convertible.*?([0-9,]+)\s*shares",
            ]

            for pattern in convertible_patterns:
                match = re.search(pattern, supplement_text, re.IGNORECASE)
                if match:
                    convertible_str = match.group(1).replace(",", "")
                    metrics["convertible_shares"] = int(convertible_str)
                    break

            # Set None for missing fields
            expected_fields = [
                "shares_outstanding",
                "offering_amount",
                "options_outstanding",
                "reserved_shares",
                "convertible_shares",
            ]
            for field in expected_fields:
                if field not in metrics:
                    metrics[field] = None

            logger.debug(f"Regex parsed prospectus metrics: {metrics}")
            return metrics

        except Exception as e:
            logger.error(f"Error in regex parsing: {e}")
            return {"error": str(e)}


if __name__ == "__main__":
    # Test the dilution confirmation
    logger.info("Testing dilution confirmation module...")

    confirmer = DilutionConfirmation()

    # Test with a known example
    test_symbol = "SNDL"
    test_date = "2024-01-15"

    print(f"\n=== Testing Dilution Check for {test_symbol} ===")
    result = confirmer.check_for_dilution(test_symbol, test_date)

    print(f"Dilution Confirmed: {result['dilution_confirmed']}")
    print(f"Reason: {result['reason']}")

    if result["dilution_confirmed"]:
        print(f"Confidence: {result.get('confidence', 0):.1%}")
        print(f"Days After Gap: {result.get('days_after_gap', 'N/A')}")

    # Test batch confirmation
    print("\n=== Testing Batch Confirmation ===")
    test_events = [
        {"symbol": "SNDL", "gap_date": "2024-01-15"},
        {"symbol": "GEVO", "gap_date": "2024-01-20"},
        {"symbol": "FTFT", "gap_date": "2024-01-25"},
    ]

    batch_results = confirmer.batch_confirm_dilutions(test_events)
    print(f"\nBatch Results:\n{batch_results}")

    # Calculate metrics
    metrics = confirmer.calculate_dilution_metrics(batch_results)

    # Generate report
    report = confirmer.generate_dilution_report(metrics)
    print(report)
