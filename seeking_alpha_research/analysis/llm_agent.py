import os
import json
import litellm
from dotenv import load_dotenv
from datetime import datetime
from core.database import llm_filing_analysis
from sqlalchemy import create_engine, insert
from utils.llm_cache import cached_llm_call
from core.logger import get_logger

logger = get_logger(__name__)

# Load environment variables
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

# Configure LiteLLM for Gemini
litellm.set_verbose = False  # Set to True for debugging
os.environ["GEMINI_API_KEY"] = os.getenv("GEMINI_API_KEY", "")

# Check if Gemini API key is configured
if (
    not os.getenv("GEMINI_API_KEY")
    or os.getenv("GEMINI_API_KEY") == "YOUR_GEMINI_API_KEY_HERE"
):
    print(
        "WARNING: GEMINI_API_KEY not configured in .env file. LLM features will use fallback mode."
    )


class CalculatorTool:
    """A simple tool for the LLM agent to perform calculations."""

    def use(self, expression: str):
        """
        Evaluates a simple mathematical expression.

        Args:
            expression (str): A string like "100 / 5".

        Returns:
            The result of the calculation.
        """
        try:
            # Using eval is generally unsafe, but for this controlled demo with a simple
            # calculator, it's a straightforward approach. A production system would
            # use a more secure parser.
            return eval(expression)
        except Exception as e:
            return f"Error evaluating expression: {e}"


class FilingSectionTool:
    """
    A tool for the LLM agent to read specific sections of a filing.
    The agent can search for keywords to find relevant sections.
    """

    def __init__(self, filing_text: str):
        self.filing_text = filing_text.lower()

    def use(self, keyword: str, context_window: int = 2000):
        """
        Finds a keyword in the filing and returns the surrounding text.

        Args:
            keyword (str): The term to search for (e.g., "at-the-market").
            context_window (int): The number of characters to return around the keyword.

        Returns:
            The text snippet containing the keyword or an error message.
        """
        keyword = keyword.lower()
        index = self.filing_text.find(keyword)

        if index == -1:
            return f"Keyword '{keyword}' not found in the filing."

        start = max(0, index - context_window // 2)
        end = min(len(self.filing_text), index + len(keyword) + context_window // 2)

        return self.filing_text[start:end]


class ReActAgent:
    """
    An agent that uses the ReAct (Reasoning and Acting) framework with Gemini LLM.
    """

    def __init__(self, tools: list, model: str = "gemini/gemini-1.5-flash"):
        self.tools = {tool.__class__.__name__: tool for tool in tools}
        self.model = model
        self.max_iterations = 5

    def _call_llm(self, messages: list) -> str:
        """Call the LLM with error handling"""
        try:
            response = litellm.completion(
                model=self.model,
                messages=messages,
                temperature=0.1,  # Lower temperature for more consistent analysis
                max_tokens=2000,
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"Error calling LLM: {e}")
            # Fallback to a simpler analysis if LLM fails
            return self._fallback_analysis()

    def _fallback_analysis(self) -> str:
        """Fallback analysis when LLM is not available"""
        return """I need to analyze the SEC filing for ATM risk.
        
Thought: I should search for key terms related to ATM offerings and cash position.
Action: FilingSectionTool.use('at-the-market')
"""

    def run(self, prompt: str):
        print("--- Running ReAct Agent with Gemini ---")
        print(f"Initial Prompt: {prompt[:200]}...")

        # Initialize conversation
        messages = [
            {
                "role": "system",
                "content": """You are a financial analyst examining SEC filings for ATM offering risk.
Use the ReAct framework: Thought, Action, Observation.
Available tools:
- FilingSectionTool.use(keyword): Search for a keyword in the filing
- CalculatorTool.use(expression): Calculate mathematical expressions

Format your response as:
Thought: [your reasoning]
Action: [tool_name].use('[parameter]')

When you have enough information, provide a final answer with:
Final Answer: {
    "atm_risk": "High/Medium/Low",
    "has_atm_program": true/false,
    "cash_months_remaining": number,
    "monthly_burn_rate": number,
    "atm_amount_available": number
}""",
            },
            {"role": "user", "content": prompt},
        ]

        # ReAct loop
        for i in range(self.max_iterations):
            # Get LLM response
            response = self._call_llm(messages)
            print(f"\n--- Iteration {i+1} ---")
            print(f"LLM Response: {response[:500]}...")

            # Check if we have a final answer
            if "Final Answer:" in response:
                # Extract JSON from response
                try:
                    json_start = response.find("{")
                    json_end = response.rfind("}") + 1
                    result_json = response[json_start:json_end]
                    return json.loads(result_json)
                except Exception as e:
                    # CRITICAL: LLM response parsing failed - this affects trading decisions!
                    logger.error(
                        f"CRITICAL: Failed to parse LLM response for SEC filing analysis: {e}"
                    )
                    logger.error(f"Raw LLM response was: {response}")
                    raise ValueError(
                        f"LLM response parsing failed - cannot make trading decisions with invalid data: {e}"
                    ) from e

            # Parse action from response
            if "Action:" in response:
                action_line = response.split("Action:")[1].split("\n")[0].strip()

                # Execute the action
                try:
                    if "FilingSectionTool.use" in action_line:
                        keyword = action_line.split("'")[1]
                        result = self.tools["FilingSectionTool"].use(keyword)
                    elif "CalculatorTool.use" in action_line:
                        expression = action_line.split("'")[1]
                        result = self.tools["CalculatorTool"].use(expression)
                    else:
                        result = "Unknown action"

                    # Add observation to conversation
                    messages.append({"role": "assistant", "content": response})
                    messages.append(
                        {"role": "user", "content": f"Observation: {result[:1000]}"}
                    )

                except Exception as e:
                    messages.append({"role": "assistant", "content": response})
                    messages.append(
                        {"role": "user", "content": f"Error executing action: {str(e)}"}
                    )

        # If we reach max iterations without a final answer
        return {
            "atm_risk": "Analysis incomplete",
            "error": "Max iterations reached without conclusion",
        }


@cached_llm_call
def calculate_cash_burn(filing_text: str, save_to_db: bool = True) -> dict:
    """
    Calculate cash burn rate from SEC filing using LLM.

    Args:
        filing_text: The SEC filing text to analyze
        save_to_db: Whether to save results to database

    Returns:
        Dict with cash burn analysis
    """
    tools = [CalculatorTool(), FilingSectionTool(filing_text=filing_text)]
    agent = ReActAgent(tools=tools)

    prompt = """Analyze this SEC filing to calculate the cash burn rate and predict ATM date:
1. Find the most recent quarter's operating cash flow or operating expenses
2. Find the current cash and cash equivalents
3. Calculate monthly burn rate (quarterly expenses / 3)
4. Calculate months of runway (cash / monthly burn)
5. CRITICAL: Calculate the expected date when cash will run out (filing date + runway months)
6. Show your work: provide the exact numbers and dates used in calculations

Example response format:
- Cash position: $X million (as of DATE)
- Quarterly burn: $Y million
- Monthly burn rate: $Y/3 = $Z million
- Runway: $X / $Z = N months
- Expected cash depletion date: DATE + N months = YYYY-MM-DD
- ATM likely needed by: YYYY-MM-DD (explain why - e.g., "2 months before depletion for safety")

Focus on finding concrete numbers from financial statements and show all calculations."""

    result = agent.run(prompt)

    # Add analysis metadata
    result["analysis_date"] = datetime.now().isoformat()
    result["model_used"] = agent.model

    return result


@cached_llm_call
def predict_atm_offering_date(filing_text: str, filing_date: str, save_to_db: bool = True) -> dict:
    """
    Predict when a company will need to do an ATM offering based on cash burn.
    
    Args:
        filing_text: The SEC filing text to analyze
        filing_date: Date of the filing (YYYY-MM-DD)
        save_to_db: Whether to save results to database
        
    Returns:
        Dict with ATM date prediction and supporting calculations
    """
    tools = [CalculatorTool(), FilingSectionTool(filing_text=filing_text)]
    agent = ReActAgent(tools=tools)
    
    prompt = f"""You are analyzing an SEC filing dated {filing_date} to predict when the company will need an ATM offering.

TASK: Calculate the exact date when this company will likely execute an ATM offering.

STEPS:
1. Extract from the filing:
   - Current cash and cash equivalents (most recent amount)
   - Quarterly operating expenses or cash used in operations
   - Any existing ATM facility amounts remaining

2. Calculate:
   - Monthly burn rate = Quarterly expenses / 3
   - Months of runway = Current cash / Monthly burn rate
   - Cash depletion date = Filing date ({filing_date}) + runway months

3. Predict ATM timing:
   - Companies typically raise cash 2-3 months BEFORE running out
   - If they have an active ATM facility, they may tap it sooner
   - Consider any mentioned milestones or clinical trials that need funding

4. Final answer must include:
   - Predicted ATM date: YYYY-MM-DD
   - Confidence level: HIGH/MEDIUM/LOW
   - Justification with specific numbers
   - Key risk factors that could accelerate/delay the ATM

Example calculation:
"Based on $30M cash (as of Sept 30, 2024) and $10M quarterly burn, monthly burn is $3.33M.
Runway = $30M / $3.33M = 9 months from Sept 30, 2024 = June 30, 2025.
ATM likely by April 30, 2025 (2 months buffer) to maintain operations.
Confidence: HIGH - consistent burn rate over last 4 quarters."
"""
    
    result = agent.run(prompt)
    
    # Parse the result to extract key dates and numbers
    result["analysis_date"] = datetime.now().isoformat()
    result["filing_date"] = filing_date
    result["model_used"] = agent.model
    
    return result


@cached_llm_call
def analyze_filing_for_atm_risk(filing_text: str, filing_id: int = None):
    """
    Analyzes a filing's text for ATM offering risk using a ReAct agent.

    Args:
        filing_text: The SEC filing text to analyze
        filing_id: Optional filing ID for database storage
    """
    # The agent is equipped with tools to analyze the filing.
    tools = [CalculatorTool(), FilingSectionTool(filing_text=filing_text)]
    agent = ReActAgent(tools=tools)

    prompt = (
        "Analyze the provided SEC filing to assess the risk of an 'At-The-Market' (ATM) offering. "
        "Determine the following: "
        "1. Is there an active ATM offering program mentioned? Look for terms like 'at-the-market', 'ATM', 'equity distribution agreement' "
        "2. What is the company's cash on hand (cash and cash equivalents)? "
        "3. What is their recent cash burn rate? Look for operating expenses or cash used in operations "
        "4. How much can they raise through the ATM program? "
        "Provide specific numbers where possible."
    )

    result = agent.run(prompt)

    print("\n--- Final Structured Output ---")
    print(json.dumps(result, indent=2))

    # Save to database if filing_id provided
    if filing_id and isinstance(result, dict):
        try:
            from core.database_config import get_database_url

            db_engine = create_engine(get_database_url())

            # Extract key metrics
            cash_burn = result.get("monthly_burn_rate", 0)
            cash_on_hand = (
                result.get("cash_on_hand", 0) / 1_000_000
                if result.get("cash_on_hand")
                else 0
            )
            has_atm = result.get("has_atm_program", False)

            insert_stmt = insert(llm_filing_analysis).values(
                filing_id=filing_id,
                cash_burn_m_usd=cash_burn / 1_000_000 if cash_burn else None,
                cash_on_hand_m_usd=cash_on_hand,
                has_active_atm=has_atm,
                atm_details=json.dumps(result),
                confidence_score=0.8 if not result.get("error") else 0.3,
                raw_analysis=json.dumps(result),
            )

            with db_engine.connect() as conn:
                conn.execute(insert_stmt)
                conn.commit()

            print("Analysis saved to database")

        except Exception as e:
            print(f"Error saving to database: {e}")

    return result


class LLMAgent:
    """
    A general purpose LLM agent for text analysis.
    This provides a simple interface for various text analysis tasks.
    """

    def __init__(self, model: str = "gemini/gemini-2.0-flash-exp"):
        self.model = model

    def analyze_text_with_prompt(self, prompt: str) -> str:
        """
        Analyze text with a custom prompt.

        Args:
            prompt: The prompt to send to the LLM

        Returns:
            LLM response as string
        """
        try:
            response = litellm.completion(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=2000,
            )
            return response.choices[0].message.content

        except Exception as e:
            logger.warning(f"LLM analysis failed: {e}")
            return f'{{"error": "LLM analysis failed: {str(e)}"}}'

    def analyze_cash_burn(self, filing_text: str) -> dict:
        """
        Analyze cash burn from filing text.
        This wraps the existing function for compatibility.
        """
        return calculate_cash_burn(filing_text, save_to_db=False)

    def predict_atm_probability(self, filing_data: dict) -> dict:
        """
        Predict ATM probability from filing data.
        """
        try:
            # Extract text from filing data
            filing_text = filing_data.get("text", "") or filing_data.get(
                "filing_text", ""
            )

            if not filing_text:
                return {
                    "probability": 0.0,
                    "confidence": 0.0,
                    "reasoning": "No filing text provided",
                }

            # Use the existing ATM analysis function
            result = analyze_filing_for_atm_risk(filing_text)

            # Convert to probability format
            probability = 0.15  # Default low probability
            if result.get("has_atm_program"):
                probability = 0.85
            elif "need additional financing" in filing_text.lower():
                probability = 0.65
            elif result.get("cash_on_hand", 0) < 10_000_000:  # Less than $10M
                probability = 0.45

            return {
                "probability": probability,
                "confidence": 0.8,
                "reasoning": f"Analysis based on ATM program detection and cash position",
            }

        except Exception as e:
            logger.error(f"Error in ATM probability prediction: {e}")
            return {
                "probability": 0.0,
                "confidence": 0.0,
                "reasoning": f"Analysis failed: {str(e)}",
            }

    def get_cached_analysis(self, filing_id: str) -> dict:
        """
        Get cached analysis for a filing.
        This is a placeholder implementation.
        """
        # In a real implementation, this would check the database
        return None


def get_llm_agent(model: str = "gemini/gemini-2.0-flash-exp") -> LLMAgent:
    """
    Get an LLM agent instance.

    Args:
        model: The model to use for the agent

    Returns:
        LLMAgent instance
    """
    return LLMAgent(model=model)


if __name__ == "__main__":
    # A more realistic dummy filing text snippet
    dummy_filing = """
    This is a long document...
    Item 1.01 Entry into a Material Definitive Agreement.
    On December 1, 2023, we entered into an at-the-market offering agreement with Cowen,
    allowing us to sell up to $50,000,000 of our common stock.
    ...
    As of September 30, 2023, we had cash and cash equivalents of $5.2 million.
    Our operating expenses for the quarter were $10.4 million, representing a significant
    cash burn. We will need to raise additional capital in the near future.
    ...
    This document is very long.
    """
    analyze_filing_for_atm_risk(dummy_filing)
