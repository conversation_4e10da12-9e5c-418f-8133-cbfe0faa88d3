"""
Enhanced ReAct SEC Analyzer with Multi-Round Analysis and Critique

This analyzer implements:
1. Full filing chunk processing (each agent sees full chunks)
2. Multi-round ReAct with critique evaluation
3. Data validation and confirmation rounds
4. Sophisticated cash burn and ATM prediction

NO FAKES, NO MOCKS - Real SEC filings, real LLM analysis
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib

from core.data_service import DataService
from utils.backtest_aware_llm_cache import BacktestAwareLLMCache
from analysis.llm_agent import get_llm_agent
from utils.xbrl_financial_extractor import XBRLFinancialExtractor

logger = logging.getLogger(__name__)


class EnhancedReActAnalyzer:
    """Enhanced ReAct analyzer with multi-round analysis and critique."""
    
    def __init__(self, data_service: DataService = None, max_workers: int = 4):
        self.data_service = data_service or DataService()
        self.max_workers = max_workers
        self.llm_cache = BacktestAwareLLMCache()
        self.xbrl_extractor = XBRLFinancialExtractor()
        self.chunk_size = 12000  # Larger chunks for better context
        logger.info(f"Enhanced ReAct Analyzer initialized with {max_workers} workers and XBRL extractor")
    
    def analyze_atm_risk_enhanced(
        self, symbol: str, analysis_date: str, lookback_days: int = 730,
        fundamentals_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Enhanced ATM risk analysis with multi-round ReAct.
        
        Process:
        1. Round 1: Initial extraction from full filing chunks
        2. Round 2: Cross-validation and additional data requests
        3. Round 3: Critique and final synthesis
        """
        logger.info(f"Starting enhanced ReAct analysis for {symbol} as of {analysis_date}")
        
        try:
            # Get recent SEC filings
            filings = self._get_recent_filings(symbol, analysis_date, lookback_days)
            if filings is None or (hasattr(filings, 'empty') and filings.empty):
                raise ValueError(f"CRITICAL: No SEC filings found for {symbol}")
            
            # ROUND 1: XBRL structured data extraction
            logger.info(f"Round 1: XBRL extraction from {len(filings)} filings...")
            round1_results = self._round1_xbrl_extraction(filings, symbol)
            
            if not round1_results:
                raise ValueError(f"CRITICAL: No data extracted from filings for {symbol}")
            
            logger.info(f"Round 1 complete: Extracted data from {len(round1_results)} filings")
            
            # ROUND 2: Cross-validation and gap filling
            logger.info("Round 2: Cross-validation and data confirmation...")
            round2_results = self._round2_validation(round1_results, filings, symbol)
            
            # ROUND 3: Critique and final synthesis
            logger.info("Round 3: Critique evaluation and final synthesis...")
            final_assessment = self._round3_critique_synthesis(
                round1_results, round2_results, symbol, analysis_date
            )
            
            # Calculate ATM date prediction with window
            atm_prediction = self._predict_atm_date_window(final_assessment, analysis_date)
            final_assessment.update(atm_prediction)
            
            # Calculate comprehensive metrics
            final_assessment['analysis_quality'] = self._assess_analysis_quality(
                round1_results, round2_results, final_assessment
            )
            
            logger.info(
                f"Enhanced ReAct analysis complete for {symbol}. "
                f"Risk: {final_assessment.get('risk_category', 'UNKNOWN')} "
                f"({final_assessment.get('atm_probability', 0):.0%})"
            )
            
            return final_assessment
            
        except Exception as e:
            logger.error(f"Enhanced ATM analysis failed for {symbol}: {e}")
            raise ValueError(f"CRITICAL: Enhanced analysis failed for {symbol} - {e}")
    
    def _round1_xbrl_extraction(self, filings: pd.DataFrame, symbol: str) -> List[Dict]:
        """Round 1: Extract structured data using XBRL instead of text parsing."""
        extracted_data = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_filing = {
                executor.submit(
                    self._extract_xbrl_from_filing, filing, symbol
                ): filing for _, filing in filings.iterrows()
            }
            
            for future in as_completed(future_to_filing):
                filing = future_to_filing[future]
                try:
                    result = future.result(timeout=60)
                    if result and result.get('confidence', 0) > 0:
                        extracted_data.append(result)
                        logger.info(f"Successfully extracted XBRL data from {filing['form_type']} (confidence: {result.get('confidence', 0):.2f})")
                except Exception as e:
                    logger.error(f"Failed to extract XBRL from {filing['form_type']}: {e}")
        
        return extracted_data
    
    def _extract_xbrl_from_filing(self, filing: pd.Series, symbol: str) -> Optional[Dict]:
        """Extract structured data from a single filing using XBRL."""
        try:
            # Check cache first
            cache_key = f"xbrl_{filing['accession_number']}"
            cached = self.llm_cache.get_as_of_date(
                symbol=symbol,
                analysis_type="xbrl_extraction",
                filing_date=filing['filed_at'].strftime('%Y-%m-%d'),
                prompt=cache_key,
                as_of_date=filing['filed_at'].strftime('%Y-%m-%d')
            )
            
            if cached:
                logger.info(f"Using cached XBRL data for {filing['accession_number']}")
                return cached
            
            # Extract XBRL data
            xbrl_data = self.xbrl_extractor.extract_financial_data(
                symbol=symbol,
                accession_number=filing['accession_number'],
                form_type=filing['form_type']
            )
            
            if xbrl_data.get('error'):
                logger.warning(f"XBRL extraction error for {filing['accession_number']}: {xbrl_data['error']}")
                return None
            
            # Enhance with filing metadata
            result = {
                'filing_type': filing['form_type'],
                'filing_date': filing['filed_at'].strftime('%Y-%m-%d'),
                'accession_number': filing['accession_number'],
                **xbrl_data
            }
            
            # Cache the result
            if result.get('confidence', 0) > 0:
                self.llm_cache.set(
                    symbol=symbol,
                    analysis_type="xbrl_extraction",
                    filing_date=filing['filed_at'].strftime('%Y-%m-%d'),
                    prompt=cache_key,
                    response=result,
                    analysis_date=filing['filed_at'].strftime('%Y-%m-%d')
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting XBRL from {filing['form_type']}: {e}")
            return None

    def _round1_full_extraction(self, filings: pd.DataFrame, symbol: str) -> List[Dict]:
        """Round 1: Extract data from full filing chunks in parallel."""
        extracted_data = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_filing = {
                executor.submit(
                    self._extract_from_full_filing, filing, symbol
                ): filing for _, filing in filings.iterrows()
            }
            
            for future in as_completed(future_to_filing):
                filing = future_to_filing[future]
                try:
                    result = future.result(timeout=60)
                    if result:
                        extracted_data.append(result)
                        logger.info(f"Successfully extracted data from {filing['form_type']}")
                except Exception as e:
                    logger.error(f"Failed to extract from {filing['form_type']}: {e}")
        
        return extracted_data
    
    def _extract_from_full_filing(self, filing: pd.Series, symbol: str) -> Optional[Dict]:
        """Extract data from a single filing using full chunk processing."""
        try:
            filing_text = self._get_filing_text(filing)
            if not filing_text:
                return None
            
            # Split into chunks
            chunks = self._smart_chunk_text(filing_text, self.chunk_size)
            logger.info(f"Processing {filing['form_type']} in {len(chunks)} chunks")
            
            # Process each chunk with full context
            chunk_results = []
            conversation_history = []
            
            for i, chunk in enumerate(chunks):
                chunk_result = self._process_chunk_with_context(
                    chunk, i, len(chunks), filing, symbol, conversation_history
                )
                if chunk_result:
                    chunk_results.append(chunk_result)
                    # Add to conversation history for context
                    conversation_history.append({
                        "chunk_num": i + 1,
                        "extracted": chunk_result
                    })
            
            # Aggregate chunk results
            return self._aggregate_chunk_results(chunk_results, filing)
            
        except Exception as e:
            logger.error(f"Error extracting from {filing['form_type']}: {e}")
            return None
    
    def _process_chunk_with_context(
        self, chunk: str, chunk_num: int, total_chunks: int, 
        filing: pd.Series, symbol: str, history: List[Dict]
    ) -> Optional[Dict]:
        """Process a single chunk with full conversation context."""
        
        agent = get_llm_agent()
        
        # Build context from previous chunks
        context_summary = ""
        if history:
            context_summary = "\n\nPrevious chunks found:\n"
            for h in history[-3:]:  # Last 3 chunks for context
                if h['extracted'].get('cash_position'):
                    context_summary += f"- Cash: ${h['extracted']['cash_position']:,.0f}\n"
                if h['extracted'].get('quarterly_expenses'):
                    context_summary += f"- Quarterly expenses: ${h['extracted']['quarterly_expenses']:,.0f}\n"
        
        prompt = f"""You are analyzing chunk {chunk_num + 1}/{total_chunks} of a {filing['form_type']} filing for {symbol}.

{context_summary}

CHUNK CONTENT:
{chunk}

Extract ALL financial data you can find:
1. Cash and cash equivalents (exact amounts)
2. Operating expenses or cash burn (quarterly/annual)
3. Revenue (if any)
4. ATM program details (capacity, usage)
5. Going concern warnings
6. Debt levels
7. Any other critical financial metrics

Return ONLY valid JSON:
{{
    "cash_position": [number or null],
    "quarterly_expenses": [number or null],
    "annual_revenue": [number or null],
    "has_atm_mentions": [true/false],
    "atm_capacity": [number or null],
    "atm_used": [number or null],
    "going_concern_warning": [true/false],
    "total_debt": [number or null],
    "key_quotes": ["exact quote 1", "exact quote 2"],
    "confidence": [0.0-1.0]
}}"""
        
        # Check cache first
        cached = self.llm_cache.get_as_of_date(
            symbol=symbol, 
            analysis_type="chunk_extraction", 
            filing_date=filing['filed_at'].strftime('%Y-%m-%d'), 
            prompt=prompt,
            as_of_date=filing['filed_at'].strftime('%Y-%m-%d')
        )
        
        if cached:
            return cached
        
        try:
            response = agent.analyze_text_with_prompt(prompt)
            result = self._parse_json_response(response)
            
            if result:
                self.llm_cache.set(
                    symbol=symbol,
                    analysis_type="chunk_extraction", 
                    filing_date=filing['filed_at'].strftime('%Y-%m-%d'),
                    prompt=prompt,
                    response=result,
                    analysis_date=filing['filed_at'].strftime('%Y-%m-%d')
                )
                return result
                
        except Exception as e:
            logger.error(f"LLM processing failed for chunk {chunk_num}: {e}")
            
        return None
    
    def _round2_validation(
        self, round1_results: List[Dict], filings: pd.DataFrame, symbol: str
    ) -> Dict[str, Any]:
        """Round 2: Validate and fill gaps in extracted data."""
        
        # Identify gaps and inconsistencies
        validation_needs = self._identify_validation_needs(round1_results)
        
        if not validation_needs:
            logger.info("Round 2: No validation needed, data is complete")
            return {"validation_status": "complete", "additional_data": {}}
        
        logger.info(f"Round 2: Validating {len(validation_needs)} data points...")
        
        # Create validation prompts for specific data points
        validation_results = {}
        agent = get_llm_agent()
        
        for need in validation_needs:
            filing_idx = need['filing_idx']
            filing = filings.iloc[filing_idx]
            
            # Get specific sections of filing for validation
            filing_text = self._get_filing_text(filing)
            if not filing_text:
                continue
            
            # Extract relevant section based on need
            relevant_section = self._extract_relevant_section(
                filing_text, need['data_type']
            )
            
            if relevant_section:
                validation_prompt = self._create_validation_prompt(
                    need, relevant_section, round1_results[filing_idx]
                )
                
                try:
                    response = agent.analyze_text_with_prompt(validation_prompt)
                    validated_data = self._parse_json_response(response)
                    if validated_data:
                        validation_results[f"{filing_idx}_{need['data_type']}"] = validated_data
                except Exception as e:
                    logger.error(f"Validation failed for {need['data_type']}: {e}")
        
        return {
            "validation_status": "completed",
            "validated_items": len(validation_results),
            "additional_data": validation_results
        }
    
    def _round3_critique_synthesis(
        self, round1: List[Dict], round2: Dict, symbol: str, analysis_date: str
    ) -> Dict[str, Any]:
        """Round 3: Critical evaluation and final synthesis."""
        
        # Aggregate all data
        all_cash_positions = []
        all_expenses = []
        all_revenues = []
        atm_data = []
        
        for r1 in round1:
            if r1.get('cash_position'):
                # Ensure amount is a number, not a list
                amount = r1['cash_position']
                if isinstance(amount, list):
                    amount = amount[0] if amount else 0
                all_cash_positions.append({
                    'amount': amount,
                    'date': r1['filing_date'],
                    'confidence': r1.get('confidence', 0.5)
                })
            if r1.get('quarterly_expenses'):
                # Ensure amount is a number, not a list
                amount = r1['quarterly_expenses']
                if isinstance(amount, list):
                    amount = amount[0] if amount else 0
                all_expenses.append({
                    'amount': amount,
                    'date': r1['filing_date'],
                    'type': 'quarterly'
                })
            if r1.get('annual_revenue'):
                # Ensure amount is a number, not a list
                amount = r1['annual_revenue']
                if isinstance(amount, list):
                    amount = amount[0] if amount else 0
                all_revenues.append({
                    'amount': amount,
                    'date': r1['filing_date']
                })
            if r1.get('has_atm_mentions'):
                atm_data.append({
                    'capacity': r1.get('atm_capacity', 0),
                    'used': r1.get('atm_used', 0),
                    'date': r1['filing_date']
                })
        
        # Run critique agent
        critique_prompt = self._create_critique_prompt(
            symbol, all_cash_positions, all_expenses, all_revenues, atm_data
        )
        
        agent = get_llm_agent()
        critique_response = agent.analyze_text_with_prompt(critique_prompt)
        critique_result = self._parse_json_response(critique_response)
        
        if not critique_result:
            # Fallback to basic aggregation
            return self._basic_aggregation(all_cash_positions, all_expenses, atm_data)
        
        # Enhance with critique insights
        final_assessment = {
            'cash_position': critique_result.get('verified_cash_position'),
            'monthly_burn_rate': critique_result.get('verified_monthly_burn'),
            'cash_runway_months': critique_result.get('cash_runway_months'),
            'atm_probability': critique_result.get('atm_probability'),
            'risk_category': critique_result.get('risk_category'),
            'confidence': critique_result.get('overall_confidence'),
            'critique_insights': critique_result.get('key_insights', []),
            'data_quality_score': critique_result.get('data_quality_score', 0.5)
        }
        
        return final_assessment
    
    def _predict_atm_date_window(
        self, assessment: Dict[str, Any], analysis_date: str
    ) -> Dict[str, Any]:
        """Predict ATM date with confidence window."""
        
        runway_months = assessment.get('cash_runway_months') or 0
        if runway_months <= 0:
            return {
                'predicted_atm_date': 'Immediate',
                'atm_window_start': analysis_date,
                'atm_window_end': analysis_date,
                'window_confidence': 0.1
            }
        
        # Calculate base ATM date
        base_date = pd.to_datetime(analysis_date)
        
        # Conservative estimate: subtract 2 months buffer
        predicted_months = max(runway_months - 2, 1)
        predicted_date = base_date + pd.DateOffset(months=int(predicted_months))
        
        # Calculate window based on confidence
        confidence = assessment.get('confidence') or 0.5
        data_quality = assessment.get('data_quality_score') or 0.5
        
        # Window size inversely proportional to confidence
        window_months = int(3 * (1 - confidence * data_quality))  # 0-3 months
        
        window_start = predicted_date - pd.DateOffset(months=window_months)
        window_end = predicted_date + pd.DateOffset(months=window_months)
        
        return {
            'predicted_atm_date': predicted_date.strftime('%Y-%m-%d'),
            'atm_window_start': window_start.strftime('%Y-%m-%d'),
            'atm_window_end': window_end.strftime('%Y-%m-%d'),
            'window_confidence': confidence * data_quality,
            'window_size_months': window_months * 2
        }
    
    def _create_critique_prompt(
        self, symbol: str, cash_data: List[Dict], 
        expense_data: List[Dict], revenue_data: List[Dict], atm_data: List[Dict]
    ) -> str:
        """Create prompt for critique agent."""
        
        # Sort by date
        cash_data.sort(key=lambda x: x['date'])
        expense_data.sort(key=lambda x: x['date'])
        
        cash_summary = "\n".join([
            f"- {c['date']}: ${c['amount']:,.0f} (confidence: {c['confidence']:.1f})"
            for c in cash_data[-5:]  # Last 5 entries
        ])
        
        expense_summary = "\n".join([
            f"- {e['date']}: ${e['amount']:,.0f}/quarter"
            for e in expense_data[-5:]
        ])
        
        revenue_summary = "\n".join([
            f"- {r['date']}: ${r['amount']:,.0f}/year"
            for r in revenue_data[-3:]
        ]) if revenue_data else "No revenue data"
        
        atm_summary = "\n".join([
            f"- {a['date']}: Capacity ${a['capacity']:,.0f}, Used ${a['used']:,.0f}"
            for a in atm_data[-3:]
        ]) if atm_data else "No ATM data"
        
        return f"""You are a critical financial analyst evaluating extracted data for {symbol}.

EXTRACTED CASH POSITIONS:
{cash_summary}

EXTRACTED EXPENSES:
{expense_summary}

REVENUE DATA:
{revenue_summary}

ATM PROGRAM DATA:
{atm_summary}

CRITICAL EVALUATION TASKS:
1. Verify cash burn trend - is it accelerating or stable?
2. Calculate accurate monthly burn rate with seasonal adjustments
3. Assess data quality and identify any inconsistencies
4. Predict ATM probability based on ALL factors
5. Determine months of runway with confidence bounds

RETURN ONLY THIS JSON:
{{
    "verified_cash_position": [latest reliable cash amount],
    "verified_monthly_burn": [calculated monthly burn],
    "burn_trend": "accelerating/stable/decreasing",
    "cash_runway_months": [months until cash depleted],
    "atm_probability": [0.0-1.0],
    "risk_category": "HIGH/MEDIUM/LOW",
    "overall_confidence": [0.0-1.0],
    "data_quality_score": [0.0-1.0],
    "key_insights": [
        "Critical insight 1",
        "Critical insight 2",
        "Critical insight 3"
    ],
    "red_flags": ["any concerning findings"]
}}"""
    
    def _smart_chunk_text(self, text: str, chunk_size: int) -> List[str]:
        """Smart chunking that tries to preserve context."""
        chunks = []
        
        # Split by sections first
        sections = text.split('\n\n')
        
        current_chunk = ""
        for section in sections:
            if len(current_chunk) + len(section) < chunk_size:
                current_chunk += section + "\n\n"
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                
                # If section itself is too large, split it
                if len(section) > chunk_size:
                    words = section.split()
                    temp_chunk = ""
                    for word in words:
                        if len(temp_chunk) + len(word) < chunk_size:
                            temp_chunk += word + " "
                        else:
                            chunks.append(temp_chunk.strip())
                            temp_chunk = word + " "
                    if temp_chunk:
                        chunks.append(temp_chunk.strip())
                else:
                    current_chunk = section + "\n\n"
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _identify_validation_needs(self, round1_results: List[Dict]) -> List[Dict]:
        """Identify what data needs validation."""
        needs = []
        
        for idx, result in enumerate(round1_results):
            # Check for missing critical data
            if not result.get('cash_position'):
                needs.append({
                    'filing_idx': idx,
                    'data_type': 'cash_position',
                    'reason': 'missing'
                })
            
            if not result.get('quarterly_expenses') and not result.get('annual_expenses'):
                needs.append({
                    'filing_idx': idx,
                    'data_type': 'expenses',
                    'reason': 'missing'
                })
            
            # Check for low confidence extractions
            if result.get('confidence', 0) < 0.5:
                needs.append({
                    'filing_idx': idx,
                    'data_type': 'all',
                    'reason': 'low_confidence'
                })
        
        return needs
    
    def _extract_relevant_section(self, filing_text: str, data_type: str) -> str:
        """Extract the most relevant section for validation."""
        
        if data_type == 'cash_position':
            # Look for balance sheet or liquidity sections
            keywords = ['balance sheet', 'cash and cash equivalents', 'liquidity', 'financial position']
        elif data_type == 'expenses':
            # Look for income statement or cash flow
            keywords = ['operating expenses', 'cash flow', 'income statement', 'costs and expenses']
        else:
            keywords = ['financial', 'cash', 'liquidity']
        
        # Find sections containing keywords
        lines = filing_text.split('\n')
        relevant_lines = []
        
        for i, line in enumerate(lines):
            lower_line = line.lower()
            if any(keyword in lower_line for keyword in keywords):
                # Get context around the keyword
                start = max(0, i - 50)
                end = min(len(lines), i + 50)
                relevant_lines.extend(lines[start:end])
        
        return '\n'.join(relevant_lines[:3000])  # Limit size
    
    def _create_validation_prompt(
        self, need: Dict, section: str, original_result: Dict
    ) -> str:
        """Create a validation prompt for specific data."""
        
        data_type = need['data_type']
        
        return f"""You are validating {data_type} from a financial filing.

ORIGINAL EXTRACTION:
{json.dumps(original_result, indent=2)}

RELEVANT SECTION:
{section}

Please verify or correct the {data_type} with high precision.
Look for specific numbers and exact financial data.

Return ONLY this JSON:
{{
    "validated_{data_type}": [exact number or null],
    "validation_confidence": [0.0-1.0],
    "supporting_quote": "exact quote from text"
}}"""
    
    def _assess_analysis_quality(
        self, round1: List[Dict], round2: Dict, final: Dict
    ) -> float:
        """Assess overall quality of the analysis."""
        
        quality_score = 0.0
        
        # Check data completeness
        if final.get('cash_position'):
            quality_score += 0.2
        if final.get('monthly_burn_rate'):
            quality_score += 0.2
        if final.get('cash_runway_months'):
            quality_score += 0.1
        
        # Check consistency across rounds
        if round2.get('validated_items', 0) > 0:
            quality_score += 0.1
        
        # Check confidence levels
        avg_confidence = np.mean([r.get('confidence', 0.5) for r in round1])
        quality_score += avg_confidence * 0.2
        
        # Check for critique insights
        if final.get('critique_insights'):
            quality_score += 0.1
        
        # Data recency
        if round1:
            latest_date = max(r['filing_date'] for r in round1)
            days_old = (pd.to_datetime('today') - pd.to_datetime(latest_date)).days
            if days_old < 90:
                quality_score += 0.1
        
        return min(quality_score, 1.0)
    
    def _parse_json_response(self, response: str) -> Optional[Dict]:
        """Parse JSON response from LLM."""
        try:
            # Find JSON in response
            import re
            json_match = re.search(r'\{[\s\S]*\}', response)
            if json_match:
                return json.loads(json_match.group())
        except Exception as e:
            logger.error(f"Failed to parse JSON response: {e}")
        return None
    
    def _get_recent_filings(
        self, symbol: str, analysis_date: str, lookback_days: int
    ) -> pd.DataFrame:
        """Get recent SEC filings."""
        start_date = (
            pd.to_datetime(analysis_date) - timedelta(days=lookback_days)
        ).strftime('%Y-%m-%d')
        
        filings = self.data_service.get_sec_filings(symbol, start_date, analysis_date)
        
        # Filter for ATM-relevant filing types (expanded for ATM detection)
        relevant_types = [
            '10-K', '10-Q', '8-K',  # Core financial reports
            'S-3', '424B5', 'S-1',  # Registration statements 
            '424B2', '424B3', '424B7',  # Prospectus supplements (ATM)
            'ATM', 'FWP',  # At-the-market and free writing prospectus
            '10-K/A', '10-Q/A',  # Amendments to quarterly/annual reports
            'POS AM'  # Post-effective amendments
        ]
        filings = filings[filings['form_type'].isin(relevant_types)]
        
        return filings.sort_values('filed_at', ascending=False)
    
    def _get_filing_text(self, filing: pd.Series) -> Optional[str]:
        """Get full filing text."""
        from utils.filing_text_cache import FilingTextCache
        cache = FilingTextCache()
        
        # Get filing text with proper arguments
        filing_text, _ = cache.get_filing_text(
            filing_id=filing.get('id', 0),  # Use 0 if no id available
            accession_number=filing['accession_number'],
            filing_url=filing.get('filing_url'),
            symbol=filing.get('symbol'),
            form_type=filing.get('form_type')
        )
        return filing_text
    
    def _aggregate_chunk_results(self, chunk_results: List[Dict], filing: pd.Series) -> Dict:
        """Aggregate results from multiple chunks."""
        
        # Get the most confident values
        cash_positions = [r['cash_position'] for r in chunk_results if r.get('cash_position')]
        expenses = [r['quarterly_expenses'] for r in chunk_results if r.get('quarterly_expenses')]
        revenues = [r['annual_revenue'] for r in chunk_results if r.get('annual_revenue')]
        
        # Collect all key quotes
        all_quotes = []
        for r in chunk_results:
            all_quotes.extend(r.get('key_quotes', []))
        
        # Check for ATM mentions
        has_atm = any(r.get('has_atm_mentions', False) for r in chunk_results)
        atm_capacities = [r['atm_capacity'] for r in chunk_results if r.get('atm_capacity')]
        
        # Average confidence
        confidences = [r.get('confidence', 0.5) for r in chunk_results]
        avg_confidence = np.mean(confidences) if confidences else 0.5
        
        return {
            'filing_type': filing['form_type'],
            'filing_date': filing['filed_at'].strftime('%Y-%m-%d'),
            'cash_position': max(cash_positions) if cash_positions else None,
            'quarterly_expenses': max(expenses) if expenses else None,
            'annual_revenue': max(revenues) if revenues else None,
            'has_atm_mentions': has_atm,
            'atm_capacity': max(atm_capacities) if atm_capacities else None,
            'key_quotes': all_quotes[:5],  # Top 5 quotes
            'confidence': avg_confidence,
            'chunks_processed': len(chunk_results)
        }
    
    def _basic_aggregation(
        self, cash_data: List[Dict], expense_data: List[Dict], atm_data: List[Dict]
    ) -> Dict[str, Any]:
        """Fallback basic aggregation if critique fails."""
        
        latest_cash = cash_data[-1]['amount'] if cash_data else 0
        avg_expense = np.mean([e['amount'] for e in expense_data]) if expense_data else 0
        monthly_burn = avg_expense / 3 if avg_expense else 0
        
        runway = latest_cash / monthly_burn if monthly_burn > 0 else 999
        
        has_atm = len(atm_data) > 0
        atm_prob = 0.8 if has_atm and runway < 6 else 0.3 if runway < 12 else 0.1
        
        risk = 'HIGH' if runway < 6 else 'MEDIUM' if runway < 12 else 'LOW'
        
        return {
            'cash_position': latest_cash,
            'monthly_burn_rate': monthly_burn,
            'cash_runway_months': runway,
            'atm_probability': atm_prob,
            'risk_category': risk,
            'confidence': 0.6,
            'critique_insights': ['Fallback analysis - critique failed'],
            'data_quality_score': 0.5
        }