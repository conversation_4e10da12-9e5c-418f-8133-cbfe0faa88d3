"""
ReAct-based Comprehensive ATM Strategy Analyzer

Key optimization: Extract data from filings first, then do ReAct analysis on reduced data
1. Fast extraction phase - pull key financial metrics from all filings
2. ReAct analysis phase - analyze the aggregated data for ATM risk
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tuple
import time
import re
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

from core.data_service import DataService as ComprehensiveDataService
from utils.filing_cache_manager import FilingCacheManager
from utils.filing_text_cache import FilingTextCache
from utils.backtest_aware_llm_cache import BacktestAwareLLMCache
from core.logger import get_logger
import litellm

logger = get_logger(__name__)

# Configure LiteLLM
os.environ["GEMINI_API_KEY"] = os.getenv("GEMINI_API_KEY", "")
litellm.set_verbose = False


class ReactATMAnalyzer:
    """
    ReAct-based ATM analyzer using two-phase approach:
    1. Extract key data from all filings
    2. Run ReAct analysis on the extracted data
    """

    def __init__(self, max_workers: int = 4):
        self.data_service = ComprehensiveDataService()
        self.cache_manager = FilingCacheManager()
        self.text_cache = FilingTextCache()
        self.llm_cache = BacktestAwareLLMCache()
        self.max_workers = max_workers

        # Validate requirements
        if not os.getenv("GEMINI_API_KEY"):
            raise ValueError("GEMINI_API_KEY required for filing analysis")

        logger.info(f"ReAct ATM Analyzer initialized with {max_workers} workers")

    def analyze_atm_risk(
        self, symbol: str, analysis_date: str = None, lookback_days: int = 730, 
        as_of_date: str = None, fundamentals_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        ReAct-based ATM risk analysis.
        """
        if analysis_date is None:
            analysis_date = datetime.now().strftime("%Y-%m-%d")
        
        if as_of_date is None:
            as_of_date = analysis_date

        start_time = datetime.now()

        logger.info(
            f"Starting ReAct ATM analysis for {symbol} as of {analysis_date}"
        )

        try:
            # Step 1: Get SEC filings
            end_date = pd.to_datetime(analysis_date)
            start_date = end_date - timedelta(days=lookback_days)
            
            filings = self.data_service.get_sec_filings(
                symbol, 
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )

            if filings.empty:
                raise ValueError(f"CRITICAL: No SEC filings found for {symbol}")

            # Step 2: Extract key data from filings (fast phase)
            logger.info(f"Phase 1: Extracting data from {len(filings)} filings...")
            extracted_data = self._extract_filing_data_parallel(filings, symbol, as_of_date)
            
            if not extracted_data:
                raise ValueError(f"CRITICAL: No data extracted from filings for {symbol}")
            
            logger.info(f"Extracted data from {len(extracted_data)} filings")

            # Step 3: Run ReAct analysis on aggregated data
            logger.info("Phase 2: Running ReAct analysis on extracted data...")
            final_assessment = self._react_analysis(
                symbol=symbol,
                analysis_date=analysis_date,
                extracted_data=extracted_data,
                as_of_date=as_of_date,
                fundamentals_data=fundamentals_data
            )

            processing_time = (datetime.now() - start_time).total_seconds()
            final_assessment["processing_time"] = processing_time

            logger.info(
                f"Completed ReAct ATM analysis for {symbol} in {processing_time:.1f}s"
            )
            return final_assessment

        except Exception as e:
            logger.error(f"ATM analysis failed for {symbol}: {e}")
            raise ValueError(f"CRITICAL: ATM analysis failed for {symbol} - {e}")

    def _extract_filing_data_parallel(
        self, filings: pd.DataFrame, symbol: str, as_of_date: str
    ) -> List[Dict[str, Any]]:
        """Extract key data from filings in parallel."""
        
        extracted_data = []
        
        # Limit to most recent 6 filings for speed
        filings = filings.head(6)
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all extraction tasks
            future_to_filing = {}
            
            for _, filing in filings.iterrows():
                future = executor.submit(
                    self._extract_single_filing_data,
                    filing, symbol
                )
                future_to_filing[future] = filing
            
            # Collect results with timeout
            for future in future_to_filing:
                filing = future_to_filing[future]
                try:
                    # 15 second timeout per filing extraction (chunked LLM needs more time)
                    result = future.result(timeout=15)
                    if result:
                        extracted_data.append(result)
                        logger.info(f"Successfully extracted data from {filing['form_type']}")
                except FutureTimeoutError:
                    logger.error(
                        f"CRITICAL: Timeout extracting data from {filing['form_type']}"
                    )
                    # Continue to try other filings but log as error
                except Exception as e:
                    logger.error(
                        f"CRITICAL: Failed to extract data from {filing['form_type']}: {e}"
                    )
                    # Continue to try other filings but log as error
        
        # Ensure we have sufficient data from extraction
        if len(extracted_data) < 2:
            raise ValueError(
                f"CRITICAL: Insufficient data extracted - only {len(extracted_data)} "
                f"out of {len(filings)} filings processed successfully. "
                f"Need at least 2 filings for trend analysis."
            )
        
        return extracted_data

    def _extract_single_filing_data(
        self, filing: pd.Series, symbol: str
    ) -> Optional[Dict[str, Any]]:
        """Extract key financial data from a single filing using LLM in chunks."""
        
        try:
            # Get filing text
            filing_text = self._get_filing_text(filing)
            if not filing_text or len(filing_text) < 1000:
                raise ValueError(f"CRITICAL: Insufficient filing text for {filing.get('form_type')} - only {len(filing_text) if filing_text else 0} chars")
            
            filing_date = filing['filed_at'].strftime('%Y-%m-%d') if hasattr(filing['filed_at'], 'strftime') else str(filing['filed_at'])
            
            # Check cache first
            cached_result = self.llm_cache.get_as_of_date(
                symbol=symbol,
                analysis_type="filing_extraction",
                filing_date=filing_date,
                prompt=filing_text[:1000],  # Use first 1000 chars as identifier
                as_of_date=filing_date,
                model="gemini/gemini-1.5-flash"
            )
            
            if cached_result:
                logger.info(f"Using cached extraction for {filing['form_type']}")
                return cached_result
            
            # Extract using LLM with chunked approach
            extracted_data = self._extract_with_chunked_llm(filing_text, filing, symbol)
            
            if extracted_data:
                # Cache the result
                self.llm_cache.set(
                    symbol=symbol,
                    analysis_type="filing_extraction",
                    filing_date=filing_date,
                    prompt=filing_text[:1000],
                    response=extracted_data,
                    analysis_date=filing_date,
                    model="gemini/gemini-1.5-flash"
                )
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Error extracting data from {filing['form_type']}: {e}")
            raise ValueError(f"CRITICAL: Failed to extract data from {filing['form_type']} - {e}")

    def _extract_with_chunked_llm(self, filing_text: str, filing: pd.Series, symbol: str) -> Optional[Dict[str, Any]]:
        """Extract data using LLM with chunked conversation approach."""
        
        filing_date = filing['filed_at'].strftime('%Y-%m-%d') if hasattr(filing['filed_at'], 'strftime') else str(filing['filed_at'])
        form_type = filing.get('form_type', '10-Q')
        
        # Import enhanced prompts
        try:
            from analysis.enhanced_extraction_prompts import get_extraction_prompt, get_aggregation_prompt
            extraction_prompt = get_extraction_prompt(form_type, symbol, filing_date)
        except ImportError:
            logger.warning("Enhanced extraction prompts not available, using basic prompt")
            extraction_prompt = self._get_basic_extraction_prompt(form_type, symbol, filing_date)
        
        # Split filing into chunks (8000 chars each to stay under token limits)
        chunk_size = 8000
        chunks = [filing_text[i:i+chunk_size] for i in range(0, len(filing_text), chunk_size)]
        
        # Start conversation with extraction prompt
        conversation = [
            {
                "role": "user", 
                "content": extraction_prompt
            }
        ]
        
        # Process chunks in conversation
        all_extractions = []
        
        for i, chunk in enumerate(chunks[:5]):  # Limit to first 5 chunks for speed
            try:
                # Add user message with chunk
                conversation.append({
                    "role": "user",
                    "content": f"Chunk {i+1}/{min(len(chunks), 5)}:\n\n{chunk}"
                })
                
                # Get LLM response
                response = litellm.completion(
                    model="gemini/gemini-1.5-flash",
                    messages=conversation,
                    max_tokens=300,
                    temperature=0.1,
                    timeout=10
                )
                
                response_text = response.choices[0].message.content.strip()
                
                # Parse JSON response
                json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
                if json_match:
                    chunk_data = json.loads(json_match.group())
                    all_extractions.append(chunk_data)
                
                # Add assistant response to conversation
                conversation.append({
                    "role": "assistant",
                    "content": response_text
                })
                
                logger.info(f"Processed chunk {i+1} for {filing['form_type']}")
                
            except Exception as e:
                logger.warning(f"Failed to process chunk {i+1}: {e}")
                continue
        
        if not all_extractions:
            raise ValueError(f"CRITICAL: Failed to extract any data from {filing['form_type']} chunks")
        
        # Aggregate results from all chunks with enhanced prompt
        return self._aggregate_chunk_extractions_enhanced(all_extractions, filing, filing_date, form_type)
    
    def _aggregate_chunk_extractions(self, extractions: List[Dict], filing: pd.Series, filing_date: str) -> Dict[str, Any]:
        """Aggregate extraction results from multiple chunks."""
        
        # Find best values across chunks
        cash_positions = [e.get('cash_position') for e in extractions if e.get('cash_position')]
        quarterly_expenses = [e.get('quarterly_expenses') for e in extractions if e.get('quarterly_expenses')]
        has_atm = any(e.get('has_atm_mentions', False) for e in extractions)
        shelf_capacities = [e.get('shelf_capacity') for e in extractions if e.get('shelf_capacity')]
        
        # Collect all key quotes
        all_quotes = []
        for e in extractions:
            if e.get('key_quotes'):
                all_quotes.extend(e['key_quotes'])
        
        return {
            "filing_type": filing["form_type"],
            "filed_date": filing_date,
            "accession_number": filing.get("accession_number", ""),
            "cash_position": cash_positions[-1] if cash_positions else None,  # Take most recent
            "quarterly_expenses": quarterly_expenses[-1] if quarterly_expenses else None,
            "has_atm_mentions": has_atm,
            "shelf_capacity": max(shelf_capacities) if shelf_capacities else None,
            "key_quotes": all_quotes[:5],  # Top 5 quotes
            "chunks_processed": len(extractions)
        }

    def _extract_key_sections(self, filing_text: str) -> str:
        """Extract only the most relevant sections for analysis."""
        
        sections = []
        
        # Financial position section
        for keyword in ["financial position", "liquidity", "cash flow", "balance sheet"]:
            idx = filing_text.lower().find(keyword)
            if idx != -1:
                sections.append(filing_text[max(0, idx-200):idx+2000])
        
        # Risk factors related to financing
        risk_idx = filing_text.lower().find("risk factor")
        if risk_idx != -1:
            sections.append(filing_text[risk_idx:risk_idx+3000])
        
        # Management discussion
        md_idx = filing_text.lower().find("management's discussion")
        if md_idx != -1:
            sections.append(filing_text[md_idx:md_idx+3000])
        
        # Combine and limit
        combined = "\n\n---\n\n".join(sections)
        return combined[:8000]  # Limit to 8k chars

    def _react_analysis(
        self, symbol: str, analysis_date: str, extracted_data: List[Dict], 
        as_of_date: str, fundamentals_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Run ReAct analysis on the extracted data."""
        
        # Check cache first
        cache_key = f"{symbol}_react_{analysis_date}_{len(extracted_data)}"
        
        # Try to get from backtest-aware cache
        filing_dates = [d['filed_date'] for d in extracted_data]
        latest_filing_date = max(filing_dates) if filing_dates else analysis_date
        
        cached_result = self.llm_cache.get_as_of_date(
            symbol=symbol,
            analysis_type="react_atm_analysis",
            filing_date=latest_filing_date,
            prompt=cache_key,
            as_of_date=as_of_date,
            model="gemini/gemini-1.5-flash"
        )
        
        if cached_result:
            logger.info(f"Using cached ReAct analysis for {symbol}")
            return self._format_cached_react_result(cached_result, extracted_data)
        
        # Prepare aggregated data for ReAct
        aggregated = self._aggregate_extracted_data(extracted_data)
        
        # Use token-aware conversation format
        try:
            from analysis.react_token_aware import TokenAwareReActAnalyzer
            token_analyzer = TokenAwareReActAnalyzer()
            
            # Analyze using conversation format
            result = token_analyzer.analyze_with_conversation(
                symbol, aggregated, extracted_data, fundamentals_data
            )
            
            # Cache the result
            self.llm_cache.set(
                symbol=symbol,
                analysis_type="react_atm_analysis",
                filing_date=latest_filing_date,
                prompt=cache_key,
                response=result,
                analysis_date=as_of_date,
                model="gemini/gemini-1.5-flash"
            )
            
            # Format final result  
            return self._format_react_result(result, extracted_data, aggregated, symbol)
            
        except ImportError:
            logger.warning("Token-aware analyzer not available, using standard prompt")
            # Fallback to original implementation
            prompt = self._create_react_prompt(symbol, aggregated, extracted_data, fundamentals_data)
            
            # Call LLM with ReAct approach
            response = litellm.completion(
                model="gemini/gemini-1.5-flash",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1500,
                temperature=0.1,
                timeout=20
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Parse ReAct response
            result = self._parse_react_response(response_text)
            
            # Cache the result
            self.llm_cache.set(
                symbol=symbol,
                analysis_type="react_atm_analysis",
                filing_date=latest_filing_date,
                prompt=cache_key,
                response=result,
                analysis_date=as_of_date,
                model="gemini/gemini-1.5-flash"
            )
            
            # Format final result
            return self._format_react_result(result, extracted_data, aggregated, symbol)
            
        except Exception as e:
            logger.error(f"ReAct analysis failed: {e}")
            # CRITICAL: Money is on the line - NO FALLBACKS, hard fail
            raise ValueError(f"CRITICAL: ReAct analysis failed for {symbol} - {e}")

    def _aggregate_extracted_data(self, extracted_data: List[Dict]) -> Dict[str, Any]:
        """Aggregate the extracted filing data."""
        
        # Extract and filter values safely
        cash_positions = []
        quarterly_expenses = []
        shelf_capacities = []
        
        for d in extracted_data:
            # Cash position - convert to float and filter None/invalid values
            cash_val = d.get('cash_position')
            if cash_val is not None:
                try:
                    cash_float = float(cash_val)
                    if not np.isnan(cash_float) and cash_float > 0:
                        cash_positions.append(cash_float)
                except (ValueError, TypeError):
                    pass
            
            # Quarterly expenses - convert to float and filter None/invalid values  
            expense_val = d.get('quarterly_expenses')
            if expense_val is not None:
                try:
                    expense_float = float(expense_val)
                    if not np.isnan(expense_float) and expense_float > 0:
                        quarterly_expenses.append(expense_float)
                except (ValueError, TypeError):
                    pass
            
            # Shelf capacity - convert to float and filter None/invalid values
            shelf_val = d.get('shelf_capacity')
            if shelf_val is not None:
                try:
                    shelf_float = float(shelf_val)
                    if not np.isnan(shelf_float) and shelf_float > 0:
                        shelf_capacities.append(shelf_float)
                except (ValueError, TypeError):
                    pass
        
        has_atm = any(d.get('has_atm_mentions', False) for d in extracted_data)
        
        # Get filing types and dates
        filing_types = [d['filing_type'] for d in extracted_data]
        filing_dates = [d['filed_date'] for d in extracted_data]
        
        # Calculate trends (ensure numeric values)
        if len(cash_positions) < 2:
            raise ValueError(f"CRITICAL: Insufficient cash position data for trend analysis - only {len(cash_positions)} data points")
        
        latest_cash = cash_positions[-1]
        earliest_cash = cash_positions[0]
        if latest_cash < earliest_cash * 0.8:
            cash_trend = "declining"
        elif latest_cash > earliest_cash * 1.2:
            cash_trend = "increasing"
        else:
            cash_trend = "stable"
        
        # Now we know these lists contain only valid float values
        valid_cash = cash_positions
        valid_expenses = quarterly_expenses
        
        # Ensure we have valid data before returning
        if not valid_cash:
            raise ValueError("CRITICAL: No valid cash position data extracted from filings")
        if not valid_expenses:
            raise ValueError("CRITICAL: No valid expense data extracted from filings")
        if not filing_dates:
            raise ValueError("CRITICAL: No filing dates found in extracted data")
        
        return {
            "latest_cash": valid_cash[-1],
            "avg_cash": np.mean(valid_cash),
            "cash_trend": cash_trend,
            "latest_quarterly_expense": valid_expenses[-1],
            "avg_quarterly_expense": np.mean(valid_expenses),
            "has_atm_program": has_atm,
            "max_shelf_capacity": max(shelf_capacities) if shelf_capacities else 0,
            "num_filings": len(extracted_data),
            "filing_types": list(set(filing_types)),
            "date_range": f"{min(filing_dates)} to {max(filing_dates)}"
        }

    def _create_react_prompt(self, symbol: str, aggregated: Dict, extracted_data: List[Dict], fundamentals_data: Dict[str, Any] = None) -> str:
        """Create ReAct prompt for analysis."""
        
        # Get key quotes from most recent filings
        key_insights = []
        for data in extracted_data[:3]:  # Top 3 most recent
            if 'key_quotes' in data and data['key_quotes']:
                quotes = "\n".join([f"- {quote}" for quote in data['key_quotes'][:3]])
                key_insights.append(f"\n{data['filing_type']} ({data['filed_date']}):\n{quotes}")
        
        combined_insights = "\n".join(key_insights)
        
        # Get required values - fail if not present
        latest_cash = aggregated['latest_cash']
        latest_expense = aggregated['latest_quarterly_expense']
        shelf_capacity = aggregated['max_shelf_capacity']
        
        # Get required aggregated values
        cash_trend = aggregated['cash_trend']
        has_atm_program = aggregated['has_atm_program']
        num_filings = aggregated['num_filings']
        date_range = aggregated['date_range']
        
        # Extract fundamentals data - require it for proper analysis
        if not fundamentals_data:
            raise ValueError("CRITICAL: Fundamentals data required for ATM analysis")
        
        shares_outstanding = fundamentals_data['shares_outstanding_millions']
        float_millions = fundamentals_data['float_millions'] 
        insider_percent = fundamentals_data['insider_percent']
        institutional_percent = fundamentals_data['institutional_percent']
        
        prompt = f"""
You are an expert financial analyst using ReAct (Reasoning and Acting) to assess ATM dilution risk for {symbol}.
This is for a gap-up ATM trading strategy targeting cash-burning small-caps that dilute during volume spikes.

COMPANY FUNDAMENTALS:
- Shares Outstanding: {shares_outstanding:.1f}M
- Float: {float_millions:.1f}M ({float_millions/shares_outstanding*100:.1f}% of outstanding)
- Insider Ownership: {insider_percent:.1f}%
- Institutional Ownership: {institutional_percent:.1f}%
- Float Analysis: {'LOW FLOAT - EXPLOSIVE POTENTIAL' if float_millions < 20 and (float_millions/shares_outstanding*100) < 30 else 'MODERATE FLOAT' if float_millions < 20 or (float_millions/shares_outstanding*100) < 40 else 'HIGH FLOAT - LIMITED GAP POTENTIAL'}

FINANCIAL POSITION:
- Latest Cash: ${latest_cash:,.0f}
- Cash Trend: {cash_trend}
- Quarterly Expenses: ${latest_expense:,.0f}
- Monthly Burn Rate: ${latest_expense/3:,.0f}

DILUTION CAPABILITY:
- ATM Program Active: {has_atm_program}
- Shelf Registration: ${shelf_capacity:,.0f}
- Limited Float Supply: {'YES - Only {:.1f}M shares available'.format(float_millions) if float_millions < 20 else 'NO - {:.1f}M shares (high liquidity)'.format(float_millions)}
- Filings Analyzed: {num_filings} ({date_range})

KEY FILING INSIGHTS:
{combined_insights[:3000] if combined_insights else "CRITICAL: No insights could be extracted from filings"}

REACT ANALYSIS STEPS:

Step 1 - REASON about cash runway:
Calculate months of runway. Consider: cash position, monthly burn, any revenue.

Step 2 - REASON about gap potential and dilution dynamics:
Analyze: 
- Float of {float_millions:.1f}M ({float_millions/shares_outstanding*100:.1f}% of outstanding)
- Low float (<20M shares, <30% ratio) = explosive gap potential but limited dilution capacity
- Insider ownership {insider_percent:.1f}% + Institutional {institutional_percent:.1f}% = {insider_percent + institutional_percent:.1f}% locked up
- Free float available for trading: {float_millions:.1f}M shares

Step 3 - ACT to determine ATM timing window:
Based on runway and burn rate, calculate when they'll need cash.
Consider: filing date + runway - 2 month buffer for safety.

Step 4 - REASON about ATM execution probability:
Synthesize: ATM facility status, float adequacy, insider incentives, cash burn urgency.
Score probability 0-1 based on all factors.

Step 5 - CRITIQUE your analysis:
What could go wrong? Are there alternative funding sources? Market conditions?

Step 6 - ACT to assign final risk assessment:
Combine all factors into HIGH/MEDIUM/LOW risk with confidence score.

RETURN ONLY THIS JSON:
{{
  "reasoning_steps": [
    {{"step": 1, "thought": "cash runway reasoning", "calculation": "show math", "runway_months": X}},
    {{"step": 2, "thought": "dilution incentives analysis", "insider_risk": "LOW/MEDIUM/HIGH", "float_adequacy": "YES/NO"}},
    {{"step": 3, "thought": "ATM timing reasoning", "predicted_date_range": "YYYY-MM to YYYY-MM"}},
    {{"step": 4, "thought": "probability reasoning", "factors": ["list key factors"], "probability_score": 0.X}},
    {{"step": 5, "thought": "critical analysis", "risks": ["risk 1", "risk 2"], "alternatives": ["funding option 1"]}},
    {{"step": 6, "thought": "final assessment", "conclusion": "synthesis of all factors"}}
  ],
  "cash_position": {latest_cash},
  "monthly_burn": X.X,
  "cash_runway_months": X,
  "predicted_atm_date": "YYYY-MM-DD",
  "atm_probability": 0.X,
  "risk_category": "LOW/MEDIUM/HIGH",
  "confidence": 0.X,
  "dilution_factors": {{
    "float_category": "LOW/MODERATE/HIGH",
    "explosive_gap_potential": true/false,
    "float_metrics": "X.XM shares (Y.Y% of outstanding)",
    "estimated_dilution_impact": "X.X% potential price impact"
  }},
  "key_insights": ["insight 1", "insight 2", "insight 3"]
}}
"""
        
        return prompt

    def _parse_react_response(self, response_text: str) -> Dict[str, Any]:
        """Parse ReAct response."""
        
        # Clean and extract JSON
        response_text = response_text.replace("```json", "").replace("```", "")
        
        json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
        if not json_match:
            raise ValueError("No JSON found in ReAct response")
        
        result = json.loads(json_match.group())
        
        # Validate required fields
        required = ["cash_position", "monthly_burn", "cash_runway_months", 
                   "predicted_atm_date", "atm_probability", "risk_category"]
        
        for field in required:
            if field not in result:
                raise ValueError(f"Missing required field: {field}")
        
        return result

    def _format_react_result(
        self, react_result: Dict, extracted_data: List[Dict], aggregated: Dict, symbol: str = None
    ) -> Dict[str, Any]:
        """Format ReAct result into final assessment."""
        
        # Extract reasoning summary
        reasoning_summary = ""
        if "reasoning_steps" in react_result:
            for step in react_result["reasoning_steps"]:
                if step.get("step") == 6:
                    reasoning_summary = step.get("conclusion", "")
                    break
        
        # Get symbol from react_result or aggregated data if not passed
        if not symbol:
            symbol = react_result.get('symbol', 'UNKNOWN')
        
        return {
            "symbol": symbol,
            "analysis_date": datetime.now().strftime("%Y-%m-%d"),
            "analysis_method": "ReAct",
            # Core metrics
            "latest_cash_position": react_result["cash_position"],
            "avg_monthly_burn": react_result["monthly_burn"],
            "estimated_runway_months": react_result["cash_runway_months"],
            "predicted_atm_date": react_result["predicted_atm_date"],
            # Risk assessment
            "atm_probability": react_result["atm_probability"],
            "risk_category": react_result["risk_category"],
            "has_active_atm": aggregated["has_atm_program"],
            "max_atm_capacity": aggregated["max_shelf_capacity"],
            # Analysis quality
            "filings_analyzed": len(extracted_data),
            "confidence_score": react_result["confidence"],
            "cash_trend": aggregated["cash_trend"],
            # Insights
            "key_insights": react_result["key_insights"],
            "reasoning_summary": reasoning_summary,
            "dilution_factors": react_result["dilution_factors"],
            # Summary
            "summary": f"ATM Risk: {react_result['risk_category']} "
                      f"({react_result['atm_probability']:.0%}). "
                      f"Analyzed {len(extracted_data)} filings using ReAct. "
                      f"Runway: {react_result['cash_runway_months']:.0f} months. "
                      f"ATM Date: {react_result['predicted_atm_date']}",
            # Metadata
            "analyzer": "react_comprehensive_v1",
            "version": "1.0.0",
            "from_cache": False
        }

    def _format_cached_react_result(self, cached_result: Dict, extracted_data: List[Dict]) -> Dict[str, Any]:
        """Format cached ReAct result."""
        
        # Remove cache metadata
        cache_meta = cached_result.pop('_cache_metadata', {})
        
        # Add current context
        result = cached_result.copy()
        result.update({
            "filings_analyzed": len(extracted_data),
            "from_cache": True,
            "cache_date": cache_meta.get('analysis_date')
        })
        
        return result


    def _get_filing_text(self, filing: pd.Series) -> str:
        """Get filing text from cache."""
        
        filing_id = filing.get('id') or filing.get('filing_id')
        accession_number = filing.get('accession_number', '')
        filing_url = filing.get('filing_url', '')
        symbol = filing.get('symbol', '')
        form_type = filing.get('form_type', '')
        
        if filing_id and accession_number:
            try:
                text, metadata = self.text_cache.get_filing_text(
                    filing_id=filing_id,
                    accession_number=accession_number,
                    filing_url=filing_url,
                    symbol=symbol,
                    form_type=form_type
                )
                
                if text:
                    return text
                    
            except Exception as e:
                logger.warning(f"Text cache error: {e}")
        
        return ""

    def _get_basic_extraction_prompt(self, form_type: str, symbol: str, filing_date: str) -> str:
        """Basic extraction prompt as fallback."""
        return f"""You are analyzing a {form_type} filing for {symbol} filed on {filing_date}.

I will send you the filing in chunks. For each chunk, extract:
1. Cash and cash equivalents (exact USD amount)
2. Operating expenses or cash burn (quarterly amount)
3. Any mentions of ATM programs or equity distribution agreements
4. Shelf registration capacity

Respond with ONLY this JSON format for each chunk:
{{
  "cash_position": [USD amount or null],
  "quarterly_expenses": [USD amount or null], 
  "has_atm_mentions": [true/false],
  "shelf_capacity": [USD amount or null],
  "key_quotes": ["relevant quote 1", "relevant quote 2"]
}}

Ready for first chunk?"""
    
    def _aggregate_chunk_extractions_enhanced(self, extractions: List[Dict], filing: pd.Series, filing_date: str, form_type: str) -> Dict[str, Any]:
        """Aggregate extraction results from multiple chunks using enhanced prompt."""
        
        try:
            from analysis.enhanced_extraction_prompts import get_aggregation_prompt
            
            # Use LLM to aggregate intelligently
            aggregation_prompt = get_aggregation_prompt(extractions, form_type)
            
            response = litellm.completion(
                model="gemini/gemini-1.5-flash",
                messages=[{"role": "user", "content": aggregation_prompt}],
                max_tokens=500,
                temperature=0.1,
                timeout=10
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Parse JSON response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                aggregated = json.loads(json_match.group())
                
                # Add filing metadata
                return {
                    "filing_type": filing["form_type"],
                    "filed_date": filing_date,
                    "accession_number": filing.get("accession_number", ""),
                    "cash_position": aggregated.get('cash_position'),
                    "quarterly_expenses": aggregated.get('quarterly_expenses'),
                    "has_atm_mentions": aggregated.get('has_atm_mentions', False),
                    "shelf_capacity": aggregated.get('shelf_capacity'),
                    "key_quotes": aggregated.get('key_insights', [])[:5],
                    "chunks_processed": len(extractions),
                    "confidence": aggregated.get('confidence', 0.5)
                }
            else:
                logger.warning("No JSON found in aggregation response, using manual aggregation")
                return self._aggregate_chunk_extractions(extractions, filing, filing_date)
                
        except ImportError:
            logger.warning("Enhanced aggregation not available, using manual aggregation")
            return self._aggregate_chunk_extractions(extractions, filing, filing_date)
        except Exception as e:
            logger.warning(f"Enhanced aggregation failed: {e}, using manual aggregation")
            return self._aggregate_chunk_extractions(extractions, filing, filing_date)

    def close(self):
        """Clean up resources."""
        self.data_service.close()


# Alias for compatibility
FinalATMAnalyzer = ReactATMAnalyzer