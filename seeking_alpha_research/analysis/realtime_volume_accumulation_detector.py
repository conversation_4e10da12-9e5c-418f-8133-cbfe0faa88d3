#!/usr/bin/env python3
"""
Real-Time Volume Accumulation Detector with Backtesting Support

This module provides sophisticated volume analysis that:
1. Works in real-time with minute-by-minute entry signals
2. Prevents look-ahead bias in backtesting (no future data)
3. Uses a rolling window approach for continuous monitoring
4. Provides precise entry timestamps when accumulation threshold is met

NO FAKES, NO MOCKS - Real-time analysis for real trading decisions.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
from collections import deque

from core.data_service import DataService

logger = logging.getLogger(__name__)


class RealtimeVolumeAccumulationDetector:
    """
    Sophisticated real-time volume accumulation detector with precise entry signals.
    
    Key Features:
    - Rolling window analysis (no look-ahead bias)
    - Minute-level entry signals
    - Real-time and backtest modes
    - Accumulation strength scoring
    - Multiple confirmation signals
    """
    
    def __init__(self, data_service: DataService = None):
        self.data_service = data_service or DataService()
        
        # Thresholds for entry signals
        self.accumulation_threshold = 0.5  # Minimum score to trigger entry
        self.confirmation_window = 60  # Minutes to confirm signal
        self.lookback_window = 14  # Days for rolling analysis
        
    def detect_realtime_entry(
        self, 
        symbol: str, 
        current_time: datetime,
        mode: str = 'backtest'
    ) -> Dict[str, Any]:
        """
        Detect if current time is a valid entry point based on accumulation.
        
        For backtesting: Only uses data up to current_time (no future data)
        For real-time: Uses latest available data
        
        Returns:
            Dict with entry signal, confidence, and supporting data
        """
        logger.info(f"Checking entry signal for {symbol} at {current_time} (mode: {mode})")
        
        # Ensure we don't look into the future during backtesting
        if mode == 'backtest':
            data_end = current_time
        else:
            data_end = datetime.now()
            
        data_start = data_end - timedelta(days=self.lookback_window + 5)
        
        try:
            # Get minute data up to current point ONLY
            minute_bars = self.data_service.get_minute_bars(
                symbol,
                data_start.strftime('%Y-%m-%d'),
                data_end.strftime('%Y-%m-%d')
            )
            
            if minute_bars.empty:
                return self._no_signal_result("No data available")
            
            # Filter to ensure no future data in backtest mode
            if mode == 'backtest':
                minute_bars = minute_bars[minute_bars.index <= current_time]
            
            # Run rolling window analysis
            signal = self._analyze_rolling_window(minute_bars, current_time)
            
            # Check for entry conditions
            if signal['accumulation_score'] >= self.accumulation_threshold:
                # Confirm signal strength
                confirmation = self._confirm_entry_signal(minute_bars, current_time)
                
                if confirmation['confirmed']:
                    return {
                        'entry_signal': True,
                        'entry_time': current_time,
                        'accumulation_score': signal['accumulation_score'],
                        'confidence': confirmation['confidence'],
                        'signal_strength': signal['signal_strength'],
                        'supporting_indicators': signal['indicators'],
                        'entry_reasons': confirmation['reasons'],
                        'risk_factors': confirmation['risk_factors'],
                        'suggested_position_size': self._calculate_position_size(
                            signal['accumulation_score'],
                            confirmation['confidence']
                        )
                    }
            
            # No entry signal
            return {
                'entry_signal': False,
                'entry_time': current_time,
                'accumulation_score': signal['accumulation_score'],
                'reason': 'Accumulation below threshold',
                'next_check': current_time + timedelta(minutes=15)
            }
            
        except Exception as e:
            logger.error(f"Real-time entry detection failed: {e}")
            return self._no_signal_result(f"Error: {e}")
    
    def backtest_entry_points(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        check_interval_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Find all historical entry points for backtesting.
        
        Scans through historical data minute by minute to find
        when accumulation signals would have triggered.
        
        NO LOOK-AHEAD BIAS: Each point only uses past data.
        """
        logger.info(f"Backtesting entry points for {symbol} from {start_date} to {end_date}")
        
        entry_points = []
        
        # Parse dates
        current = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        
        # Get all minute data for the period (with buffer)
        all_start = current - timedelta(days=self.lookback_window + 5)
        minute_bars = self.data_service.get_minute_bars(
            symbol,
            all_start.strftime('%Y-%m-%d'),
            end.strftime('%Y-%m-%d')
        )
        
        if minute_bars.empty:
            logger.warning(f"No data available for backtesting {symbol}")
            return entry_points
        
        # Scan through time checking for entry signals
        check_count = 0
        last_entry = None
        
        while current <= end:
            # Skip weekends and non-trading hours
            if current.weekday() >= 5:  # Weekend
                current += timedelta(days=1)
                continue
                
            if current.hour < 9 or current.hour >= 16:  # Outside market hours
                current += timedelta(hours=1)
                continue
            
            # Don't check too frequently after recent entry
            if last_entry and (current - last_entry) < timedelta(hours=4):
                current += timedelta(minutes=check_interval_minutes)
                continue
            
            # Check for entry signal at this point
            signal = self.detect_realtime_entry(symbol, current, mode='backtest')
            
            if signal['entry_signal']:
                entry_points.append(signal)
                last_entry = current
                logger.info(f"Entry signal found at {current}: score={signal['accumulation_score']:.2f}")
            
            # Move to next check point
            current += timedelta(minutes=check_interval_minutes)
            check_count += 1
            
            # Progress update
            if check_count % 100 == 0:
                logger.debug(f"Checked {check_count} points, found {len(entry_points)} entries")
        
        logger.info(f"Backtest complete: {check_count} checks, {len(entry_points)} entry points")
        
        return entry_points
    
    def _analyze_rolling_window(
        self, 
        minute_bars: pd.DataFrame, 
        current_time: datetime
    ) -> Dict[str, Any]:
        """
        Analyze accumulation using only data up to current time.
        
        Uses multiple indicators in a rolling window fashion.
        """
        # Get recent window
        window_start = current_time - timedelta(days=self.lookback_window)
        window_data = minute_bars[
            (minute_bars.index >= window_start) & 
            (minute_bars.index <= current_time)
        ]
        
        if len(window_data) < 100:  # Need minimum data
            return {
                'accumulation_score': 0.0,
                'signal_strength': 'none',
                'indicators': {}
            }
        
        indicators = {}
        scores = []
        
        # 1. Volume trend analysis
        volume_trend = self._analyze_volume_trend(window_data)
        indicators['volume_trend'] = volume_trend
        scores.append(volume_trend['score'])
        
        # 2. Price-volume divergence
        pv_divergence = self._analyze_price_volume_divergence(window_data)
        indicators['pv_divergence'] = pv_divergence
        scores.append(pv_divergence['score'])
        
        # 3. Accumulation patterns
        patterns = self._detect_accumulation_patterns(window_data)
        indicators['patterns'] = patterns
        scores.append(patterns['score'])
        
        # 4. Smart money flow
        smart_money = self._analyze_smart_money_flow(window_data)
        indicators['smart_money'] = smart_money
        scores.append(smart_money['score'])
        
        # 5. Time-of-day analysis
        tod_analysis = self._analyze_time_of_day_patterns(window_data)
        indicators['time_of_day'] = tod_analysis
        scores.append(tod_analysis['score'])
        
        # Calculate overall score
        overall_score = np.mean(scores)
        
        # Determine signal strength
        if overall_score >= 0.7:
            signal_strength = 'strong'
        elif overall_score >= 0.5:
            signal_strength = 'moderate'
        elif overall_score >= 0.3:
            signal_strength = 'weak'
        else:
            signal_strength = 'none'
        
        return {
            'accumulation_score': overall_score,
            'signal_strength': signal_strength,
            'indicators': indicators,
            'timestamp': current_time
        }
    
    def _analyze_volume_trend(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze volume trends for accumulation signals."""
        # Daily volume aggregation
        daily_volume = data.resample('D')['volume'].agg(['sum', 'mean', 'std'])
        
        if len(daily_volume) < 5:
            return {'score': 0.0, 'trend': 'insufficient_data'}
        
        # Recent vs historical volume
        recent_days = 3
        recent_vol = daily_volume['sum'].iloc[-recent_days:].mean()
        historical_vol = daily_volume['sum'].iloc[:-recent_days].mean()
        
        volume_ratio = recent_vol / historical_vol if historical_vol > 0 else 1.0
        
        # Volume increasing on flat/declining price = accumulation
        price_change = (data['close'].iloc[-1] - data['close'].iloc[0]) / data['close'].iloc[0]
        
        score = 0.0
        if volume_ratio > 1.5 and abs(price_change) < 0.1:
            score = 0.8
        elif volume_ratio > 1.2 and price_change < 0.05:
            score = 0.6
        elif volume_ratio > 1.0:
            score = 0.4
        
        return {
            'score': score,
            'volume_ratio': volume_ratio,
            'price_change': price_change,
            'trend': 'increasing' if volume_ratio > 1.2 else 'stable'
        }
    
    def _analyze_price_volume_divergence(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Detect price-volume divergences indicating accumulation."""
        # Calculate correlation in rolling windows
        window = 60  # 1 hour
        
        correlations = []
        for i in range(window, len(data), 30):
            window_data = data.iloc[i-window:i]
            corr = window_data['close'].pct_change().corr(window_data['volume'])
            correlations.append(corr)
        
        if not correlations:
            return {'score': 0.0, 'divergence': 'no_data'}
        
        # Negative correlation = divergence (accumulation)
        avg_correlation = np.mean(correlations)
        
        score = 0.0
        if avg_correlation < -0.3:
            score = 0.9
        elif avg_correlation < -0.1:
            score = 0.6
        elif avg_correlation < 0:
            score = 0.3
        
        return {
            'score': score,
            'correlation': avg_correlation,
            'divergence': 'strong' if score > 0.6 else 'weak'
        }
    
    def _detect_accumulation_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Detect specific accumulation patterns."""
        patterns_found = []
        scores = []
        
        # Pattern 1: Afternoon accumulation
        data['hour'] = data.index.hour
        afternoon_data = data[data['hour'] >= 13]
        morning_data = data[data['hour'] < 13]
        
        if len(afternoon_data) > 0 and len(morning_data) > 0:
            afternoon_vol = afternoon_data['volume'].mean()
            morning_vol = morning_data['volume'].mean()
            
            if afternoon_vol > morning_vol * 1.3:
                patterns_found.append('afternoon_accumulation')
                scores.append(0.7)
        
        # Pattern 2: Low volume drift up
        daily_data = data.resample('D').agg({
            'close': 'last',
            'volume': 'sum'
        })
        
        if len(daily_data) >= 5:
            price_trend = np.polyfit(range(len(daily_data)), daily_data['close'], 1)[0]
            volume_trend = np.polyfit(range(len(daily_data)), daily_data['volume'], 1)[0]
            
            if price_trend > 0 and volume_trend < 0:
                patterns_found.append('low_volume_drift')
                scores.append(0.8)
        
        # Pattern 3: Consolidation with volume spikes
        price_range = (data['high'].max() - data['low'].min()) / data['close'].mean()
        volume_spikes = len(data[data['volume'] > data['volume'].mean() * 2])
        
        if price_range < 0.05 and volume_spikes > 5:
            patterns_found.append('consolidation_accumulation')
            scores.append(0.6)
        
        final_score = np.mean(scores) if scores else 0.0
        
        return {
            'score': final_score,
            'patterns': patterns_found,
            'pattern_count': len(patterns_found)
        }
    
    def _analyze_smart_money_flow(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze smart money flow indicators."""
        # Large trades (block trades)
        avg_trade_size = data['volume'].mean()
        large_trades = data[data['volume'] > avg_trade_size * 3]
        
        # Smart money typically trades at specific times
        smart_hours = [9, 10, 14, 15]  # First and last hours
        smart_time_trades = large_trades[large_trades.index.hour.isin(smart_hours)]
        
        score = 0.0
        if len(large_trades) > 0:
            smart_ratio = len(smart_time_trades) / len(large_trades)
            
            if smart_ratio > 0.6:
                score = 0.8
            elif smart_ratio > 0.4:
                score = 0.6
            else:
                score = 0.3
        
        return {
            'score': score,
            'large_trades': len(large_trades),
            'smart_time_ratio': len(smart_time_trades) / len(large_trades) if len(large_trades) > 0 else 0,
            'assessment': 'institutional' if score > 0.6 else 'retail'
        }
    
    def _analyze_time_of_day_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze intraday patterns for accumulation."""
        data['hour'] = data.index.hour
        hourly_volume = data.groupby('hour')['volume'].agg(['mean', 'std'])
        
        # Look for consistent patterns
        morning_hours = [10, 11]
        afternoon_hours = [14, 15]
        
        morning_vol = hourly_volume.loc[morning_hours, 'mean'].mean()
        afternoon_vol = hourly_volume.loc[afternoon_hours, 'mean'].mean()
        
        # U-shaped volume is normal, but skewed patterns indicate accumulation
        score = 0.0
        if afternoon_vol > morning_vol * 1.5:
            score = 0.7
        elif afternoon_vol > morning_vol * 1.2:
            score = 0.5
        
        return {
            'score': score,
            'morning_avg': morning_vol,
            'afternoon_avg': afternoon_vol,
            'pattern': 'afternoon_bias' if score > 0.5 else 'normal'
        }
    
    def _confirm_entry_signal(
        self, 
        data: pd.DataFrame, 
        signal_time: datetime
    ) -> Dict[str, Any]:
        """
        Confirm entry signal with additional checks.
        
        This prevents false positives by requiring multiple confirmations.
        """
        # Get recent data around signal time
        confirm_window = timedelta(minutes=self.confirmation_window)
        confirm_start = signal_time - confirm_window
        confirm_data = data[
            (data.index >= confirm_start) & 
            (data.index <= signal_time)
        ]
        
        confirmations = []
        risk_factors = []
        
        # Check 1: Volume consistency
        if len(confirm_data) > 30:
            recent_vol = confirm_data['volume'].iloc[-10:].mean()
            earlier_vol = confirm_data['volume'].iloc[:-10].mean()
            
            if recent_vol > earlier_vol:
                confirmations.append("Volume increasing into signal")
            else:
                risk_factors.append("Volume declining")
        
        # Check 2: Price stability
        price_volatility = confirm_data['close'].pct_change().std()
        if price_volatility < 0.005:  # Less than 0.5% volatility
            confirmations.append("Price stable during accumulation")
        else:
            risk_factors.append("High price volatility")
        
        # Check 3: No major gaps
        gaps = (confirm_data['open'] - confirm_data['close'].shift(1)) / confirm_data['close'].shift(1)
        max_gap = gaps.abs().max()
        
        if max_gap < 0.02:  # No gaps > 2%
            confirmations.append("No disruptive gaps")
        else:
            risk_factors.append(f"Gap detected: {max_gap:.1%}")
        
        # Calculate confidence
        base_confidence = 0.5
        confidence = base_confidence + (len(confirmations) * 0.15) - (len(risk_factors) * 0.1)
        confidence = max(0, min(1, confidence))
        
        return {
            'confirmed': len(confirmations) >= 2,
            'confidence': confidence,
            'reasons': confirmations,
            'risk_factors': risk_factors
        }
    
    def _calculate_position_size(self, accumulation_score: float, confidence: float) -> float:
        """
        Calculate recommended position size based on signal strength.
        
        Conservative sizing to protect capital.
        """
        # Base position size (% of portfolio)
        base_size = 2.0
        
        # Adjust based on accumulation score
        if accumulation_score >= 0.8:
            size_multiplier = 1.5
        elif accumulation_score >= 0.6:
            size_multiplier = 1.0
        else:
            size_multiplier = 0.5
        
        # Adjust based on confidence
        confidence_multiplier = 0.5 + (confidence * 0.5)
        
        final_size = base_size * size_multiplier * confidence_multiplier
        
        # Cap at maximum position size
        return min(final_size, 5.0)
    
    def _no_signal_result(self, reason: str) -> Dict[str, Any]:
        """Return standardized no-signal result."""
        return {
            'entry_signal': False,
            'accumulation_score': 0.0,
            'reason': reason,
            'confidence': 0.0
        }


def backtest_example():
    """Example of using the detector for backtesting."""
    detector = RealtimeVolumeAccumulationDetector()
    
    # Find all entry points in a historical period
    entries = detector.backtest_entry_points(
        symbol='PLUG',
        start_date='2024-01-01',
        end_date='2024-03-31',
        check_interval_minutes=60  # Check every hour
    )
    
    print(f"Found {len(entries)} entry points:")
    for entry in entries:
        if entry['entry_signal']:
            print(f"  {entry['entry_time']}: Score={entry['accumulation_score']:.2f}, "
                  f"Confidence={entry['confidence']:.1%}, "
                  f"Position={entry['suggested_position_size']:.1f}%")


def realtime_example():
    """Example of using the detector in real-time."""
    detector = RealtimeVolumeAccumulationDetector()
    
    # Check current moment for entry signal
    signal = detector.detect_realtime_entry(
        symbol='PLUG',
        current_time=datetime.now(),
        mode='realtime'
    )
    
    if signal['entry_signal']:
        print(f"ENTRY SIGNAL DETECTED!")
        print(f"  Accumulation Score: {signal['accumulation_score']:.2f}")
        print(f"  Confidence: {signal['confidence']:.1%}")
        print(f"  Position Size: {signal['suggested_position_size']:.1f}%")
        print(f"  Reasons: {', '.join(signal['entry_reasons'])}")
    else:
        print(f"No entry signal. Score: {signal['accumulation_score']:.2f}")
        print(f"  Reason: {signal['reason']}")


if __name__ == "__main__":
    # Run examples
    print("=== Backtest Example ===")
    backtest_example()
    
    print("\n=== Real-time Example ===")
    realtime_example()