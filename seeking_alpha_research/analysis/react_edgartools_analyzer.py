#!/usr/bin/env python3
"""
ReAct ATM Analyzer using EdgarTools Native XBRL Parsing
NO FAKES, NO MOCKS - Real financial data from real SEC filings

Uses EdgarTools' built-in XBRL parsing to extract financial data directly,
eliminating the $0 extraction issues from LLM-based parsing.
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
import time
from concurrent.futures import Thread<PERSON>oolExecutor, TimeoutError as FutureTimeoutError

from core.data_service import DataService as ComprehensiveDataService
from utils.filing_cache_manager import FilingCacheManager
from utils.backtest_aware_llm_cache import BacktestAwareLLMCache
from core.logger import get_logger
import litellm

# EdgarTools imports
try:
    from edgar import Company, set_identity, XBRL
    from edgar.xbrl import XBRLS
    EDGARTOOLS_AVAILABLE = True
    # Set SEC identity (required for EdgarTools)
    set_identity('<EMAIL>')
except ImportError:
    EDGARTOOLS_AVAILABLE = False
    print("CRITICAL: EdgarTools not available - cannot proceed")

logger = get_logger(__name__)

# Configure LiteLLM
os.environ["GEMINI_API_KEY"] = os.getenv("GEMINI_API_KEY", "")
litellm.set_verbose = False


class ReactEdgarToolsAnalyzer:
    """
    ReAct-based ATM analyzer using EdgarTools native XBRL parsing.
    
    Key improvements:
    1. Uses EdgarTools XBRL parsing for accurate financial data extraction
    2. No more $0 issues from LLM misunderstanding filing text
    3. Faster and more reliable than chunked LLM extraction
    """
    
    def __init__(self, max_workers: int = 4):
        self.data_service = ComprehensiveDataService()
        self.cache_manager = FilingCacheManager()
        self.llm_cache = BacktestAwareLLMCache()
        self.max_workers = max_workers
        
        # Validate requirements
        if not os.getenv("GEMINI_API_KEY"):
            raise ValueError("GEMINI_API_KEY required for ReAct analysis")
            
        if not EDGARTOOLS_AVAILABLE:
            raise ValueError("EdgarTools required for native XBRL parsing")
            
        logger.info(f"ReAct EdgarTools Analyzer initialized with {max_workers} workers")
    
    def analyze_atm_risk(
        self, symbol: str, analysis_date: str = None, lookback_days: int = 730, 
        as_of_date: str = None, fundamentals_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        ReAct-based ATM risk analysis using EdgarTools XBRL parsing.
        """
        if analysis_date is None:
            analysis_date = datetime.now().strftime("%Y-%m-%d")
        
        if as_of_date is None:
            as_of_date = analysis_date
            
        start_time = datetime.now()
        
        logger.info(
            f"Starting EdgarTools ReAct ATM analysis for {symbol} as of {analysis_date}"
        )
        
        try:
            # Step 1: Get SEC filings
            end_date = pd.to_datetime(analysis_date)
            start_date = end_date - timedelta(days=lookback_days)
            
            filings = self.data_service.get_sec_filings(
                symbol, 
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )
            
            if filings.empty:
                raise ValueError(f"CRITICAL: No SEC filings found for {symbol}")
            
            # Step 2: Extract financial data using EdgarTools
            logger.info(f"Phase 1: Extracting data from {len(filings)} filings using EdgarTools...")
            extracted_data = self._extract_filing_data_edgartools(filings, symbol, as_of_date)
            
            if not extracted_data:
                raise ValueError(f"CRITICAL: No data extracted from filings for {symbol}")
            
            logger.info(f"Extracted data from {len(extracted_data)} filings")
            
            # Step 3: Run ReAct analysis on extracted data
            logger.info("Phase 2: Running ReAct analysis on extracted data...")
            final_assessment = self._react_analysis(
                symbol=symbol,
                analysis_date=analysis_date,
                extracted_data=extracted_data,
                as_of_date=as_of_date,
                fundamentals_data=fundamentals_data
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            final_assessment["processing_time"] = processing_time
            final_assessment["extraction_method"] = "EdgarTools_XBRL"
            
            logger.info(
                f"Completed EdgarTools ReAct ATM analysis for {symbol} in {processing_time:.1f}s"
            )
            return final_assessment
            
        except Exception as e:
            logger.error(f"ATM analysis failed for {symbol}: {e}")
            raise ValueError(f"CRITICAL: ATM analysis failed for {symbol} - {e}")
    
    def _extract_filing_data_edgartools(
        self, filings: pd.DataFrame, symbol: str, as_of_date: str
    ) -> List[Dict[str, Any]]:
        """Extract financial data using EdgarTools native XBRL parsing."""
        
        extracted_data = []
        
        # Focus on 10-Q and 10-K filings which have XBRL data
        financial_filings = filings[filings['form_type'].isin(['10-Q', '10-K'])].head(6)
        
        if financial_filings.empty:
            logger.warning(f"No 10-Q/10-K filings found, trying 8-K filings")
            financial_filings = filings.head(6)
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all extraction tasks
            future_to_filing = {}
            
            for _, filing in financial_filings.iterrows():
                future = executor.submit(
                    self._extract_single_filing_edgartools,
                    filing, symbol
                )
                future_to_filing[future] = filing
            
            # Collect results with timeout
            for future in future_to_filing:
                filing = future_to_filing[future]
                try:
                    # 30 second timeout per filing for XBRL parsing
                    result = future.result(timeout=30)
                    if result:
                        extracted_data.append(result)
                        logger.info(f"Successfully extracted data from {filing['form_type']}")
                except FutureTimeoutError:
                    logger.error(
                        f"CRITICAL: Timeout extracting data from {filing['form_type']}"
                    )
                except Exception as e:
                    logger.error(
                        f"CRITICAL: Failed to extract data from {filing['form_type']}: {e}"
                    )
        
        # Ensure we have at least some data
        if len(extracted_data) == 0:
            raise ValueError(
                f"CRITICAL: No data extracted from {len(financial_filings)} filings."
            )
        
        # Log warning if we have less than ideal amount of data
        if len(extracted_data) < 2:
            logger.warning(
                f"Limited data: only {len(extracted_data)} filing(s) extracted "
                f"out of {len(financial_filings)} attempted."
            )
        
        return extracted_data
    
    def _extract_single_filing_edgartools(
        self, filing: pd.Series, symbol: str
    ) -> Optional[Dict[str, Any]]:
        """Extract financial data from a single filing using EdgarTools."""
        
        try:
            filing_date = filing['filed_at'].strftime('%Y-%m-%d') if hasattr(filing['filed_at'], 'strftime') else str(filing['filed_at'])
            form_type = filing.get('form_type', '')
            
            # Check cache first
            cache_key = f"{symbol}_{form_type}_{filing_date}_edgartools"
            cached_result = self.llm_cache.get_as_of_date(
                symbol=symbol,
                analysis_type="edgartools_extraction",
                filing_date=filing_date,
                prompt=cache_key,
                as_of_date=filing_date,
                model="edgartools"
            )
            
            if cached_result:
                logger.info(f"Using cached EdgarTools extraction for {form_type}")
                return cached_result
            
            # Extract using EdgarTools
            if form_type in ['10-Q', '10-K']:
                extracted_data = self._extract_financial_statements_edgartools(
                    filing, symbol, form_type, filing_date
                )
            else:
                # For 8-K and other forms, extract what we can
                extracted_data = self._extract_basic_filing_data(
                    filing, symbol, form_type, filing_date
                )
            
            if extracted_data:
                # Cache the result
                self.llm_cache.set(
                    symbol=symbol,
                    analysis_type="edgartools_extraction",
                    filing_date=filing_date,
                    prompt=cache_key,
                    response=extracted_data,
                    analysis_date=filing_date,
                    model="edgartools"
                )
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Error extracting data from {filing['form_type']}: {e}")
            raise ValueError(f"CRITICAL: Failed to extract data from {filing['form_type']} - {e}")
    
    def _extract_financial_statements_edgartools(
        self, filing: pd.Series, symbol: str, form_type: str, filing_date: str
    ) -> Optional[Dict[str, Any]]:
        """Extract financial statements using EdgarTools XBRL parsing."""
        
        try:
            # Create Company object
            company = Company(symbol)
            
            # Get filing by accession number if available
            accession_number = filing.get('accession_number', '')
            
            if accession_number:
                # Get specific filing from company's filings
                try:
                    # Get all filings and find the one with matching accession number
                    all_filings = company.get_filings(form=form_type).head(50)
                    edgar_filing = None
                    
                    for f in all_filings:
                        if hasattr(f, 'accession_no') and f.accession_no == accession_number:
                            edgar_filing = f
                            break
                        elif hasattr(f, 'accession_number') and f.accession_number == accession_number:
                            edgar_filing = f
                            break
                            
                    if not edgar_filing:
                        logger.warning(f"Could not find filing with accession {accession_number}")
                except Exception as e:
                    logger.warning(f"Error finding filing by accession: {e}")
                    edgar_filing = None
            else:
                # Try to match by date and form type
                company_filings = company.get_filings(form=form_type).head(20)
                # Find filing closest to our date
                edgar_filing = None
                target_date = pd.to_datetime(filing_date).date()
                
                for f in company_filings:
                    if hasattr(f, 'filing_date'):
                        if f.filing_date.date() == target_date:
                            edgar_filing = f
                            break
            
            if not edgar_filing:
                logger.warning(f"Could not find EdgarTools filing for {form_type} on {filing_date}")
                return self._extract_basic_filing_data(filing, symbol, form_type, filing_date)
            
            # Parse XBRL data
            xbrl = None
            try:
                if hasattr(edgar_filing, 'xbrl'):
                    xbrl = edgar_filing.xbrl()
                else:
                    # Fallback to XBRL.from_filing
                    xbrl = XBRL(edgar_filing)
            except Exception as e:
                logger.warning(f"Could not parse XBRL: {e}")
                xbrl = None
            
            if not xbrl:
                logger.warning(f"No XBRL data available for {form_type} on {filing_date}")
                return self._extract_basic_filing_data(filing, symbol, form_type, filing_date)
            
            # Extract financial data
            cash_position = None
            quarterly_expenses = None
            revenue = None
            has_atm_mentions = False
            shelf_capacity = None
            key_insights = []
            
            # Method 1: Try using facts API
            try:
                # Extract cash position
                cash_concepts = [
                    'CashAndCashEquivalentsAtCarryingValue',
                    'CashAndCashEquivalents',
                    'Cash',
                    'CashCashEquivalentsAndShortTermInvestments'
                ]
                
                for concept in cash_concepts:
                    try:
                        # Try to get facts by concept
                        facts_df = xbrl.facts.get_facts_by_concept(concept)
                        
                        if not facts_df.empty:
                            # Filter for instant period type
                            if 'period_type' in facts_df.columns:
                                instant_facts = facts_df[facts_df['period_type'] == 'instant']
                                if instant_facts.empty:
                                    instant_facts = facts_df
                            else:
                                instant_facts = facts_df
                            
                            # Sort by period_instant to get most recent
                            if 'period_instant' in instant_facts.columns:
                                instant_facts = instant_facts.sort_values('period_instant', ascending=False)
                            
                            if not instant_facts.empty:
                                # Get the first (most recent) fact
                                latest_fact = instant_facts.iloc[0]
                                
                                # Get value - try numeric_value first, then value
                                cash_value = latest_fact.get('numeric_value') or latest_fact.get('value')
                                
                                if cash_value and cash_value > 0:
                                    cash_position = float(cash_value)
                                    key_insights.append(f"Cash and cash equivalents: ${cash_position:,.0f}")
                                    logger.info(f"Extracted cash: ${cash_position:,.0f} from {concept}")
                                    break
                                
                    except Exception as e:
                        logger.debug(f"Could not extract {concept}: {e}")
                        continue
                        
            except Exception as e:
                logger.warning(f"Facts-based cash extraction failed: {e}")
            
            # Extract operating expenses
            try:
                expense_concepts = [
                    'OperatingExpenses',
                    'OperatingCostsAndExpenses',
                    'CostsAndExpenses',
                    'CostOfRevenue',
                    'CostOfGoodsAndServicesSold',
                    'ResearchAndDevelopmentExpense',
                    'SellingGeneralAndAdministrativeExpense',
                    'GeneralAndAdministrativeExpense'
                ]
                
                for concept in expense_concepts:
                    try:
                        facts_df = xbrl.facts.get_facts_by_concept(concept)
                        
                        if not facts_df.empty:
                            # Filter for duration period type (flow items)
                            if 'period_type' in facts_df.columns:
                                duration_facts = facts_df[facts_df['period_type'] == 'duration']
                                if duration_facts.empty:
                                    duration_facts = facts_df
                            else:
                                duration_facts = facts_df
                            
                            # Sort by period_instant to get most recent
                            if 'period_instant' in duration_facts.columns:
                                duration_facts = duration_facts.sort_values('period_instant', ascending=False)
                            
                            if not duration_facts.empty:
                                latest_fact = duration_facts.iloc[0]
                                expense_value = latest_fact.get('numeric_value') or latest_fact.get('value')
                                
                                if expense_value is not None:  # Remove > 0 check
                                    quarterly_expenses = abs(float(expense_value))  # Use absolute value
                                    key_insights.append(f"Operating expenses: ${quarterly_expenses:,.0f}")
                                    logger.info(f"Extracted expenses: ${quarterly_expenses:,.0f} from {concept}")
                                    break
                                
                    except Exception as e:
                        logger.debug(f"Could not extract {concept}: {e}")
                        continue
                        
            except Exception as e:
                logger.warning(f"Facts-based expense extraction failed: {e}")
            
            # Extract revenue
            try:
                revenue_concepts = [
                    'Revenues',
                    'Revenue',
                    'RevenueFromContractWithCustomerExcludingAssessedTax',
                    'SalesRevenueNet'
                ]
                
                for concept in revenue_concepts:
                    try:
                        facts_df = xbrl.facts.get_facts_by_concept(concept)
                        
                        if not facts_df.empty:
                            # Filter for duration period type
                            if 'period_type' in facts_df.columns:
                                duration_facts = facts_df[facts_df['period_type'] == 'duration']
                                if duration_facts.empty:
                                    duration_facts = facts_df
                            else:
                                duration_facts = facts_df
                            
                            # Sort by period_instant to get most recent
                            if 'period_instant' in duration_facts.columns:
                                duration_facts = duration_facts.sort_values('period_instant', ascending=False)
                            
                            if not duration_facts.empty:
                                latest_fact = duration_facts.iloc[0]
                                revenue_value = latest_fact.get('numeric_value') or latest_fact.get('value')
                                
                                if revenue_value is not None:  # Revenue can be 0
                                    revenue = float(revenue_value)
                                    key_insights.append(f"Revenue: ${revenue:,.0f}")
                                    logger.info(f"Extracted revenue: ${revenue:,.0f} from {concept}")
                                    break
                                
                    except Exception as e:
                        logger.debug(f"Could not extract {concept}: {e}")
                        continue
                        
            except Exception as e:
                logger.warning(f"Facts-based revenue extraction failed: {e}")
            
            # Method 2: If facts extraction failed, try a simpler approach
            if cash_position is None or quarterly_expenses is None:
                try:
                    # Get all facts and search for relevant concepts
                    all_facts = xbrl.facts.to_dataframe()
                    
                    if not all_facts.empty and cash_position is None:
                        # Search for cash concepts
                        cash_mask = all_facts['concept'].str.contains('Cash', case=False, na=False)
                        cash_facts = all_facts[cash_mask]
                        
                        if not cash_facts.empty:
                            # Get most recent instant fact
                            if 'period_type' in cash_facts.columns:
                                instant_cash = cash_facts[cash_facts['period_type'] == 'instant']
                                if instant_cash.empty:
                                    instant_cash = cash_facts
                            else:
                                instant_cash = cash_facts
                            
                            if not instant_cash.empty:
                                # Sort by period_instant if available
                                if 'period_instant' in instant_cash.columns:
                                    instant_cash = instant_cash.sort_values('period_instant', ascending=False)
                                
                                # Get value from first row
                                cash_row = instant_cash.iloc[0]
                                cash_value = cash_row.get('numeric_value') or cash_row.get('value', 0)
                                
                                if cash_value and cash_value > 0:
                                    cash_position = float(cash_value)
                                    key_insights.append(f"Cash (fallback): ${cash_position:,.0f}")
                                    logger.info(f"Extracted cash via fallback: ${cash_position:,.0f}")
                    
                    if not all_facts.empty and quarterly_expenses is None:
                        # Search for expense concepts
                        expense_mask = all_facts['concept'].str.contains('Expense|Cost', case=False, na=False)
                        expense_facts = all_facts[expense_mask]
                        
                        if not expense_facts.empty:
                            # Get most recent duration fact
                            if 'period_type' in expense_facts.columns:
                                duration_expenses = expense_facts[expense_facts['period_type'] == 'duration']
                                if duration_expenses.empty:
                                    duration_expenses = expense_facts
                            else:
                                duration_expenses = expense_facts
                            
                            if not duration_expenses.empty:
                                # Sort by period_instant if available
                                if 'period_instant' in duration_expenses.columns:
                                    duration_expenses = duration_expenses.sort_values('period_instant', ascending=False)
                                
                                # Get value from first row
                                expense_row = duration_expenses.iloc[0]
                                expense_value = expense_row.get('numeric_value') or expense_row.get('value', 0)
                                
                                if expense_value and expense_value > 0:
                                    quarterly_expenses = float(expense_value)
                                    key_insights.append(f"Expenses (fallback): ${quarterly_expenses:,.0f}")
                                    logger.info(f"Extracted expenses via fallback: ${quarterly_expenses:,.0f}")
                            
                except Exception as e:
                    logger.warning(f"Fallback extraction failed: {e}")
            
            # Check for ATM program mentions in filing text
            try:
                # Get filing text - try different methods
                filing_text = None
                if hasattr(edgar_filing, 'text'):
                    filing_text = edgar_filing.text()
                elif hasattr(edgar_filing, 'full_text'):
                    filing_text = edgar_filing.full_text
                elif hasattr(edgar_filing, 'document'):
                    filing_text = str(edgar_filing.document)
                    
                if filing_text and len(filing_text) > 100:
                    atm_keywords = [
                        'at-the-market', 'at the market', 'ATM',
                        'equity distribution agreement',
                        'sales agreement', 'open market sale',
                        'controlled equity offering'
                    ]
                    
                    filing_text_lower = filing_text.lower()
                    for keyword in atm_keywords:
                        if keyword.lower() in filing_text_lower:
                            has_atm_mentions = True
                            key_insights.append(f"ATM program mentioned (keyword: {keyword})")
                            break
                            
                    # Look for shelf registration amounts
                    import re
                    shelf_patterns = [
                        r'\$([0-9,]+(?:\.[0-9]+)?)\s*(?:million|billion)?\s*(?:aggregate|shelf|universal shelf)',
                        r'shelf registration.*?\$([0-9,]+(?:\.[0-9]+)?)\s*(?:million|billion)?',
                        r'up to\s*\$([0-9,]+(?:\.[0-9]+)?)\s*(?:million|billion)?'
                    ]
                    
                    for pattern in shelf_patterns:
                        shelf_matches = re.findall(pattern, filing_text, re.IGNORECASE)
                        if shelf_matches:
                            # Convert to number
                            shelf_amount = shelf_matches[0].replace(',', '')
                            shelf_capacity = float(shelf_amount)
                            
                            # Check context for million/billion
                            context_start = max(0, filing_text.find(shelf_matches[0]) - 50)
                            context_end = min(len(filing_text), filing_text.find(shelf_matches[0]) + 50)
                            context = filing_text[context_start:context_end].lower()
                            
                            if 'million' in context:
                                shelf_capacity *= 1_000_000
                            elif 'billion' in context:
                                shelf_capacity *= 1_000_000_000
                                
                            if shelf_capacity > 0:
                                key_insights.append(f"Shelf capacity: ${shelf_capacity:,.0f}")
                                break
                            
            except Exception as e:
                logger.warning(f"Could not check filing text: {e}")
            
            # Log extraction summary
            logger.info(f"EdgarTools extraction summary for {symbol} {form_type}:")
            logger.info(f"  - Cash: ${cash_position:,.0f}" if cash_position else "  - Cash: Not found")
            logger.info(f"  - Expenses: ${quarterly_expenses:,.0f}" if quarterly_expenses else "  - Expenses: Not found")
            logger.info(f"  - Revenue: ${revenue:,.0f}" if revenue is not None else "  - Revenue: Not found")
            logger.info(f"  - ATM mentions: {has_atm_mentions}")
            
            return {
                "filing_type": form_type,
                "filed_date": filing_date,
                "accession_number": accession_number,
                "cash_position": cash_position,
                "quarterly_expenses": quarterly_expenses,
                "revenue": revenue,
                "has_atm_mentions": has_atm_mentions,
                "shelf_capacity": shelf_capacity,
                "key_quotes": key_insights[:5],
                "extraction_method": "EdgarTools_XBRL"
            }
            
        except Exception as e:
            logger.error(f"EdgarTools extraction failed for {symbol}: {e}")
            # NO FALLBACKS - fail hard
            raise ValueError(f"CRITICAL: EdgarTools extraction failed for {symbol} - {e}")
    
    def _extract_basic_filing_data(
        self, filing: pd.Series, symbol: str, form_type: str, filing_date: str
    ) -> Dict[str, Any]:
        """Basic extraction for non-XBRL filings."""
        
        return {
            "filing_type": form_type,
            "filed_date": filing_date,
            "accession_number": filing.get('accession_number', ''),
            "cash_position": None,
            "quarterly_expenses": None,
            "has_atm_mentions": False,
            "shelf_capacity": None,
            "key_quotes": [f"{form_type} filing - limited financial data available"],
            "extraction_method": "basic"
        }
    
    def _react_analysis(
        self, symbol: str, analysis_date: str, extracted_data: List[Dict], 
        as_of_date: str, fundamentals_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Run ReAct analysis on the extracted data."""
        
        # Aggregate the extracted data
        aggregated = self._aggregate_extracted_data(extracted_data)
        
        # Check cache first
        cache_key = f"{symbol}_react_{analysis_date}_{len(extracted_data)}_edgartools"
        
        filing_dates = [d['filed_date'] for d in extracted_data]
        latest_filing_date = max(filing_dates) if filing_dates else analysis_date
        
        cached_result = self.llm_cache.get_as_of_date(
            symbol=symbol,
            analysis_type="react_atm_analysis_edgartools",
            filing_date=latest_filing_date,
            prompt=cache_key,
            as_of_date=as_of_date,
            model="gemini/gemini-1.5-flash"
        )
        
        if cached_result:
            logger.info(f"Using cached ReAct analysis for {symbol}")
            return self._format_react_result(cached_result, extracted_data, aggregated, symbol)
        
        # Create enhanced ReAct prompt
        prompt = self._create_enhanced_react_prompt(symbol, aggregated, extracted_data, fundamentals_data)
        
        try:
            # Call LLM with ReAct approach
            response = litellm.completion(
                model="gemini/gemini-1.5-flash",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1500,
                temperature=0.1,
                timeout=30
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Parse ReAct response
            result = self._parse_react_response(response_text)
            
            # Cache the result
            self.llm_cache.set(
                symbol=symbol,
                analysis_type="react_atm_analysis_edgartools",
                filing_date=latest_filing_date,
                prompt=cache_key,
                response=result,
                analysis_date=as_of_date,
                model="gemini/gemini-1.5-flash"
            )
            
            # Format final result
            return self._format_react_result(result, extracted_data, aggregated, symbol)
            
        except Exception as e:
            logger.error(f"ReAct analysis failed: {e}")
            raise ValueError(f"CRITICAL: ReAct analysis failed for {symbol} - {e}")
    
    def _aggregate_extracted_data(self, extracted_data: List[Dict]) -> Dict[str, Any]:
        """Aggregate the extracted filing data."""
        
        # Extract and filter values
        cash_positions = []
        quarterly_expenses = []
        shelf_capacities = []
        
        for d in extracted_data:
            if d.get('cash_position') is not None:
                cash_positions.append(float(d['cash_position']))
            if d.get('quarterly_expenses') is not None:
                quarterly_expenses.append(float(d['quarterly_expenses']))
            if d.get('shelf_capacity') is not None:
                shelf_capacities.append(float(d['shelf_capacity']))
        
        has_atm = any(d.get('has_atm_mentions', False) for d in extracted_data)
        
        # Get filing types and dates
        filing_types = [d['filing_type'] for d in extracted_data]
        filing_dates = [d['filed_date'] for d in extracted_data]
        
        # Calculate trends
        if len(cash_positions) >= 2:
            latest_cash = cash_positions[-1]
            earliest_cash = cash_positions[0]
            if latest_cash < earliest_cash * 0.8:
                cash_trend = "declining"
            elif latest_cash > earliest_cash * 1.2:
                cash_trend = "increasing"
            else:
                cash_trend = "stable"
        else:
            cash_trend = "unknown"
            latest_cash = cash_positions[-1] if cash_positions else 0
        
        return {
            "latest_cash": latest_cash if cash_positions else 0,
            "avg_cash": np.mean(cash_positions) if cash_positions else 0,
            "cash_trend": cash_trend,
            "latest_quarterly_expense": quarterly_expenses[-1] if quarterly_expenses else 0,
            "avg_quarterly_expense": np.mean(quarterly_expenses) if quarterly_expenses else 0,
            "has_atm_program": has_atm,
            "max_shelf_capacity": max(shelf_capacities) if shelf_capacities else 0,
            "num_filings": len(extracted_data),
            "filing_types": list(set(filing_types)),
            "date_range": f"{min(filing_dates)} to {max(filing_dates)}" if filing_dates else "unknown",
            "has_valid_data": bool(cash_positions or quarterly_expenses)
        }
    
    def _create_enhanced_react_prompt(
        self, symbol: str, aggregated: Dict, extracted_data: List[Dict], 
        fundamentals_data: Dict[str, Any] = None
    ) -> str:
        """Create enhanced ReAct prompt with EdgarTools extraction confidence."""
        
        # Get key insights from extraction
        key_insights = []
        for data in extracted_data[:3]:  # Top 3 most recent
            if data.get('key_quotes'):
                quotes = "\n".join([f"- {quote}" for quote in data['key_quotes'][:3]])
                key_insights.append(f"\n{data['filing_type']} ({data['filed_date']}):\n{quotes}")
        
        combined_insights = "\n".join(key_insights)
        
        # Get financial values
        latest_cash = aggregated.get('latest_cash', 0)
        latest_expense = aggregated.get('latest_quarterly_expense', 0)
        shelf_capacity = aggregated.get('max_shelf_capacity', 0)
        
        # Extract fundamentals
        if not fundamentals_data:
            raise ValueError("CRITICAL: Fundamentals data required for ATM analysis")
        
        shares_outstanding = fundamentals_data['shares_outstanding_millions']
        float_millions = fundamentals_data['float_millions'] 
        insider_percent = fundamentals_data['insider_percent']
        institutional_percent = fundamentals_data.get('institutional_percent', 0)
        
        # Check data quality
        data_quality = "HIGH" if aggregated.get('has_valid_data') else "LOW"
        
        prompt = f"""
You are an expert financial analyst using ReAct (Reasoning and Acting) to assess ATM dilution risk for {symbol}.
This is for a gap-up ATM trading strategy targeting cash-burning small-caps that dilute during volume spikes.

DATA QUALITY: {data_quality} - Extracted using EdgarTools XBRL parsing

COMPANY FUNDAMENTALS:
- Shares Outstanding: {shares_outstanding:.1f}M
- Float: {float_millions:.1f}M ({float_millions/shares_outstanding*100:.1f}% of outstanding)
- Insider Ownership: {insider_percent:.1f}%
- Institutional Ownership: {institutional_percent:.1f}%
- Float Analysis: {'LOW FLOAT - EXPLOSIVE POTENTIAL' if float_millions < 20 and (float_millions/shares_outstanding*100) < 30 else 'MODERATE FLOAT' if float_millions < 20 or (float_millions/shares_outstanding*100) < 40 else 'HIGH FLOAT - LIMITED GAP POTENTIAL'}

FINANCIAL POSITION (from XBRL):
- Latest Cash: ${latest_cash:,.0f} {'[VERIFIED]' if latest_cash > 0 else '[NO DATA]'}
- Cash Trend: {aggregated['cash_trend']}
- Quarterly Expenses: ${latest_expense:,.0f} {'[VERIFIED]' if latest_expense > 0 else '[NO DATA]'}
- Monthly Burn Rate: ${latest_expense/3:,.0f} {'(calculated)' if latest_expense > 0 else '(unknown)'}

DILUTION CAPABILITY:
- ATM Program Active: {aggregated['has_atm_program']}
- Shelf Registration: ${shelf_capacity:,.0f}
- Limited Float Supply: {'YES - Only {:.1f}M shares available'.format(float_millions) if float_millions < 20 else 'NO - {:.1f}M shares (high liquidity)'.format(float_millions)}
- Filings Analyzed: {aggregated['num_filings']} ({aggregated['date_range']})

KEY FILING INSIGHTS:
{combined_insights[:3000] if combined_insights else "CRITICAL: No insights could be extracted from filings"}

CRITICAL NOTES:
1. If cash position is $0, it likely means no 10-Q/10-K data was available
2. Monthly burn calculation requires valid expense data
3. ATM timing predictions depend on accurate cash runway calculations

REACT ANALYSIS STEPS:

Step 1 - REASON about data quality and cash runway:
- Assess if we have valid financial data (cash > 0, expenses > 0)
- If valid: Calculate months of runway = cash / (monthly burn)
- If invalid: State we cannot calculate runway without data

Step 2 - REASON about gap potential and dilution dynamics:
Analyze: 
- Float of {float_millions:.1f}M ({float_millions/shares_outstanding*100:.1f}% of outstanding)
- Low float (<20M shares, <30% ratio) = explosive gap potential
- Consider how much dilution the float can absorb

Step 3 - ACT to determine ATM timing window:
- If runway calculable: predicted date = today + runway months - 2 month buffer
- If not calculable: state "UNKNOWN - insufficient financial data"

Step 4 - REASON about ATM execution probability:
- Weight factors based on data availability
- If no financial data: lower confidence significantly

Step 5 - CRITIQUE your analysis:
- Explicitly state what data was missing
- Note confidence limitations

Step 6 - ACT to assign final risk assessment:
- Adjust confidence based on data quality

RETURN ONLY THIS JSON:
{{
  "reasoning_steps": [
    {{"step": 1, "thought": "data quality and runway analysis", "data_quality": "HIGH/LOW", "calculation": "show math if possible", "runway_months": X or null}},
    {{"step": 2, "thought": "dilution dynamics", "float_impact": "analysis", "gap_potential": "HIGH/MEDIUM/LOW"}},
    {{"step": 3, "thought": "ATM timing", "predicted_date_range": "YYYY-MM to YYYY-MM or UNKNOWN"}},
    {{"step": 4, "thought": "probability assessment", "factors": ["list factors"], "probability_score": 0.X}},
    {{"step": 5, "thought": "limitations", "missing_data": ["list missing"], "impact": "assessment"}},
    {{"step": 6, "thought": "final assessment", "conclusion": "synthesis"}}
  ],
  "cash_position": {latest_cash},
  "monthly_burn": {latest_expense/3 if latest_expense > 0 else 0},
  "cash_runway_months": X or null,
  "predicted_atm_date": "YYYY-MM-DD" or "UNKNOWN",
  "atm_probability": 0.X,
  "risk_category": "LOW/MEDIUM/HIGH",
  "confidence": 0.X,
  "data_quality_note": "describe extraction quality",
  "dilution_factors": {{
    "float_category": "LOW/MODERATE/HIGH",
    "explosive_gap_potential": true/false,
    "float_metrics": "{float_millions:.1f}M shares ({float_millions/shares_outstanding*100:.1f}% of outstanding)",
    "estimated_dilution_impact": "X.X% potential price impact"
  }},
  "key_insights": ["insight 1", "insight 2", "insight 3"]
}}
"""
        
        return prompt
    
    def _parse_react_response(self, response_text: str) -> Dict[str, Any]:
        """Parse ReAct response."""
        
        # Clean and extract JSON
        import re
        response_text = response_text.replace("```json", "").replace("```", "")
        
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if not json_match:
            raise ValueError("No JSON found in ReAct response")
        
        result = json.loads(json_match.group())
        
        # Validate required fields
        required = ["cash_position", "atm_probability", "risk_category"]
        
        for field in required:
            if field not in result:
                raise ValueError(f"Missing required field: {field}")
        
        return result
    
    def _format_react_result(
        self, react_result: Dict, extracted_data: List[Dict], 
        aggregated: Dict, symbol: str
    ) -> Dict[str, Any]:
        """Format ReAct result into final assessment."""
        
        # Extract reasoning summary
        reasoning_summary = ""
        if "reasoning_steps" in react_result:
            for step in react_result["reasoning_steps"]:
                if step.get("step") == 6:
                    reasoning_summary = step.get("conclusion", "")
                    break
        
        return {
            "symbol": symbol,
            "analysis_date": datetime.now().strftime("%Y-%m-%d"),
            "analysis_method": "ReAct_EdgarTools",
            # Core metrics
            "latest_cash_position": react_result.get("cash_position", 0),
            "avg_monthly_burn": react_result.get("monthly_burn", 0),
            "estimated_runway_months": react_result.get("cash_runway_months"),
            "predicted_atm_date": react_result.get("predicted_atm_date", "UNKNOWN"),
            # Risk assessment
            "atm_probability": react_result.get("atm_probability", 0),
            "risk_category": react_result.get("risk_category", "UNKNOWN"),
            "has_active_atm": aggregated.get("has_atm_program", False),
            "max_atm_capacity": aggregated.get("max_shelf_capacity", 0),
            # Analysis quality
            "filings_analyzed": len(extracted_data),
            "confidence_score": react_result.get("confidence", 0),
            "cash_trend": aggregated.get("cash_trend", "unknown"),
            "data_quality_note": react_result.get("data_quality_note", ""),
            # Insights
            "key_insights": react_result.get("key_insights", []),
            "reasoning_summary": reasoning_summary,
            "dilution_factors": react_result.get("dilution_factors", {}),
            # Summary
            "summary": f"ATM Risk: {react_result.get('risk_category', 'UNKNOWN')} "
                      f"({react_result.get('atm_probability', 0):.0%}). "
                      f"Analyzed {len(extracted_data)} filings using EdgarTools XBRL. "
                      f"Runway: {react_result.get('cash_runway_months', 'unknown')} months. "
                      f"ATM Date: {react_result.get('predicted_atm_date', 'UNKNOWN')}",
            # Metadata
            "analyzer": "react_edgartools_v1",
            "version": "1.0.0",
            "from_cache": False
        }
    
    def close(self):
        """Clean up resources."""
        self.data_service.close()


# Alias for compatibility
EdgarToolsATMAnalyzer = ReactEdgarToolsAnalyzer