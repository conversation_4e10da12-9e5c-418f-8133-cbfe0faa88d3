"""
Sophisticated Tick Data Analysis for Insider Accumulation Detection

This module analyzes tick-level data to detect insider accumulation patterns
1-2 weeks before gap events. Uses comprehensive IB tick data including:
- Individual trade ticks with timestamp, price, size
- Bid/ask spreads and depth
- Volume distribution analysis
- Unusual trading pattern detection
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import sqlite3
from dataclasses import dataclass, asdict
from collections import defaultdict
import logging


@dataclass
class TickData:
    """Individual tick data point"""

    timestamp: datetime
    price: float
    size: int
    exchange: str
    bid: Optional[float] = None
    ask: Optional[float] = None
    bid_size: Optional[int] = None
    ask_size: Optional[int] = None


@dataclass
class InsiderSignal:
    """Detected insider accumulation signal"""

    symbol: str
    detection_date: datetime
    signal_strength: float  # 0-1 score
    unusual_volume_score: float
    large_block_score: float
    time_weighted_score: float
    spread_analysis_score: float
    momentum_score: float
    supporting_evidence: List[str]
    tick_analysis: Dict
    confidence_level: str  # 'low', 'medium', 'high'


@dataclass
class VolumeProfile:
    """Volume analysis profile"""

    total_volume: int
    avg_trade_size: float
    large_trades_count: int  # Trades > 10k shares
    block_trades_count: int  # Trades > 50k shares
    institutional_volume_pct: float
    retail_volume_pct: float
    unusual_size_trades: List[Dict]


@dataclass
class SpreadAnalysis:
    """Bid-ask spread analysis"""

    avg_spread: float
    avg_spread_pct: float
    spread_volatility: float
    tight_spread_periods: int
    wide_spread_periods: int
    depth_analysis: Dict


class TickAnalyzer:
    """
    Comprehensive tick data analyzer for insider detection

    Analyzes multiple signals:
    1. Unusual volume patterns (sustained buying)
    2. Large block trades (institutional activity)
    3. Time-weighted accumulation (gradual building)
    4. Spread behavior (price improvement seeking)
    5. Momentum indicators (persistent buying pressure)
    """

    def __init__(self, ib_connector, database_path: str):
        self.ib_connector = ib_connector
        self.db_path = database_path
        self.logger = logging.getLogger(__name__)
        self._init_database()

        # Thresholds for insider detection
        self.LARGE_TRADE_THRESHOLD = 10000  # 10k+ shares
        self.BLOCK_TRADE_THRESHOLD = 50000  # 50k+ shares
        self.UNUSUAL_VOLUME_MULTIPLIER = 3.0  # 3x average volume
        self.MIN_ACCUMULATION_DAYS = 5  # Minimum days of accumulation
        self.MAX_LOOKBACK_DAYS = 14  # Maximum lookback period

    def _init_database(self):
        """Initialize tick data database with comprehensive schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Tick data table - store every tick
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS tick_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                price REAL NOT NULL,
                size INTEGER NOT NULL,
                exchange TEXT,
                bid REAL,
                ask REAL,
                bid_size INTEGER,
                ask_size INTEGER,
                trade_type TEXT,  -- 'buy', 'sell', 'neutral'
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        # Create indexes separately
        cursor.execute(
            """
            CREATE INDEX IF NOT EXISTS idx_tick_symbol_timestamp 
            ON tick_data (symbol, timestamp)
        """
        )

        # Daily volume profiles
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS daily_volume_profiles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                date DATE NOT NULL,
                total_volume INTEGER,
                avg_trade_size REAL,
                large_trades_count INTEGER,
                block_trades_count INTEGER,
                institutional_volume_pct REAL,
                retail_volume_pct REAL,
                volume_profile_json TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, date)
            )
        """
        )

        # Insider signals
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS insider_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                detection_date DATETIME NOT NULL,
                signal_strength REAL NOT NULL,
                unusual_volume_score REAL,
                large_block_score REAL,
                time_weighted_score REAL,
                spread_analysis_score REAL,
                momentum_score REAL,
                confidence_level TEXT,
                supporting_evidence_json TEXT,
                tick_analysis_json TEXT,
                days_before_gap INTEGER,
                gap_occurred BOOLEAN DEFAULT FALSE,
                gap_date DATE,
                gap_percentage REAL,
                prediction_accuracy REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        # Spread analysis
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS spread_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                date DATE NOT NULL,
                avg_spread REAL,
                avg_spread_pct REAL,
                spread_volatility REAL,
                tight_spread_periods INTEGER,
                wide_spread_periods INTEGER,
                depth_analysis_json TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, date)
            )
        """
        )

        conn.commit()
        conn.close()

    def collect_comprehensive_tick_data(
        self, symbol: str, start_date: datetime, end_date: datetime
    ) -> List[TickData]:
        """
        Collect ALL available tick data from IB for the period

        Uses multiple IB API endpoints:
        1. reqTickByTickData for live/recent ticks
        2. reqHistoricalTicks for historical tick data
        3. reqMktDepth for level 2 data
        """
        all_ticks = []

        # Request historical tick data (most comprehensive)
        contract = self.ib_connector.create_stock_contract(symbol)

        # Break down into daily chunks to get maximum data
        current_date = start_date
        while current_date <= end_date:
            try:
                # Request all tick types for comprehensive analysis
                tick_types = ["TRADES", "BID_ASK", "MIDPOINT"]

                for tick_type in tick_types:
                    reqId = self.ib_connector.get_next_req_id()

                    # Request historical ticks for this day
                    end_time = current_date.strftime("%Y%m%d 23:59:59 EST")

                    self.ib_connector.reqHistoricalTicks(
                        reqId=reqId,
                        contract=contract,
                        startDateTime="",
                        endDateTime=end_time,
                        numberOfTicks=1000,  # Max ticks per request
                        whatToShow=tick_type,
                        useRth=0,  # Include extended hours
                        ignoreSize=False,
                        miscOptions=[],
                    )

                    # Wait for data and process
                    ticks = self._wait_for_tick_data(reqId)
                    all_ticks.extend(ticks)

                    # Rate limiting to avoid IB restrictions
                    time.sleep(0.1)

            except Exception as e:
                self.logger.error(
                    f"Failed to get tick data for {symbol} on {current_date}: {e}"
                )

            current_date += timedelta(days=1)

        # Store in database
        self._store_tick_data(symbol, all_ticks)

        return all_ticks

    def _wait_for_tick_data(self, reqId: int, timeout: int = 10) -> List[TickData]:
        """Wait for tick data response from IB API"""
        import time

        start_time = time.time()

        while (
            reqId not in self.ib_connector.historical_ticks
            and time.time() - start_time < timeout
        ):
            time.sleep(0.1)

        if reqId in self.ib_connector.historical_ticks:
            raw_ticks = self.ib_connector.historical_ticks[reqId]
            return [self._convert_ib_tick(tick) for tick in raw_ticks]
        else:
            self.logger.warning(f"Timeout waiting for tick data reqId {reqId}")
            return []

    def _convert_ib_tick(self, ib_tick) -> TickData:
        """Convert IB tick format to our TickData format"""
        return TickData(
            timestamp=datetime.fromtimestamp(ib_tick.time),
            price=ib_tick.price,
            size=ib_tick.size,
            exchange=getattr(ib_tick, "exchange", ""),
            bid=getattr(ib_tick, "bid", None),
            ask=getattr(ib_tick, "ask", None),
            bid_size=getattr(ib_tick, "bidSize", None),
            ask_size=getattr(ib_tick, "askSize", None),
        )

    def _store_tick_data(self, symbol: str, ticks: List[TickData]):
        """Store tick data in database with classification"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for tick in ticks:
            # Classify trade type (buy/sell/neutral)
            trade_type = self._classify_trade_type(tick)

            cursor.execute(
                """
                INSERT OR REPLACE INTO tick_data 
                (symbol, timestamp, price, size, exchange, bid, ask, 
                 bid_size, ask_size, trade_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    symbol,
                    tick.timestamp,
                    tick.price,
                    tick.size,
                    tick.exchange,
                    tick.bid,
                    tick.ask,
                    tick.bid_size,
                    tick.ask_size,
                    trade_type,
                ),
            )

        conn.commit()
        conn.close()

    def _classify_trade_type(self, tick: TickData) -> str:
        """Classify trade as buy/sell/neutral based on bid/ask"""
        if tick.bid and tick.ask:
            mid_point = (tick.bid + tick.ask) / 2
            if tick.price >= mid_point + (tick.ask - tick.bid) * 0.25:
                return "buy"
            elif tick.price <= mid_point - (tick.ask - tick.bid) * 0.25:
                return "sell"
        return "neutral"

    def analyze_volume_profile(self, symbol: str, date: datetime) -> VolumeProfile:
        """Analyze volume patterns for potential insider activity"""
        conn = sqlite3.connect(self.db_path)

        # Get all ticks for this date
        df = pd.read_sql_query(
            """
            SELECT * FROM tick_data 
            WHERE symbol = ? AND date(timestamp) = ?
            ORDER BY timestamp
        """,
            conn,
            params=(symbol, date.date()),
        )

        if df.empty:
            return VolumeProfile(0, 0, 0, 0, 0, 0, [])

        # Calculate volume metrics
        total_volume = df["size"].sum()
        avg_trade_size = df["size"].mean()

        # Identify large trades
        large_trades = df[df["size"] >= self.LARGE_TRADE_THRESHOLD]
        block_trades = df[df["size"] >= self.BLOCK_TRADE_THRESHOLD]

        # Classify institutional vs retail
        institutional_volume = large_trades["size"].sum()
        retail_volume = total_volume - institutional_volume

        institutional_pct = (
            institutional_volume / total_volume * 100 if total_volume > 0 else 0
        )
        retail_pct = retail_volume / total_volume * 100 if total_volume > 0 else 0

        # Find unusual size trades
        size_threshold = avg_trade_size * 5  # 5x average
        unusual_trades = df[df["size"] >= size_threshold]

        unusual_size_trades = []
        for _, trade in unusual_trades.iterrows():
            unusual_size_trades.append(
                {
                    "timestamp": trade["timestamp"],
                    "price": trade["price"],
                    "size": trade["size"],
                    "multiple_of_avg": trade["size"] / avg_trade_size,
                }
            )

        profile = VolumeProfile(
            total_volume=int(total_volume),
            avg_trade_size=float(avg_trade_size),
            large_trades_count=len(large_trades),
            block_trades_count=len(block_trades),
            institutional_volume_pct=institutional_pct,
            retail_volume_pct=retail_pct,
            unusual_size_trades=unusual_size_trades,
        )

        # Store in database
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT OR REPLACE INTO daily_volume_profiles
            (symbol, date, total_volume, avg_trade_size, large_trades_count,
             block_trades_count, institutional_volume_pct, retail_volume_pct,
             volume_profile_json)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                symbol,
                date.date(),
                profile.total_volume,
                profile.avg_trade_size,
                profile.large_trades_count,
                profile.block_trades_count,
                profile.institutional_volume_pct,
                profile.retail_volume_pct,
                json.dumps(asdict(profile)),
            ),
        )

        conn.commit()
        conn.close()

        return profile

    def analyze_spread_behavior(self, symbol: str, date: datetime) -> SpreadAnalysis:
        """Analyze bid-ask spread behavior for insider signals"""
        conn = sqlite3.connect(self.db_path)

        df = pd.read_sql_query(
            """
            SELECT * FROM tick_data 
            WHERE symbol = ? AND date(timestamp) = ?
            AND bid IS NOT NULL AND ask IS NOT NULL
            ORDER BY timestamp
        """,
            conn,
            params=(symbol, date.date()),
        )

        if df.empty:
            return SpreadAnalysis(0, 0, 0, 0, 0, {})

        # Calculate spreads
        df["spread"] = df["ask"] - df["bid"]
        df["spread_pct"] = df["spread"] / df["price"] * 100

        avg_spread = df["spread"].mean()
        avg_spread_pct = df["spread_pct"].mean()
        spread_volatility = df["spread_pct"].std()

        # Identify tight/wide spread periods
        median_spread = df["spread_pct"].median()
        tight_threshold = median_spread * 0.5  # 50% below median
        wide_threshold = median_spread * 2.0  # 200% above median

        tight_spread_periods = len(df[df["spread_pct"] <= tight_threshold])
        wide_spread_periods = len(df[df["spread_pct"] >= wide_threshold])

        # Depth analysis
        depth_analysis = {
            "avg_bid_size": df["bid_size"].mean() if "bid_size" in df else 0,
            "avg_ask_size": df["ask_size"].mean() if "ask_size" in df else 0,
            "total_bid_volume": df["bid_size"].sum() if "bid_size" in df else 0,
            "total_ask_volume": df["ask_size"].sum() if "ask_size" in df else 0,
        }

        analysis = SpreadAnalysis(
            avg_spread=float(avg_spread),
            avg_spread_pct=float(avg_spread_pct),
            spread_volatility=float(spread_volatility),
            tight_spread_periods=tight_spread_periods,
            wide_spread_periods=wide_spread_periods,
            depth_analysis=depth_analysis,
        )

        # Store in database
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT OR REPLACE INTO spread_analysis
            (symbol, date, avg_spread, avg_spread_pct, spread_volatility,
             tight_spread_periods, wide_spread_periods, depth_analysis_json)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                symbol,
                date.date(),
                analysis.avg_spread,
                analysis.avg_spread_pct,
                analysis.spread_volatility,
                analysis.tight_spread_periods,
                analysis.wide_spread_periods,
                json.dumps(analysis.depth_analysis),
            ),
        )

        conn.commit()
        conn.close()

        return analysis

    def detect_insider_accumulation(
        self, symbol: str, analysis_date: datetime
    ) -> Optional[InsiderSignal]:
        """
        Comprehensive insider accumulation detection

        Analyzes 1-2 weeks of tick data before analysis_date to detect:
        1. Sustained unusual volume
        2. Large block trades increase
        3. Time-weighted accumulation patterns
        4. Spread behavior changes
        5. Momentum indicators
        """
        lookback_start = analysis_date - timedelta(days=self.MAX_LOOKBACK_DAYS)

        # Collect volume profiles for lookback period
        volume_profiles = []
        spread_analyses = []

        current_date = lookback_start
        while current_date <= analysis_date:
            if current_date.weekday() < 5:  # Trading days only
                volume_profile = self.analyze_volume_profile(symbol, current_date)
                spread_analysis = self.analyze_spread_behavior(symbol, current_date)

                volume_profiles.append((current_date, volume_profile))
                spread_analyses.append((current_date, spread_analysis))

            current_date += timedelta(days=1)

        if len(volume_profiles) < self.MIN_ACCUMULATION_DAYS:
            return None

        # Calculate detection scores
        scores = self._calculate_insider_scores(volume_profiles, spread_analyses)

        # Determine overall signal strength
        signal_strength = np.mean(
            [
                scores["unusual_volume_score"],
                scores["large_block_score"],
                scores["time_weighted_score"],
                scores["spread_analysis_score"],
                scores["momentum_score"],
            ]
        )

        # Only generate signal if strength is significant
        if signal_strength < 0.3:  # 30% threshold
            return None

        # Determine confidence level
        if signal_strength >= 0.7:
            confidence = "high"
        elif signal_strength >= 0.5:
            confidence = "medium"
        else:
            confidence = "low"

        # Generate supporting evidence
        evidence = self._generate_supporting_evidence(
            scores, volume_profiles, spread_analyses
        )

        signal = InsiderSignal(
            symbol=symbol,
            detection_date=analysis_date,
            signal_strength=signal_strength,
            unusual_volume_score=scores["unusual_volume_score"],
            large_block_score=scores["large_block_score"],
            time_weighted_score=scores["time_weighted_score"],
            spread_analysis_score=scores["spread_analysis_score"],
            momentum_score=scores["momentum_score"],
            supporting_evidence=evidence,
            tick_analysis=scores["detailed_analysis"],
            confidence_level=confidence,
        )

        # Store signal in database
        self._store_insider_signal(signal)

        return signal

    def _calculate_insider_scores(
        self, volume_profiles: List, spread_analyses: List
    ) -> Dict:
        """Calculate individual detection scores"""

        # 1. Unusual Volume Score
        volumes = [vp[1].total_volume for vp in volume_profiles]
        avg_volume = np.mean(volumes)
        recent_volumes = volumes[-5:]  # Last 5 days
        recent_avg = np.mean(recent_volumes)

        unusual_volume_score = min(
            recent_avg / (avg_volume * self.UNUSUAL_VOLUME_MULTIPLIER), 1.0
        )

        # 2. Large Block Score
        block_counts = [vp[1].block_trades_count for vp in volume_profiles]
        avg_blocks = np.mean(block_counts)
        recent_blocks = np.mean(block_counts[-5:])

        large_block_score = min(recent_blocks / max(avg_blocks, 1) * 0.5, 1.0)

        # 3. Time-weighted accumulation
        weights = np.linspace(0.5, 1.0, len(volume_profiles))  # More weight to recent
        weighted_volumes = [vol * weight for vol, weight in zip(volumes, weights)]
        trend_strength = np.corrcoef(range(len(weighted_volumes)), weighted_volumes)[
            0, 1
        ]
        time_weighted_score = (
            max(trend_strength, 0) if not np.isnan(trend_strength) else 0
        )

        # 4. Spread analysis score
        spreads = [sa[1].avg_spread_pct for sa in spread_analyses]
        spread_trend = np.corrcoef(range(len(spreads)), spreads)[0, 1]
        # Tightening spreads indicate institutional interest
        spread_analysis_score = (
            max(-spread_trend, 0) if not np.isnan(spread_trend) else 0
        )

        # 5. Momentum score (acceleration in activity)
        if len(volumes) >= 10:
            early_period = np.mean(volumes[:5])
            late_period = np.mean(volumes[-5:])
            momentum_score = min(
                (late_period - early_period) / max(early_period, 1), 1.0
            )
        else:
            momentum_score = 0

        detailed_analysis = {
            "avg_volume": avg_volume,
            "recent_avg_volume": recent_avg,
            "volume_trend": trend_strength,
            "avg_blocks": avg_blocks,
            "recent_blocks": recent_blocks,
            "spread_trend": spread_trend,
            "momentum_acceleration": momentum_score,
        }

        return {
            "unusual_volume_score": max(unusual_volume_score, 0),
            "large_block_score": max(large_block_score, 0),
            "time_weighted_score": max(time_weighted_score, 0),
            "spread_analysis_score": max(spread_analysis_score, 0),
            "momentum_score": max(momentum_score, 0),
            "detailed_analysis": detailed_analysis,
        }

    def _generate_supporting_evidence(
        self, scores: Dict, volume_profiles: List, spread_analyses: List
    ) -> List[str]:
        """Generate human-readable supporting evidence"""
        evidence = []

        if scores["unusual_volume_score"] > 0.5:
            evidence.append("Sustained above-average volume indicating accumulation")

        if scores["large_block_score"] > 0.3:
            evidence.append(
                "Increased large block trades suggesting institutional activity"
            )

        if scores["time_weighted_score"] > 0.4:
            evidence.append("Progressive volume increase over analysis period")

        if scores["spread_analysis_score"] > 0.3:
            evidence.append("Tightening spreads indicate informed trading")

        if scores["momentum_score"] > 0.4:
            evidence.append("Accelerating trading activity in recent days")

        # Add specific metrics
        recent_volume_increase = scores["detailed_analysis"].get(
            "recent_avg_volume", 0
        ) / max(scores["detailed_analysis"].get("avg_volume", 1), 1)

        if recent_volume_increase > 2:
            evidence.append(
                f"Recent volume {recent_volume_increase:.1f}x higher than average"
            )

        return evidence

    def _store_insider_signal(self, signal: InsiderSignal):
        """Store insider signal in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            INSERT INTO insider_signals
            (symbol, detection_date, signal_strength, unusual_volume_score,
             large_block_score, time_weighted_score, spread_analysis_score,
             momentum_score, confidence_level, supporting_evidence_json,
             tick_analysis_json)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                signal.symbol,
                signal.detection_date,
                signal.signal_strength,
                signal.unusual_volume_score,
                signal.large_block_score,
                signal.time_weighted_score,
                signal.spread_analysis_score,
                signal.momentum_score,
                signal.confidence_level,
                json.dumps(signal.supporting_evidence),
                json.dumps(signal.tick_analysis),
            ),
        )

        conn.commit()
        conn.close()

    def get_entry_signal(self, symbol: str, analysis_date: datetime) -> Dict:
        """
        Determine if current conditions are suitable for entry

        Checks:
        1. Volume and spread are adequate for execution
        2. Insider accumulation signal is present
        3. Price action supports entry timing
        """
        # Get insider signal
        insider_signal = self.detect_insider_accumulation(symbol, analysis_date)

        # Get current volume/spread analysis
        volume_profile = self.analyze_volume_profile(symbol, analysis_date)
        spread_analysis = self.analyze_spread_behavior(symbol, analysis_date)

        # Entry criteria
        entry_signal = {
            "symbol": symbol,
            "analysis_date": analysis_date.isoformat(),
            "insider_signal_present": insider_signal is not None,
            "insider_signal_strength": (
                insider_signal.signal_strength if insider_signal else 0
            ),
            "volume_adequate": volume_profile.total_volume > 100000,  # Min 100k volume
            "spread_acceptable": spread_analysis.avg_spread_pct < 0.5,  # <0.5% spread
            "large_trades_present": volume_profile.large_trades_count > 0,
            "institutional_activity": volume_profile.institutional_volume_pct > 20,
            "entry_recommended": False,
            "confidence_level": "none",
            "risk_factors": [],
        }

        # Determine entry recommendation
        positive_factors = sum(
            [
                entry_signal["insider_signal_present"],
                entry_signal["volume_adequate"],
                entry_signal["spread_acceptable"],
                entry_signal["large_trades_present"],
                entry_signal["institutional_activity"],
            ]
        )

        if (
            positive_factors >= 4
            and insider_signal
            and insider_signal.signal_strength > 0.5
        ):
            entry_signal["entry_recommended"] = True
            entry_signal["confidence_level"] = insider_signal.confidence_level

        # Add risk factors
        risk_factors = []
        if not entry_signal["volume_adequate"]:
            risk_factors.append("Low volume may impact execution")
        if not entry_signal["spread_acceptable"]:
            risk_factors.append("Wide spreads increase transaction costs")
        if entry_signal["insider_signal_strength"] < 0.3:
            risk_factors.append("Weak insider accumulation signal")

        entry_signal["risk_factors"] = risk_factors

        return entry_signal

    def analyze_single_stock_day(self, symbol: str, target_date: datetime) -> Dict:
        """
        Complete analysis of a single stock for a single day

        This is the main feedback loop function that:
        1. Collects comprehensive tick data
        2. Analyzes insider accumulation patterns
        3. Determines entry signals
        4. Saves all data to database
        5. Returns complete JSON analysis
        """
        analysis_start = datetime.now()
        self.logger.info(
            f"Starting comprehensive analysis for {symbol} on {target_date}"
        )

        # 1. Collect tick data for lookback period
        lookback_start = target_date - timedelta(days=self.MAX_LOOKBACK_DAYS)
        tick_data = self.collect_comprehensive_tick_data(
            symbol, lookback_start, target_date
        )

        # 2. Analyze patterns
        insider_signal = self.detect_insider_accumulation(symbol, target_date)
        entry_signal = self.get_entry_signal(symbol, target_date)
        volume_profile = self.analyze_volume_profile(symbol, target_date)
        spread_analysis = self.analyze_spread_behavior(symbol, target_date)

        # 3. Compile comprehensive analysis
        complete_analysis = {
            "symbol": symbol,
            "analysis_date": target_date.isoformat(),
            "analysis_duration_seconds": (
                datetime.now() - analysis_start
            ).total_seconds(),
            "tick_data_points": len(tick_data),
            "lookback_days": self.MAX_LOOKBACK_DAYS,
            # Insider analysis
            "insider_signal": asdict(insider_signal) if insider_signal else None,
            # Entry recommendation
            "entry_signal": entry_signal,
            # Volume analysis
            "volume_profile": asdict(volume_profile),
            # Spread analysis
            "spread_analysis": asdict(spread_analysis),
            # Raw data summary
            "tick_summary": {
                "total_ticks": len(tick_data),
                "total_volume": sum(tick.size for tick in tick_data),
                "price_range": {
                    "min": min(tick.price for tick in tick_data) if tick_data else 0,
                    "max": max(tick.price for tick in tick_data) if tick_data else 0,
                },
                "exchanges_used": list(set(tick.exchange for tick in tick_data)),
            },
            # Metadata
            "created_at": datetime.now().isoformat(),
            "analysis_version": "1.0",
            "data_source": "Interactive Brokers",
        }

        # 4. Store complete analysis
        self._store_complete_analysis(complete_analysis)

        self.logger.info(
            f"Analysis complete for {symbol}: {len(tick_data)} ticks, "
            f"insider signal: {insider_signal.signal_strength if insider_signal else 0:.2f}"
        )

        return complete_analysis

    def _store_complete_analysis(self, analysis: Dict):
        """Store complete analysis in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create table for complete analyses if not exists
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS complete_analyses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                analysis_date DATE NOT NULL,
                analysis_json TEXT NOT NULL,
                tick_data_points INTEGER,
                insider_signal_strength REAL,
                entry_recommended BOOLEAN,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, analysis_date)
            )
        """
        )

        cursor.execute(
            """
            INSERT OR REPLACE INTO complete_analyses
            (symbol, analysis_date, analysis_json, tick_data_points,
             insider_signal_strength, entry_recommended)
            VALUES (?, ?, ?, ?, ?, ?)
        """,
            (
                analysis["symbol"],
                analysis["analysis_date"][:10],  # Date only
                json.dumps(analysis, indent=2),
                analysis["tick_data_points"],
                (
                    analysis["insider_signal"]["signal_strength"]
                    if analysis["insider_signal"]
                    else 0
                ),
                analysis["entry_signal"]["entry_recommended"],
            ),
        )

        conn.commit()
        conn.close()
