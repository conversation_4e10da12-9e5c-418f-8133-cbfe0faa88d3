"""
Sophisticated price action detection for insider trading signals.
Uses tick data, volume patterns, and multiple timeframe analysis.
"""

from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from sqlalchemy import select, insert, and_

from core.data_service import DataService
from core.database import engine, stock_ticks, tick_analysis, stock_bars_minute
from core.logger import get_logger

logger = get_logger(__name__)


@dataclass
class InsiderSignal:
    """Represents an insider trading signal."""

    symbol: str
    signal_date: datetime
    signal_type: str  # 'accumulation', 'distribution', 'unusual_activity'
    strength: float  # 0-1 score
    indicators: List[str]
    tick_metrics: Dict[str, float]
    volume_metrics: Dict[str, float]
    price_metrics: Dict[str, float]


class InsiderPriceActionDetector:
    """
    Sophisticated price action detection for insider trading signals.

    Key patterns detected:
    1. Accumulation: Low volatility + steady volume + price support
    2. Smart money footprints: Large blocks at specific price levels
    3. Pre-news positioning: Unusual activity 1-5 days before events
    4. Algorithmic patterns: Rapid fire trades indicating informed trading
    """

    def __init__(self, data_service: DataService = None):
        self.data_service = data_service or DataService()
        self.db_conn = engine.connect()

    def detect_insider_signals(
        self, symbol: str, date: datetime, lookback_days: int = 5
    ) -> List[InsiderSignal]:
        """
        Detect insider trading signals for a symbol.

        Args:
            symbol: Stock symbol
            date: Date to analyze
            lookback_days: Days to look back for patterns

        Returns:
            List of insider signals detected
        """
        signals = []

        # 1. Analyze tick patterns
        tick_signal = self._analyze_tick_patterns(symbol, date, lookback_days)
        if tick_signal:
            signals.append(tick_signal)

        # 2. Analyze volume patterns
        volume_signal = self._analyze_volume_patterns(symbol, date, lookback_days)
        if volume_signal:
            signals.append(volume_signal)

        # 3. Analyze price action
        price_signal = self._analyze_price_action(symbol, date, lookback_days)
        if price_signal:
            signals.append(price_signal)

        # 4. Combine signals for stronger conviction
        combined_signal = self._combine_signals(signals)
        if combined_signal:
            return [combined_signal]

        return signals

    def _analyze_tick_patterns(
        self, symbol: str, date: datetime, lookback_days: int
    ) -> Optional[InsiderSignal]:
        """Analyze tick data for insider patterns."""

        # Get minute bars for the period
        start_date = date - timedelta(days=lookback_days)
        minute_bars = self.data_service.get_minute_bars(
            symbol, start_date.strftime("%Y-%m-%d"), date.strftime("%Y-%m-%d")
        )

        if minute_bars.empty:
            return None

        indicators = []
        metrics = {}

        # 1. Block trade detection
        # Look for trades that are significantly larger than average
        if "volume" in minute_bars.columns:
            avg_volume = minute_bars["volume"].mean()
            std_volume = minute_bars["volume"].std()

            # Trades > 3 standard deviations
            block_trades = minute_bars[
                minute_bars["volume"] > avg_volume + 3 * std_volume
            ]
            metrics["block_trade_count"] = len(block_trades)
            metrics["block_trade_ratio"] = len(block_trades) / len(minute_bars)

            if metrics["block_trade_count"] > 5:
                indicators.append(
                    f"Multiple block trades detected ({metrics['block_trade_count']})"
                )

        # 2. Time clustering analysis
        # Look for concentrated trading in specific time windows
        minute_bars["hour"] = pd.to_datetime(minute_bars.index).hour
        minute_bars["minute"] = pd.to_datetime(minute_bars.index).minute

        # Group by 30-minute windows
        minute_bars["time_window"] = minute_bars["hour"] * 2 + (
            minute_bars["minute"] // 30
        )
        volume_by_window = minute_bars.groupby("time_window")["volume"].sum()

        # Find windows with unusual volume
        if len(volume_by_window) > 0:
            window_avg = volume_by_window.mean()
            window_std = volume_by_window.std()
            unusual_windows = volume_by_window[
                volume_by_window > window_avg + 2 * window_std
            ]

            metrics["unusual_time_windows"] = len(unusual_windows)
            if len(unusual_windows) > 0:
                indicators.append(
                    f"Concentrated trading in {len(unusual_windows)} time windows"
                )

        # 3. Price efficiency analysis
        # Inefficient price movement can indicate informed trading
        if all(col in minute_bars.columns for col in ["high", "low", "close"]):
            # Calculate price efficiency ratio
            price_range = minute_bars["high"] - minute_bars["low"]
            price_move = abs(minute_bars["close"] - minute_bars["close"].shift(1))

            efficiency = price_move / (price_range + 0.0001)  # Avoid division by zero
            metrics["avg_price_efficiency"] = efficiency.mean()

            # Low efficiency with high volume indicates accumulation
            if metrics["avg_price_efficiency"] < 0.3 and minute_bars[
                "volume"
            ].sum() > avg_volume * len(minute_bars):
                indicators.append(
                    "Low price efficiency with high volume (accumulation pattern)"
                )

        # 4. Rapid trade detection (using minute bars as proxy)
        # Look for consecutive high-volume minutes
        high_vol_threshold = avg_volume * 2
        consecutive_high_vol = 0
        max_consecutive = 0

        for vol in minute_bars["volume"]:
            if vol > high_vol_threshold:
                consecutive_high_vol += 1
                max_consecutive = max(max_consecutive, consecutive_high_vol)
            else:
                consecutive_high_vol = 0

        metrics["max_consecutive_high_volume"] = max_consecutive
        if max_consecutive >= 5:
            indicators.append(
                f"Sustained high volume for {max_consecutive} consecutive minutes"
            )

        # Calculate signal strength
        signal_strength = 0.0
        if metrics.get("block_trade_ratio", 0) > 0.05:
            signal_strength += 0.3
        if metrics.get("unusual_time_windows", 0) > 2:
            signal_strength += 0.3
        if metrics.get("avg_price_efficiency", 1) < 0.3:
            signal_strength += 0.2
        if metrics.get("max_consecutive_high_volume", 0) >= 5:
            signal_strength += 0.2

        if signal_strength > 0.3 and indicators:
            return InsiderSignal(
                symbol=symbol,
                signal_date=date,
                signal_type="unusual_activity",
                strength=min(signal_strength, 1.0),
                indicators=indicators,
                tick_metrics=metrics,
                volume_metrics={},
                price_metrics={},
            )

        return None

    def _analyze_volume_patterns(
        self, symbol: str, date: datetime, lookback_days: int
    ) -> Optional[InsiderSignal]:
        """Analyze volume patterns for insider activity."""

        # Get daily bars for volume analysis
        start_date = date - timedelta(
            days=lookback_days + 20
        )  # Extra days for baseline
        daily_bars = self.data_service.get_daily_bars(
            symbol, start_date.strftime("%Y-%m-%d"), date.strftime("%Y-%m-%d")
        )

        if len(daily_bars) < 10:
            return None

        indicators = []
        metrics = {}

        # 1. Volume trend analysis
        recent_volume = daily_bars["volume"].iloc[-lookback_days:].mean()
        baseline_volume = daily_bars["volume"].iloc[:-lookback_days].mean()

        if baseline_volume > 0:
            volume_ratio = recent_volume / baseline_volume
            metrics["volume_increase_ratio"] = volume_ratio

            if volume_ratio > 2.0:
                indicators.append(f"Volume {volume_ratio:.1f}x above baseline")

        # 2. On-Balance Volume (OBV) analysis
        daily_bars["price_change"] = daily_bars["close"].diff()
        daily_bars["obv"] = (
            daily_bars["volume"] * np.sign(daily_bars["price_change"])
        ).cumsum()

        # Check if OBV is trending up while price is flat
        recent_bars = daily_bars.iloc[-lookback_days:]
        if len(recent_bars) > 2:
            obv_slope = np.polyfit(range(len(recent_bars)), recent_bars["obv"], 1)[0]
            price_slope = np.polyfit(range(len(recent_bars)), recent_bars["close"], 1)[
                0
            ]

            metrics["obv_slope"] = obv_slope
            metrics["price_slope"] = price_slope

            # Positive OBV with flat price indicates accumulation
            if obv_slope > 0 and abs(price_slope) < recent_bars["close"].std() * 0.1:
                indicators.append("Positive OBV divergence (accumulation)")

        # 3. Volume distribution analysis
        # Look for increasing volume on up days
        up_days = recent_bars[recent_bars["price_change"] > 0]
        down_days = recent_bars[recent_bars["price_change"] < 0]

        if len(up_days) > 0 and len(down_days) > 0:
            avg_up_volume = up_days["volume"].mean()
            avg_down_volume = down_days["volume"].mean()

            if avg_up_volume > avg_down_volume * 1.5:
                metrics["up_down_volume_ratio"] = avg_up_volume / avg_down_volume
                indicators.append(
                    f"Higher volume on up days ({metrics['up_down_volume_ratio']:.1f}x)"
                )

        # 4. Accumulation/Distribution line
        daily_bars["money_flow_mult"] = (
            (daily_bars["close"] - daily_bars["low"])
            - (daily_bars["high"] - daily_bars["close"])
        ) / (daily_bars["high"] - daily_bars["low"] + 0.0001)
        daily_bars["money_flow_vol"] = (
            daily_bars["money_flow_mult"] * daily_bars["volume"]
        )
        daily_bars["ad_line"] = daily_bars["money_flow_vol"].cumsum()

        # Check A/D line trend
        if len(recent_bars) > 2 and "ad_line" in recent_bars.columns:
            ad_slope = np.polyfit(range(len(recent_bars)), recent_bars["ad_line"], 1)[0]
            metrics["ad_line_slope"] = ad_slope

            if ad_slope > 0:
                indicators.append("Positive A/D line trend")

        # Calculate signal strength
        signal_strength = 0.0
        if metrics.get("volume_increase_ratio", 0) > 2.0:
            signal_strength += 0.3
        if "Positive OBV divergence" in " ".join(indicators):
            signal_strength += 0.3
        if metrics.get("up_down_volume_ratio", 0) > 1.5:
            signal_strength += 0.2
        if metrics.get("ad_line_slope", 0) > 0:
            signal_strength += 0.2

        if signal_strength > 0.3 and indicators:
            return InsiderSignal(
                symbol=symbol,
                signal_date=date,
                signal_type="accumulation",
                strength=min(signal_strength, 1.0),
                indicators=indicators,
                tick_metrics={},
                volume_metrics=metrics,
                price_metrics={},
            )

        return None

    def _analyze_price_action(
        self, symbol: str, date: datetime, lookback_days: int
    ) -> Optional[InsiderSignal]:
        """Analyze price action for insider patterns."""

        # Get daily bars
        start_date = date - timedelta(days=lookback_days + 20)
        daily_bars = self.data_service.get_daily_bars(
            symbol, start_date.strftime("%Y-%m-%d"), date.strftime("%Y-%m-%d")
        )

        if len(daily_bars) < 10:
            return None

        indicators = []
        metrics = {}
        recent_bars = daily_bars.iloc[-lookback_days:]

        # 1. Support level testing
        # Multiple tests of a support level can indicate accumulation
        lows = recent_bars["low"]
        support_level = lows.min()
        support_tests = len(
            lows[abs(lows - support_level) / support_level < 0.02]
        )  # Within 2%

        metrics["support_tests"] = support_tests
        if support_tests >= 3:
            indicators.append(f"Support level tested {support_tests} times")

        # 2. Volatility contraction
        # Decreasing volatility can precede a big move
        recent_volatility = recent_bars["close"].pct_change().std()
        baseline_volatility = (
            daily_bars.iloc[:-lookback_days]["close"].pct_change().std()
        )

        if baseline_volatility > 0:
            volatility_ratio = recent_volatility / baseline_volatility
            metrics["volatility_contraction"] = volatility_ratio

            if volatility_ratio < 0.6:
                indicators.append(
                    f"Volatility contraction ({volatility_ratio:.1f}x baseline)"
                )

        # 3. Higher lows pattern
        # Series of higher lows indicates accumulation
        lows_series = recent_bars["low"].values
        higher_lows = sum(
            1 for i in range(1, len(lows_series)) if lows_series[i] > lows_series[i - 1]
        )

        metrics["higher_lows_ratio"] = (
            higher_lows / (len(lows_series) - 1) if len(lows_series) > 1 else 0
        )
        if metrics["higher_lows_ratio"] > 0.6:
            indicators.append(
                f"Higher lows pattern ({metrics['higher_lows_ratio']:.0%})"
            )

        # 4. Narrow range days
        # Days with small high-low range can indicate accumulation
        daily_bars["range_pct"] = (daily_bars["high"] - daily_bars["low"]) / daily_bars[
            "close"
        ]
        avg_range = daily_bars["range_pct"].mean()

        # Make sure recent_bars has the range_pct column
        recent_bars = daily_bars.iloc[-lookback_days:]
        narrow_days = recent_bars[recent_bars["range_pct"] < avg_range * 0.5]

        metrics["narrow_range_days"] = len(narrow_days)
        if len(narrow_days) >= 3:
            indicators.append(f"{len(narrow_days)} narrow range days (consolidation)")

        # 5. Close position analysis
        # Consistently closing in upper half of range
        daily_bars["close_position"] = (daily_bars["close"] - daily_bars["low"]) / (
            daily_bars["high"] - daily_bars["low"] + 0.0001
        )

        # Update recent_bars again to include close_position
        recent_bars = daily_bars.iloc[-lookback_days:]
        avg_close_position = recent_bars["close_position"].mean()
        metrics["avg_close_position"] = avg_close_position

        if avg_close_position > 0.65:
            indicators.append(f"Strong closes (avg {avg_close_position:.0%} of range)")

        # Calculate signal strength
        signal_strength = 0.0
        if metrics.get("support_tests", 0) >= 3:
            signal_strength += 0.25
        if metrics.get("volatility_contraction", 1) < 0.6:
            signal_strength += 0.25
        if metrics.get("higher_lows_ratio", 0) > 0.6:
            signal_strength += 0.25
        if metrics.get("avg_close_position", 0) > 0.65:
            signal_strength += 0.25

        if signal_strength > 0.3 and indicators:
            return InsiderSignal(
                symbol=symbol,
                signal_date=date,
                signal_type="accumulation",
                strength=min(signal_strength, 1.0),
                indicators=indicators,
                tick_metrics={},
                volume_metrics={},
                price_metrics=metrics,
            )

        return None

    def _combine_signals(self, signals: List[InsiderSignal]) -> Optional[InsiderSignal]:
        """Combine multiple signals for stronger conviction."""

        if len(signals) < 2:
            return None

        # Aggregate indicators and metrics
        all_indicators = []
        all_tick_metrics = {}
        all_volume_metrics = {}
        all_price_metrics = {}

        for signal in signals:
            all_indicators.extend(signal.indicators)
            all_tick_metrics.update(signal.tick_metrics)
            all_volume_metrics.update(signal.volume_metrics)
            all_price_metrics.update(signal.price_metrics)

        # Calculate combined strength
        combined_strength = 1.0 - np.prod([1.0 - s.strength for s in signals])

        # Add combination bonus if multiple signal types
        signal_types = set(s.signal_type for s in signals)
        if len(signal_types) > 1:
            combined_strength = min(combined_strength * 1.2, 1.0)
            all_indicators.append(f"Multiple signal types: {', '.join(signal_types)}")

        if combined_strength > 0.5:
            return InsiderSignal(
                symbol=signals[0].symbol,
                signal_date=signals[0].signal_date,
                signal_type="combined",
                strength=combined_strength,
                indicators=list(set(all_indicators)),  # Remove duplicates
                tick_metrics=all_tick_metrics,
                volume_metrics=all_volume_metrics,
                price_metrics=all_price_metrics,
            )

        return None

    def scan_for_insider_activity(
        self, symbols: List[str], date: datetime = None
    ) -> List[InsiderSignal]:
        """
        Scan multiple symbols for insider activity.

        Args:
            symbols: List of symbols to scan
            date: Date to scan (default: today)

        Returns:
            List of insider signals found
        """
        if date is None:
            date = datetime.now()

        all_signals = []

        for symbol in symbols:
            logger.info(f"Scanning {symbol} for insider activity...")
            try:
                signals = self.detect_insider_signals(symbol, date)
                all_signals.extend(signals)
            except Exception as e:
                logger.error(f"Error scanning {symbol}: {e}")

        # Sort by strength
        all_signals.sort(key=lambda s: s.strength, reverse=True)

        return all_signals

    def close(self):
        """Clean up resources."""
        if self.db_conn:
            self.db_conn.close()
        if self.data_service:
            self.data_service.close()


def test_insider_detection():
    """Test insider detection with real data."""
    print("=== Testing Insider Price Action Detection ===\n")

    detector = InsiderPriceActionDetector()

    # Test with known symbols
    test_symbols = ["AAPL", "TSLA", "NVDA"]
    test_date = datetime.now()

    print(f"Scanning for insider activity on {test_date.strftime('%Y-%m-%d')}...")
    signals = detector.scan_for_insider_activity(test_symbols, test_date)

    if signals:
        print(f"\nFound {len(signals)} insider signals:\n")
        for signal in signals:
            print(
                f"{signal.symbol} - {signal.signal_type} (strength: {signal.strength:.2f})"
            )
            print(f"  Date: {signal.signal_date.strftime('%Y-%m-%d')}")
            print("  Indicators:")
            for indicator in signal.indicators:
                print(f"    - {indicator}")

            if signal.tick_metrics:
                print("  Tick Metrics:")
                for key, value in signal.tick_metrics.items():
                    print(f"    - {key}: {value}")

            if signal.volume_metrics:
                print("  Volume Metrics:")
                for key, value in signal.volume_metrics.items():
                    if isinstance(value, float):
                        print(f"    - {key}: {value:.3f}")
                    else:
                        print(f"    - {key}: {value}")

            if signal.price_metrics:
                print("  Price Metrics:")
                for key, value in signal.price_metrics.items():
                    if isinstance(value, float):
                        print(f"    - {key}: {value:.3f}")
                    else:
                        print(f"    - {key}: {value}")
            print()
    else:
        print("No insider signals detected.")

    detector.close()


if __name__ == "__main__":
    test_insider_detection()
