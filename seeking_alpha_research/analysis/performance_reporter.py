"""
Performance Reporting Module

Provides comprehensive performance analytics and reporting functionality.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import json
from dataclasses import dataclass, asdict


@dataclass
class PerformanceReport:
    """Container for performance metrics"""

    total_return: float
    annualized_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    trades: int


class PerformanceReporter:
    """
    Comprehensive performance reporting and analytics.

    Calculates key metrics including:
    - Sharpe ratio
    - Drawdowns
    - Win/loss statistics
    - Risk metrics
    - Monte Carlo projections
    """

    def __init__(self):
        self.risk_free_rate = 0.02  # Default 2% annual

    def calculate_sharpe_ratio(
        self,
        returns: pd.Series,
        risk_free_rate: float = None,
        periods_per_year: int = 252,
    ) -> Dict:
        """Calculate Sharpe ratio with detailed components."""
        if risk_free_rate is None:
            risk_free_rate = self.risk_free_rate

        # Annualize returns
        mean_return = returns.mean()
        annual_return = (1 + mean_return) ** periods_per_year - 1

        # Annualize volatility
        volatility = returns.std()
        annual_volatility = volatility * np.sqrt(periods_per_year)

        # Calculate Sharpe
        sharpe_ratio = (
            (annual_return - risk_free_rate) / annual_volatility
            if annual_volatility > 0
            else 0
        )

        return {
            "sharpe_ratio": sharpe_ratio,
            "annual_return": annual_return,
            "annual_volatility": annual_volatility,
            "risk_free_rate": risk_free_rate,
        }

    def analyze_drawdowns(self, cumulative_returns: pd.Series) -> Dict:
        """Analyze drawdown characteristics."""
        # Calculate running maximum
        running_max = cumulative_returns.cummax()

        # Calculate drawdowns
        drawdowns = (cumulative_returns - running_max) / running_max

        # Find maximum drawdown
        max_drawdown = drawdowns.min()

        # Find drawdown periods
        in_drawdown = drawdowns < 0
        drawdown_starts = (~in_drawdown).shift(1) & in_drawdown
        drawdown_ends = in_drawdown.shift(1) & (~in_drawdown)

        # Collect drawdown periods
        periods = []
        start_dates = drawdown_starts[drawdown_starts].index
        end_dates = drawdown_ends[drawdown_ends].index

        for i, start in enumerate(start_dates):
            if i < len(end_dates):
                end = end_dates[i]
                depth = drawdowns[start:end].min()
                # Handle both datetime and integer indices
                if hasattr(end - start, "days"):
                    duration = (end - start).days
                else:
                    # For integer indices, just use the difference
                    duration = int(end - start)

                periods.append(
                    {"start": start, "end": end, "depth": depth, "duration": duration}
                )

        # Current drawdown
        current_dd = drawdowns.iloc[-1] if drawdowns.iloc[-1] < 0 else 0

        # Max drawdown duration
        if periods:
            max_duration = max(p["duration"] for p in periods)
        else:
            max_duration = 0

        return {
            "max_drawdown": max_drawdown,
            "max_drawdown_duration": max_duration,
            "drawdown_periods": periods,
            "current_drawdown": current_dd,
            "recovery_time": None,  # Would need more logic
        }

    def calculate_underwater_curve(self, cumulative_returns: pd.Series) -> pd.Series:
        """Calculate underwater curve (drawdown over time)."""
        running_max = cumulative_returns.cummax()
        underwater = (cumulative_returns - running_max) / running_max
        return underwater

    def analyze_win_loss(self, trades: pd.DataFrame) -> Dict:
        """Analyze win/loss statistics from trades."""
        if "pnl" not in trades.columns:
            raise ValueError("Trades must have 'pnl' column")

        wins = trades[trades["pnl"] > 0]
        losses = trades[trades["pnl"] <= 0]

        total_trades = len(trades)
        winning_trades = len(wins)
        losing_trades = len(losses)

        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

        avg_win = wins["pnl"].mean() if len(wins) > 0 else 0
        avg_loss = losses["pnl"].mean() if len(losses) > 0 else 0

        # Profit factor
        total_wins = wins["pnl"].sum() if len(wins) > 0 else 0
        total_losses = abs(losses["pnl"].sum()) if len(losses) > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else float("inf")

        # Expectancy
        expectancy = trades["pnl"].mean()

        # Largest win/loss
        largest_win = wins["pnl"].max() if len(wins) > 0 else 0
        largest_loss = losses["pnl"].min() if len(losses) > 0 else 0

        # Consecutive wins/losses
        is_win = trades["pnl"] > 0
        consecutive_wins = self._max_consecutive(is_win, True)
        consecutive_losses = self._max_consecutive(is_win, False)

        return {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": win_rate,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
            "profit_factor": profit_factor,
            "expectancy": expectancy,
            "largest_win": largest_win,
            "largest_loss": largest_loss,
            "consecutive_wins": consecutive_wins,
            "consecutive_losses": consecutive_losses,
        }

    def _max_consecutive(self, series: pd.Series, value: bool) -> int:
        """Find maximum consecutive occurrences of value."""
        groups = (series != value).cumsum()
        counts = series[series == value].groupby(groups).size()
        return counts.max() if len(counts) > 0 else 0

    def calculate_risk_metrics(
        self, returns: pd.Series, confidence_level: float = 0.95
    ) -> Dict:
        """Calculate comprehensive risk metrics."""
        # Basic stats
        volatility_daily = returns.std()
        volatility_annual = volatility_daily * np.sqrt(252)

        # Value at Risk (VaR)
        var_percentile = (1 - confidence_level) * 100
        var_95 = np.percentile(returns, var_percentile)

        # Conditional VaR (CVaR)
        cvar_95 = returns[returns <= var_95].mean()

        # Downside deviation
        negative_returns = returns[returns < 0]
        downside_deviation = negative_returns.std() if len(negative_returns) > 0 else 0

        # Sortino ratio (using downside deviation)
        mean_return = returns.mean()
        annual_return = (1 + mean_return) ** 252 - 1
        sortino_ratio = (
            (annual_return - self.risk_free_rate) / (downside_deviation * np.sqrt(252))
            if downside_deviation > 0
            else 0
        )

        # Calmar ratio (return / max drawdown)
        cumulative = (1 + returns).cumprod()
        max_dd = self.analyze_drawdowns(cumulative)["max_drawdown"]
        calmar_ratio = annual_return / abs(max_dd) if max_dd != 0 else 0

        # Omega ratio
        threshold = 0
        gains = returns[returns > threshold] - threshold
        losses = threshold - returns[returns <= threshold]
        omega_ratio = gains.sum() / losses.sum() if losses.sum() > 0 else float("inf")

        # Skewness and kurtosis
        skewness = returns.skew()
        kurtosis = returns.kurtosis()

        # Max daily loss
        max_daily_loss = returns.min()

        return {
            "volatility_daily": volatility_daily,
            "volatility_annual": volatility_annual,
            "var_95": var_95,
            "cvar_95": cvar_95,
            "downside_deviation": downside_deviation,
            "sortino_ratio": sortino_ratio,
            "calmar_ratio": calmar_ratio,
            "omega_ratio": omega_ratio,
            "skewness": skewness,
            "kurtosis": kurtosis,
            "max_daily_loss": max_daily_loss,
        }

    def generate_full_report(
        self,
        trades: pd.DataFrame,
        daily_returns: pd.Series,
        initial_capital: float,
        benchmark_returns: pd.Series = None,
    ) -> Dict:
        """Generate comprehensive performance report."""
        # Calculate all metrics
        cumulative_returns = (1 + daily_returns).cumprod()

        # Summary statistics
        total_return = cumulative_returns.iloc[-1] - 1
        annualized_return = (1 + total_return) ** (252 / len(daily_returns)) - 1

        sharpe_data = self.calculate_sharpe_ratio(daily_returns)
        drawdown_data = self.analyze_drawdowns(cumulative_returns)

        # Prepare trades data
        if (
            "pnl" not in trades.columns
            and "exit_price" in trades.columns
            and "entry_price" in trades.columns
        ):
            trades["pnl"] = (trades["exit_price"] - trades["entry_price"]) * trades.get(
                "shares", 100
            )

        win_loss_data = self.analyze_win_loss(trades)
        risk_data = self.calculate_risk_metrics(daily_returns)

        report = {
            "summary": {
                "total_return": total_return,
                "annualized_return": annualized_return,
                "sharpe_ratio": sharpe_data["sharpe_ratio"],
                "max_drawdown": drawdown_data["max_drawdown"],
                "win_rate": win_loss_data["win_rate"],
                "profit_factor": win_loss_data["profit_factor"],
                "total_trades": win_loss_data["total_trades"],
            },
            "returns_analysis": sharpe_data,
            "risk_analysis": risk_data,
            "trade_analysis": win_loss_data,
            "drawdown_analysis": drawdown_data,
        }

        # Benchmark comparison if provided
        if benchmark_returns is not None:
            report["benchmark_comparison"] = self.compare_to_benchmark(
                daily_returns, benchmark_returns
            )

        return report

    def format_report_text(self, report: Dict) -> str:
        """Format report as readable text."""
        lines = [
            "=" * 60,
            "PERFORMANCE REPORT",
            "=" * 60,
            "",
            "Summary Statistics",
            "-" * 30,
            f"Total Return: {report['summary']['total_return']:.2%}",
            f"Annualized Return: {report['summary']['annualized_return']:.2%}",
            f"Sharpe Ratio: {report['summary']['sharpe_ratio']:.2f}",
            f"Max Drawdown: {report['summary']['max_drawdown']:.2%}",
            f"Win Rate: {report['summary']['win_rate']:.1f}%",
            f"Profit Factor: {report['summary']['profit_factor']:.2f}",
            f"Total Trades: {report['summary']['total_trades']}",
            "",
            "Risk Analysis",
            "-" * 30,
            f"Daily Volatility: {report['risk_analysis']['volatility_daily']:.3%}",
            f"Annual Volatility: {report['risk_analysis']['volatility_annual']:.2%}",
            f"95% VaR: {report['risk_analysis']['var_95']:.3%}",
            f"95% CVaR: {report['risk_analysis']['cvar_95']:.3%}",
            f"Sortino Ratio: {report['risk_analysis']['sortino_ratio']:.2f}",
            f"Calmar Ratio: {report['risk_analysis']['calmar_ratio']:.2f}",
            "",
            "Trade Analysis",
            "-" * 30,
            f"Winning Trades: {report['trade_analysis']['winning_trades']}",
            f"Losing Trades: {report['trade_analysis']['losing_trades']}",
            f"Average Win: ${report['trade_analysis']['avg_win']:.2f}",
            f"Average Loss: ${report['trade_analysis']['avg_loss']:.2f}",
            f"Largest Win: ${report['trade_analysis']['largest_win']:.2f}",
            f"Largest Loss: ${report['trade_analysis']['largest_loss']:.2f}",
            "=" * 60,
        ]

        return "\n".join(lines)

    def export_json(self, report: Dict) -> str:
        """Export report as JSON."""
        return json.dumps(report, indent=2, default=str)

    def calculate_rolling_metrics(
        self, returns: pd.Series, window: int, metrics: List[str] = None
    ) -> pd.DataFrame:
        """Calculate rolling performance metrics."""
        if metrics is None:
            metrics = ["sharpe", "volatility", "max_drawdown"]

        results = pd.DataFrame(index=returns.index[window - 1 :])

        for i in range(window - 1, len(returns)):
            window_returns = returns.iloc[i - window + 1 : i + 1]

            if "sharpe" in metrics:
                sharpe_data = self.calculate_sharpe_ratio(window_returns)
                results.loc[returns.index[i], "rolling_sharpe"] = sharpe_data[
                    "sharpe_ratio"
                ]

            if "volatility" in metrics:
                results.loc[returns.index[i], "rolling_volatility"] = (
                    window_returns.std() * np.sqrt(252)
                )

            if "max_drawdown" in metrics:
                cum_returns = (1 + window_returns).cumprod()
                dd_data = self.analyze_drawdowns(cum_returns)
                results.loc[returns.index[i], "rolling_max_drawdown"] = dd_data[
                    "max_drawdown"
                ]

        return results

    def monte_carlo_projection(
        self,
        historical_returns: pd.Series,
        initial_capital: float,
        n_simulations: int = 1000,
        n_days: int = 252,
    ) -> Dict:
        """Run Monte Carlo simulation for future projections."""
        # Calculate return statistics
        mean_return = historical_returns.mean()
        std_return = historical_returns.std()

        # Run simulations
        simulations = np.zeros((n_simulations, n_days))

        for i in range(n_simulations):
            # Generate random returns
            random_returns = np.random.normal(mean_return, std_return, n_days)

            # Calculate cumulative values
            cumulative = initial_capital * np.cumprod(1 + random_returns)
            simulations[i] = cumulative

        # Calculate statistics
        final_values = simulations[:, -1]

        # Calculate max drawdowns for each simulation
        max_drawdowns = []
        for sim in simulations:
            cum_returns = pd.Series(sim / initial_capital)
            dd = self.analyze_drawdowns(cum_returns)["max_drawdown"]
            max_drawdowns.append(dd)

        return {
            "simulations": simulations,
            "statistics": {
                "mean_final_value": np.mean(final_values),
                "median_final_value": np.median(final_values),
                "std_final_value": np.std(final_values),
                "probability_of_profit": np.mean(final_values > initial_capital),
                "expected_max_drawdown": np.mean(max_drawdowns),
            },
            "percentiles": {
                5: np.percentile(final_values, 5),
                25: np.percentile(final_values, 25),
                50: np.percentile(final_values, 50),
                75: np.percentile(final_values, 75),
                95: np.percentile(final_values, 95),
            },
        }

    def compare_to_benchmark(
        self, strategy_returns: pd.Series, benchmark_returns: pd.Series
    ) -> Dict:
        """Compare strategy performance to benchmark."""
        # Align series
        aligned = pd.DataFrame(
            {"strategy": strategy_returns, "benchmark": benchmark_returns}
        ).dropna()

        # Calculate beta
        covariance = aligned["strategy"].cov(aligned["benchmark"])
        benchmark_variance = aligned["benchmark"].var()
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 0

        # Calculate alpha
        strategy_annual = (1 + aligned["strategy"].mean()) ** 252 - 1
        benchmark_annual = (1 + aligned["benchmark"].mean()) ** 252 - 1
        alpha = strategy_annual - (
            self.risk_free_rate + beta * (benchmark_annual - self.risk_free_rate)
        )

        # Correlation
        correlation = aligned["strategy"].corr(aligned["benchmark"])

        # Tracking error
        tracking_error = (aligned["strategy"] - aligned["benchmark"]).std() * np.sqrt(
            252
        )

        # Information ratio
        excess_returns = aligned["strategy"] - aligned["benchmark"]
        information_ratio = (
            excess_returns.mean() / excess_returns.std() * np.sqrt(252)
            if excess_returns.std() > 0
            else 0
        )

        # Capture ratios
        up_market = aligned[aligned["benchmark"] > 0]
        down_market = aligned[aligned["benchmark"] < 0]

        if len(up_market) > 0:
            upside_capture = (
                up_market["strategy"].mean() / up_market["benchmark"].mean()
            )
        else:
            upside_capture = 0

        if len(down_market) > 0:
            downside_capture = (
                down_market["strategy"].mean() / down_market["benchmark"].mean()
            )
        else:
            downside_capture = 0

        return {
            "alpha": alpha,
            "beta": beta,
            "correlation": correlation,
            "tracking_error": tracking_error,
            "information_ratio": information_ratio,
            "capture_ratios": {
                "upside_capture": upside_capture,
                "downside_capture": downside_capture,
            },
        }

    def calculate_attribution(
        self, trades: pd.DataFrame, attribution_factors: List[str]
    ) -> Dict:
        """Calculate performance attribution by factors."""
        if "pnl" not in trades.columns:
            if "return_pct" in trades.columns and "position_value" in trades.columns:
                # Real calculation: PnL = return % * position value
                trades["pnl"] = trades["return_pct"] / 100.0 * trades["position_value"]
            elif (
                "return_pct" in trades.columns
                and "shares" in trades.columns
                and "entry_price" in trades.columns
            ):
                # Real calculation: PnL = return % * (shares * entry_price)
                trades["pnl"] = (
                    trades["return_pct"]
                    / 100.0
                    * (trades["shares"] * trades["entry_price"])
                )
            else:
                raise ValueError(
                    "Trades must have 'pnl' column OR ('return_pct' + 'position_value') OR ('return_pct' + 'shares' + 'entry_price')"
                )

        attribution = {}

        for factor in attribution_factors:
            if factor not in trades.columns:
                continue

            # Group by factor
            grouped = trades.groupby(factor)

            factor_attribution = {}
            for name, group in grouped:
                total_pnl = group["pnl"].sum()
                trade_count = len(group)
                avg_pnl = group["pnl"].mean()
                win_rate = (group["pnl"] > 0).mean() * 100

                factor_attribution[name] = {
                    "total_pnl": total_pnl,
                    "trade_count": trade_count,
                    "avg_pnl": avg_pnl,
                    "win_rate": win_rate,
                    "contributions": (
                        total_pnl / trades["pnl"].sum()
                        if trades["pnl"].sum() != 0
                        else 0
                    ),
                    "return_pct": group.get("return_pct", group["pnl"] / 1000).mean(),
                }

            attribution[factor] = factor_attribution

        return attribution

    def export_csv(self, report_data: Dict) -> Dict[str, str]:
        """Export report data as CSV strings."""
        csv_outputs = {}

        # Export trades if available
        if "trades" in report_data and isinstance(report_data["trades"], pd.DataFrame):
            csv_outputs["trades.csv"] = report_data["trades"].to_csv(index=False)

        # Export daily performance if available
        if "daily_performance" in report_data and isinstance(
            report_data["daily_performance"], pd.DataFrame
        ):
            csv_outputs["daily_performance.csv"] = report_data[
                "daily_performance"
            ].to_csv()

        return csv_outputs
