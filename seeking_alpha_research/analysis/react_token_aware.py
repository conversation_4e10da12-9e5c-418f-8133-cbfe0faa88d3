#!/usr/bin/env python3
"""
Token-Aware ReAct Analyzer with Conversation Format

Sends prompts as a conversation to manage token limits.
NO FAKES, NO MOCKS - Real analysis with real data.
"""

import tiktoken
import litellm
import logging
from typing import Dict, List, Any, Tuple
import json

logger = logging.getLogger(__name__)


class TokenAwareReActAnalyzer:
    """ReAct analyzer that manages tokens through conversation format."""
    
    def __init__(self):
        # Initialize tokenizer for token counting
        self.tokenizer = tiktoken.encoding_for_model("gpt-3.5-turbo")  # Works for most models
        self.max_tokens_per_message = 3000  # Conservative limit
        self.max_conversation_tokens = 12000  # Total conversation limit
        
    def count_tokens(self, text: str) -> int:
        """Count tokens in text."""
        return len(self.tokenizer.encode(text))
    
    def create_react_conversation(
        self, 
        symbol: str,
        aggregated_data: Dict[str, Any],
        extracted_data: List[Dict],
        fundamentals_data: Dict[str, Any] = None
    ) -> List[Dict[str, str]]:
        """Create a conversation format for ReAct analysis."""
        
        conversation = []
        total_tokens = 0
        
        # System message - Brief and focused
        system_msg = f"""You are a financial analyst using ReAct to assess ATM dilution risk for {symbol}.
Focus on: cash burn rate, ATM probability, and timing prediction.
Be concise but thorough."""
        
        system_tokens = self.count_tokens(system_msg)
        total_tokens += system_tokens
        logger.info(f"System message: {system_tokens} tokens")
        
        conversation.append({
            "role": "system",
            "content": system_msg
        })
        
        # Step 1: Provide financial overview
        financial_overview = self._create_financial_overview(
            symbol, aggregated_data, fundamentals_data
        )
        overview_tokens = self.count_tokens(financial_overview)
        total_tokens += overview_tokens
        logger.info(f"Financial overview: {overview_tokens} tokens")
        
        conversation.append({
            "role": "user",
            "content": financial_overview
        })
        
        # Assistant acknowledges
        ack = "I understand. I'll analyze the financial position and ATM risk. Let me start with the cash runway calculation."
        ack_tokens = self.count_tokens(ack)
        total_tokens += ack_tokens
        
        conversation.append({
            "role": "assistant",
            "content": ack
        })
        
        # Step 2: Provide key filing insights (chunked if needed)
        insights_added = 0
        for i, data in enumerate(extracted_data[:3]):  # Top 3 most recent
            if total_tokens > self.max_conversation_tokens * 0.7:  # Leave room for response
                break
                
            insight = self._create_filing_insight(data, i+1)
            insight_tokens = self.count_tokens(insight)
            
            if total_tokens + insight_tokens < self.max_conversation_tokens:
                conversation.append({
                    "role": "user",
                    "content": insight
                })
                total_tokens += insight_tokens
                insights_added += 1
                logger.info(f"Filing insight {i+1}: {insight_tokens} tokens")
                
                # Brief assistant response
                brief_response = f"Noted filing {i+1}. Analyzing the implications."
                response_tokens = self.count_tokens(brief_response)
                conversation.append({
                    "role": "assistant",
                    "content": brief_response
                })
                total_tokens += response_tokens
        
        # Final prompt for analysis
        final_prompt = self._create_final_analysis_prompt()
        prompt_tokens = self.count_tokens(final_prompt)
        total_tokens += prompt_tokens
        logger.info(f"Final prompt: {prompt_tokens} tokens")
        
        conversation.append({
            "role": "user",
            "content": final_prompt
        })
        
        logger.info(f"Total conversation tokens: {total_tokens}")
        logger.info(f"Messages in conversation: {len(conversation)}")
        
        return conversation
    
    def _create_financial_overview(
        self, 
        symbol: str,
        aggregated: Dict[str, Any],
        fundamentals: Dict[str, Any] = None
    ) -> str:
        """Create concise financial overview."""
        
        # Extract key metrics
        latest_cash = aggregated.get('latest_cash', 0) or 0
        latest_expense = aggregated.get('latest_quarterly_expense', 0) or 0
        has_atm = aggregated.get('has_atm_program', False)
        shelf_capacity = aggregated.get('max_shelf_capacity', 0) or 0
        
        overview = f"""FINANCIAL OVERVIEW for {symbol}:

Cash Position: ${latest_cash:,.0f}
Quarterly Expenses: ${latest_expense:,.0f}
Monthly Burn Rate: ${latest_expense/3:,.0f}
ATM Program: {'Yes' if has_atm else 'No'}
Shelf Capacity: ${shelf_capacity:,.0f}"""

        if fundamentals:
            shares_out = fundamentals.get('shares_outstanding_millions', 0)
            float_m = fundamentals.get('float_millions', 0)
            insider_pct = fundamentals.get('insider_percent', 0)
            
            overview += f"""

SHARE STRUCTURE:
Outstanding: {shares_out:.1f}M shares
Float: {float_m:.1f}M ({float_m/shares_out*100:.1f}% of outstanding)
Insider Ownership: {insider_pct:.1f}%
Float Category: {'LOW (<20M)' if float_m < 20 else 'MODERATE' if float_m < 50 else 'HIGH'}"""

        return overview
    
    def _create_filing_insight(self, filing_data: Dict, number: int) -> str:
        """Create concise filing insight."""
        
        filing_type = filing_data.get('filing_type', 'Unknown')
        filed_date = filing_data.get('filed_date', 'Unknown')
        
        # Get most important quotes only
        quotes = filing_data.get('key_quotes', [])[:2]  # Max 2 quotes
        quotes_text = "\n".join([f"- {q[:150]}..." if len(q) > 150 else f"- {q}" for q in quotes])
        
        insight = f"""Filing {number} - {filing_type} ({filed_date}):
{quotes_text if quotes_text else "- No key insights extracted"}"""
        
        # Add financial data if available
        if filing_data.get('cash_position'):
            insight += f"\nCash: ${filing_data['cash_position']:,.0f}"
        if filing_data.get('quarterly_expenses'):
            insight += f"\nExpenses: ${filing_data['quarterly_expenses']:,.0f}"
            
        return insight
    
    def _create_final_analysis_prompt(self) -> str:
        """Create final analysis prompt."""
        
        return """Based on the financial data and filings, provide your FINAL ANALYSIS.

REQUIRED JSON FORMAT:
{
  "cash_runway_months": X,
  "monthly_burn": X,
  "predicted_atm_date": "YYYY-MM-DD",
  "atm_probability": 0.X,
  "risk_category": "LOW/MEDIUM/HIGH",
  "confidence": 0.X,
  "key_insights": ["insight 1", "insight 2", "insight 3"],
  "reasoning": "Brief explanation of your analysis"
}

Respond with ONLY the JSON, no other text."""
    
    def analyze_with_conversation(
        self,
        symbol: str,
        aggregated_data: Dict[str, Any],
        extracted_data: List[Dict],
        fundamentals_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Run analysis using conversation format."""
        
        try:
            # Create conversation
            conversation = self.create_react_conversation(
                symbol, aggregated_data, extracted_data, fundamentals_data
            )
            
            # Call LLM with conversation
            logger.info(f"Calling LLM with {len(conversation)} message conversation")
            
            response = litellm.completion(
                model="gemini/gemini-1.5-flash",
                messages=conversation,
                max_tokens=800,  # Reasonable response size
                temperature=0.1,
                timeout=30
            )
            
            response_text = response.choices[0].message.content.strip()
            logger.info(f"LLM response tokens: {self.count_tokens(response_text)}")
            
            # Parse JSON response
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return result
            else:
                raise ValueError("No JSON found in response")
                
        except Exception as e:
            logger.error(f"Conversation analysis failed: {e}")
            raise


# Test token counting
if __name__ == "__main__":
    analyzer = TokenAwareReActAnalyzer()
    
    # Test text
    test_text = """This is a test of token counting. 
    It should give us an idea of how many tokens we're using."""
    
    tokens = analyzer.count_tokens(test_text)
    print(f"Test text has {tokens} tokens")
    
    # Test with sample data
    sample_aggregated = {
        'latest_cash': 100000000,
        'latest_quarterly_expense': 30000000,
        'has_atm_program': True,
        'max_shelf_capacity': 500000000
    }
    
    sample_fundamentals = {
        'shares_outstanding_millions': 500,
        'float_millions': 15,
        'insider_percent': 2.5
    }
    
    conversation = analyzer.create_react_conversation(
        'TEST', 
        sample_aggregated, 
        [], 
        sample_fundamentals
    )
    
    print(f"\nConversation has {len(conversation)} messages")
    for i, msg in enumerate(conversation):
        tokens = analyzer.count_tokens(msg['content'])
        print(f"Message {i+1} ({msg['role']}): {tokens} tokens")