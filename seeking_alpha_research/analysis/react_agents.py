"""
Fixed ReAct Agents using LiteLLM with Gemini + IBKR Fundamental Data

This replaces the broken react_agents.py with:
1. Real LiteLLM integration for Gemini
2. IBKR fundamental data integration
3. Actual filing analysis (not hardcoded values)
4. Proper error handling (fails loudly)
"""

import os
import json
import re
import requests
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np

try:
    import ib_async
    from ib_fundamental import CompanyFinancials

    IBKR_AVAILABLE = True
except ImportError:
    IBKR_AVAILABLE = False
    print(
        "WARNING: ib_async and ib_fundamental not available - fundamental data will be limited"
    )

from utils.filing_cache_manager import FilingCacheManager
from core.logger import get_logger
import litellm

logger = get_logger(__name__)

# Configure LiteLLM for Gemini
os.environ["GEMINI_API_KEY"] = os.getenv("GEMINI_API_KEY", "")
litellm.set_verbose = False


class IBKRFundamentalData:
    """Fetch fundamental data from IBKR for enhanced analysis."""

    def __init__(self, ib_host="localhost", ib_port=4001):
        self.ib_host = ib_host
        self.ib_port = ib_port
        self.ib = None
        self.connected = False

        if IBKR_AVAILABLE:
            self._connect()

    def _connect(self):
        """Connect to IBKR Gateway."""
        try:
            self.ib = ib_async.IB()
            self.ib.connect(self.ib_host, self.ib_port)
            self.connected = True
            logger.info(f"Connected to IBKR Gateway at {self.ib_host}:{self.ib_port}")
        except Exception as e:
            logger.warning(f"Could not connect to IBKR Gateway: {e}")
            self.connected = False

    def get_fundamental_data(self, symbol: str) -> Dict[str, Any]:
        """Get fundamental data for symbol from IBKR."""
        if not self.connected or not IBKR_AVAILABLE:
            return {}

        try:
            company = CompanyFinancials(ib=self.ib, symbol=symbol)

            # Get latest financial data
            data = {
                "company_info": {},
                "income_statement": {},
                "balance_sheet": {},
                "cash_flow": {},
                "eps_ttm": 0,
                "revenue_ttm": 0,
            }

            # Company information
            if hasattr(company, "company_information"):
                company_info = company.company_information
                if not company_info.empty:
                    data["company_info"] = company_info.to_dict()

            # Latest income statement (quarterly)
            if hasattr(company, "income_quarter"):
                income_q = company.income_quarter
                if not income_q.empty and len(income_q.columns) > 1:
                    latest_quarter = income_q.columns[-1]  # Most recent quarter

                    # Extract key metrics
                    data["income_statement"] = {
                        "quarter": latest_quarter,
                        "revenue": self._extract_value(
                            income_q, "Revenue", latest_quarter
                        ),
                        "operating_income": self._extract_value(
                            income_q, "Operating Income", latest_quarter
                        ),
                        "net_income": self._extract_value(
                            income_q, "Net Income", latest_quarter
                        ),
                        "rd_expenses": self._extract_value(
                            income_q, "Research & Development", latest_quarter
                        ),
                        "operating_expenses": self._extract_value(
                            income_q, "Total Operating Expense", latest_quarter
                        ),
                    }

            # EPS TTM
            if hasattr(company, "eps_ttm"):
                eps_data = company.eps_ttm
                if not eps_data.empty:
                    data["eps_ttm"] = float(eps_data.iloc[-1]["eps"])

            # Revenue TTM (from income annual)
            if hasattr(company, "income_annual"):
                income_annual = company.income_annual
                if not income_annual.empty and len(income_annual.columns) > 1:
                    latest_year = income_annual.columns[-1]
                    data["revenue_ttm"] = self._extract_value(
                        income_annual, "Revenue", latest_year
                    )

            logger.info(f"Retrieved IBKR fundamental data for {symbol}")
            return data

        except Exception as e:
            logger.warning(f"Error fetching IBKR fundamental data for {symbol}: {e}")
            return {}

    def _extract_value(self, df, metric_name, column):
        """Extract numeric value from financial dataframe."""
        try:
            # Find row with metric name
            matching_rows = df[
                df["map_item"].str.contains(metric_name, case=False, na=False)
            ]
            if not matching_rows.empty:
                value = matching_rows.iloc[0][column]
                return float(value) if pd.notna(value) else None
        except Exception:
            pass
        return None

    def disconnect(self):
        """Disconnect from IBKR."""
        if self.ib and self.connected:
            self.ib.disconnect()
            self.connected = False


class EnhancedSECFilingAnalyzer:
    """Enhanced filing analyzer using LiteLLM + IBKR fundamental data."""

    def __init__(self, cache_manager: FilingCacheManager):
        self.cache_manager = cache_manager
        self.ibkr_data = IBKRFundamentalData()

        # Check API key
        if not os.getenv("GEMINI_API_KEY"):
            raise ValueError(
                "GEMINI_API_KEY not set - cannot analyze filings without LLM access"
            )

    def _fetch_filing_text(self, filing_url: str) -> Optional[str]:
        """Fetch SEC filing text from URL."""
        try:
            # Convert SEC URL to text format if needed
            if "sec.gov" in filing_url and "htm" in filing_url:
                text_url = filing_url.replace(".htm", ".txt")
            else:
                text_url = filing_url

            response = requests.get(text_url, timeout=30)
            response.raise_for_status()

            # Extract meaningful content (skip SGML headers)
            content = response.text

            # Find document start
            doc_start = content.find("<DOCUMENT>")
            if doc_start != -1:
                content = content[doc_start:]

            # Remove HTML tags
            content = re.sub(r"<[^>]+>", " ", content)

            # Clean up whitespace
            content = re.sub(r"\s+", " ", content).strip()

            return content[:50000]  # Limit to first 50k chars

        except Exception as e:
            logger.error(f"Failed to fetch filing text from {filing_url}: {e}")
            return None

    def _analyze_single_filing(
        self, filing: pd.Series, ibkr_data: Dict = None
    ) -> Dict[str, Any]:
        """Analyze a single filing using LiteLLM with Gemini + IBKR data."""

        try:
            # Fetch filing text
            filing_text = self._fetch_filing_text(filing["filing_url"])
            if not filing_text:
                logger.warning(
                    f"Could not fetch filing text for {filing['filing_url']}"
                )
                return None

            # Prepare IBKR context
            ibkr_context = ""
            if ibkr_data and ibkr_data.get("income_statement"):
                income = ibkr_data["income_statement"]
                ibkr_context = f"""
IBKR Fundamental Data (Recent Quarter):
- Revenue: ${income.get('revenue', 'N/A'):,}
- Operating Income: ${income.get('operating_income', 'N/A'):,}
- Net Income: ${income.get('net_income', 'N/A'):,}
- Operating Expenses: ${income.get('operating_expenses', 'N/A'):,}
- EPS TTM: ${ibkr_data.get('eps_ttm', 'N/A')}
"""

            # Enhanced analysis prompt with IBKR data
            analysis_prompt = f"""
You are an expert SEC filing analyst. Analyze this filing for At-The-Market (ATM) offering risk.

Filing Details:
- Type: {filing['form_type']}
- Date: {filing['filed_at']}
- Symbol: {filing.get('symbol', 'Unknown')}

{ibkr_context}

Filing Text (first 8000 chars):
{filing_text[:8000]}

CRITICAL ANALYSIS REQUIRED:

1. CASH POSITION: Find exact cash and cash equivalents amount (look for balance sheet)
2. BURN RATE: Calculate monthly cash burn from operating expenses or net loss
3. ATM PROGRAM: Search for "at-the-market", "equity distribution agreement", "ATM", "shelf registration"
4. OFFERING HISTORY: Look for recent equity offerings or dilution
5. CASH RUNWAY: Calculate months until cash depletion

Compare filing data with IBKR fundamental data for consistency.

Return ONLY valid JSON:
{{
  "cash_position": [exact amount in USD, not null],
  "quarterly_expenses": [quarterly operating expenses/loss],
  "cash_burn_monthly": [monthly burn rate calculated],
  "has_atm_shelf": [true if ATM program found],
  "atm_amount": [ATM facility size if found],
  "months_runway": [cash_position / monthly_burn],
  "confidence": [0.0-1.0 confidence in analysis],
  "key_findings": "[2-3 sentence summary]"
}}

IMPORTANT: Return realistic numbers based on actual filing content. Do NOT use placeholder values.
"""

            response = litellm.completion(
                model="gemini/gemini-1.5-flash",
                messages=[{"role": "user", "content": analysis_prompt}],
                max_tokens=800,
                temperature=0.1,
            )

            # Parse response
            response_text = response.choices[0].message.content

            # Extract JSON from response
            json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())

                # Validation - fail loudly if data looks fake
                if result.get("cash_position") == 5_000_000:
                    raise ValueError(
                        "CRITICAL: LLM returned placeholder cash position of $5M"
                    )

                if result.get("cash_burn_monthly") == 1_000_000:
                    raise ValueError(
                        "CRITICAL: LLM returned placeholder burn rate of $1M/month"
                    )

                # Add metadata
                result["filing_type"] = filing["form_type"]
                result["filed_date"] = filing["filed_at"]
                result["filing_url"] = filing["filing_url"]
                result["analysis_timestamp"] = datetime.now().isoformat()

                # Cross-check with IBKR data
                if ibkr_data and ibkr_data.get("income_statement"):
                    ibkr_expenses = ibkr_data["income_statement"].get(
                        "operating_expenses"
                    )
                    if ibkr_expenses and result.get("quarterly_expenses"):
                        ratio = abs(result["quarterly_expenses"] - ibkr_expenses) / max(
                            result["quarterly_expenses"], ibkr_expenses
                        )
                        if ratio > 0.5:  # More than 50% difference
                            result["ibkr_discrepancy"] = (
                                f"Filing shows {result['quarterly_expenses']:,.0f} vs IBKR {ibkr_expenses:,.0f}"
                            )

                logger.info(
                    f"Analyzed {filing['form_type']} - Cash: ${result.get('cash_position', 0):,}, Burn: ${result.get('cash_burn_monthly', 0):,}/month"
                )
                return result
            else:
                logger.error(f"Could not parse JSON from LLM response: {response_text}")
                raise ValueError(f"LLM returned invalid JSON: {response_text[:200]}")

        except Exception as e:
            logger.error(f"Failed to analyze filing {filing['filing_url']}: {e}")
            # FAIL LOUDLY - don't return fake data
            raise ValueError(
                f"CRITICAL: Filing analysis failed for {filing.get('form_type', 'Unknown')} - {e}"
            )

    def analyze_company_filings(
        self, symbol: str, filings_df: pd.DataFrame, analysis_date: str
    ) -> Dict[str, Any]:
        """
        Analyze ALL filings for a company over 2 years.

        This replaces the broken version that only analyzed 1 filing.
        """
        start_time = datetime.now()

        # Check cache first
        filing_urls = filings_df["filing_url"].tolist()

        needs_analysis, reason = self.cache_manager.needs_analysis(
            symbol, analysis_date, filing_urls
        )

        if not needs_analysis:
            cached = self.cache_manager.get_cached_analysis(symbol, analysis_date)
            if cached:
                logger.info(f"Using cached analysis for {symbol}: {reason}")
                return cached

        logger.info(f"Analyzing {len(filings_df)} filings for {symbol}: {reason}")

        # Get IBKR fundamental data for cross-validation
        ibkr_data = self.ibkr_data.get_fundamental_data(symbol)

        # Analyze each filing - CRITICAL: Analyze ALL, not just 1
        analyses = []
        total_tokens = 0
        total_cost = 0

        for idx, filing in filings_df.iterrows():
            try:
                # Add symbol to filing data
                filing_with_symbol = filing.copy()
                filing_with_symbol["symbol"] = symbol

                analysis = self._analyze_single_filing(filing_with_symbol, ibkr_data)
                if analysis:
                    analyses.append(analysis)

                    # Save individual filing analysis to cache
                    filed_date = pd.to_datetime(filing["filed_at"]).to_pydatetime()
                    self.cache_manager.save_filing_analysis(
                        symbol=symbol,
                        filing_url=filing["filing_url"],
                        filing_type=filing["form_type"],
                        filed_date=filed_date,
                        analysis_result=analysis,
                    )

                    # Estimate token usage (rough)
                    total_tokens += 1000  # Approximate
                    total_cost += 0.001  # Approximate Gemini cost

            except Exception as e:
                logger.error(
                    f"Failed to analyze {filing['form_type']} from {filing['filed_at']}: {e}"
                )
                # Continue with other filings, but log the failure
                continue

        # CRITICAL CHECK: Must have analyzed multiple filings for 2-year analysis
        if len(analyses) < 2:
            raise ValueError(
                f"CRITICAL: Only analyzed {len(analyses)} filings for {symbol} - need multiple filings for 2-year analysis"
            )

        # Aggregate results
        aggregated = self._aggregate_filing_analyses(analyses, ibkr_data)

        # Add performance metrics
        processing_time = (datetime.now() - start_time).total_seconds()

        performance_metrics = {
            "total_tokens": total_tokens,
            "total_cost": total_cost,
            "processing_time": processing_time,
            "filings_analyzed": len(analyses),
            "filings_failed": len(filings_df) - len(analyses),
        }

        # Save company analysis to cache
        self.cache_manager.save_company_analysis(
            symbol=symbol,
            analysis_date=analysis_date,
            filing_urls=filing_urls,
            analysis_result=aggregated,
            performance_metrics=performance_metrics,
        )

        logger.info(
            f"Completed analysis for {symbol}: {len(analyses)} filings, ${total_cost:.4f} cost"
        )

        return aggregated

    def _aggregate_filing_analyses(
        self, analyses: List[Dict], ibkr_data: Dict = None
    ) -> Dict[str, Any]:
        """Aggregate multiple filing analyses into company-level assessment."""

        if not analyses:
            raise ValueError("CRITICAL: No filing analyses to aggregate")

        # Extract trends over time
        cash_positions = [
            a["cash_position"] for a in analyses if a.get("cash_position")
        ]
        burn_rates = [
            a["cash_burn_monthly"] for a in analyses if a.get("cash_burn_monthly")
        ]
        runways = [a["months_runway"] for a in analyses if a.get("months_runway")]

        # ATM program detection
        has_atm = any(a.get("has_atm_shelf", False) for a in analyses)
        atm_amounts = [a["atm_amount"] for a in analyses if a.get("atm_amount")]

        # Calculate sophisticated ATM probability
        atm_probability = self._calculate_atm_probability(
            cash_positions=cash_positions,
            burn_rates=burn_rates,
            runways=runways,
            has_atm=has_atm,
            ibkr_data=ibkr_data,
        )

        # Trend analysis
        cash_trend = "stable"
        if len(cash_positions) >= 2:
            if cash_positions[-1] < cash_positions[0] * 0.8:
                cash_trend = "declining"
            elif cash_positions[-1] > cash_positions[0] * 1.2:
                cash_trend = "increasing"

        return {
            "atm_probability": atm_probability,
            "cash_burn_months": np.mean(runways) if runways else None,
            "has_active_atm": has_atm,
            "max_atm_amount": max(atm_amounts) if atm_amounts else 0,
            "cash_trend": cash_trend,
            "latest_cash_position": cash_positions[-1] if cash_positions else None,
            "avg_monthly_burn": np.mean(burn_rates) if burn_rates else None,
            "filings_analyzed": len(analyses),
            "confidence": np.mean([a.get("confidence", 0.5) for a in analyses]),
            "analysis_summary": f"Analyzed {len(analyses)} filings. Cash trend: {cash_trend}. ATM risk: {'High' if atm_probability > 0.7 else 'Medium' if atm_probability > 0.4 else 'Low'}",
            "ibkr_cross_check": bool(ibkr_data),
            "analysis_timestamp": datetime.now().isoformat(),
        }

    def _calculate_atm_probability(
        self,
        cash_positions: List[float],
        burn_rates: List[float],
        runways: List[float],
        has_atm: bool,
        ibkr_data: Dict = None,
    ) -> float:
        """Calculate ATM probability using HISTORICAL DATA ONLY.
        Per specs: 'no fakes, no mocks. money is on the line.'
        """

        # Import historical predictor
        from .historical_atm_predictor import HistoricalATMPredictor

        if not has_atm:
            return 0.0  # Cannot do ATM without shelf

        if not runways:
            # FAIL LOUDLY - no guessing
            raise ValueError(
                "CRITICAL: Cannot calculate ATM probability without runway data"
            )

        try:
            predictor = HistoricalATMPredictor()

            # Use most recent data for prediction
            current_runway = runways[-1] if runways else 0
            current_burn = burn_rates[-1] if burn_rates else 0
            current_cash = cash_positions[-1] if cash_positions else 0

            result = predictor.calculate_atm_probability(
                runway_months=current_runway,
                has_atm_shelf=has_atm,
                monthly_burn=current_burn,
                cash_position=current_cash,
            )

            # Adjust based on trends if we have historical data
            trend_multiplier = 1.0

            if len(runways) >= 2:
                # Runway deteriorating faster than expected
                runway_decline_rate = (runways[0] - runways[-1]) / len(runways)
                if runway_decline_rate > 2:  # Losing >2 months runway per filing
                    trend_multiplier *= 1.2

            if len(cash_positions) >= 2:
                # Cash declining rapidly
                cash_decline = (
                    cash_positions[0] - cash_positions[-1]
                ) / cash_positions[0]
                if cash_decline > 0.5:  # Lost >50% of cash
                    trend_multiplier *= 1.3

            final_probability = min(result["probability"] * trend_multiplier, 0.95)

            predictor.close()

            logger.info(
                f"ATM probability from historical data: {final_probability:.2%} "
                f"(base: {result['probability']:.2%}, trend factor: {trend_multiplier:.2f})"
            )

            return final_probability

        except Exception as e:
            # FAIL LOUDLY - no fallback probabilities
            raise ValueError(f"CRITICAL: Cannot calculate ATM probability: {e}")

    def close(self):
        """Clean up resources."""
        if self.ibkr_data:
            self.ibkr_data.disconnect()


# Alias for backward compatibility
SECFilingAnalyzer = EnhancedSECFilingAnalyzer


def test_enhanced_analyzer():
    """Test the enhanced analyzer."""
    print("Testing Enhanced SEC Filing Analyzer with LiteLLM + IBKR...")

    cache_manager = FilingCacheManager()
    analyzer = EnhancedSECFilingAnalyzer(cache_manager)

    try:
        # Test with sample filing data
        sample_filings = pd.DataFrame(
            {
                "form_type": ["10-Q", "10-K"],
                "filed_at": ["2023-05-01", "2023-02-01"],
                "filing_url": [
                    "https://www.sec.gov/ix?doc=/Archives/edgar/data/1652044/000165204423000038/sava-20230331.htm",
                    "https://www.sec.gov/ix?doc=/Archives/edgar/data/1652044/000165204423000013/sava-20221231.htm",
                ],
            }
        )

        result = analyzer.analyze_company_filings("SAVA", sample_filings, "2023-06-01")

        print(f"Analysis Result:")
        print(f"  ATM Probability: {result['atm_probability']:.0%}")
        print(f"  Cash Runway: {result.get('cash_burn_months', 'N/A')} months")
        print(f"  Has ATM: {result['has_active_atm']}")
        print(f"  Filings Analyzed: {result['filings_analyzed']}")
        print(f"  Confidence: {result['confidence']:.0%}")

    except Exception as e:
        print(f"Test failed: {e}")
    finally:
        analyzer.close()
        cache_manager.close()


if __name__ == "__main__":
    test_enhanced_analyzer()
