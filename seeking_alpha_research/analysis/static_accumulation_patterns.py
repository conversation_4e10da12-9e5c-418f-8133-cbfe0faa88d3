#!/usr/bin/env python3
"""
Static Volume Accumulation Pattern Detector

Detects insider accumulation using minute data patterns 1-2 weeks before gaps.
These static patterns will later serve as features for ML models.

NO FAKES, NO MOCKS - Real patterns from real data.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging

from core.data_service import DataService

logger = logging.getLogger(__name__)


class StaticAccumulationDetector:
    """Detects accumulation patterns using static rules based on observed patterns."""
    
    def __init__(self, data_service: DataService = None):
        self.data_service = data_service or DataService()
        
    def detect_accumulation(
        self, symbol: str, target_date: str, lookback_days: int = 14
    ) -> Dict[str, any]:
        """
        Detect accumulation patterns in the 2 weeks before target date.
        
        Key patterns to detect:
        1. Low volume drift higher (stealth accumulation)
        2. Afternoon volume spikes with minimal price movement
        3. Consistent buying at bid (detected via close > open on low volume)
        4. Narrowing daily ranges with stable volume
        5. Volume clustering in specific price zones
        """
        logger.info(f"Detecting accumulation for {symbol} before {target_date}")
        
        # Get minute and daily data
        end_date = pd.to_datetime(target_date)
        start_date = end_date - timedelta(days=lookback_days + 5)  # Extra days for calculations
        
        try:
            # Fetch data
            daily_bars = self.data_service.get_daily_bars(
                symbol, 
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )
            
            minute_bars = self.data_service.get_minute_bars(
                symbol,
                (end_date - timedelta(days=lookback_days)).strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )
            
            if daily_bars.empty or minute_bars.empty:
                logger.warning(f"Insufficient data for {symbol}")
                return self._empty_result()
            
            # Run pattern detections
            results = {
                'symbol': symbol,
                'analysis_date': target_date,
                'lookback_days': lookback_days,
                'data_points': len(minute_bars)
            }
            
            # Pattern 1: Low volume drift higher
            drift_pattern = self._detect_low_volume_drift(daily_bars, minute_bars)
            results['low_volume_drift'] = drift_pattern
            
            # Pattern 2: Afternoon accumulation
            afternoon_pattern = self._detect_afternoon_accumulation(minute_bars)
            results['afternoon_accumulation'] = afternoon_pattern
            
            # Pattern 3: Bid support (close > open)
            bid_support = self._detect_bid_support(minute_bars, daily_bars)
            results['bid_support'] = bid_support
            
            # Pattern 4: Range contraction
            range_pattern = self._detect_range_contraction(daily_bars)
            results['range_contraction'] = range_pattern
            
            # Pattern 5: Price zone accumulation
            zone_pattern = self._detect_price_zone_accumulation(minute_bars)
            results['price_zone_accumulation'] = zone_pattern
            
            # Calculate overall score
            accumulation_score = self._calculate_accumulation_score(results)
            results['accumulation_score'] = accumulation_score
            results['accumulation_detected'] = accumulation_score > 0.6
            
            # Entry recommendation
            entry_signal = self._generate_entry_signal(results)
            results['entry_signal'] = entry_signal
            
            logger.info(
                f"Accumulation analysis complete. Score: {accumulation_score:.2f}, "
                f"Entry: {entry_signal['recommendation']}"
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Accumulation detection failed: {e}")
            return self._empty_result()
    
    def _detect_low_volume_drift(
        self, daily_bars: pd.DataFrame, minute_bars: pd.DataFrame
    ) -> Dict[str, any]:
        """Detect steady price increase on declining volume."""
        
        # Last 10 days
        recent_daily = daily_bars.iloc[-10:] if len(daily_bars) >= 10 else daily_bars
        
        if len(recent_daily) < 5:
            return {'detected': False, 'confidence': 0.0}
        
        # Calculate trend
        price_trend = np.polyfit(range(len(recent_daily)), recent_daily['close'].values, 1)[0]
        volume_trend = np.polyfit(range(len(recent_daily)), recent_daily['volume'].values, 1)[0]
        
        # Average volume decline
        first_half_vol = recent_daily.iloc[:len(recent_daily)//2]['volume'].mean()
        second_half_vol = recent_daily.iloc[len(recent_daily)//2:]['volume'].mean()
        volume_decline = (first_half_vol - second_half_vol) / first_half_vol
        
        # Drift detection: price up, volume down
        drift_detected = price_trend > 0 and volume_trend < 0 and volume_decline > 0.2
        
        # Intraday confirmation
        if drift_detected and not minute_bars.empty:
            # Check for steady accumulation vs volatile moves
            minute_bars['returns'] = minute_bars['close'].pct_change()
            volatility = minute_bars['returns'].std()
            steady_move = volatility < minute_bars['returns'].mean() * 2
            drift_detected = drift_detected and steady_move
        
        confidence = 0.0
        if drift_detected:
            # Higher confidence for stronger patterns
            confidence = min(0.9, abs(price_trend) * 10 + volume_decline)
        
        return {
            'detected': drift_detected,
            'confidence': float(confidence),
            'price_trend': float(price_trend),
            'volume_trend': float(volume_trend),
            'volume_decline_pct': float(volume_decline * 100)
        }
    
    def _detect_afternoon_accumulation(self, minute_bars: pd.DataFrame) -> Dict[str, any]:
        """Detect accumulation in afternoon sessions (1-4 PM)."""
        
        if minute_bars.empty:
            return {'detected': False, 'confidence': 0.0}
        
        # Add hour column
        minute_bars = minute_bars.copy()
        minute_bars['hour'] = minute_bars.index.hour
        minute_bars['date'] = minute_bars.index.date
        
        # Analyze each day
        daily_patterns = []
        
        for date in minute_bars['date'].unique()[-10:]:  # Last 10 days
            day_data = minute_bars[minute_bars['date'] == date]
            
            if len(day_data) < 390:  # Need full day
                continue
            
            # Morning vs afternoon volume
            morning_vol = day_data[day_data['hour'] < 13]['volume'].sum()
            afternoon_vol = day_data[day_data['hour'] >= 13]['volume'].sum()
            
            # Afternoon price movement
            lunch_price = day_data[day_data['hour'] == 12]['close'].iloc[-1] if any(day_data['hour'] == 12) else day_data['close'].iloc[len(day_data)//2]
            close_price = day_data['close'].iloc[-1]
            afternoon_move = (close_price - lunch_price) / lunch_price
            
            # Pattern: Higher afternoon volume with positive price action
            if afternoon_vol > morning_vol * 1.2 and afternoon_move > 0:
                daily_patterns.append({
                    'date': date,
                    'afternoon_ratio': afternoon_vol / morning_vol,
                    'price_move': afternoon_move
                })
        
        # Consistency check
        pattern_detected = len(daily_patterns) >= 3  # At least 3 days
        
        confidence = 0.0
        if pattern_detected:
            avg_ratio = np.mean([p['afternoon_ratio'] for p in daily_patterns])
            consistency = len(daily_patterns) / 10.0
            confidence = min(0.9, (avg_ratio - 1.0) * consistency)
        
        return {
            'detected': pattern_detected,
            'confidence': float(confidence),
            'pattern_days': len(daily_patterns),
            'avg_afternoon_ratio': float(np.mean([p['afternoon_ratio'] for p in daily_patterns])) if daily_patterns else 0.0
        }
    
    def _detect_bid_support(
        self, minute_bars: pd.DataFrame, daily_bars: pd.DataFrame
    ) -> Dict[str, any]:
        """Detect consistent buying at bid (close > open on individual bars)."""
        
        if minute_bars.empty:
            return {'detected': False, 'confidence': 0.0}
        
        # Calculate bid support metrics
        minute_bars = minute_bars.copy()
        minute_bars['bid_support'] = minute_bars['close'] > minute_bars['open']
        minute_bars['range'] = minute_bars['high'] - minute_bars['low']
        minute_bars['close_position'] = (minute_bars['close'] - minute_bars['low']) / (minute_bars['range'] + 0.0001)
        
        # Daily aggregation
        daily_support = []
        for date in minute_bars.index.date:
            day_data = minute_bars[minute_bars.index.date == date]
            if len(day_data) < 100:
                continue
                
            support_ratio = day_data['bid_support'].sum() / len(day_data)
            avg_close_position = day_data['close_position'].mean()
            
            # Strong support: >60% bars close > open and close in upper half
            if support_ratio > 0.6 and avg_close_position > 0.6:
                daily_support.append({
                    'date': date,
                    'support_ratio': support_ratio,
                    'close_position': avg_close_position
                })
        
        # Pattern detection
        pattern_detected = len(daily_support) >= 5  # At least 5 days
        
        confidence = 0.0
        if pattern_detected:
            avg_support = np.mean([d['support_ratio'] for d in daily_support])
            confidence = min(0.9, (avg_support - 0.5) * 2)
        
        return {
            'detected': pattern_detected,
            'confidence': float(confidence),
            'support_days': len(daily_support),
            'avg_support_ratio': float(np.mean([d['support_ratio'] for d in daily_support])) if daily_support else 0.0
        }
    
    def _detect_range_contraction(self, daily_bars: pd.DataFrame) -> Dict[str, any]:
        """Detect narrowing daily ranges (consolidation before move)."""
        
        if len(daily_bars) < 10:
            return {'detected': False, 'confidence': 0.0}
        
        recent = daily_bars.iloc[-10:]
        
        # Calculate daily ranges
        recent = recent.copy()
        recent['range'] = recent['high'] - recent['low']
        recent['range_pct'] = recent['range'] / recent['close']
        
        # Check for contraction
        first_half_range = recent.iloc[:5]['range_pct'].mean()
        second_half_range = recent.iloc[5:]['range_pct'].mean()
        
        contraction_ratio = (first_half_range - second_half_range) / first_half_range
        
        # Volume should remain stable or increase slightly
        first_half_vol = recent.iloc[:5]['volume'].mean()
        second_half_vol = recent.iloc[5:]['volume'].mean()
        volume_stable = 0.7 < (second_half_vol / first_half_vol) < 1.3
        
        pattern_detected = contraction_ratio > 0.3 and volume_stable
        
        confidence = 0.0
        if pattern_detected:
            confidence = min(0.8, contraction_ratio)
        
        return {
            'detected': pattern_detected,
            'confidence': float(confidence),
            'contraction_ratio': float(contraction_ratio),
            'current_range_pct': float(second_half_range * 100)
        }
    
    def _detect_price_zone_accumulation(self, minute_bars: pd.DataFrame) -> Dict[str, any]:
        """Detect volume clustering in specific price zones."""
        
        if len(minute_bars) < 1000:  # Need sufficient data
            return {'detected': False, 'confidence': 0.0}
        
        # Create price bins
        price_range = minute_bars['close'].max() - minute_bars['close'].min()
        num_bins = 20
        
        minute_bars = minute_bars.copy()
        minute_bars['price_bin'] = pd.cut(minute_bars['close'], bins=num_bins)
        
        # Volume by price zone
        volume_profile = minute_bars.groupby('price_bin')['volume'].sum()
        
        # Find accumulation zones (high volume nodes)
        threshold = volume_profile.mean() + volume_profile.std()
        accumulation_zones = volume_profile[volume_profile > threshold]
        
        # Check if accumulation is happening at current price level
        current_price = minute_bars['close'].iloc[-1]
        current_bin = pd.cut([current_price], bins=volume_profile.index.categories)[0]
        
        active_accumulation = False
        if current_bin in accumulation_zones.index:
            active_accumulation = True
        
        # Pattern strength
        concentration = accumulation_zones.sum() / volume_profile.sum()
        
        pattern_detected = len(accumulation_zones) > 0 and concentration > 0.4
        
        confidence = 0.0
        if pattern_detected:
            confidence = min(0.8, concentration + (0.2 if active_accumulation else 0))
        
        return {
            'detected': pattern_detected,
            'confidence': float(confidence),
            'accumulation_zones': len(accumulation_zones),
            'volume_concentration': float(concentration),
            'active_zone': active_accumulation
        }
    
    def _calculate_accumulation_score(self, results: Dict[str, any]) -> float:
        """Calculate overall accumulation score."""
        
        score = 0.0
        weights = {
            'low_volume_drift': 0.25,
            'afternoon_accumulation': 0.20,
            'bid_support': 0.20,
            'range_contraction': 0.15,
            'price_zone_accumulation': 0.20
        }
        
        for pattern, weight in weights.items():
            if pattern in results and results[pattern].get('detected', False):
                score += weight * results[pattern].get('confidence', 0)
        
        return float(score)
    
    def _generate_entry_signal(self, results: Dict[str, any]) -> Dict[str, any]:
        """Generate entry signal based on accumulation patterns."""
        
        score = results.get('accumulation_score', 0)
        
        if score >= 0.7:
            signal = 'STRONG_BUY'
            confidence = 0.9
        elif score >= 0.5:
            signal = 'BUY'
            confidence = 0.7
        elif score >= 0.3:
            signal = 'WEAK_BUY'
            confidence = 0.5
        else:
            signal = 'NO_SIGNAL'
            confidence = 0.3
        
        # Key insights
        insights = []
        if results.get('low_volume_drift', {}).get('detected'):
            insights.append("Steady accumulation on declining volume")
        if results.get('afternoon_accumulation', {}).get('detected'):
            insights.append("Consistent afternoon buying detected")
        if results.get('bid_support', {}).get('detected'):
            insights.append("Strong bid support present")
        if results.get('range_contraction', {}).get('detected'):
            insights.append("Price consolidating - potential breakout setup")
        if results.get('price_zone_accumulation', {}).get('detected'):
            insights.append("Volume accumulation at key price levels")
        
        return {
            'recommendation': signal,
            'confidence': float(confidence),
            'score': float(score),
            'insights': insights
        }
    
    def _empty_result(self) -> Dict[str, any]:
        """Return empty result structure."""
        return {
            'symbol': '',
            'analysis_date': '',
            'accumulation_score': 0.0,
            'accumulation_detected': False,
            'entry_signal': {
                'recommendation': 'NO_DATA',
                'confidence': 0.0,
                'score': 0.0,
                'insights': []
            }
        }