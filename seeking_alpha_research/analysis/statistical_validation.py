"""
Statistical Validation Module

This module provides the StatisticalValidator class for strategy validation.
It wraps the ProfessionalAlphaValidator for backward compatibility.
"""

from validation.alpha_validator import ProfessionalAlphaValidator

# Alias for backward compatibility
StatisticalValidator = ProfessionalAlphaValidator

# Re-export key classes and functions
__all__ = ["StatisticalValidator", "ProfessionalAlphaValidator"]
