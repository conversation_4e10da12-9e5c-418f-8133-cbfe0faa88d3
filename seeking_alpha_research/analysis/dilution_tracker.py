"""
Dilution Confirmation Tracker

Tracks ATM offerings and dilution events after gap-ups, including:
- Same-day announcements (at market close)
- 424B5 prospectus supplements
- S-3 shelf registrations becoming active
- Direct offerings and private placements

CRITICAL: Money is on the line - we need to track these in real-time!
"""

import pandas as pd
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum
import requests
from bs4 import BeautifulSoup
import json
import re

from core.data_service import DataService
from core.database import engine, metadata
from sqlalchemy import (
    Table,
    Column,
    Integer,
    String,
    Float,
    DateTime,
    Boolean,
    Date,
    JSON,
    select,
    insert,
)
from core.logger import get_logger

logger = get_logger(__name__)


class DilutionType(Enum):
    """Types of dilution events."""

    ATM_OFFERING = "atm_offering"  # At-the-market offering
    DIRECT_OFFERING = "direct_offering"  # Direct registered offering
    PRIVATE_PLACEMENT = "private_placement"  # PIPE deals
    WARRANT_EXERCISE = "warrant_exercise"  # Warrant exercises
    CONVERTIBLE_NOTE = "convertible_note"  # Convertible debt


@dataclass
class DilutionEvent:
    """Represents a dilution event."""

    symbol: str
    announcement_time: datetime
    dilution_type: DilutionType
    filing_type: str  # 424B5, S-3, 8-K, etc.
    shares_offered: Optional[int]
    offering_price: Optional[float]
    gross_proceeds: Optional[float]
    underwriters: List[str]
    is_same_day: bool  # Was it announced same day as gap?
    gap_date: Optional[datetime]
    filing_url: str
    key_terms: Dict[str, any]


# Create dilution tracking table
dilution_events = Table(
    "dilution_events",
    metadata,
    Column("id", Integer, primary_key=True),
    Column("symbol", String(10), nullable=False, index=True),
    Column("announcement_time", DateTime, nullable=False, index=True),
    Column("dilution_type", String(50), nullable=False),
    Column("filing_type", String(20), nullable=False),
    Column("shares_offered", Integer),
    Column("offering_price", Float),
    Column("gross_proceeds", Float),
    Column("underwriters", JSON),  # List of underwriter names
    Column("is_same_day", Boolean, default=False),
    Column("gap_date", Date),
    Column("filing_url", String(500)),
    Column("key_terms", JSON),  # Additional terms
    Column("created_at", DateTime, default=datetime.now),
)


class DilutionTracker:
    """
    Tracks dilution events in real-time and historically.

    Key features:
    1. Real-time monitoring of SEC filings
    2. Same-day detection (gap day close announcements)
    3. Pattern recognition for common dilution setups
    4. Historical analysis of dilution timing
    """

    def __init__(self):
        self.data_service = DataService()
        self.db_conn = engine.connect()

        # Common patterns in dilution announcements
        self.dilution_keywords = [
            "at-the-market",
            "atm offering",
            "424b5",
            "prospectus supplement",
            "public offering",
            "direct offering",
            "registered direct",
            "private placement",
            "pipe",
            "warrant exercise",
            "convertible",
            "shelf registration",
            "s-3",
        ]

        # Underwriters that frequently handle ATM offerings
        self.common_atm_underwriters = [
            "H.C. Wainwright",
            "Maxim Group",
            "B. Riley",
            "Cantor Fitzgerald",
            "Jefferies",
            "Piper Sandler",
            "Craig-Hallum",
            "Ladenburg Thalmann",
            "A.G.P.",
            "ThinkEquity",
        ]

    def check_for_dilution(
        self,
        symbol: str,
        gap_date: datetime,
        check_same_day: bool = True,
        lookforward_days: int = 5,
    ) -> List[DilutionEvent]:
        """
        Check for dilution events after a gap.

        Args:
            symbol: Stock symbol
            gap_date: Date of the gap event
            check_same_day: Whether to check for same-day announcements
            lookforward_days: Days to look forward for dilution

        Returns:
            List of dilution events found
        """
        dilution_events_found = []

        # 1. Check same-day filings (after market close)
        if check_same_day:
            same_day_events = self._check_same_day_filings(symbol, gap_date)
            dilution_events_found.extend(same_day_events)

        # 2. Check subsequent days
        for days_after in range(1, lookforward_days + 1):
            check_date = gap_date + timedelta(days=days_after)

            # Skip weekends
            if check_date.weekday() >= 5:
                continue

            events = self._check_filings_on_date(symbol, check_date, gap_date)
            dilution_events_found.extend(events)

        # 3. Store events in database
        for event in dilution_events_found:
            self._store_dilution_event(event)

        return dilution_events_found

    def _check_same_day_filings(
        self, symbol: str, gap_date: datetime
    ) -> List[DilutionEvent]:
        """
        Check for filings on the same day as the gap, particularly after market close.
        This is CRITICAL - many dilutions are announced right after the gap day closes!
        """
        logger.info(f"Checking same-day filings for {symbol} on {gap_date.date()}")

        events = []

        # Get all filings for the gap date
        filings = self.data_service.get_sec_filings(
            symbol, gap_date.strftime("%Y-%m-%d"), gap_date.strftime("%Y-%m-%d")
        )

        if filings.empty:
            return events

        # Market close is typically 4 PM ET
        market_close = time(16, 0)  # 4 PM

        for _, filing in filings.iterrows():
            # Check filing time (if available)
            filing_datetime = filing.get("filed_at")

            # Check if it's a dilution-related filing
            form_type = filing.get("form_type", "").upper()

            if self._is_dilution_filing(form_type):
                # Analyze the filing content
                event = self._analyze_filing_for_dilution(
                    symbol=symbol, filing=filing, gap_date=gap_date, is_same_day=True
                )

                if event:
                    logger.warning(
                        f"CRITICAL: Same-day dilution detected for {symbol}! "
                        f"Filing: {form_type} at {filing_datetime}"
                    )
                    events.append(event)

        return events

    def _check_filings_on_date(
        self, symbol: str, check_date: datetime, gap_date: datetime
    ) -> List[DilutionEvent]:
        """Check filings on a specific date."""
        events = []

        filings = self.data_service.get_sec_filings(
            symbol, check_date.strftime("%Y-%m-%d"), check_date.strftime("%Y-%m-%d")
        )

        if filings.empty:
            return events

        for _, filing in filings.iterrows():
            form_type = filing.get("form_type", "").upper()

            if self._is_dilution_filing(form_type):
                event = self._analyze_filing_for_dilution(
                    symbol=symbol, filing=filing, gap_date=gap_date, is_same_day=False
                )

                if event:
                    days_after_gap = (check_date - gap_date).days
                    logger.info(
                        f"Dilution detected for {symbol} {days_after_gap} days after gap. "
                        f"Filing: {form_type}"
                    )
                    events.append(event)

        return events

    def _is_dilution_filing(self, form_type: str) -> bool:
        """Check if a filing type typically contains dilution announcements."""
        dilution_forms = [
            "424B5",  # Prospectus supplement (most common for ATM)
            "424B3",  # Another prospectus type
            "424B2",  # Prospectus filed pursuant to Rule 424(b)(2)
            "S-3",  # Shelf registration
            "S-3/A",  # Amended shelf
            "8-K",  # Current report (often announces offerings)
            "EFFECT",  # Notice of effectiveness
            "POS AM",  # Post-effective amendment
            "FWP",  # Free writing prospectus
        ]

        return any(form in form_type.upper() for form in dilution_forms)

    def _analyze_filing_for_dilution(
        self, symbol: str, filing: pd.Series, gap_date: datetime, is_same_day: bool
    ) -> Optional[DilutionEvent]:
        """
        Analyze filing content to determine if it's a dilution event.
        In production, this would fetch and parse the actual filing.
        """
        form_type = filing.get("form_type", "")
        filing_date = filing.get("filed_at")

        # For 424B5 filings, it's almost certainly a dilution
        if "424B5" in form_type.upper():
            return DilutionEvent(
                symbol=symbol,
                announcement_time=filing_date,
                dilution_type=DilutionType.ATM_OFFERING,
                filing_type=form_type,
                shares_offered=None,  # Would parse from filing
                offering_price=None,  # Would parse from filing
                gross_proceeds=None,  # Would parse from filing
                underwriters=[],  # Would parse from filing
                is_same_day=is_same_day,
                gap_date=gap_date,
                filing_url=filing.get("filing_url", ""),
                key_terms={},
            )

        # For 8-K, need to check the content for offering announcements
        if "8-K" in form_type.upper():
            # In production, would fetch and analyze the 8-K content
            # For now, return None (would need actual content analysis)
            pass

        return None

    def analyze_dilution_patterns(self, symbol: str = None) -> Dict:
        """
        Analyze historical patterns of dilution timing.

        Key questions:
        1. How often do dilutions happen same-day vs later?
        2. What's the typical delay between gap and dilution?
        3. Which underwriters are most associated with quick dilutions?
        """
        query = select(dilution_events)

        if symbol:
            query = query.where(dilution_events.c.symbol == symbol)

        df = pd.read_sql(query, self.db_conn)

        if df.empty:
            return {}

        # Calculate timing statistics
        same_day_count = len(df[df["is_same_day"] == True])
        total_count = len(df)

        # Days between gap and dilution
        df["gap_date"] = pd.to_datetime(df["gap_date"])
        df["announcement_time"] = pd.to_datetime(df["announcement_time"])
        df["days_to_dilution"] = (df["announcement_time"] - df["gap_date"]).dt.days

        # Underwriter analysis
        underwriter_stats = {}
        if "underwriters" in df.columns:
            all_underwriters = []
            for uw_list in df["underwriters"]:
                if uw_list:
                    all_underwriters.extend(uw_list)

            for uw in set(all_underwriters):
                uw_events = df[
                    df["underwriters"].apply(lambda x: uw in x if x else False)
                ]
                underwriter_stats[uw] = {
                    "total_deals": len(uw_events),
                    "same_day_deals": len(uw_events[uw_events["is_same_day"] == True]),
                    "avg_days_to_dilution": uw_events["days_to_dilution"].mean(),
                }

        return {
            "total_dilutions": total_count,
            "same_day_dilutions": same_day_count,
            "same_day_percentage": (
                same_day_count / total_count if total_count > 0 else 0
            ),
            "avg_days_to_dilution": df["days_to_dilution"].mean(),
            "median_days_to_dilution": df["days_to_dilution"].median(),
            "dilution_by_day": {
                "day_0": same_day_count,
                "day_1": len(df[df["days_to_dilution"] == 1]),
                "day_2": len(df[df["days_to_dilution"] == 2]),
                "day_3": len(df[df["days_to_dilution"] == 3]),
                "day_4": len(df[df["days_to_dilution"] == 4]),
                "day_5": len(df[df["days_to_dilution"] == 5]),
            },
            "underwriter_stats": underwriter_stats,
        }

    def _store_dilution_event(self, event: DilutionEvent):
        """Store dilution event in database."""
        try:
            insert_stmt = insert(dilution_events).values(
                symbol=event.symbol,
                announcement_time=event.announcement_time,
                dilution_type=event.dilution_type.value,
                filing_type=event.filing_type,
                shares_offered=event.shares_offered,
                offering_price=event.offering_price,
                gross_proceeds=event.gross_proceeds,
                underwriters=event.underwriters,
                is_same_day=event.is_same_day,
                gap_date=event.gap_date,
                filing_url=event.filing_url,
                key_terms=event.key_terms,
            )

            self.db_conn.execute(insert_stmt)
            self.db_conn.commit()

        except Exception as e:
            logger.error(f"Error storing dilution event: {e}")
            self.db_conn.rollback()

    def get_high_risk_times(self) -> Dict[str, float]:
        """
        Get high-risk times for dilution announcements.

        Returns:
            Dict mapping time periods to dilution probability
        """
        query = select(dilution_events)
        df = pd.read_sql(query, self.db_conn)

        if df.empty:
            return {}

        # Extract hour from announcement time
        df["announcement_time"] = pd.to_datetime(df["announcement_time"])
        df["hour"] = df["announcement_time"].dt.hour

        # Count dilutions by hour
        hourly_counts = df["hour"].value_counts().sort_index()
        total = len(df)

        risk_by_hour = {}
        for hour in range(24):
            count = hourly_counts.get(hour, 0)
            risk_by_hour[f"{hour:02d}:00"] = count / total if total > 0 else 0

        return risk_by_hour

    def close(self):
        """Clean up resources."""
        if self.db_conn:
            self.db_conn.close()
        if self.data_service:
            self.data_service.close()


def test_dilution_tracker():
    """Test dilution tracking functionality."""
    print("=== Testing Dilution Tracker ===\n")

    # Create tables
    metadata.create_all(engine)

    tracker = DilutionTracker()

    # Test with a known symbol and date
    test_symbol = "SAVA"
    test_date = datetime(2024, 1, 15)  # Example gap date

    print(f"1. Checking for dilution events after gap on {test_date.date()}...")

    # Check for dilution
    events = tracker.check_for_dilution(
        symbol=test_symbol, gap_date=test_date, check_same_day=True, lookforward_days=5
    )

    if events:
        print(f"\nFound {len(events)} dilution events:")
        for event in events:
            print(f"  - {event.filing_type} on {event.announcement_time}")
            print(f"    Type: {event.dilution_type.value}")
            print(f"    Same day: {event.is_same_day}")
    else:
        print("No dilution events found (this is normal for test data)")

    # Analyze patterns
    print("\n2. Analyzing dilution patterns...")
    patterns = tracker.analyze_dilution_patterns()

    if patterns:
        print(f"  Total dilutions tracked: {patterns.get('total_dilutions', 0)}")
        print(f"  Same-day dilutions: {patterns.get('same_day_percentage', 0):.1%}")
        print(f"  Avg days to dilution: {patterns.get('avg_days_to_dilution', 0):.1f}")

    # Get high-risk times
    print("\n3. High-risk times for dilution announcements:")
    risk_times = tracker.get_high_risk_times()

    if risk_times:
        # Sort by risk level
        sorted_times = sorted(risk_times.items(), key=lambda x: x[1], reverse=True)
        for time_period, risk in sorted_times[:5]:
            print(f"  {time_period}: {risk:.1%} of dilutions")

    tracker.close()

    print("\n=== Dilution Tracker Test Complete ===")


if __name__ == "__main__":
    test_dilution_tracker()
