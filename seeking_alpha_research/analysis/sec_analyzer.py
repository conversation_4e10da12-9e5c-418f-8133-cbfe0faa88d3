"""
Enhanced SEC Filing Analyzer using edgartools
https://github.com/dgunning/edgartools

This replaces the complex URL parsing with edgartools' simple API:
- company = Company("AAPL")
- filings = company.get_filings(form="10-Q")
- filing.text for analysis

Integrates with LiteLLM for real analysis (no more hardcoded values).
"""

import os
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np

try:
    from edgar import Company, set_identity

    EDGAR_AVAILABLE = True
except ImportError:
    EDGAR_AVAILABLE = False
    print("WARNING: edgartools not available - install with: pip install edgartools")

from utils.filing_cache_manager import FilingCacheManager
from core.logger import get_logger
import litellm

logger = get_logger(__name__)

# Configure LiteLLM for Gemini
os.environ["GEMINI_API_KEY"] = os.getenv("GEMINI_API_KEY", "")
litellm.set_verbose = False

# Set Edgar identity (required by SEC)
if EDGAR_AVAILABLE:
    set_identity("Seeking <NAME_EMAIL>")


class EdgarToolsAnalyzer:
    """SEC Filing analyzer using edgartools + LiteLLM."""

    def __init__(self, cache_manager: FilingCacheManager):
        self.cache_manager = cache_manager

        # Check requirements
        if not EDGAR_AVAILABLE:
            raise ValueError(
                "edgartools not available - install with: pip install edgartools"
            )

        if not os.getenv("GEMINI_API_KEY"):
            raise ValueError(
                "GEMINI_API_KEY not set - cannot analyze filings without LLM access"
            )

    def get_company_filings(
        self, symbol: str, date_before: str, lookback_days: int = 730
    ) -> pd.DataFrame:
        """
        Get 2 years of filings using edgartools.
        Much simpler than URL construction!
        """
        try:
            company = Company(symbol)

            # Calculate date range (2 years back from date_before)
            end_date = pd.to_datetime(date_before)
            start_date = end_date - timedelta(days=lookback_days)

            # Get recent filings (edgartools approach - simpler)
            # First get all recent filings
            filings = company.get_filings(trigger_full_load=True)

            # Filter by date range manually
            filtered_filings = []
            for filing in filings:
                filing_date = pd.to_datetime(filing.filing_date)
                if start_date <= filing_date <= end_date:
                    filtered_filings.append(filing)

            filings = filtered_filings

            # Convert to DataFrame
            filing_data = []
            for filing in filings:
                # Safe CIK access - filing.company might be string or object
                try:
                    if hasattr(filing.company, "cik"):
                        company_cik = filing.company.cik
                    else:
                        company_cik = str(filing.company)  # Fallback to string
                except:
                    company_cik = "unknown"

                filing_data.append(
                    {
                        "symbol": symbol,
                        "form_type": filing.form,
                        "filed_at": filing.filing_date.strftime("%Y-%m-%d"),
                        "accession_number": filing.accession_no,
                        "filing_url": f"https://www.sec.gov/Archives/edgar/data/{company_cik}/{filing.accession_no.replace('-', '')}/{filing.accession_no}-index.htm",
                        "edgar_filing": filing,  # Keep reference to edgar Filing object
                    }
                )

            df = pd.DataFrame(filing_data)
            logger.info(f"Retrieved {len(df)} filings for {symbol} using edgartools")

            return df

        except Exception as e:
            logger.error(f"Error getting filings for {symbol} with edgartools: {e}")
            raise ValueError(
                "CRITICAL: No data available - cannot proceed with empty DataFrame"
            )

    def _analyze_single_filing_with_edgartools(
        self, filing_row: pd.Series
    ) -> Dict[str, Any]:
        """Analyze a single filing using edgartools + LiteLLM."""

        try:
            # Get the edgar Filing object
            edgar_filing = filing_row["edgar_filing"]

            # Get filing text using edgartools (much easier!)
            # The .text might be a method, try both property and method
            try:
                if hasattr(edgar_filing.text, "__call__"):
                    filing_text = edgar_filing.text()
                else:
                    filing_text = edgar_filing.text
            except Exception as text_error:
                logger.error(f"Failed to get filing text: {text_error}")
                return None

            if not filing_text or len(filing_text) < 1000:
                logger.warning(
                    f"Insufficient filing text for {filing_row['form_type']}"
                )
                return None

            # Extract key sections for analysis
            sections = self._extract_key_sections(filing_text)

            # Enhanced analysis prompt with structured sections
            analysis_prompt = f"""
You are an expert SEC filing analyst. Analyze this {filing_row['form_type']} filing for At-The-Market (ATM) offering risk.

Filing Details:
- Symbol: {filing_row['symbol']}
- Form: {filing_row['form_type']}
- Date: {filing_row['filed_at']}
- Accession: {filing_row['accession_number']}

Key Sections Extracted:
BALANCE SHEET:
{sections.get('balance_sheet', 'Not found')[:2000]}

CASH FLOW:
{sections.get('cash_flow', 'Not found')[:2000]}

NOTES (ATM/EQUITY):
{sections.get('equity_notes', 'Not found')[:2000]}

RISK FACTORS:
{sections.get('risk_factors', 'Not found')[:1000]}

CRITICAL ANALYSIS REQUIRED:

1. CASH POSITION: Extract exact "cash and cash equivalents" from balance sheet
2. BURN RATE: Calculate quarterly burn from cash flow from operations
3. ATM PROGRAM: Search for "at-the-market", "equity distribution", "ATM facility"
4. SHELF REGISTRATION: Look for S-3 references, available shelf capacity
5. RECENT DILUTION: Check for recent equity offerings or warrant exercises

Return ONLY valid JSON (no markdown formatting):
{{
  "cash_position": [exact amount in USD from balance sheet],
  "quarterly_burn": [quarterly cash burn from operations],
  "cash_burn_monthly": [monthly burn calculated],
  "has_atm_program": [true if ATM program mentioned],
  "atm_capacity": [ATM facility size if found, 0 if none],
  "shelf_available": [available shelf capacity if mentioned],
  "months_runway": [cash_position / monthly_burn],
  "recent_dilution": [true if recent equity offerings mentioned],
  "confidence": [0.0-1.0 confidence in analysis],
  "key_risks": "[2-3 key risk factors related to dilution]"
}}

IMPORTANT: Extract real numbers from the filing. Do NOT use placeholder values like 5000000 or 1000000.
"""

            response = litellm.completion(
                model="gemini/gemini-1.5-flash",
                messages=[{"role": "user", "content": analysis_prompt}],
                max_tokens=800,
                temperature=0.1,
            )

            # Parse response
            response_text = response.choices[0].message.content.strip()

            # Clean up markdown formatting if present
            response_text = re.sub(r"^```json\s*", "", response_text)
            response_text = re.sub(r"\s*```$", "", response_text)

            # Extract JSON from response
            json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    # Ensure all required fields exist
                    if not isinstance(result, dict):
                        raise ValueError(f"LLM returned non-dict: {type(result)}")

                    # Map LLM output to expected format
                    if (
                        "monthly_burn_rate" in result
                        and "cash_burn_monthly" not in result
                    ):
                        result["cash_burn_monthly"] = result["monthly_burn_rate"]

                    if (
                        "cash_months_remaining" in result
                        and "monthly_burn_rate" in result
                    ):
                        result["cash_position"] = (
                            result["cash_months_remaining"]
                            * result["monthly_burn_rate"]
                        )

                    if "has_atm_program" in result:
                        result["has_atm_shelf"] = result["has_atm_program"]

                    if "atm_amount_available" in result:
                        result["atm_capacity"] = result["atm_amount_available"]

                    # Calculate ATM probability from risk level
                    risk_to_prob = {"Low": 0.2, "Medium": 0.6, "High": 0.9}
                    if "atm_risk" in result and "atm_probability" not in result:
                        result["atm_probability"] = risk_to_prob.get(
                            result["atm_risk"], 0.5
                        )

                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON from LLM: {json_match.group()[:200]}")
                    raise ValueError(f"LLM returned invalid JSON: {e}")

                # Validation - fail loudly if data looks fake
                if result.get("cash_position") in [5_000_000, 1_000_000]:
                    raise ValueError(
                        "CRITICAL: LLM returned common placeholder cash position"
                    )

                if result.get("cash_burn_monthly") in [1_000_000, 500_000]:
                    raise ValueError(
                        "CRITICAL: LLM returned common placeholder burn rate"
                    )

                # Add metadata
                result["filing_type"] = filing_row["form_type"]
                result["filed_date"] = filing_row["filed_at"]
                result["accession_number"] = filing_row["accession_number"]
                result["analysis_timestamp"] = datetime.now().isoformat()
                result["analyzer"] = "edgartools_litellm"

                # Calculate derived metrics
                if result.get("cash_position") and result.get("cash_burn_monthly"):
                    if result["cash_burn_monthly"] > 0:
                        result["months_runway"] = (
                            result["cash_position"] / result["cash_burn_monthly"]
                        )
                    else:
                        result["months_runway"] = 999  # Profitable companies

                # Safe formatting - handle None values
                cash_pos = result.get("cash_position", 0) or 0
                cash_burn = result.get("cash_burn_monthly", 0) or 0
                logger.info(
                    f"Analyzed {filing_row['form_type']} - Cash: ${cash_pos:,}, "
                    f"Burn: ${cash_burn:,}/month"
                )
                return result
            else:
                logger.error(
                    f"Could not parse JSON from LLM response: {response_text[:200]}"
                )
                raise ValueError(f"LLM returned invalid JSON: {response_text[:200]}")

        except Exception as e:
            logger.error(
                f"Failed to analyze filing {filing_row.get('form_type', 'Unknown')}: {e}"
            )
            # FAIL LOUDLY - don't return fake data
            raise ValueError(f"CRITICAL: Filing analysis failed - {e}")

    def _extract_key_sections(self, filing_text: str) -> Dict[str, str]:
        """Extract key sections from filing text for focused analysis."""

        sections = {}
        filing_lower = filing_text.lower()

        # Balance Sheet section
        balance_patterns = [
            r"consolidated balance sheets?.*?(?=consolidated statements?|notes to)",
            r"balance sheets?.*?(?=statements? of operations|notes to)",
            r"financial position.*?(?=results of operations|notes to)",
        ]

        for pattern in balance_patterns:
            match = re.search(pattern, filing_lower, re.DOTALL)
            if match:
                sections["balance_sheet"] = match.group()
                break

        # Cash Flow section
        cash_flow_patterns = [
            r"consolidated statements? of cash flows?.*?(?=notes to|consolidated balance)",
            r"cash flows?.*?(?=notes to|balance sheets?)",
        ]

        for pattern in cash_flow_patterns:
            match = re.search(pattern, filing_lower, re.DOTALL)
            if match:
                sections["cash_flow"] = match.group()
                break

        # Notes section (look for equity/ATM mentions)
        notes_start = filing_lower.find("notes to consolidated financial statements")
        if notes_start != -1:
            notes_section = filing_text[
                notes_start : notes_start + 50000
            ]  # First 50k chars of notes

            # Look for equity-related notes
            equity_keywords = [
                "equity",
                "at-the-market",
                "atm",
                "shelf registration",
                "offering",
            ]
            equity_text = ""

            for keyword in equity_keywords:
                keyword_pos = notes_section.lower().find(keyword)
                if keyword_pos != -1:
                    # Extract 2000 chars around the keyword
                    start = max(0, keyword_pos - 1000)
                    end = min(len(notes_section), keyword_pos + 1000)
                    equity_text += notes_section[start:end] + "\n\n"

            sections["equity_notes"] = equity_text

        # Risk Factors section
        risk_start = filing_lower.find("risk factors")
        if risk_start != -1:
            risk_end = filing_lower.find("unresolved staff comments", risk_start)
            if risk_end == -1:
                risk_end = risk_start + 10000  # First 10k chars

            sections["risk_factors"] = filing_text[risk_start:risk_end]

        return sections

    def analyze_company_filings(
        self, symbol: str, date_before: str, lookback_days: int = 730
    ) -> Dict[str, Any]:
        """
        Analyze 2 years of filings using edgartools.

        This is the main entry point that replaces the broken react_agents.
        """
        start_time = datetime.now()

        # Get filings using edgartools
        filings_df = self.get_company_filings(symbol, date_before, lookback_days)

        if filings_df.empty:
            raise ValueError(
                f"CRITICAL: No filings found for {symbol} using edgartools"
            )

        # Check cache
        filing_urls = filings_df["filing_url"].tolist()

        needs_analysis, reason = self.cache_manager.needs_analysis(
            symbol, date_before, filing_urls
        )

        if not needs_analysis:
            cached = self.cache_manager.get_cached_analysis(symbol, date_before)
            if cached:
                logger.info(f"Using cached analysis for {symbol}: {reason}")
                return cached

        logger.info(f"Analyzing {len(filings_df)} filings for {symbol}: {reason}")

        # Analyze each filing - CRITICAL: Analyze ALL, not just 1
        analyses = []
        total_cost = 0

        # Focus on most important filing types for ATM analysis
        important_forms = ["10-Q", "10-K", "8-K", "S-3", "424B5"]
        important_filings = filings_df[filings_df["form_type"].isin(important_forms)]

        if important_filings.empty:
            important_filings = filings_df.head(10)  # At least analyze some filings

        for idx, filing in important_filings.iterrows():
            try:
                analysis = self._analyze_single_filing_with_edgartools(filing)
                if analysis:
                    analyses.append(analysis)
                    total_cost += 0.001  # Approximate Gemini cost

            except Exception as e:
                logger.error(
                    f"Failed to analyze {filing['form_type']} from {filing['filed_at']}: {e}"
                )
                # Continue with other filings but count the failure
                continue

        # CRITICAL CHECK: Must have analyzed multiple filings
        if len(analyses) < 2:
            raise ValueError(
                f"CRITICAL: Only analyzed {len(analyses)} filings for {symbol} - "
                f"need multiple filings for 2-year analysis"
            )

        # Aggregate results
        aggregated = self._aggregate_filing_analyses(analyses)

        # Add performance metrics
        processing_time = (datetime.now() - start_time).total_seconds()

        performance_metrics = {
            "total_cost": total_cost,
            "processing_time": processing_time,
            "filings_analyzed": len(analyses),
            "filings_available": len(filings_df),
            "analyzer": "edgartools_litellm",
        }

        # Save to cache
        self.cache_manager.save_company_analysis(
            symbol=symbol,
            analysis_date=date_before,
            filing_urls=filing_urls,
            analysis_result=aggregated,
            performance_metrics=performance_metrics,
        )

        logger.info(
            f"Completed edgartools analysis for {symbol}: {len(analyses)} filings, "
            f"${total_cost:.4f} cost"
        )

        return aggregated

    def _aggregate_filing_analyses(self, analyses: List[Dict]) -> Dict[str, Any]:
        """Aggregate multiple filing analyses into company-level assessment."""

        if not analyses:
            raise ValueError("CRITICAL: No filing analyses to aggregate")

        # Extract trends over time
        cash_positions = [
            a["cash_position"] for a in analyses if a.get("cash_position")
        ]
        burn_rates = [
            a["cash_burn_monthly"] for a in analyses if a.get("cash_burn_monthly")
        ]
        runways = [a["months_runway"] for a in analyses if a.get("months_runway")]

        # ATM program detection
        has_atm = any(a.get("has_atm_program", False) for a in analyses)
        atm_capacities = [
            a["atm_capacity"] for a in analyses if a.get("atm_capacity", 0) > 0
        ]
        recent_dilution = any(a.get("recent_dilution", False) for a in analyses)

        # Calculate sophisticated ATM probability
        atm_probability = self._calculate_atm_probability_advanced(
            cash_positions=cash_positions,
            burn_rates=burn_rates,
            runways=runways,
            has_atm=has_atm,
            recent_dilution=recent_dilution,
        )

        # Trend analysis
        cash_trend = "stable"
        if len(cash_positions) >= 2:
            if cash_positions[-1] < cash_positions[0] * 0.8:
                cash_trend = "declining"
            elif cash_positions[-1] > cash_positions[0] * 1.2:
                cash_trend = "increasing"

        return {
            "atm_probability": atm_probability,
            "cash_burn_months": np.mean(runways) if runways else None,
            "has_active_atm": has_atm,
            "max_atm_capacity": max(atm_capacities) if atm_capacities else 0,
            "recent_dilution": recent_dilution,
            "cash_trend": cash_trend,
            "latest_cash_position": cash_positions[-1] if cash_positions else None,
            "avg_monthly_burn": np.mean(burn_rates) if burn_rates else None,
            "filings_analyzed": len(analyses),
            "confidence": np.mean([a.get("confidence", 0.5) for a in analyses]),
            "analysis_summary": f"EdgarTools analysis of {len(analyses)} filings. "
            f"Cash trend: {cash_trend}. ATM risk: "
            f"{'High' if atm_probability > 0.7 else 'Medium' if atm_probability > 0.4 else 'Low'}",
            "analyzer": "edgartools_litellm",
            "analysis_timestamp": datetime.now().isoformat(),
        }

    def _calculate_atm_probability_advanced(
        self,
        cash_positions: List[float],
        burn_rates: List[float],
        runways: List[float],
        has_atm: bool,
        recent_dilution: bool,
    ) -> float:
        """Calculate sophisticated ATM probability based on multiple factors."""

        probability = 0.1  # Base probability

        # Factor 1: Has existing ATM program
        if has_atm:
            probability += 0.4

        # Factor 2: Recent dilution activity
        if recent_dilution:
            probability += 0.2

        # Factor 3: Cash runway analysis
        if runways:
            min_runway = min(runways)
            avg_runway = np.mean(runways)

            if min_runway < 3:
                probability += 0.3
            elif min_runway < 6:
                probability += 0.2
            elif min_runway < 12:
                probability += 0.1

            # Runway deteriorating trend
            if len(runways) >= 2 and runways[-1] < runways[0] * 0.8:
                probability += 0.15

        # Factor 4: Cash burn acceleration
        if len(burn_rates) >= 2:
            burn_increase = (burn_rates[-1] - burn_rates[0]) / max(burn_rates[0], 1)
            if burn_increase > 0.2:  # 20% burn increase
                probability += 0.1

        # Factor 5: Cash position decline
        if len(cash_positions) >= 2:
            cash_decline = (cash_positions[0] - cash_positions[-1]) / max(
                cash_positions[0], 1
            )
            if cash_decline > 0.3:  # 30% cash decline
                probability += 0.15

        return min(probability, 1.0)

    def close(self):
        """Clean up resources."""
        if self.cache_manager:
            self.cache_manager.close()


def test_edgartools_analyzer():
    """Test the enhanced analyzer with edgartools."""
    print("Testing EdgarTools SEC Filing Analyzer...")

    if not EDGAR_AVAILABLE:
        print("ERROR: edgartools not available - install with: pip install edgartools")
        return

    cache_manager = FilingCacheManager()
    analyzer = EdgarToolsAnalyzer(cache_manager)

    try:
        # Test with a known company
        symbol = "SAVA"
        date_before = "2023-06-01"

        result = analyzer.analyze_company_filings(symbol, date_before)

        print(f"\nEdgarTools Analysis Result for {symbol}:")
        print(f"  ATM Probability: {result['atm_probability']:.0%}")
        print(f"  Cash Runway: {result.get('cash_burn_months', 'N/A')} months")
        print(f"  Has ATM: {result['has_active_atm']}")
        print(f"  Recent Dilution: {result['recent_dilution']}")
        print(f"  Filings Analyzed: {result['filings_analyzed']}")
        print(f"  Confidence: {result['confidence']:.0%}")
        print(f"  Summary: {result['analysis_summary']}")

        # Validate it's not returning fake data
        if result["filings_analyzed"] >= 2:
            print("\n✅ SUCCESS: Analyzed multiple filings (not fake data)")
        else:
            print("\n❌ FAIL: Only analyzed one filing")

    except Exception as e:
        print(f"Test failed: {e}")
        import traceback

        traceback.print_exc()
    finally:
        analyzer.close()


if __name__ == "__main__":
    test_edgartools_analyzer()
