# API Integration Documentation

This document details all real API integrations used in the STK_V5 gap-up ATM strategy system.

## Core Philosophy: NO MOCKS

All API integrations use real endpoints with real data. This ensures:
- Real latency and rate limiting behavior
- Real data quality and edge cases
- Real error conditions and recovery
- Real market conditions and timing

## Alpaca Markets API

### Configuration
```python
# data_service.py
import alpaca_trade_api as tradeapi

class DataService:
    def __init__(self):
        self.alpaca = tradeapi.REST(
            key_id=os.getenv('ALPACA_API_KEY'),
            secret_key=os.getenv('ALPACA_SECRET_KEY'),
            base_url='https://paper-api.alpaca.markets'  # Paper trading
        )
```

### Endpoints Used

#### Historical Bars
- **Endpoint**: `/v2/stocks/{symbol}/bars`
- **Usage**: Daily and minute-level price data
- **Rate Limit**: 200 requests/minute
- **Data Range**: 2016-present

```python
def get_historical_data(self, symbol, start_date, end_date, timeframe='1Day'):
    bars = self.alpaca.get_bars(
        symbol,
        timeframe,
        start=start_date,
        end=end_date,
        adjustment='raw'
    )
    return bars.df
```

#### Real-time Quotes
- **Endpoint**: `/v2/stocks/{symbol}/quotes/latest`
- **Usage**: Pre-market gap detection
- **Rate Limit**: 200 requests/minute
- **Latency**: <100ms typical

#### Corporate Actions ✅ FULLY IMPLEMENTED
- **Endpoint**: `https://data.alpaca.markets/v1/corporate-actions`
- **Usage**: Stock splits, dividends, spin-offs
- **Rate Limit**: 200 requests/minute
- **Historical**: Full corporate action history

**Implementation Details**:
```python
def get_corporate_actions(self, symbol: str, start: str, end: str):
    """
    Fetches corporate actions with automatic cache invalidation.
    Critical for preventing split-related pricing errors.
    """
    # Check local cache first
    existing = pd.read_sql(query, self.db_conn)
    if not existing.empty:
        return existing
    
    # Fetch from API
    response = requests.get(
        "https://data.alpaca.markets/v1/corporate-actions",
        params={
            'symbols': symbol,
            'start': start,
            'end': end,
            'types': 'reverse_split,forward_split,cash_dividend,stock_dividend,spin_off'
        },
        headers={
            'APCA-API-KEY-ID': API_KEY,
            'APCA-API-SECRET-KEY': API_SECRET
        }
    )
    
    # Process splits and invalidate cache
    if 'forward_splits' in data['corporate_actions']:
        for split in data['corporate_actions']['forward_splits']:
            ratio = split['new_rate'] / split['old_rate']
            self.invalidate_cache_for_split(symbol, split['ex_date'], ratio)
```

**Cache Invalidation**:
- Automatically deletes cached price data after split dates
- Forces re-fetching of properly adjusted data
- Prevents mixing pre-split and post-split prices

**Critical Methods**:
- `get_daily_bars_with_split_check()` - Always use for production
- `invalidate_cache_for_split()` - Clears stale data
- `check_and_update_corporate_actions()` - Auto-detection

### Error Handling
```python
try:
    data = self.alpaca.get_bars(symbol, timeframe, start=start, end=end)
except APIError as e:
    if e.status_code == 429:  # Rate limit
        time.sleep(60)
        return self.get_historical_data(symbol, start_date, end_date, timeframe)
    else:
        raise Exception(f"Alpaca API error: {e}")
```

## SEC Edgar API

### Configuration
```python
# dilution_confirmation.py
import requests
from sec_api import QueryApi

class DilutionConfirmation:
    def __init__(self, data_service):
        self.edgar_headers = {
            'User-Agent': '<EMAIL>'  # Required by SEC
        }
        self.sec_api = QueryApi(api_key=os.getenv('SEC_API_KEY'))
```

### Endpoints Used

#### Company Filings Search
- **Endpoint**: `https://www.sec.gov/Archives/edgar/daily-index/`
- **Usage**: Find recent filings by form type
- **Rate Limit**: 10 requests/second
- **Forms**: 424B5, EFFECT, 8-K, 10-K, 10-Q

```python
def find_filings_by_ticker(self, ticker, form_types, start_date):
    query = {
        "query": f"ticker:{ticker} AND formType:({' OR '.join(form_types)})",
        "from": "0",
        "size": "50",
        "sort": [{"filedAt": {"order": "desc"}}]
    }
    return self.sec_api.get_filings(query)
```

#### Filing Content Retrieval
- **Endpoint**: `https://www.sec.gov/Archives/edgar/data/{cik}/{filing}.txt`
- **Usage**: Extract filing content for LLM analysis
- **Rate Limit**: 10 requests/second
- **Format**: Raw HTML/XBRL

### ATM-Specific Filings

#### 424B5 (Prospectus Supplement)
- **Purpose**: ATM offering announcements
- **Detection**: "at-the-market", "ATM", "equity distribution"
- **Timing**: Filed before or during ATM offering

#### EFFECT (Effectiveness Notice)
- **Purpose**: Registration statement effectiveness
- **Detection**: Makes ATM program active
- **Timing**: Usually filed same day as ATM starts

#### 8-K (Current Report)
- **Purpose**: Material events disclosure
- **Detection**: ATM program establishment or modifications
- **Timing**: Filed within 4 business days

### Error Handling
```python
try:
    response = requests.get(filing_url, headers=self.edgar_headers)
    if response.status_code == 429:
        time.sleep(0.1)  # SEC rate limit
        return self._fetch_filing_content(filing_url)
    response.raise_for_status()
except requests.RequestException as e:
    logger.error(f"SEC filing fetch failed: {e}")
    return None
```

## Google Gemini API (LLM)

### Configuration
```python
# llm_agent.py
import google.generativeai as genai

genai.configure(api_key=os.getenv('GOOGLE_AI_API_KEY'))
model = genai.GenerativeModel('gemini-1.5-flash')
```

### Usage Patterns

#### Filing Analysis
- **Input**: SEC filing text (10-K, 10-Q)
- **Output**: Cash burn rate, runway, ATM risk score
- **Rate Limit**: 60 requests/minute
- **Context**: 32k tokens

```python
def analyze_filing_for_atm_risk(filing_text):
    prompt = f"""
    Analyze this SEC filing for ATM offering risk:
    
    {filing_text[:30000]}  # Truncate to fit context
    
    Extract:
    1. Quarterly cash burn rate
    2. Current cash position
    3. Months until cash depletion
    4. ATM program details
    5. Risk score (0-1)
    
    Respond in JSON format.
    """
    
    response = model.generate_content(prompt)
    return json.loads(response.text)
```

#### Cash Burn Calculation
- **Input**: Financial statements from filings
- **Output**: Monthly burn rate, runway analysis
- **Accuracy**: Validated against actual dilutions

### Error Handling
```python
try:
    response = model.generate_content(prompt)
    return json.loads(response.text)
except Exception as e:
    logger.error(f"LLM analysis failed: {e}")
    # Return conservative estimates
    return {
        'cash_burn_rate': 1000000,  # $1M/month default
        'months_until_cash_out': 6,
        'atm_risk_score': 0.5
    }
```

## Interactive Brokers API ✅ FULLY IMPLEMENTED

### Configuration
```python
# ib_connector.py
from ibapi.client import EClient
from ibapi.wrapper import EWrapper

class IBConnector(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self, self)
        self.host = "127.0.0.1"
        self.port = 4001  # IB Gateway port (7497 for TWS)
        self.clientId = 1
        self.nextOrderId = None
        self.data_received = {}
        self.historical_data = {}
```

### Real Data Operations

#### Historical Bars (Primary Data Source)
- **Purpose**: Daily/minute bars for backtesting and analysis
- **Usage**: Replace Alpaca for all bar data needs
- **Permissions**: Level 1 data included with IB account
- **Rate Limit**: 60 requests/10 minutes for historical data

```python
def req_historical_data(self, reqId, contract, endDateTime, durationStr, 
                       barSizeSetting, whatToShow, useRTH, formatDate):
    """
    Request historical bars for backtesting
    
    Args:
        reqId: Unique request identifier
        contract: IB Contract object (stock/ETF)
        endDateTime: End date/time (format: "******** 23:59:59 EST")
        durationStr: How far back ("1 Y", "6 M", "30 D")
        barSizeSetting: Bar size ("1 day", "1 hour", "5 mins", "1 min")
        whatToShow: Data type ("TRADES", "MIDPOINT", "BID", "ASK")
        useRTH: Regular trading hours only (1) or extended (0)
        formatDate: Date format (1 for yyyyMMdd HH:mm:ss, 2 for seconds)
    """
    super().reqHistoricalData(reqId, contract, endDateTime, durationStr,
                             barSizeSetting, whatToShow, useRTH, formatDate)
```

#### Market Data Subscription
- **Purpose**: Real-time Level 1 quotes for premarket gaps
- **Usage**: Live price monitoring during market hours
- **Permissions**: Market data subscriptions required for real-time

```python
def req_mkt_data(self, reqId, contract, genericTickList="", snapshot=False):
    """
    Subscribe to real-time market data
    
    Args:
        reqId: Unique request ID
        contract: IB Contract object
        genericTickList: Additional tick types (empty for basic L1)
        snapshot: True for one-time data, False for streaming
    """
    super().reqMktData(reqId, contract, genericTickList, snapshot, False, [])
```

#### Tick-by-Tick Data (For Insider Detection)
- **Purpose**: Individual trade data for insider accumulation analysis
- **Usage**: Detect unusual buying patterns before gaps
- **Granularity**: Every trade with timestamp, price, size

```python
def req_tick_by_tick_data(self, reqId, contract, tickType, 
                         numberOfTicks, ignoreSize):
    """
    Request tick-by-tick trade data
    
    Args:
        reqId: Request identifier
        contract: Stock contract
        tickType: "Last", "AllLast", "BidAsk", "MidPoint"
        numberOfTicks: Number of ticks (0 for streaming)
        ignoreSize: Ignore trades below minimum size
    """
    super().reqTickByTickData(reqId, contract, tickType, 
                             numberOfTicks, ignoreSize)
```

### Contract Creation
```python
def create_stock_contract(self, symbol, exchange="SMART", currency="USD"):
    """Create IB Contract for US stocks"""
    contract = Contract()
    contract.symbol = symbol
    contract.secType = "STK"
    contract.exchange = exchange
    contract.currency = currency
    contract.primaryExchange = "NASDAQ"  # or "NYSE"
    return contract
```

### Data Callbacks
```python
def historicalData(self, reqId, bar):
    """Receive historical bar data"""
    if reqId not in self.historical_data:
        self.historical_data[reqId] = []
    
    self.historical_data[reqId].append({
        'date': bar.date,
        'open': bar.open,
        'high': bar.high,
        'low': bar.low,
        'close': bar.close,
        'volume': bar.volume,
        'barCount': bar.barCount,
        'WAP': bar.average
    })

def tickPrice(self, reqId, tickType, price, attrib):
    """Receive real-time price updates"""
    tick_types = {
        1: 'bid', 2: 'ask', 4: 'last', 6: 'high', 7: 'low', 9: 'close'
    }
    
    if reqId not in self.data_received:
        self.data_received[reqId] = {}
    
    self.data_received[reqId][tick_types.get(tickType, str(tickType))] = price

def tickByTickAllLast(self, reqId, tickType, time, price, size, 
                     tickAtrribLast, exchange, specialConditions):
    """Receive individual trade ticks"""
    if reqId not in self.tick_data:
        self.tick_data[reqId] = []
    
    self.tick_data[reqId].append({
        'timestamp': time,
        'price': price,
        'size': size,
        'exchange': exchange
    })
```

### Connection Management
```python
def connect_to_ib(self):
    """Establish connection to IB Gateway/TWS"""
    try:
        self.connect(self.host, self.port, self.clientId)
        self.run()  # Start message processing thread
        logger.info(f"Connected to IB on {self.host}:{self.port}")
        return True
    except Exception as e:
        logger.error(f"IB connection failed: {e}")
        return False

def disconnect_from_ib(self):
    """Clean disconnect from IB"""
    self.disconnect()
    logger.info("Disconnected from IB")
```

### Error Handling
```python
def error(self, reqId, errorCode, errorString):
    """Handle IB API errors with specific codes"""
    if errorCode == 162:  # Historical data farm connection OK
        logger.info("IB historical data farm connected")
    elif errorCode == 2104:  # Market data farm connection OK
        logger.info("IB market data farm connected")
    elif errorCode == 2106:  # HMDS data farm connection OK
        logger.info("IB historical market data service connected")
    elif errorCode == 502:  # Can't connect to TWS
        logger.error("Cannot connect to TWS/Gateway - check if running")
        self.connection_failed = True
    elif errorCode == 1100:  # Connectivity lost
        logger.warning("IB connectivity lost - attempting reconnection")
        self.reconnect()
    elif errorCode >= 100:  # System errors
        logger.error(f"IB Error {errorCode}: {errorString}")
    else:
        logger.info(f"IB Info {errorCode}: {errorString}")
```

### Production Usage
```python
# Real implementation in ib_connector.py
def get_daily_bars(self, symbol, days_back=252):
    """Get daily bars for backtesting"""
    contract = self.create_stock_contract(symbol)
    reqId = self.get_next_req_id()
    
    end_date = datetime.now().strftime("%Y%m%d 23:59:59 EST")
    duration = f"{days_back} D"
    
    self.req_historical_data(
        reqId=reqId,
        contract=contract,
        endDateTime=end_date,
        durationStr=duration,
        barSizeSetting="1 day",
        whatToShow="TRADES",
        useRTH=1,  # Regular trading hours only
        formatDate=1
    )
    
    # Wait for data with timeout
    timeout = 30
    start_time = time.time()
    while reqId not in self.historical_data and time.time() - start_time < timeout:
        time.sleep(0.1)
    
    if reqId in self.historical_data:
        return pd.DataFrame(self.historical_data[reqId])
    else:
        raise TimeoutError(f"IB historical data timeout for {symbol}")
```

## Rate Limiting Strategy

### Alpaca Markets
- **Limit**: 200 requests/minute
- **Strategy**: Batch requests, cache results
- **Backoff**: Exponential backoff on 429 errors

### SEC Edgar
- **Limit**: 10 requests/second
- **Strategy**: 100ms delay between requests
- **Headers**: Required User-Agent identification

### Google Gemini
- **Limit**: 60 requests/minute
- **Strategy**: Queue requests, cache results
- **Retry**: Exponential backoff on rate limits

## Data Quality Measures

### Alpaca Data Validation
```python
def validate_bars_data(self, df):
    # Check for missing data
    if df.empty:
        raise DataQualityError("No bars data returned")
    
    # Validate price relationships
    invalid_bars = df[df['high'] < df['low']]
    if not invalid_bars.empty:
        logger.warning(f"Invalid OHLC relationships: {len(invalid_bars)} bars")
    
    # Check for extreme gaps
    price_changes = df['close'].pct_change()
    extreme_gaps = price_changes.abs() > 0.5  # >50% gaps
    if extreme_gaps.any():
        logger.info(f"Extreme gaps detected: {extreme_gaps.sum()} instances")
```

### SEC Filing Validation
```python
def validate_filing_content(self, content):
    if not content or len(content) < 1000:
        raise DataQualityError("Filing content too short or empty")
    
    # Check for common filing indicators
    required_phrases = ['SECURITIES AND EXCHANGE COMMISSION', 'FORM ', 'FILED PURSUANT']
    if not any(phrase in content.upper() for phrase in required_phrases):
        raise DataQualityError("Content doesn't appear to be a valid SEC filing")
```

## Monitoring and Alerting

### API Health Checks
```python
def check_api_health(self):
    health_status = {}
    
    # Test Alpaca connection
    try:
        account = self.alpaca.get_account()
        health_status['alpaca'] = 'healthy'
    except Exception as e:
        health_status['alpaca'] = f'error: {e}'
    
    # Test SEC API
    try:
        response = requests.get('https://www.sec.gov/Archives/edgar/daily-index/', 
                              headers=self.edgar_headers, timeout=10)
        health_status['sec'] = 'healthy' if response.status_code == 200 else 'degraded'
    except Exception as e:
        health_status['sec'] = f'error: {e}'
    
    return health_status
```

### Performance Metrics
- **API Response Times**: Track latency for each endpoint
- **Success Rates**: Monitor 200 vs error responses
- **Data Completeness**: Track missing data incidents
- **Rate Limit Usage**: Monitor quota consumption

## Security Considerations

### API Key Management
- Environment variables for all credentials
- No hardcoded keys in source code
- Separate keys for development/production

### Request Security
- HTTPS only for all API calls
- Proper User-Agent headers
- Request signing where required

### Data Privacy
- No PII stored or transmitted
- Public market data only
- SEC filings are public domain

## Testing Strategy

### Real API Tests
All tests use real API endpoints to ensure:
- Actual rate limiting behavior
- Real data quality issues
- Authentic error conditions
- True latency measurements

### Test Data Management
```python
def test_alpaca_historical_data(self):
    # Use real symbol with known history
    data = self.data_service.get_historical_data('AAPL', '2023-01-01', '2023-01-31')
    assert not data.empty
    assert 'close' in data.columns
    assert data.index.is_monotonic_increasing
```

### Error Simulation
```python
def test_rate_limit_handling(self):
    # Make rapid requests to trigger rate limiting
    for i in range(250):  # Exceed 200/minute limit
        try:
            self.data_service.get_historical_data('AAPL', '2023-01-01', '2023-01-02')
        except RateLimitError:
            # Verify proper handling
            break
    else:
        self.fail("Rate limit not properly enforced")
```