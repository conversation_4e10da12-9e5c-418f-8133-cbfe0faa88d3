#!/usr/bin/env python3
"""
Backtesting Strategy with Real-Time Volume Entry Signals

Shows how to integrate the real-time volume detector into backtesting
without look-ahead bias.

NO FAKES, NO MOCKS - Real backtesting with realistic entry timing.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Tuple, Optional
import logging

from core.data_service import DataService
from analysis.realtime_volume_accumulation_detector import RealtimeVolumeAccumulationDetector
from analysis.react_comprehensive_analyzer import ReactATMAnalyzer

logger = logging.getLogger(__name__)


class VolumeBasedBacktestStrategy:
    """
    Backtest strategy that enters positions based on real-time volume signals.
    
    Key Features:
    - No look-ahead bias (only uses past data at each point)
    - Minute-level precision for entry timing
    - ATM prediction window management
    - Realistic entry/exit execution
    """
    
    def __init__(self):
        self.data_service = DataService()
        self.volume_detector = RealtimeVolumeAccumulationDetector()
        self.atm_analyzer = ReactATMAnalyzer()
        
    def run_backtest(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        initial_capital: float = 100000
    ) -> Dict[str, any]:
        """
        Run complete backtest with realistic volume-based entries.
        
        Process:
        1. Get ATM prediction window from SEC analysis
        2. Monitor volume accumulation in real-time
        3. Enter when accumulation signal triggers
        4. Exit based on gap events or stop loss
        """
        logger.info(f"Starting backtest for {symbol} from {start_date} to {end_date}")
        
        trades = []
        current_position = None
        capital = initial_capital
        
        # Parse dates
        current_date = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        
        # Main backtest loop - day by day
        while current_date <= end:
            # Skip weekends
            if current_date.weekday() >= 5:
                current_date += timedelta(days=1)
                continue
                
            # Step 1: Check if we need ATM analysis (monthly)
            if current_date.day == 1 or current_position is None:
                atm_window = self._get_atm_prediction_window(symbol, current_date)
                logger.info(f"ATM window for {current_date}: {atm_window}")
            
            # Step 2: If within ATM window, monitor volume
            if self._within_atm_window(current_date, atm_window):
                # Check volume accumulation throughout the day
                entry_signal = self._monitor_day_for_entry(symbol, current_date)
                
                if entry_signal and current_position is None:
                    # Enter position
                    entry_price = self._get_entry_price(symbol, entry_signal['entry_time'])
                    position_size = self._calculate_position_size(
                        capital, 
                        entry_signal['suggested_position_size']
                    )
                    
                    current_position = {
                        'symbol': symbol,
                        'entry_time': entry_signal['entry_time'],
                        'entry_price': entry_price,
                        'shares': int(position_size / entry_price),
                        'entry_reason': entry_signal,
                        'atm_window': atm_window
                    }
                    
                    logger.info(f"ENTRY: {symbol} at ${entry_price:.2f} "
                              f"({current_position['shares']} shares) "
                              f"on {entry_signal['entry_time']}")
            
            # Step 3: If in position, check for exit
            if current_position:
                exit_signal = self._check_exit_conditions(
                    symbol, current_date, current_position
                )
                
                if exit_signal:
                    # Exit position
                    exit_price = exit_signal['exit_price']
                    pnl = (exit_price - current_position['entry_price']) * current_position['shares']
                    capital += pnl
                    
                    trade = {
                        **current_position,
                        'exit_time': exit_signal['exit_time'],
                        'exit_price': exit_price,
                        'exit_reason': exit_signal['reason'],
                        'pnl': pnl,
                        'return_pct': (exit_price / current_position['entry_price'] - 1) * 100
                    }
                    trades.append(trade)
                    
                    logger.info(f"EXIT: {symbol} at ${exit_price:.2f} "
                              f"PnL: ${pnl:.2f} ({trade['return_pct']:.1f}%) "
                              f"Reason: {exit_signal['reason']}")
                    
                    current_position = None
            
            # Move to next day
            current_date += timedelta(days=1)
        
        # Calculate performance metrics
        metrics = self._calculate_performance_metrics(trades, initial_capital, capital)
        
        return {
            'trades': trades,
            'metrics': metrics,
            'final_capital': capital
        }
    
    def _get_atm_prediction_window(
        self, 
        symbol: str, 
        analysis_date: datetime
    ) -> Dict[str, any]:
        """
        Get ATM prediction window from SEC analysis.
        
        Returns window start/end dates for monitoring.
        """
        try:
            # Run ATM analysis
            result = self.atm_analyzer.analyze_atm_risk(
                symbol=symbol,
                analysis_date=analysis_date.strftime('%Y-%m-%d'),
                lookback_days=730
            )
            
            if result.get('predicted_atm_date'):
                # Create window around prediction
                pred_date = pd.to_datetime(result['predicted_atm_date'])
                window_days = 14  # +/- 2 weeks
                
                return {
                    'start': pred_date - timedelta(days=window_days),
                    'end': pred_date + timedelta(days=window_days),
                    'center': pred_date,
                    'probability': result.get('atm_probability', 0)
                }
            else:
                # No prediction - use default window
                return {
                    'start': analysis_date,
                    'end': analysis_date + timedelta(days=30),
                    'center': analysis_date + timedelta(days=15),
                    'probability': 0
                }
                
        except Exception as e:
            logger.error(f"ATM analysis failed: {e}")
            # Fallback window
            return {
                'start': analysis_date,
                'end': analysis_date + timedelta(days=30),
                'center': analysis_date + timedelta(days=15),
                'probability': 0
            }
    
    def _within_atm_window(self, current_date: datetime, atm_window: Dict) -> bool:
        """Check if current date is within ATM prediction window."""
        return atm_window['start'] <= current_date <= atm_window['end']
    
    def _monitor_day_for_entry(
        self, 
        symbol: str, 
        date: datetime
    ) -> Optional[Dict[str, any]]:
        """
        Monitor throughout the day for volume accumulation entry signal.
        
        Checks every 30 minutes during market hours.
        """
        # Market hours: 9:30 AM - 4:00 PM ET
        market_open = date.replace(hour=9, minute=30, second=0)
        market_close = date.replace(hour=16, minute=0, second=0)
        
        current_time = market_open
        
        while current_time <= market_close:
            # Check for entry signal at this exact time
            signal = self.volume_detector.detect_realtime_entry(
                symbol=symbol,
                current_time=current_time,
                mode='backtest'  # Prevents look-ahead
            )
            
            if signal['entry_signal']:
                return signal
            
            # Move to next check (every 30 minutes)
            current_time += timedelta(minutes=30)
        
        return None
    
    def _get_entry_price(self, symbol: str, entry_time: datetime) -> float:
        """
        Get realistic entry price at signal time.
        
        Uses next bar's open to simulate real execution.
        """
        try:
            # Get minute bars around entry time
            start = entry_time - timedelta(hours=1)
            end = entry_time + timedelta(hours=1)
            
            minute_bars = self.data_service.get_minute_bars(
                symbol,
                start.strftime('%Y-%m-%d'),
                end.strftime('%Y-%m-%d')
            )
            
            # Find next bar after signal
            future_bars = minute_bars[minute_bars.index > entry_time]
            
            if not future_bars.empty:
                # Use next bar's open (realistic execution)
                return float(future_bars.iloc[0]['open'])
            else:
                # Use last available price
                past_bars = minute_bars[minute_bars.index <= entry_time]
                return float(past_bars.iloc[-1]['close'])
                
        except Exception as e:
            logger.error(f"Failed to get entry price: {e}")
            # Fallback to daily bar
            daily_bars = self.data_service.get_daily_bars(
                symbol,
                entry_time.strftime('%Y-%m-%d'),
                entry_time.strftime('%Y-%m-%d')
            )
            return float(daily_bars.iloc[0]['close'])
    
    def _check_exit_conditions(
        self,
        symbol: str,
        current_date: datetime,
        position: Dict
    ) -> Optional[Dict[str, any]]:
        """
        Check for exit conditions:
        1. Gap up detected (main exit)
        2. Stop loss hit (20% down)
        3. Outside ATM window
        4. Time-based exit
        """
        try:
            # Get current price
            daily_bars = self.data_service.get_daily_bars(
                symbol,
                current_date.strftime('%Y-%m-%d'),
                current_date.strftime('%Y-%m-%d')
            )
            
            if daily_bars.empty:
                return None
                
            current_bar = daily_bars.iloc[0]
            
            # Check 1: Gap up (main exit signal)
            if len(daily_bars) >= 2:
                prev_close = float(daily_bars.iloc[1]['close'])
                gap_pct = (float(current_bar['open']) - prev_close) / prev_close * 100
                
                if gap_pct >= 30:  # 30%+ gap
                    return {
                        'exit_time': current_date.replace(hour=7, minute=30),  # Pre-market
                        'exit_price': float(current_bar['open']),
                        'reason': f'Gap up {gap_pct:.1f}%'
                    }
            
            # Check 2: Stop loss
            current_price = float(current_bar['close'])
            loss_pct = (current_price / position['entry_price'] - 1) * 100
            
            if loss_pct <= -20:  # 20% stop loss
                return {
                    'exit_time': current_date.replace(hour=10, minute=0),
                    'exit_price': current_price,
                    'reason': 'Stop loss'
                }
            
            # Check 3: Outside ATM window
            if current_date > position['atm_window']['end']:
                return {
                    'exit_time': current_date.replace(hour=15, minute=30),
                    'exit_price': current_price,
                    'reason': 'ATM window expired'
                }
            
            # Check 4: Time-based (holding too long)
            days_held = (current_date - position['entry_time']).days
            if days_held > 30:
                return {
                    'exit_time': current_date.replace(hour=15, minute=30),
                    'exit_price': current_price,
                    'reason': 'Time limit (30 days)'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Exit check failed: {e}")
            return None
    
    def _calculate_position_size(self, capital: float, recommended_pct: float) -> float:
        """Calculate position size based on current capital."""
        return capital * (recommended_pct / 100)
    
    def _calculate_performance_metrics(
        self, 
        trades: List[Dict], 
        initial_capital: float,
        final_capital: float
    ) -> Dict[str, any]:
        """Calculate backtest performance metrics."""
        if not trades:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'total_return': 0,
                'sharpe_ratio': 0
            }
        
        # Basic metrics
        returns = [t['return_pct'] for t in trades]
        wins = [r for r in returns if r > 0]
        
        # Calculate metrics
        total_return = (final_capital / initial_capital - 1) * 100
        win_rate = len(wins) / len(trades) * 100
        avg_return = np.mean(returns)
        
        # Sharpe ratio (simplified)
        if len(returns) > 1:
            sharpe = np.mean(returns) / np.std(returns) * np.sqrt(252)
        else:
            sharpe = 0
        
        return {
            'total_trades': len(trades),
            'win_rate': win_rate,
            'avg_return': avg_return,
            'total_return': total_return,
            'sharpe_ratio': sharpe,
            'max_win': max(returns) if returns else 0,
            'max_loss': min(returns) if returns else 0,
            'avg_days_held': np.mean([(t['exit_time'] - t['entry_time']).days for t in trades])
        }


def run_example_backtest():
    """Run example backtest showing volume-based entry timing."""
    strategy = VolumeBasedBacktestStrategy()
    
    # Run backtest
    result = strategy.run_backtest(
        symbol='PLUG',
        start_date='2024-01-01',
        end_date='2024-06-30',
        initial_capital=100000
    )
    
    # Print results
    print(f"\n=== Backtest Results ===")
    print(f"Total Trades: {result['metrics']['total_trades']}")
    print(f"Win Rate: {result['metrics']['win_rate']:.1f}%")
    print(f"Avg Return: {result['metrics']['avg_return']:.2f}%")
    print(f"Total Return: {result['metrics']['total_return']:.2f}%")
    print(f"Sharpe Ratio: {result['metrics']['sharpe_ratio']:.2f}")
    print(f"Avg Days Held: {result['metrics']['avg_days_held']:.1f}")
    
    # Show individual trades
    print(f"\n=== Individual Trades ===")
    for i, trade in enumerate(result['trades']):
        print(f"\nTrade {i+1}:")
        print(f"  Entry: {trade['entry_time']} @ ${trade['entry_price']:.2f}")
        print(f"  Exit: {trade['exit_time']} @ ${trade['exit_price']:.2f}")
        print(f"  Return: {trade['return_pct']:.2f}% ({trade['exit_reason']})")
        print(f"  Accumulation Score: {trade['entry_reason']['accumulation_score']:.2f}")


if __name__ == "__main__":
    run_example_backtest()