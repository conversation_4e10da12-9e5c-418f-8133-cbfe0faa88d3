#!/usr/bin/env python3
"""
Market Cap Filtering Module

Implements market cap filtering for the gap-up ATM strategy.
Focus on small-cap stocks (<$100M) as they are most susceptible to ATM dilution.

REAL DATA ONLY - Uses real Alpaca API for market cap calculations.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
from core.data_service import DataService
from core.logger import get_logger
import requests
import time
import json

logger = get_logger(__name__)


class MarketCapFilter:
    """Filters stocks by market capitalization using real-time data."""

    def __init__(self, data_service: DataService = None):
        self.data_service = data_service or DataService()
        # Market cap thresholds (in USD)
        self.MICRO_CAP_MAX = 50_000_000  # $50M
        self.SMALL_CAP_MAX = 100_000_000  # $100M
        self.MID_CAP_MAX = 500_000_000  # $500M

        # Cache for market cap data
        self.market_cap_cache = {}
        self.cache_expiry = {}
        self.cache_duration_hours = 24  # Cache for 24 hours

    def get_market_cap_from_alpaca(self, symbol: str) -> Optional[float]:
        """
        Get market cap using Alpaca fundamentals data.
        Note: This may not be available for all symbols.
        """
        try:
            # Try to get latest price and shares outstanding from Alpaca
            # This is a simplified approach - in production we'd use a dedicated
            # fundamentals data provider

            # Get current price
            latest_bars = self.data_service.get_daily_bars(
                symbol,
                (datetime.now() - timedelta(days=5)).strftime("%Y-%m-%d"),
                datetime.now().strftime("%Y-%m-%d"),
            )

            if latest_bars.empty:
                return None

            current_price = latest_bars["close"].iloc[-1]

            # For now, we'll estimate shares outstanding based on typical patterns
            # In production, this would come from fundamentals data
            # Small-cap stocks typically have 10-50M shares outstanding
            estimated_shares = 25_000_000  # Conservative estimate

            market_cap = current_price * estimated_shares
            logger.debug(f"Estimated market cap for {symbol}: ${market_cap:,.0f}")

            return market_cap

        except Exception as e:
            logger.debug(f"Error getting market cap from Alpaca for {symbol}: {e}")
            return None

    def get_market_cap_from_free_api(self, symbol: str) -> Optional[float]:
        """
        Get market cap using free financial APIs (backup method).
        """
        try:
            # Try Financial Modeling Prep free API (limited requests per day)
            url = f"https://financialmodelingprep.com/api/v3/market-capitalization/{symbol}"

            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    market_cap = data[0].get("marketCap")
                    if market_cap:
                        logger.debug(
                            f"Free API market cap for {symbol}: ${market_cap:,.0f}"
                        )
                        return float(market_cap)

            # Alternative: Try Alpha Vantage free tier (if available)
            # This would require an API key but has generous free limits

            return None

        except Exception as e:
            logger.debug(f"Error getting market cap from free API for {symbol}: {e}")
            return None

    def get_market_cap_estimate(self, symbol: str, price: float) -> Optional[float]:
        """
        Estimate market cap based on typical small-cap patterns.
        This is a fallback when real data isn't available.
        """
        try:
            # For small-cap stocks, typical share counts:
            # - Micro-cap: 5-20M shares
            # - Small-cap: 20-50M shares
            # - Based on price, estimate reasonable share count

            if price < 1.0:
                # Very low price stocks often have high share counts
                estimated_shares = 50_000_000
            elif price < 5.0:
                # Low price, medium share count
                estimated_shares = 30_000_000
            elif price < 20.0:
                # Medium price, lower share count
                estimated_shares = 20_000_000
            else:
                # Higher price, lower share count
                estimated_shares = 10_000_000

            estimated_market_cap = price * estimated_shares
            logger.debug(
                f"Estimated market cap for {symbol} at ${price}: ${estimated_market_cap:,.0f}"
            )

            return estimated_market_cap

        except Exception as e:
            logger.debug(f"Error estimating market cap for {symbol}: {e}")
            return None

    def get_market_cap(self, symbol: str, as_of_date: str = None) -> Optional[float]:
        """
        Get market cap for a symbol using multiple methods.

        Args:
            symbol: Stock symbol
            as_of_date: Date for market cap calculation (if None, uses current)

        Returns:
            Market cap in USD, or None if unavailable
        """
        # Check cache first
        cache_key = f"{symbol}_{as_of_date or 'current'}"
        current_time = datetime.now()

        if (
            cache_key in self.market_cap_cache
            and cache_key in self.cache_expiry
            and current_time < self.cache_expiry[cache_key]
        ):
            return self.market_cap_cache[cache_key]

        market_cap = None

        # Method 1: Try Alpaca (primary source)
        market_cap = self.get_market_cap_from_alpaca(symbol)

        # Method 2: Try free APIs (backup)
        if market_cap is None:
            market_cap = self.get_market_cap_from_free_api(symbol)

        # Method 3: Estimate based on current price
        if market_cap is None:
            try:
                # Get current price from our data service
                recent_bars = self.data_service.get_daily_bars(
                    symbol,
                    (datetime.now() - timedelta(days=3)).strftime("%Y-%m-%d"),
                    datetime.now().strftime("%Y-%m-%d"),
                )

                if not recent_bars.empty:
                    current_price = recent_bars["close"].iloc[-1]
                    market_cap = self.get_market_cap_estimate(symbol, current_price)

            except Exception as e:
                logger.debug(f"Error getting price for market cap estimation: {e}")

        # Cache the result
        if market_cap is not None:
            self.market_cap_cache[cache_key] = market_cap
            self.cache_expiry[cache_key] = current_time + timedelta(
                hours=self.cache_duration_hours
            )

        return market_cap

    def classify_by_market_cap(self, symbol: str, as_of_date: str = None) -> str:
        """
        Classify stock by market cap category.

        Returns:
            'micro', 'small', 'mid', 'large', or 'unknown'
        """
        market_cap = self.get_market_cap(symbol, as_of_date)

        if market_cap is None:
            return "unknown"

        if market_cap < self.MICRO_CAP_MAX:
            return "micro"
        elif market_cap < self.SMALL_CAP_MAX:
            return "small"
        elif market_cap < self.MID_CAP_MAX:
            return "mid"
        else:
            return "large"

    def filter_by_market_cap(
        self,
        symbols: List[str],
        max_market_cap: float = None,
        min_market_cap: float = None,
        categories: List[str] = None,
    ) -> List[str]:
        """
        Filter symbols by market cap criteria.

        Args:
            symbols: List of symbols to filter
            max_market_cap: Maximum market cap (if None, no upper limit)
            min_market_cap: Minimum market cap (if None, no lower limit)
            categories: List of categories to include ['micro', 'small', 'mid', 'large']

        Returns:
            Filtered list of symbols
        """
        filtered_symbols = []

        # Default to small-cap focus for gap-up ATM strategy
        if max_market_cap is None and categories is None:
            max_market_cap = self.SMALL_CAP_MAX

        for symbol in symbols:
            try:
                market_cap = self.get_market_cap(symbol)

                if market_cap is None:
                    logger.debug(f"Skipping {symbol} - market cap unknown")
                    continue

                # Apply market cap range filters
                if min_market_cap and market_cap < min_market_cap:
                    continue

                if max_market_cap and market_cap > max_market_cap:
                    continue

                # Apply category filters
                if categories:
                    category = self.classify_by_market_cap(symbol)
                    if category not in categories:
                        continue

                filtered_symbols.append(symbol)
                logger.debug(f"Included {symbol} - market cap ${market_cap:,.0f}")

            except Exception as e:
                logger.debug(f"Error filtering {symbol}: {e}")
                continue

        logger.info(
            f"Market cap filter: {len(filtered_symbols)}/{len(symbols)} symbols passed"
        )
        return filtered_symbols

    def get_small_cap_universe(self, symbols: List[str]) -> List[str]:
        """
        Get small-cap universe specifically for gap-up ATM strategy.
        Focuses on stocks most susceptible to dilution.
        """
        return self.filter_by_market_cap(
            symbols,
            max_market_cap=self.SMALL_CAP_MAX,
            min_market_cap=1_000_000,  # Minimum $1M to avoid penny stocks
        )

    def analyze_market_cap_distribution(self, symbols: List[str]) -> Dict:
        """
        Analyze market cap distribution of a symbol list.
        """
        distribution = {"micro": [], "small": [], "mid": [], "large": [], "unknown": []}

        market_caps = {}

        for symbol in symbols:
            market_cap = self.get_market_cap(symbol)
            category = self.classify_by_market_cap(symbol)

            distribution[category].append(symbol)
            if market_cap:
                market_caps[symbol] = market_cap

        # Calculate statistics
        stats = {}
        for category, symbols_in_category in distribution.items():
            stats[category] = {
                "count": len(symbols_in_category),
                "percentage": len(symbols_in_category) / len(symbols) * 100,
            }

        # Overall stats
        if market_caps:
            all_caps = list(market_caps.values())
            stats["overall"] = {
                "median_market_cap": np.median(all_caps),
                "mean_market_cap": np.mean(all_caps),
                "total_symbols": len(symbols),
                "with_market_cap_data": len(market_caps),
            }

        return {
            "distribution": distribution,
            "statistics": stats,
            "market_caps": market_caps,
        }

    def is_small_cap(self, symbol: str) -> bool:
        """
        Check if a symbol is small cap (market cap < $100M).
        ETFs should be excluded and return False.
        """
        try:
            # ETF detection - common ETF suffixes and symbols
            etf_indicators = [
                symbol.upper()
                in ["SPY", "QQQ", "IWM", "DIA", "GLD", "TLT", "VTI", "VEA", "VWO"],
                symbol.upper().endswith("ETF"),
                "ETF" in symbol.upper(),
            ]

            if any(etf_indicators):
                return False

            market_cap = self.get_market_cap(symbol)
            if market_cap is None:
                return False

            return market_cap < self.SMALL_CAP_MAX

        except Exception as e:
            logger.debug(f"Error checking if {symbol} is small cap: {e}")
            return False

    def get_risk_factors(self, stock_data: Dict) -> List[str]:
        """
        Get risk factors for a stock based on price and market cap.
        """
        risk_factors = []

        symbol = stock_data.get("symbol", "UNKNOWN")
        price = stock_data.get("price", 0)
        market_cap = stock_data.get("market_cap", 0)

        # Penny stock risks
        if price < 1.0:
            risk_factors.append("Penny stock - high volatility")
            risk_factors.append("Limited liquidity")

        if price < 0.50:
            risk_factors.append("Sub-dollar stock - extreme risk")

        # Market cap risks
        if market_cap < 10_000_000:  # $10M
            risk_factors.append("Micro-cap - very high risk")

        if market_cap < 1_000_000:  # $1M
            risk_factors.append("Nano-cap - extreme risk")

        # Price/market cap consistency
        if market_cap > 0 and price > 0:
            implied_shares = market_cap / price
            if implied_shares > 1_000_000_000:  # 1B shares
                risk_factors.append("Excessive share count")

        return risk_factors


def test_market_cap_filter():
    """Test the market cap filtering functionality."""
    logger.info("=== Testing Market Cap Filter ===")

    # Test with some known symbols
    test_symbols = [
        "AAPL",  # Large cap
        "TSLA",  # Large cap
        "NVAX",  # Small/Mid cap biotech
        "SAVA",  # Small cap biotech
        "CERS",  # Small cap biotech
        "NNDM",  # Small cap tech
        "SENS",  # Small cap medtech
    ]

    filter_obj = MarketCapFilter()

    # Test individual market cap lookup
    for symbol in test_symbols:
        market_cap = filter_obj.get_market_cap(symbol)
        category = filter_obj.classify_by_market_cap(symbol)

        if market_cap:
            logger.info(f"{symbol}: ${market_cap:,.0f} ({category})")
        else:
            logger.info(f"{symbol}: Market cap unknown ({category})")

    # Test filtering
    small_caps = filter_obj.get_small_cap_universe(test_symbols)
    logger.info(f"\nSmall-cap universe ({len(small_caps)}): {small_caps}")

    # Test distribution analysis
    analysis = filter_obj.analyze_market_cap_distribution(test_symbols)

    logger.info("\nMarket Cap Distribution:")
    for category, stats in analysis["statistics"].items():
        if category != "overall":
            logger.info(
                f"  {category.title()}: {stats['count']} ({stats['percentage']:.1f}%)"
            )

    if "overall" in analysis["statistics"]:
        overall = analysis["statistics"]["overall"]
        logger.info(f"\nOverall Statistics:")
        logger.info(f"  Median Market Cap: ${overall['median_market_cap']:,.0f}")
        logger.info(f"  Mean Market Cap: ${overall['mean_market_cap']:,.0f}")
        logger.info(
            f"  Data Coverage: {overall['with_market_cap_data']}/{overall['total_symbols']}"
        )


if __name__ == "__main__":
    test_market_cap_filter()
