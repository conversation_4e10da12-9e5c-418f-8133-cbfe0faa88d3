#!/usr/bin/env python3
"""
Test script to verify IB async migration is working correctly.
Tests volume scaling, backward compatibility, and parallel fetching.
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.data_service import DataService
from core.data_service_enhanced import DataServiceEnhanced
from core.logger import get_logger

logger = get_logger(__name__)


def test_volume_scaling():
    """Test that volumes are correctly scaled from lots to shares"""
    logger.info("\n=== Testing Volume Scaling ===")
    
    service = DataService()
    try:
        # Get minute bars for a liquid stock
        symbol = "AAPL"
        df = service.get_minute_bars(symbol, days=1)
        
        if df.empty:
            logger.error("No minute data received")
            return False
            
        # Check volume magnitude - AAPL typically trades millions of shares per day
        avg_minute_volume = df['volume'].mean()
        total_volume = df['volume'].sum()
        
        logger.info(f"Symbol: {symbol}")
        logger.info(f"Bars retrieved: {len(df)}")
        logger.info(f"Average minute volume: {avg_minute_volume:,.0f} shares")
        logger.info(f"Total day volume: {total_volume:,.0f} shares")
        logger.info(f"Sample volumes: {df['volume'].head().tolist()}")
        
        # Sanity check - AAPL should have > 1M shares per day
        if total_volume < 1_000_000:
            logger.error(f"❌ Volume too low! Expected > 1M, got {total_volume:,.0f}")
            logger.error("Volume scaling may not be working correctly")
            return False
        else:
            logger.info("✅ Volume scaling appears correct (> 1M shares/day)")
            return True
            
    except Exception as e:
        logger.error(f"Error testing volume scaling: {e}")
        return False
    finally:
        service.close()


def test_backward_compatibility():
    """Test that existing code still works with the new connector"""
    logger.info("\n=== Testing Backward Compatibility ===")
    
    service = DataService()
    try:
        # Test daily bars
        symbol = "MSFT"
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        df = service.get_daily_bars(
            symbol, 
            start_date.strftime("%Y-%m-%d"),
            end_date.strftime("%Y-%m-%d")
        )
        
        if df.empty:
            logger.error("No daily data received")
            return False
            
        logger.info(f"Daily bars for {symbol}: {len(df)} bars")
        logger.info(f"Date range: {df.index.min()} to {df.index.max()}")
        logger.info(f"Columns: {df.columns.tolist()}")
        
        # Check data integrity
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            logger.error(f"❌ Missing columns: {missing_cols}")
            return False
        else:
            logger.info("✅ All required columns present")
            
        # Test minute bars
        minute_df = service.get_minute_bars(symbol, days=1)
        logger.info(f"Minute bars for {symbol}: {len(minute_df)} bars")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing backward compatibility: {e}")
        return False
    finally:
        service.close()


def test_parallel_fetching():
    """Test parallel data fetching with connection pool"""
    logger.info("\n=== Testing Parallel Data Fetching ===")
    
    service = DataServiceEnhanced(max_connections=5)
    try:
        symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        
        # Test sequential fetching
        logger.info("Testing sequential fetching...")
        start_time = time.time()
        sequential_results = {}
        
        for symbol in symbols:
            df = service.get_daily_bars(symbol, start_date, end_date)
            sequential_results[symbol] = len(df)
            
        sequential_time = time.time() - start_time
        logger.info(f"Sequential fetch completed in {sequential_time:.2f} seconds")
        
        # Test parallel fetching
        logger.info("\nTesting parallel fetching...")
        start_time = time.time()
        
        parallel_results = service.get_parallel_daily_bars(symbols, start_date, end_date)
        
        parallel_time = time.time() - start_time
        logger.info(f"Parallel fetch completed in {parallel_time:.2f} seconds")
        
        # Compare results
        logger.info("\nResults comparison:")
        for symbol in symbols:
            seq_count = sequential_results.get(symbol, 0)
            par_count = len(parallel_results.get(symbol, pd.DataFrame()))
            match = "✅" if seq_count == par_count else "❌"
            logger.info(f"{symbol}: Sequential={seq_count}, Parallel={par_count} {match}")
            
        # Calculate speedup
        speedup = sequential_time / parallel_time if parallel_time > 0 else 0
        logger.info(f"\nSpeedup: {speedup:.2f}x faster with parallel fetching")
        
        if speedup > 1.5:
            logger.info("✅ Parallel fetching provides significant speedup")
            return True
        else:
            logger.warning("⚠️ Parallel fetching not significantly faster")
            return True  # Still pass if results are correct
            
    except Exception as e:
        logger.error(f"Error testing parallel fetching: {e}")
        return False
    finally:
        service.cleanup()


def test_connection_pool():
    """Test connection pool management"""
    logger.info("\n=== Testing Connection Pool ===")
    
    from core.ib_async_connector_enhanced import get_connection_pool, cleanup_connection_pool_sync
    
    try:
        # Get pool
        pool = get_connection_pool(max_connections=3)
        logger.info(f"Created connection pool with max {pool.max_connections} connections")
        
        # Test getting connections
        connections = []
        for i in range(3):
            conn = pool.get_connection()
            if conn:
                connections.append(conn)
                logger.info(f"Got connection {i+1}")
            else:
                logger.error(f"Failed to get connection {i+1}")
                
        # Release connections
        for i, conn in enumerate(connections):
            pool.release_connection(conn)
            logger.info(f"Released connection {i+1}")
            
        logger.info("✅ Connection pool working correctly")
        return True
        
    except Exception as e:
        logger.error(f"Error testing connection pool: {e}")
        return False
    finally:
        cleanup_connection_pool_sync()


def main():
    """Run all tests"""
    logger.info("=" * 60)
    logger.info("IB ASYNC MIGRATION TEST SUITE")
    logger.info("=" * 60)
    
    tests = [
        ("Volume Scaling", test_volume_scaling),
        ("Backward Compatibility", test_backward_compatibility),
        ("Parallel Fetching", test_parallel_fetching),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            logger.info(f"\nRunning: {test_name}")
            passed = test_func()
            results.append((test_name, passed))
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results.append((test_name, False))
            
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASS ✅" if result else "FAIL ❌"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
            
    logger.info(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("\n🎉 All tests passed! Migration successful.")
        return 0
    else:
        logger.error("\n❌ Some tests failed. Please investigate.")
        return 1


if __name__ == "__main__":
    exit(main())