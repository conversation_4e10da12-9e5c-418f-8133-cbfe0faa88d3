#!/usr/bin/env python3
"""Quick check of data storage and fix locking issue."""

import sys
import os
from sqlalchemy import text

# Add project to path
sys.path.insert(0, os.path.dirname(__file__))

from core.data_service import DataService


def main():
    """Quick check and proper cleanup."""
    print("🔍 Quick Data Storage Check")
    print("=" * 40)
    
    ds = None
    try:
        ds = DataService()
        
        # Check what's in database
        print("\n📊 Database Contents:")
        
        # Check minute bars
        minute_count = ds.db_conn.execute(text("SELECT COUNT(*) FROM stock_bars_minute")).scalar()
        print(f"   Minute bars: {minute_count:,} records")
        
        # Check daily bars  
        daily_count = ds.db_conn.execute(text("SELECT COUNT(*) FROM stock_bars_daily")).scalar()
        print(f"   Daily bars:  {daily_count:,} records")
        
        # Check tick data
        tick_count = ds.db_conn.execute(text("SELECT COUNT(*) FROM stock_ticks")).scalar()
        print(f"   Tick data:   {tick_count:,} records")
        
        # Check symbols
        if minute_count > 0:
            symbols = ds.db_conn.execute(text("SELECT DISTINCT symbol FROM stock_bars_minute")).fetchall()
            symbol_list = [r[0] for r in symbols[:5]]  # First 5
            print(f"   Symbols: {symbol_list}")
        
        print("\n✅ Data Storage Status:")
        print(f"   Minute Data: {'✅ SAVED' if minute_count > 0 else '❌ EMPTY'}")
        print(f"   Daily Data:  {'✅ SAVED' if daily_count > 0 else '❌ EMPTY'}")
        print(f"   Tick Data:   {'✅ SAVED' if tick_count > 0 else '❌ EMPTY'}")
        
        print("\n🔧 Locking Issue Fix:")
        print("   - Scripts must call ds.close() at the end")
        print("   - This properly disconnects IB connection")
        print("   - Prevents hanging processes")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        # CRITICAL: Always close connections to prevent locking
        if ds:
            print("\n🔒 Closing connections...")
            ds.close()
            print("   ✅ Connections closed properly")
        
        print("\n✅ Script completed cleanly!")


if __name__ == "__main__":
    main()
